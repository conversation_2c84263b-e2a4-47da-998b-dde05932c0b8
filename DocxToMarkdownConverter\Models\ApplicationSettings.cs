using DocxToMarkdownConverter.ViewModels;

namespace DocxToMarkdownConverter.Models;

public class ApplicationSettings : ViewModelBase
{
    private ConversionOptions _conversionOptions = new();
    private AnimationSettings _animationSettings = new();
    private ThemeSettings _themeSettings = new();
    private ConversionStatistics _statistics = new();
    private string _lastOutputDirectory = string.Empty;
    private bool _rememberWindowState = true;
    private bool _isMaximized = false;

    public ConversionOptions ConversionOptions
    {
        get => _conversionOptions;
        set => SetProperty(ref _conversionOptions, value);
    }

    public AnimationSettings AnimationSettings
    {
        get => _animationSettings;
        set => SetProperty(ref _animationSettings, value);
    }

    public ThemeSettings ThemeSettings
    {
        get => _themeSettings;
        set => SetProperty(ref _themeSettings, value);
    }

    public ConversionStatistics Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    public string LastOutputDirectory
    {
        get => _lastOutputDirectory;
        set => SetProperty(ref _lastOutputDirectory, value);
    }

    public bool RememberWindowState
    {
        get => _rememberWindowState;
        set => SetProperty(ref _rememberWindowState, value);
    }

    public bool IsMaximized
    {
        get => _isMaximized;
        set => SetProperty(ref _isMaximized, value);
    }


}