using System.Windows.Controls;
using DocxToMarkdownConverter.ViewModels;

namespace DocxToMarkdownConverter.Controls;

/// <summary>
/// SimpleProgressControl.xaml 的交互逻辑
/// </summary>
public partial class SimpleProgressControl : System.Windows.Controls.UserControl
{
    public SimpleProgressControl()
    {
        InitializeComponent();
        DataContext = new ProgressViewModel();
        
        // 注册事件处理程序
        Loaded += SimpleProgressControl_Loaded;
        Unloaded += SimpleProgressControl_Unloaded;
    }

    /// <summary>
    /// 获取或设置 ViewModel
    /// </summary>
    public ProgressViewModel ViewModel => (ProgressViewModel)DataContext;

    /// <summary>
    /// 当日志内容更新时自动滚动到底部
    /// </summary>
    private void OnLogContentChanged()
    {
        if (ViewModel.AutoScrollEnabled)
        {
            Dispatcher.BeginInvoke(() =>
            {
                LogScrollViewer.ScrollToEnd();
            });
        }
    }

    /// <summary>
    /// 当控件加载时设置事件处理
    /// </summary>
    private void SimpleProgressControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
    {
        // 监听 ViewModel 的属性变化
        if (ViewModel != null)
        {
            ViewModel.PropertyChanged += (s, args) =>
            {
                switch (args.PropertyName)
                {
                    case nameof(ProgressViewModel.LogContent):
                        OnLogContentChanged();
                        break;
                    case nameof(ProgressViewModel.AutoScrollEnabled):
                        if (ViewModel.AutoScrollEnabled)
                        {
                            LogScrollViewer.ScrollToEnd();
                        }
                        break;
                }
            };
        }
    }

    /// <summary>
    /// 当控件卸载时清理资源
    /// </summary>
    private void SimpleProgressControl_Unloaded(object sender, System.Windows.RoutedEventArgs e)
    {
        ViewModel?.Dispose();
    }
}