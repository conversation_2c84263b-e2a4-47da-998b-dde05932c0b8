using DocxToMarkdownConverter.Models;
using System.IO;

namespace DocxToMarkdownConverter.Services;

public enum ConfigurationFormat
{
    Json,
    Xml
}

public enum ValidationResult
{
    Valid,
    Invalid,
    Warning
}

public class ConfigurationValidationResult
{
    public ValidationResult Result { get; set; }
    public string Message { get; set; } = string.Empty;
    public string PropertyName { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
}

public interface IConfigurationService
{
    // Basic configuration operations
    Task<ApplicationSettings> LoadApplicationSettingsAsync();
    Task SaveApplicationSettingsAsync(ApplicationSettings settings);
    Task<T> LoadSettingsAsync<T>() where T : new();
    Task SaveSettingsAsync<T>(T settings);
    Task ResetToDefaultsAsync();
    Task ResetToDefaultsAsync<T>() where T : new();

    // File operations
    string GetSettingsFilePath<T>();
    bool SettingsFileExists<T>();
    Task<bool> BackupSettingsAsync<T>();
    Task<bool> RestoreSettingsFromBackupAsync<T>();

    // Multi-format support
    Task<T> LoadSettingsAsync<T>(ConfigurationFormat format) where T : new();
    Task SaveSettingsAsync<T>(T settings, ConfigurationFormat format);
    Task<bool> ConvertSettingsFormatAsync<T>(ConfigurationFormat fromFormat, ConfigurationFormat toFormat) where T : class, new();

    // Import/Export functionality
    Task<bool> ExportSettingsAsync<T>(string filePath, ConfigurationFormat format = ConfigurationFormat.Json) where T : class, new();
    Task<T?> ImportSettingsAsync<T>(string filePath, ConfigurationFormat? format = null) where T : new();
    Task<bool> ExportAllSettingsAsync(string directoryPath, ConfigurationFormat format = ConfigurationFormat.Json);
    Task<bool> ImportAllSettingsAsync(string directoryPath);

    // Validation and defaults
    Task<ConfigurationValidationResult> ValidateSettingsAsync<T>(T settings);
    Task<T> GetDefaultSettingsAsync<T>() where T : new();
    Task<bool> IsSettingsValidAsync<T>(T settings);

    // Configuration monitoring
    event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;
    void StartWatchingConfiguration<T>();
    void StopWatchingConfiguration<T>();

    // Advanced operations
    Task<Dictionary<string, object>> GetAllSettingsAsync();
    Task<bool> MergeSettingsAsync<T>(T baseSettings, T overrideSettings) where T : new();
    Task<string> GetSettingsHashAsync<T>() where T : class, new();
}

public class ConfigurationChangedEventArgs : EventArgs
{
    public Type SettingsType { get; set; }
    public string PropertyName { get; set; } = string.Empty;
    public object? OldValue { get; set; }
    public object? NewValue { get; set; }
    public DateTime Timestamp { get; set; } = DateTime.Now;

    public ConfigurationChangedEventArgs(Type settingsType)
    {
        SettingsType = settingsType;
    }
}

public partial class ConfigurationService : IConfigurationService, IDisposable
{
    private readonly string _settingsDirectory;
    private readonly string _settingsFileName = "ApplicationSettings.json";
    private readonly string _backupDirectory;
    private readonly Dictionary<Type, FileSystemWatcher> _watchers = new();
    private readonly IAdvancedLoggingService? _logger;

    public event EventHandler<ConfigurationChangedEventArgs>? ConfigurationChanged;

    public ConfigurationService(IAdvancedLoggingService? logger = null)
    {
        _logger = logger;
        _settingsDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DocxToMarkdownConverter");
        _backupDirectory = Path.Combine(_settingsDirectory, "Backups");

        EnsureSettingsDirectoryExists();
        EnsureBackupDirectoryExists();
    }

    public async Task<ApplicationSettings> LoadApplicationSettingsAsync()
    {
        var filePath = Path.Combine(_settingsDirectory, _settingsFileName);

        if (!File.Exists(filePath))
        {
            _logger?.LogInfo("Application settings file not found, creating default settings");
            var defaultSettings = new ApplicationSettings();
            await SaveApplicationSettingsAsync(defaultSettings);
            return defaultSettings;
        }

        try
        {
            var json = await File.ReadAllTextAsync(filePath);
            var settings = System.Text.Json.JsonSerializer.Deserialize<ApplicationSettings>(json);
            var result = settings ?? new ApplicationSettings();

            // Validate loaded settings
            var validationResult = await ValidateSettingsAsync(result);
            if (validationResult.Result == ValidationResult.Invalid)
            {
                _logger?.LogWarning($"Invalid application settings detected: {validationResult.Message}. Using defaults.");
                return new ApplicationSettings();
            }

            _logger?.LogInfo("Application settings loaded successfully");
            return result;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to load application settings: {ex.Message}", ex);
            System.Diagnostics.Debug.WriteLine($"Failed to load application settings: {ex.Message}");
            return new ApplicationSettings();
        }
    }

    public async Task SaveApplicationSettingsAsync(ApplicationSettings settings)
    {
        var filePath = Path.Combine(_settingsDirectory, _settingsFileName);

        try
        {
            // Validate settings before saving
            var validationResult = await ValidateSettingsAsync(settings);
            if (validationResult.Result == ValidationResult.Invalid)
            {
                throw new InvalidOperationException($"Cannot save invalid settings: {validationResult.Message}");
            }

            // Create backup before saving
            await BackupSettingsAsync<ApplicationSettings>();

            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };

            var json = System.Text.Json.JsonSerializer.Serialize(settings, options);
            await File.WriteAllTextAsync(filePath, json);

            _logger?.LogInfo("Application settings saved successfully");

            // Notify configuration changed
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(typeof(ApplicationSettings)));
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to save application settings: {ex.Message}", ex);
            System.Diagnostics.Debug.WriteLine($"Failed to save application settings: {ex.Message}");
            throw;
        }
    }

    public async Task<T> LoadSettingsAsync<T>() where T : new()
    {
        var fileName = $"{typeof(T).Name}.json";
        var filePath = Path.Combine(_settingsDirectory, fileName);

        if (!File.Exists(filePath))
        {
            return new T();
        }

        try
        {
            var json = await File.ReadAllTextAsync(filePath);
            var settings = System.Text.Json.JsonSerializer.Deserialize<T>(json);
            return settings ?? new T();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to load settings for {typeof(T).Name}: {ex.Message}");
            return new T();
        }
    }

    public async Task SaveSettingsAsync<T>(T settings)
    {
        var fileName = $"{typeof(T).Name}.json";
        var filePath = Path.Combine(_settingsDirectory, fileName);

        try
        {
            var options = new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true,
                PropertyNamingPolicy = System.Text.Json.JsonNamingPolicy.CamelCase
            };

            var json = System.Text.Json.JsonSerializer.Serialize(settings, options);
            await File.WriteAllTextAsync(filePath, json);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to save settings for {typeof(T).Name}: {ex.Message}");
            throw;
        }
    }

    public async Task ResetToDefaultsAsync()
    {
        try
        {
            var defaultSettings = new ApplicationSettings();
            await SaveApplicationSettingsAsync(defaultSettings);
            _logger?.LogInfo("Application settings reset to defaults");
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to reset settings to defaults: {ex.Message}", ex);
            System.Diagnostics.Debug.WriteLine($"Failed to reset settings to defaults: {ex.Message}");
            throw;
        }
    }

    public async Task ResetToDefaultsAsync<T>() where T : new()
    {
        try
        {
            var defaultSettings = new T();
            await SaveSettingsAsync(defaultSettings);
            _logger?.LogInfo($"{typeof(T).Name} settings reset to defaults");
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to reset {typeof(T).Name} settings to defaults: {ex.Message}", ex);
            throw;
        }
    }

    public string GetSettingsFilePath<T>()
    {
        if (typeof(T) == typeof(ApplicationSettings))
        {
            return Path.Combine(_settingsDirectory, _settingsFileName);
        }

        var fileName = $"{typeof(T).Name}.json";
        return Path.Combine(_settingsDirectory, fileName);
    }

    public bool SettingsFileExists<T>()
    {
        var filePath = GetSettingsFilePath<T>();
        return File.Exists(filePath);
    }

    public async Task<bool> BackupSettingsAsync<T>()
    {
        try
        {
            var sourceFilePath = GetSettingsFilePath<T>();
            if (!File.Exists(sourceFilePath))
            {
                return true; // No file to backup
            }

            var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
            var backupFileName = $"{typeof(T).Name}_{timestamp}.json";
            var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

            File.Copy(sourceFilePath, backupFilePath, true);

            // Keep only the last 10 backups
            await CleanupOldBackupsAsync<T>();

            _logger?.LogInfo($"Created backup for {typeof(T).Name} settings: {backupFileName}");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to backup {typeof(T).Name} settings: {ex.Message}", ex);
            return false;
        }
    }

    public async Task<bool> RestoreSettingsFromBackupAsync<T>()
    {
        try
        {
            var backupFiles = Directory.GetFiles(_backupDirectory, $"{typeof(T).Name}_*.json")
                .OrderByDescending(f => f)
                .ToList();

            if (!backupFiles.Any())
            {
                _logger?.LogWarning($"No backup files found for {typeof(T).Name}");
                return false;
            }

            var latestBackup = backupFiles.First();
            var targetFilePath = GetSettingsFilePath<T>();

            File.Copy(latestBackup, targetFilePath, true);

            _logger?.LogInfo($"Restored {typeof(T).Name} settings from backup: {Path.GetFileName(latestBackup)}");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to restore {typeof(T).Name} settings from backup: {ex.Message}", ex);
            return false;
        }
        finally
        {
            await Task.CompletedTask; // 消除async警告
        }
    }

    public async Task<ConfigurationValidationResult> ValidateSettingsAsync<T>(T settings)
    {
        try
        {
            var result = new ConfigurationValidationResult { Result = ValidationResult.Valid };

            // Perform type-specific validation
            switch (settings)
            {
                case ApplicationSettings appSettings:
                    result = await ValidateApplicationSettingsAsync(appSettings);
                    break;
                case ConversionOptions convOptions:
                    result = await ValidateConversionOptionsAsync(convOptions);
                    break;
                case AnimationSettings animSettings:
                    result = await ValidateAnimationSettingsAsync(animSettings);
                    break;
                case ThemeSettings themeSettings:
                    result = await ValidateThemeSettingsAsync(themeSettings);
                    break;
                default:
                    // Generic validation - check for null values in required properties
                    result = await ValidateGenericSettingsAsync(settings);
                    break;
            }

            return result;
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    public async Task<T> GetDefaultSettingsAsync<T>() where T : new()
    {
        await Task.CompletedTask; // Make method async for consistency
        return new T();
    }

    public async Task<bool> IsSettingsValidAsync<T>(T settings)
    {
        var result = await ValidateSettingsAsync(settings);
        return result.Result == ValidationResult.Valid;
    }

    private void EnsureSettingsDirectoryExists()
    {
        if (!Directory.Exists(_settingsDirectory))
        {
            Directory.CreateDirectory(_settingsDirectory);
        }
    }

    private void EnsureBackupDirectoryExists()
    {
        if (!Directory.Exists(_backupDirectory))
        {
            Directory.CreateDirectory(_backupDirectory);
        }
    }

    private async Task CleanupOldBackupsAsync<T>()
    {
        try
        {
            var backupFiles = Directory.GetFiles(_backupDirectory, $"{typeof(T).Name}_*.json")
                .OrderByDescending(f => f)
                .Skip(10) // Keep only the latest 10 backups
                .ToList();

            foreach (var file in backupFiles)
            {
                File.Delete(file);
            }

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger?.LogWarning($"Failed to cleanup old backups for {typeof(T).Name}: {ex.Message}");
        }
    }
}