# 基于多策略融合协同式自适应进化排课算法研究

<center>陆圣安<sup>1</sup> 王新峰<sup>1</sup></center>

<center>（1. 江西水利电力大学，信息工程学院，南昌，330099）</center>

<center>（2. 江西省xxx，江西 南昌，330099；）</center>

**摘要：**高校排课问题因其多维度约束交织与资源配置优化的高度复杂性，一直是组合优化领域的挑战。为应对此难题，本研究提出并实现了一个多策略融合协同式自适应进化排课算法。该算法构建“局部约束消解-全局搜索优化-鲁棒性增强”的三级递进式架构：第一阶段，通过面向特殊需求的启发式引导贪心初始化算法，优先调度高约束课程；第二阶段，采用启发式引导初始化与冲突感知变异的快速自适应遗传算法，进行全局资源优化配置；第三阶段，基于动态局部修复的贪心再调度与冲突消解算法，有效消除残余冲突。该算法创新性融合多维度优先级函数改进密度计算，引入二元组染色体编码，以及多目标适应度函数均衡全局资源，从而高效求解出课程-教室-时间的高效三维匹配问题。实验证实，本研究为复杂约束下的课程调度提供了可扩展的实用化高效解决方案。

**关键词**：智能排课；多策略协同进化；冲突修复；自适应进化；资源均衡优化​

中国分类号：		文献标志码：

<center>A Multi-Strategy Collaborative Adaptive Evolutionary Scheduling Algorithm for University Course Timetabling</center>

<center>LU Sheng-An<sup>1 </sup>WANG Xin-Feng<sup>1</sup></center>

<center>School of Information Engineering, Jiangxi University of Water Resources and Electric Power ,Nanchang,330099,China）</center>

<center> Jiangxi Province XXX，Nanchang 330099，China)</center>

**Abstract**: The university course timetabling problem remains a significant challenge in combinatorial optimization, characterized by its high complexity arising from interwoven multi-dimensional constraints and the imperative for optimal resource allocation. To tackle this problem, this study proposes and implements a Multi-Strategy Collaborative Adaptive Evolutionary Scheduling Algorithm. This algorithm leverages a three-stage, progressive architecture designed for local constraint resolution, global search optimization, and robustness enhancement. In the first stage, a Heuristic-Guided Greedy Initialization Scheduler for Special Requirements prioritizes the allocation of highly constrained courses. The second stage employs a Heuristic-Guided Initialization and Conflict-Aware Mutation with Fast Adaptive Genetic Algorithm for Scheduling to perform global resource optimization. The third stage utilizes a Greedy Rescheduling and Conflict Resolution with Dynamic Local Repair to effectively eliminate any residual conflicts. The algorithm innovatively integrates a multi-dimensional priority function, a dyadic chromosome encoding scheme, and a multi-objective fitness function to balance global resources, thereby efficiently solving the complex three-dimensional matching of courses, classrooms, and time slots. Experimental results confirm that this research provides a scalable, practical, and highly efficient solution for course timetabling under complex constraints.

**Keywords**: Intelligent Timetabling; Multi-Strategy Collaborative Evolution; Conflict Resolution; Adaptive Evolution; Resource Balancing and Optimization

## 1 引言

高校课程排课（University Course Timetabling, UCT）是一项复杂的管理任务，需要在有限的时间和空间资源下，高效协调教师、班级、课程与教室等多维度约束。这些约束相互交织，涵盖了教师时间冲突的规避、专业课程对特殊时段的要求、教室类型与容量的适配、学生班级冲突的最小化以及对资源负载均衡等方面。UCT问题由于其固有的复杂性，一直是组合优化领域的难题<sup>[1,2]</sup>。

现有算法如启发式贪心算法、遗传算法等,在单一目标上有一定成效<sup>[3,4]</sup>，但仍存在局限：其一，现有方法难以将局部约束处理、全局搜索优化和鲁棒性修复机制有效集成到一个协同框架中，多数解决方案侧重于局部优化或全局搜索的单一层面<sup>[5,6]</sup>；其二，在资源高负载或约束高度耦合的条件下，难以有效缓解“多米诺效应”，资源分配的均衡性不足；其三，对于大规模复杂场景，现有方法的排课成功率与资源利用均匀性仍有提升空间。

未解决上述问题，本研究提出了多策略融合协同式自适应进化排课算法（Multi-Strategy Collaborative Adaptive Evolutionary Scheduling System, MSCAES）。该算法的核心研究目标主要包括：

设计分级优化架构。建立“局部约束消解→全局搜索优化→鲁棒性增强”的三阶段协同框架，系统性解决不同层次的约束和优化难题。

开发混合算法策略。融合启发式贪心策略、冲突感知变异的自适应遗传算法和动态局部修复机制，实现高效资源搜索与分配，有效消解冲突并抑制“多米诺效应”。

引入关键创新组件。设计多维度优先级函数、二元组染色体编码方式和多目标适应度函数，支撑全局资源的均衡分配。

本研究具有重要的理论意义与实践价值。从理论层面看，MSCAES通过多策略融合与架构设计，为处理复杂约束条件下的组合优化问题提供了新思路。在实践层面，该算法在真实与人工数据集中表现良好，有效提升了排课成功率和资源负载均衡性，为高校管理部门提供了实用的排课解决方案。

## 2 相关工作

高校课程排课问题因其高度约束耦合和NP难特性，一直是组合优化领域的研究热点<sup>[1,2]</sup>。现有求解方法主要分为三类：启发式与构造性算法、元启发式算法、以及多策略融合与超启发式框架。

### 2.1 启发式与构造性算法

启发式算法因其计算效率高，常用于排课系统的初始解生成。早期研究主要基于图着色模型<sup>[7]</sup>和顺序构造方法。Burke等人<sup>[7]</sup>提出的基于超节点着色的贪心框架为后续研究奠定了理论基础。然而，传统构造性策略如Obit等人<sup>[8]</sup>的固定优先级方法存在明显局限：决策模型过于简单，依赖静态规则，难以处理多班级协同、合班上课等复杂约束，导致初始解质量不高。

### 2.2 元启发式算法

元启发式算法通过模拟自然现象或物理过程进行全局搜索，在UCT领域应用广泛。

模拟退火（Simulated Annealing, SA）算法，源于对固体退火过程的模拟<sup>[4]</sup>，通过接受“差解”的概率性跳出机制来避免陷入局部最优。Abramson<sup>[9]</sup>等人成功将其应用于排课问题，展示了该算法强大的全局探索潜力。然而，SA的性能高度依赖于精细的参数调整，并且其收敛速度往往不尽人意。

禁忌搜索（Tabu Search, TS）算法通过维护一个禁忌列表来避免搜索过程中的循环，并引导算法向未探索的区域前进<sup>[10]</sup>。Costa<sup>[11]</sup>和Hertz<sup>[12]</sup>通过设计精巧的邻域结构和禁忌准则，在排课问题上取得了良好的效果，但TS的效果却高度依赖邻域结构设计。

遗传算法（Genetic Algorithm,GA）是UCT领域应用最为广泛的元启发式算法之一<sup>[13,14]</sup>。但传统的GA存在资源表征与搜索效率的瓶颈，例如，刘莉和栗超<sup>[13]</sup>提出的GA采用二进制编码描述时间槽分配，但忽视了教室资源的多维特性，导致冲突检测复杂、效率低下。虽然Han等人<sup>[15]</sup>尝试改进多目标适应度函数，将冲突次数与教室利用率纳入评估，但对关键软约束的建模不足，导致获得的“优化解”在实用性上大打折扣。

为了应对这些挑战，研究者引入了模因算法（Memetic Algorithm, MA），旨在结合GA的全局探索能力和局部搜索的精细优化能力<sup>[16,17]</sup>。但全局与局部搜索的计算资源分配平衡仍是难题<sup>[18]</sup>。本研究通过设计“教室-时间槽”二元组染色体编码，实现了时空资源的显式联合表征，并构建了包含多维度软硬约束的精细化适应度函数，从而有效地提升了算法对高质量解的引导能力与搜索效率。

### 2.3 多策略融合与超启发式框架

为克服单一算法的局限性，“贪心初始化 + 遗传优化”的两阶段框架已成为主流<sup>[1,19]</sup>。然而，其冲突修复机制通常较为粗放，将未安排的课程视为同质集合统一处理，忽视了课程之间的复杂度差异，易导致高难度课程资源被抢占而修复失败，最终陷入修复困境，形成“多米诺骨牌效应”。

超启发式（Hyper-Heuristics, HH）框架通过在更高的抽象层次上“选择或生成启发式”<sup>[20,21]</sup>算法，为解决此问题提供了新思路。根据管理低层启发式（Low-Level Heuristics, LLH）的方式，HH可分为两大类：选择性超启发式和生成性超启发式<sup>[20]</sup>。在UCT领域，选择性超启发式是其中的主流，它通过一个高层策略，从一个预定义的LLH集合中动态选择最适合当前问题状态的启发式来执行<sup>[22,23]</sup>，如Burke等人<sup>[24]</sup>提出的基于图的超启发式。

尽管Song等人<sup>[25]</sup>和Kohshori与Abadeh<sup>[26]</sup>提出了一些基于规则的修复算子，但它们大多固守原始刚性约束边界，缺乏在资源紧张时进行动态松弛适配的能力，难以应对真实场景中的密集冲突和资源缺口问题。

本研究提出的MSCAES是一个领域知识驱动的确定性多策略融合框架。它不采用HH的随机选择机制，而是根据排课问题的内在逻辑设计了一个固定的三阶段流程，并为每个阶段精心设计了专门的混合算法。其核心创新在于：（1）构建课程难度指数模型，实现对疑难课程的优先修复；（2）引入资源约束松弛策略，通过“难度优先分拣 - 松弛资源匹配”的协同机制，突破了传统修复的刚性约束边界，显著提升了在严苛约束的场景下的排课成功率与资源利用效率。

## 3 多策略融合协同式自适应进化排课算法设计

### 3.1 算法总体框架

MSCAES以数据预处理为基础，构建三级递进式优化框架，通过策略协同与阶段级联机制，实现“约束消解-资源优化-鲁棒修复”的全流程闭环优化。该框架通过三大核心算法引擎的有机耦合，实现了从初始解构建到全局优化，再到冲突修复的全流程闭环管理，为复杂约束下的排课难题提供了高效、稳健的解决方案。

数据预处理是整个算法框架的基石。针对高校的教务数据的异构性和非结构化特点，首先进行标准化解析。通过自适应周次解析算法与时空解耦建模机制，将复杂的原始数据转化为标准化的、可计算的排课单元。这一过程不仅统一了数据口径，也为后续算法的精准执行奠定了基础，显著降低了问题处理的复杂度。

阶段一为面向特殊需求的启发式引导贪心初始化算法（HGIS-SR）。此阶段聚焦于局部约束的优先消解。针对具有特殊时间、教室或合班需求的课程，通过构建多维度优先级函数，进行智能分拣与优先调度。该算法阶段旨在快速生成一个高质量的初始可行解，为后续的全局优化奠定坚实基础。

阶段二为启发式引导初始化与冲突感知变异的快速自适应遗传排课算法（HGI-CAMAR）。在获得初始解后，此阶段致力于全局资源的优化配置。通过引入创新的二元组染色体编码与冲突感知的变异算子，对普通课程进行全局搜索。该算法阶段的核心目标是在满足所有硬约束的前提下，最大化教学资源的利用均衡性，如优化教师、班级和教室的负载分布。

阶段三为动态局部修复的贪心再调度与冲突消解算法（GRCR-DLR），为确保排课的完整性与鲁棒性，此阶段专注于处理前两阶段未能成功安排的“剩余课程”。通过构建课程难度指数模型，对疑难课程进行分层、精准修复，并引入资源约束松弛策略，有效化解残余冲突，显著提升最终的排课成功率。

该三级递进式架构通过策略优势互补与技术的有机衔接，系统性地覆盖了排课流程的“预处理 - 优化 - 修复”全环节，形成了一个多策略深度融合的智能调度体系。

### 3.2异构教学数据的标准化解析与时序建模机制

为解决排课系统中跨周次时空数据异构性与多维度资源约束的强耦合问题，本研究提出了结构化数据解析框架。该框架通过三级处理流程，实现原始数据向可计算单元的映射，并设计多周期学时拆分机制与时空解耦编码方案，为核心算法提供标准化输入。主要技术突破包括：

（1）自适应周次解析算法：针对"1-8:2,9-16:4"类异构数据，通过正则表达式提取起始周w<sub>s</sub>、终止周w<sub>e</sub>及周学时h<sub>w</sub>，基于排课单元基准长度L<sub>u</sub>（默认2学时），生成标准化的排课单元集{u<sub>1</sub>,u<sub>2</sub>,...,u<sub>n</sub>}；

（2）时空解耦建模机制：引入时间槽（Time Slot）编码模型（表3-1），实现时间资源标准化表征。时间槽编码s与星期几d(s)及当日节次p(s)的转换关系如下：

<div align='justify'>		（1） </div>

$$d(s)= \left( \frac{s-1}{MAX\_PERIOD\_SLOT}\right) +1$$
<div align='justify'>		（2）</div>

$$p(s)=(s-1) mod MAX\_PERIOD\_SLOT+1$$
其中，d(s)表示时间槽s对应的天数、MAX_PERIOD_SLOT表示一天的节次数量、表示对x向下取整、P(s)表示时间槽s对应的节次、mod为取模运算符。

$\left( x \right)$<center>表3-1 时间槽映射模型（MAX_PERIOD_SLOT = 5）</center>

| **节数范围** | **节次编码** | **周一** | **周二** | **周三** | **周四** | **周五** | **周六** | **周日** |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| 1-2 | 1 | 1 | 6 | 11 | 16 | 21 | 26 | 31 |
| 3-4 | 2 | 2 | 7 | 12 | 17 | 22 | 27 | 32 |
| 5-6 | 3 | 3 | 8 | 13 | 18 | 23 | 28 | 33 |
| 7-8 | 4 | 4 | 9 | 14 | 19 | 24 | 29 | 34 |
| 9-10 | 5 | 5 | 10 | 15 | 20 | 25 | 30 | 35 |

经上述处理后，原始的复杂课程需求，被转化为标准化的排课单元集，为后续的算法提供了一致且规整的输入，使算法可以专注于为每个独立的排课单元寻找合适的时间和空间位置，从而简化了排课问题的复杂度。

### 3.3 阶段一：HGIS-SR算法

#### 3.3.1 多维度优先级建模

为量化课程的调度紧迫性与约束复杂度，构建了综合优先级函数Fp（公式3），它突破了传统单维度决策的局限性，集成五类关键课程约束因子：

<div align='justify'>		（3）</div>

$$F_{p}=(3.5 P +10 M + \frac{S}{40}+ W )\times \left( \begin{matrix}1, & R \leq1 \\ 1+0.1 R , & R >1 \end{matrix}\right)$$
其中，P为基础优先级（取值1-10）；M为多班级数量；S为学生人数；W为周次跨度；R为特殊要求数量。

该函数通过非线性加权机制，将多维度约束转化为可量化的优先级指标。为贪心策略提供了更综合的决策依据。

#### 3.3.2 冲突规避的资源分配

时间槽选择遵循遵循“硬约束优先 + 软约束优化”原则。算法首先筛选出满足硬性时间约束的可用时间槽集合S，然后采用软约束评分模型，计算每个时间槽s的综合得分*Score(s)*，该得分综合考量了时段偏好、连续课程惩罚、天数均衡、时间槽复用奖励以及特殊教室要求这五个维度。

设T<sub>load</sub>(t,d)为教师t在第d天的课程负载，T<sub>consec</sub>(t,d)为教师t在第d天的连续课程节数，C<sub>load</sub>(c,d)是班级c在第d天的课程负载，C<sub>consec</sub>(c,d)为班级c在第d天的连续课程节数，C是课程涉及的班级集合，W为课程的周次跨度，R表示是否有特殊教室要求，表示教师t的日均课程负载，表示班级c的日均课程负载，表示所有班级的平均日均课程负载，A(s)表示时段槽s是否已安排课程且周次不冲突。具体表达式如下：

$T_{load}( t )$
$C_{load}( c )$
$C_{load}$<div align='justify'>		（4）</div>

$$T_{load}( t ) = \frac{1}{MAX \_DAYS \_SLOT}\int_{i =0}^{MAX \_DAYS \_SLOT -1}T_{load}( t , i )$$
<div align='justify'>		（5）</div>

$$C_{load}( c ) = \frac{1}{MAX \_DAYS \_SLOT}\int_{i =0}^{MAX \_DAYS \_SLOT -1}C_{load}( c , i )$$
<div align='justify'>		（6）</div>

$$C_{load}= \frac{1}{\left( C \right)}\int_{c \in C}^{}C_{load}( c )$$
可用时间槽s的综合得分Score(s)的表达式如下：

<div align='justify'>		（7）</div>

$$Score ( s )= P_{period}( s )\times P_{consec}( s )\times P_{balance}( s )\times P_{reuse}( s )\times(1+0.2 R )$$
<div align='justify'>其中，*P*<sub>*period*</sub>*(s)*为时段偏好权重。中间时段 1.4 ，早晚间时段 0.8，以此提升教学时段与师生偏好的匹配度。*P*<sub>*consec*</sub>*(s)*为连续课程惩罚，通过分层约束模型（公式8-10），避免教学负荷过度集中。*P*<sub>*balance*</sub>*(s)*为天数均衡性得分，通过*β*<sub>*t*</sub>*(s)**、**β*<sub>*c*</sub>*(s)**、**γ(s)*的三重维度实现课程分布优化。其中，*β*<sub>*t*</sub>*(s)*为教师负载均衡系数。基准值为1，若教师当天课程负载超过日均负载 + 1，判定为过载并以 0.7 系数降低得分，抑制单日高强度排课。*β*<sub>*c*</sub>*(s)*为班级负载均衡系数。基于班级平均负载的单日过载检测，采用相同逻辑控制班级课程分布不均问题。*γ(s)*为教学日偏好系数，通过权重差异鼓励课程集中在教学日（周一至周五）。*P*<sub>*reuse*</sub>*(s)*为时间槽复用奖励。对于周次跨度W小于18且已安排同班级、周次不冲突的课程，给予 100 倍权重得分，以此强化时间槽复用；否则采用基准权重 1。</div>

 $\beta$<div align='justify'>		（8）</div>

$$P_{consec}( s )= \alpha_{t}( s )\times \alpha_{c}( s )$$
<div align='justify'>		（9）</div>

$$\alpha_{t}( s )= \left( \begin{matrix}0.9-0.2\times min ( T_{consec}( t , d ( s )),3), & T & if T_{consec}( t , d ( s ))>0 & T \\ 1, & ot h erwise \end{matrix}\right)$$
<div align='justify'>	（10）</div>

$$\alpha_{c}( s )= \left( \begin{matrix}0.9-0.2\times min ( \frac{1}{\left( C \right)}\int_{c ϵ C}^{}C_{consec}( c , d ( s )) ,3), & C & C_{consec}( c , d ( s )) & C & if \frac{1}{\left( C \right)}\int_{c ϵ C}^{}C_{consec}( c , d ( s )) >0 & C & C_{consec}( c , d ( s )) & C \\ 1, & ot h erwise \end{matrix}\right)$$
其中，*α*<sub>*t*</sub>*(s)*为教师的连续课程惩罚、*α*<sub>*c*</sub>*(s)*为班级的连续课程惩罚。

 $\alpha$综合得分函数*score(s)*模型纳入*P*<sub>*period*</sub>*(s)、P*<sub>*consec*</sub>*(s)、P*<sub>*balance*</sub>*(s)、P*<sub>*reuse*</sub>*(s)*核心优化因子，分别从四个维度构建评估体系；在此基础上，通过条件因子(1+0.2R)显式处理特殊教室需求：当存在特定教室或教学楼要求（即R=1）时，得分给予 1.2 倍系数，来保障特殊资源约束下的合理排课。最终，该模型通过多目标函数的结构化集成，实现对可用时间槽集合S的量化排序，从而在满足硬约束的基础上，系统性优化教学资源分配的均衡性与高效性，为高质量的课程表生成提供科学的决策依据。

教室分配同样采用多维度评分模型进行智能筛选。优先匹配指定教室，若不可用，则综合考量容量适配、资源复用、使用效率及负载均衡等指标，实现教室资源的高效利用。

设计r表示教室，*cap(r)*表示教室r的容量，*S*<sub>*stu*</sub>为课程的学生规模，*C*<sub>*min*</sub>为课程所需教室的最小容量（即*S*<sub>*stu*</sub>），*C*<sub>*ideal*</sub>为课程所需教室的理想容量(即1.3倍*S*<sub>*stu*</sub>)，*A(r)*表示教室*r*是否已安排课程且周次不冲突，*N(r)*为教室*r*的历史使用次数。教室综合得分*Score(r)*表达式如下：

<div align='justify'>		（11）</div>

$$Score ( r )=0.4 f_{cap}( r ) \times0.25 f_{reuse}( r )\times0.15 f_{eff}( r )\times0.1 f_{bal}( r )$$
其中，*f*<sub>*cap*</sub>*(r)*为容量适配度评分。具体表达式如下：

<div align='justify'>		（12）</div>

$$f_{cap}( r )= \left( \begin{matrix}0, & if cap ( r )< C_{min}& C \\ 10, & if C_{min}\leq cap ( r )\leq C_{ideal}& C & C \\ \frac{10}{( \frac{cap ( r )}{C_{ideal}})^{1.5}}, & ( \frac{cap ( r )}{C_{ideal}}) & C & if cap ( r )> C_{ideal}& C \end{matrix}\right)$$
<div align='justify'>*f*<sub>*reuse*</sub>*(r)*为教室复用奖励评分。对周次跨度W<18的课程，若教室r已安排过周次不冲突的课程（即*A(r)=True*)，赋予高额奖励分150，促进教室资源复用；否则采用基准分1，避免对未复用教室的过度惩罚。*f*<sub>*eff*</sub>*(r)*为使用效率评分。定义效率系数为学生人数与教室容量的比值，转化为 0–10 分制。具体表达式如下：</div>

<div align='justify'>		（13）</div>

$$f_{eff}( r )= min (10, \frac{S_{stu}}{cap ( r )}\times12), w h ere cap ( r )>0$$
*f*<sub>*bal*</sub>*(r)*为使用均衡性评分。通过反比例函数抑制高频使用的教室，平衡教室使用频率，避免资源过度集中。具体表达式如下：

<div align='justify'>		（14）</div>

$$f_{bal}( r )= \frac{8}{1+0.3 N ( r )}$$
该评分模型通过加权几何策略，平均整合各个维度，最终形成综合评分函数*f(r)*，其在满足基本容量需求的前提下，系统性优化教室资源分配的效率与均衡性，为智能排课提供量化决策依据。

### 3.4 阶段二：HGI-CAMAR算法

#### 3.4.1 染色体编码与初始种群生成

本算法采用二元组染色体编码，将所有课程的排课决策表示为一个n×2矩阵，每行基因[(r<sub>1 </sub>, t<sub>1</sub>),(r<sub>2 </sub>, t<sub>2</sub>),…,(r<sub>n </sub>, t<sub>n</sub>)]分别对应课程所需的“教室-时间槽”分配。这种编码方式直接映射排课的核心决策变量。对于合班授课的课程，各班级共享同一组(r<sub>i </sub>, t<sub>i</sub>)，天然地保证了时间与空间分配的一致性

在构建初始种群时，借助与处理好的教室资源索引，通过并行计算，生成多样化的初始解，基于启发式规则，动态选择可行解，减少随机初始化所带来的盲目性。

存在多个候选时间槽时，为选择最优的时间槽，本研究设计了一套启发式评分策略。该策略从教学时段偏好、课程负载均衡性以及时间槽复用等多个维度进行综合评估，为每个候选时间槽计算得分，进而筛选出最合适的安排。其时间槽总得分*Score(s)*的表达式如下：

<div align='justify'>		（15）</div>

$$Score ( s )= P_{balance}( s )∙ P_{reuse}( s )∙ P_{period}( s )$$
<div align='justify'>其中，*P*<sub>*balance*</sub>*(s)*为天数均衡性得分，通过*β*<sub>*teacher*</sub>*(s)**、**β*<sub>*class*</sub>*(s)**、**γ*<sub>*day*</sub>*(s)*的三维评分乘积实现课程分布优化。其中，*β*<sub>*teacher*</sub>*(s)*为教师负载均衡系数。以日均课程负载为基准，若教师在时间槽s对应日期的课程量超出阈值（日均负载 + 1），则赋予0.75权重，否则取基准分1。*β*<sub>*class*</sub>*(s)*为班级负载均衡系数。基于班级平均负载评估，当单日平均课程量超临界值时，采用 0.85 系数调节，否则取基准分1。*γ*<sub>*day*</sub>*(s)*为天数分布系数。对周一至周五赋予 1.2 权重，周末降为 0.8，契合常规教学节奏。*P*<sub>*reuse*</sub>*(s)*为时间槽复用奖励得分，针对周次跨度W<18的课程，若存在兼容课程安排，则赋予200的高额奖励分，以促进时间槽复用。否则采用基准值 1.0，维持评分体系稳定。*P*<sub>*period*</sub>*(s)*为时段偏好得分。对早晚节次赋予 0.8 权重；中间时段赋予 1.2 权重。</div>

 $\beta$在教室选择问题中，为实现资源高效配置，启用启发式评分策略，综合得分通过基础分与三维评分的乘积计算，各维度权重经优化设置，使评分体系既能保障课程基本需求，又能实现资源复用与均衡分配，为智能排课提供科学的量化决策依据。其教室综合得分*Score(r)*具体计算公式如下：

<div align='justify'>		（16）</div>

$$Score ( r )= f_{reuse}( r )\times f_{cap}( r )\times f_{bal}( r )$$
<div align='justify'>其中，*f*<sub>*reuse*</sub>*(r)*为周次跨度优化得分。针对周次跨度W<18的课程，若目标教室*r*在目标时间槽s已存在兼容课程安排(A(r)=True)，则赋予权重10，以鼓励教室资源的复用；否则采用基准分 1。*f*<sub>*cap*</sub>*(s)*为容量匹配度得分，避免资源浪费。具体表达式如下：</div>

<div align='justify'>		（17）</div>

$$f_{cap}( r )= \left( \begin{matrix}5, & if C_{min}\leq cap ( r )\leq C_{ideal}& C & C \\ 5\times \frac{C_{ideal}}{cap ( r )}, & C & if cap ( r )> C_{ideal}& C \end{matrix}\right)$$
*f*<sub>*bal*</sub>*(r)*为教室使用均衡性得分，通过反比例函数调节教室使用频率。具体表达式如下：

<div align='justify'>		（18）</div>

$$f_{bal}( r )= \frac{1}{1+0.1 N ( r )}$$
#### 3.4.2 多目标适应度函数

#### 为实现冲突最小化与课程分布均衡化的双重优化目标，构建多目标适应度函数。综合适应度函数*F*<sub>*total*</sub>表达式如下：

<div align='justify'>		（19）</div>

$$F_{total}= \left( \begin{matrix}1000+100 E_{combined}, & E & if F_{conflict}=1000 & F \\ F_{conflict}∙ \left( 1+0.2 E_{combined}\right) , & F & 1+0.2 E_{combined}& E & if F_{conflict}<1000 & F \end{matrix}\right)$$
其中，*F*<sub>*conflict*</sub>为冲突适应度分数，用于衡量排课方案中的冲突程度（公式20）。*E*<sub>*combined*</sub>为课程分布均匀得分，通过加权求和得到总体均匀性得分（公式21-23）。这种设计引导进化过程向着既无冲突又高度均衡的解空间探索。

<div align='justify'>		（20）</div>

$$F_{conflict}= \left( \begin{matrix}1000, & if N_{conflict}=0 & N \\ \frac{1000}{1+1.5 N_{conflict}}, & N & if N_{conflict}>0 & N \end{matrix}\right)$$
<div align='justify'>		（21）</div>

$$E_{combined}=0.4 E_{teac h er}+0.4 E_{class}+0.2 E_{classeoom}$$
<div align='justify'>		（22）</div>

$$E_{x}= exp \left( -2∙ C V_{x}\right)$$
<div align='justify'>		（23）</div>

$$C V_{x}= \frac{\sigma_{x}}{\mu_{x}}$$
其中，*E*<sub>*x*</sub>为实体x的课程分布均匀性得分；x代表教师、班级或教室；*σ*<sub>*x*</sub>和*µ*<sub>*x*</sub>为实体每日课程数量集合标准差和平均值。

 $\sigma$#### 3.4.3 进化操作优化

本算法采用精英保留锦标赛选择。保留前25%的最优个体直接进入下一代，剩余个体通过锦标赛选择产生。该策略显著提高了优质基因的保留概率。锦标赛大小T根据种群规模*|P|*动态调整（公式24）。

<div align='justify'>		（24）</div>

$$T = max \left( 2, min \left( \frac{\left( P \right)}{4},3 \right) \right)$$
基于有效基因位的数量，动态确定交叉点数*n*<sub>*p*</sub>（公式25）。对多班级课程实施协同基因交换，确保合班时间与教室一致。

<div align='justify'>		（25）</div>

$$n_{p}= \left( \begin{matrix}1， & c h rom \_len \leq5 \\ min ( \left( \frac{c h rom \_len}{5}\right) ,3)， & \frac{c h rom \_len}{5}& 5< c h rom \_len \leq20 \\ min ( \left( \frac{c h rom \_len}{10},5 \right) ), & \frac{c h rom \_len}{10},5 & c h rom \_len > 20 \end{matrix}\right)$$
引入冲突感知变异，优先调整高冲突基因位，针对性修复高冲突区域。公式如下：

<div align='justify'>		（26）</div>

$$n_{m}= \left( \begin{matrix}min \left( \left( C_{p}\right) , max \left( 1, min \left( 5, \frac{\left( C_{p}\right)}{3}\right) \right) \right) , & \left( C_{p}\right) , max \left( 1, min \left( 5, \frac{\left( C_{p}\right)}{3}\right) \right) & C_{p}& C & 1, min \left( 5, \frac{\left( C_{p}\right)}{3}\right) & 5, \frac{\left( C_{p}\right)}{3}& C_{p}& C & if C_{p}\neq\emptyset and random \_num <0.8 & C \\ max \left( 1, min \left( 5, \frac{\left( V \right)}{4}\right) \right) , & 1, min \left( 5, \frac{\left( V \right)}{4}\right) & 5, \frac{\left( V \right)}{4}& V & ot h erwise \end{matrix}\right)$$
对于在变异基因选择新资源时，采用教室容量弹性策略（公式27）。

<div align='justify'>		（27）</div>

$$m =1.2+0.1\times \left( 10- p \right)$$
筛选出合适的教室后，按容量接近程度排序，选择最适合的教室。运用时间槽复用策略，周次跨度W < 18的课程优先选择已安排课程的时间，利用局部搜索增强解的可行性。

#### 3.4.4 遗传主函数优化策略

本算法采取自适应参数调整机制，动态适应种群状态，交叉率*r*<sub>*cross*</sub>与变率率*r*<sub>*mut*</sub>随冲突率*r*<sub>*conflict*</sub>动态调整（公式28-30）。当冲突率较高时，增大交叉率促进基因重组，提高变异率以增强局部搜索能力。反之，则降低参数以稳定优质解的传递。形成 “冲突驱动” 的参数自适应机制。

<div align='justify'>		（28）</div>

$$r_{cross}= min \left( 0.95, r_{base \_cross}\times \left( 1+0.2∙ r_{conflict}\right) \right)$$
<div align='justify'>		（29）</div>

$$r_{mut}= max \left( 0.05, r_{initial \_mut}\times \left( 1+ r_{conflict}\right) \right)$$
<div align='justify'>		（30）</div>

$$r_{conflict}=1.0- \frac{f}{1000}$$
其中，为种群平均适应度。

$f$启用并行计算优化适应度和早期终止策略。利用线程池并行计算子代适应度，当子代数量超过 5 时启用分块处理(公式31)，显著降低大规模种群的评估耗时。同时，设置连续无改进代数阈值（nimp=3），若连续 3 代最佳适应度未提升，或找到无冲突解（fbest≥1000），则提前终止算法。

<div align='justify'>		（31）</div>

$$c h unk \_size = max \left( 2, \left( \frac{pop \_size}{max \_workers}\right) \right)$$
运用多样性维持策略。在种群更新阶段，采用“最优选择 + 随机抽样”策略维持多样性。保留80%的最优个体以保证收敛方向，随机抽取20%的非最优个体，避免早熟，形成结构化的种群更新机制。

通过上述多方位设计优化，阶段二在保持种群多样性的同时加速收敛，实现遗传算法效率、成功率双重提升。

### 3.5 阶段三：GRCR-DLR算法

#### 3.5.1 课程难度评估模型

为有效指导冲突课程的修复顺序，定义课程难度指数*D*<sub>*i*</sub>，表达式如下：

<div align='justify'>		（32）</div>

$$D_{i}=(2 P_{i}+ \frac{S_{i}}{50}+1.5 M_{i}+ \frac{W_{i}}{16}\times1.2)\times R_{i}$$
其中，*P*<sub>*i*</sub>为课程优先级；*S*<sub>*i*</sub>为学生数；*M*<sub>*i*</sub>为多班级数；*W*<sub>*i*</sub>为周次跨度；*R*<sub>*i*</sub>为是否有特殊约束，若存在特殊约束，则取值为1.3，否则取1。

通过综合量化课程调度的复杂程度，为后续冲突修复操作中课程的排序提供了依据，确保在处理排课冲突时，能够有针对性地进行修复操作，从而提高算法的整体效率。

#### 3.5.2 松弛资源匹配策略

在冲突修复阶段，采用松弛资源匹配策略，通过动态调整教室与时间的约束边界，构建多维度弹性匹配模型，以实现资源利用效率与排课质量的平衡。

时间分配采用带负载奖惩的加权选择机制。设计*T*<sub>*day*</sub>为教师当日课程数，*C*<sub>*day*</sub>为班级当日课程总数，*L*<sub>*teacher*</sub>为教室当日课程负载，*L*<sub>*class*</sub>为班级当日课程负载,*A(s)*为时段槽s是否已安排课程且周次不冲突。候选时间S的综合得分*Score(s)*表达式为：

<div align='justify'>		（33）</div>

$$Score ( s )= P_{base}∙ P_{teac h er \_load}∙ P_{class \_load}∙ P_{period \_preference}∙ P_{day \_balance}∙ P_{slot \_reuse}$$
<div align='justify'>		（34）</div>

$$P_{base}= \frac{10.0}{1.0+ T_{day}+ C_{day}}$$
<div align='justify'>其中，*P*<sub>*base*</sub>为基础均匀性得分，以反比例函数度量单日课程负载（公式34）。*P*<sub>*teacher_load*</sub>为教室负载均衡得分，基于教师单日课程强度动态调整评分，通过惩罚系数0.7抑制教师单日高强度授课，奖励空闲日系数1.3以均衡负载。*P*<sub>*class_load*</sub>为班级负载均衡得分，针对班级单日课程密度设计差异化权重，通过降低过载日权重为0.8、提升空闲日优先级系数为1.2，避免班级单日课程过度集中。*P*<sub>*period_preference*</sub>为时段偏好得分，通过提升黄金时段（中间时段）权重系数为1.4，早晚时段的权重系数降为0.8，优化教学效率与师生体验。*P*<sub>*day_balance*</sub>为天数分布得分，通过权重差异引导课程向常规教学日集中。*P*<sub>*slot_reuse*</sub>为时间槽复用得分，针对周次跨度W<18的短期课程，设计复用奖励，通过百倍权重提升复用时间槽优先级。</div>

针对教室资源约束，构建二维评分模型（公式35）解决教室资源匹配问题。综合评分模型*Score(r)*通过线性加权，突出复用策略与容量适配的协同优化。设计A(r)为教室r是否已安排课程且周次不冲突，*C*<sub>*cap*</sub>为教室容量，*C*<sub>*need*</sub>为课程所需容量。

<div align='justify'>		（35）</div>

$$Score \left( r \right) = f_{reuse}∙ f_{cap}$$
其中，*f*<sub>*reuse*</sub>为教室复用得分，对周次跨度W<18的课程，赋予复用教室权重优势，通过十倍权重鼓励教室资源复用，提升时间-空间协同利用率。*f*<sub>*cap*</sub>为容量适配得分，基于教室容量与课程需求的匹配度设计反比例函数（公式36）。

<div align='justify'>		（36）</div>

$$f_{cap}= \frac{1.0}{1.0+ \left( C_{cap}- C_{need}\right)}$$
本策略通过多维度量化评分与松弛约束设计，实现三大核心突破：（1）负载均衡导向。通过教师/班级负载平衡得分，降低单日课程过载风险，显著提升排课方案的实用性；（2）资源复用增强。对周次跨度W<18的课程，提升时间槽与教室复用率，有效减少排课碎片化问题；（3）柔性适配能力。通过参数可调的评分模型，支持不同院校对教学时段偏好、教室容量阈值的个性化配置。该策略通过“量化评分 - 优先级排序 - 动态调整”的闭环机制，为复杂约束下的资源匹配提供了可解释、可优化的解决方案，显著提升排课算法的鲁棒性与资源利用效率。

### 3.6 算法步骤

MSCAES算法采用分阶段多策略协同框架，将排课问题分解为三个核心阶段，在满足硬约束前提下优化资源利用率和课表均衡度。算法流程如下：

**Input:** 课程数据集C={c<sub>1</sub>,c<sub>2</sub>,…,c<sub>n</sub>}，教室数据集R={r<sub>1</sub>,r<sub>2</sub>,…,r<sub>m</sub>}，算法参数θ

**Output:** 最优排课方案S*，失败课程集合C<sub>f</sub>，性能指标M

**Algorithm 1: MSCAES主算法**

**Input:** 课程数据集C，教室数据集R，算法参数θ
**Output:** 排课方案S*，失败课程C_f，性能指标M

1. **数据预处理**：C_encoded ← PreprocessCourses(C)，R_manager ← BuildClassroomManager(R)
2. **阶段一HGIS-SR**：C_special, C_regular ← PartitionCoursesByConstraints(C_encoded)，S₁, C_rem₁ ← HGIS_SR(C_special, R_manager, θ)
3. **阶段二HGI-CAMAR**：C_to_optimize ← C_regular ∪ C_rem₁，S₂, C_f_temp ← HGI_CAMAR(C_to_optimize, R_manager, θ)
4. **阶段三GRCR-DLR**：S_partial ← S₁ ∪ S₂，S₃, C_f ← GRCR_DLR(C_f_temp, S_partial, R_manager, θ)
5. **结果整合**：S* ← S_partial ∪ S₃，M ← EvaluateSchedule(S*, C_f, C)

**Step 1 数据预处理与初始化**：对课程与教室数据进行标准化编码，构建统一数据结构，建立教室资源索引管理器，解析软硬约束条件。

**Step 2 HGIS-SR阶段**：优先处理具有强约束的特殊课程。基于多维度优先级评分模型排序，通过启发式贪心策略分配资源，生成无冲突的局部最优解S₁。

**Step 3 HGI-CAMAR阶段**：对剩余常规课程进行全局搜索优化。通过启发式方法生成高质量初始种群，引入冲突感知的交叉变异算子，寻求全局最优解S₂。

<div align='justify'>Step 4：GRCR-DLR：针对在前序阶段未能成功排课的课程，此阶段作为补充修复机制。该算法首先对失败课程按调度难度进行排序，然后分析当前已生成的课表*S*<sub>*1*</sub>*∪S*<sub>*2*</sub>，动态识别可用的资源“缝隙”，并尝试通过资源松弛或局部调整等策略进行插入式调度。该阶段作为精细化的补充手段，旨在最大化排课成功率。</div>

**Step 5 结果整合与评估**：整合各阶段排课结果形成完整课表S*，输出排课报告，包括最终课表、失败课程列表及性能指标M。

![Image](images/image_004.png)

<center>图3-1 多策略融合协同式自适应进化排课算法流程图</center>

## 4 实验结果与分析

### 4.1 实验设置​

为系统性评估排课算法在约束强度、课程规模、资源异质性三类梯度化场景中的性能边界，实验构建了“人工构造-真实业务”双层数据集体系，并选取五种代表性算法进行对比。具体如下：

#### 4.1.1 数据集

实验采用三类梯度化数据集，覆盖排课问题的“约束复杂度-场景真实性”谱系：

对于高约束人工数据集（Db-2152），针对强约束场景设计，是基于1363门课程的原始数据，并通过标准化处理拓展至2,152门课程的数据集，其集成三类非线性耦合约束：100.00%课程需同步满足特定教室类型与连排节次要求；100.00%课程强制限定授课校区；24.43%课程兼具排课优先级与多班合授特征。该数据集旨在探究算法在NP-hard问题的极端场景下的求解能力。

对于低约束基准数据集（Regular-795），是基于480门课程原始数据标准化生成795个排课单元，作为算法的基础性能评估基准。它仅保留了教师冲突规避和教室容量适配两类核心约束。所有课程对时间槽、地点无特殊要求，构成接近理想化的低约束环境。

对于真实教学数据集（Real-2152），其源自国内某综合性大学2024-2025学年第一学期的真实教务数据，经标准化处理后包含2152门课程。数据特征严格遵循高校实际教学场景，完整保留了真实世界的约束条件与资源分布特征，为算法提供了高保真的验证环境。

#### 4.1.2 对比算法

为全面评估MSCAES算法的性能，文本选取五种代表性排课算法进行比较，覆盖“单一启发式-进化算法-群智能-混合优化”四大范式，对比不同策略的约束满足能力、解质量及计算效率。这些算法包括：

纯贪心算法（Greedy）：一种快速的局部搜索算法，作为计算效率的基线。

纯遗传算法（Genetic Algorithm, GA）：该算法的实现遵循刘莉和栗超<sup>[13]</sup>的设计。其经典的全局搜索能力<sup>[27]</sup>，用于评估基本进化框架的性能。

贪心-遗传混合算法（Greedy-Genetic Hybrid Algorithm, GGHA）：结合了贪心和遗传算法的优点，旨在平衡求解速度和质量。

蚁群算法（Ant Colony Optimization, ACO）：依据韦芳萍<sup>[28]</sup>提出的框架构建出的群智能算法，常用于组合优化问题

基于多元信息引导的人工蜂群算法（Modified Artificial Bee Colony, MIG-ABC）：该算法借鉴了周新宇等人<sup>[30]</sup>提出的多元信息引导思想，构建了一个三阶段协同优化框架。

#### 4.1.3 评价指标

为全面衡量排课算法的性能表现，我们从课表质量与算法效率两个维度构建量化评价体系，覆盖“实用性-完成度-可行性”三大核心目标：

课表质量指标：包括教师均匀性（UTE）、班级均匀性（UCL）、教室均匀性（URO）以及由这三者加权平均得到的综合均匀性得分（UOV），用于量化资源的均衡分布程度。

算法效率指标：包括排课成功率（SC）、运行时间（RT）、处理效率（EP）用于衡量算法的求解能力与计算开销。

### 4.2 高约束人工数据集（Db-2152）

高约束数据集旨在模拟资源极度紧张、约束条件相互耦合的极端排课环境，用以检验算法的鲁棒性。表4-1展示了各算法在Db-2152下的性能对比。

<center>**表 4-1 各算法在高约束人工数据集（Db-2152）的效率指标****和****课表质量指标对比**</center>

| **算法名称** | **排课成功率**<br>**（SC，%）** | **运行时间**<br>**（RT，秒）** | **处理效率**<br>**（EP，门 / 秒）** | **教师均匀性**<br>**（UTE）** | **班级均匀性**<br>**（UCL）** | **教室均匀性**<br>**（URO）** | **综合均匀性得分**<br>**（UOV）** |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Greedy | 78.33 | 0.05 | 43485.88 | 0.2190 | 0.2891 | 0.3506 | 0.2734 |  |
| GA | 82.30 | 612.46 | 3.51 | 0.2372 | 0.3898 | 0.3833 | 0.3275 |  |
| GGHA | 84.98 | 14.25 | 150.87 | 0.1884 | 0.3434 | 0.3281 | 0.2784 |  |
| ACO | 89.64 | 384.77 | 5.59 | 0.2465 | 0.4067 | 0.4017 | 0.3416 |  |
| MIG-ABC | 90.29 | 2293.42 | 0.94 | 0.2314 | 0.4133 | 0.4080 | 0.3395 |  |
| MSCAES | 97.77 | 4.11 | 523.25 | 0.3430 | 0.4972 | 0.4642 | 0.4289 |  |

实验数据显示（表4-1）。MSCAES的SC高达97.77%，将失败课程数控制在 48 门，较次优的 MIG-ABC 算法（90.29%）提升了7.48个百分点。这一成果是在极高的运行效率的前提下取得的，MSCAES的RT仅为4.11秒，EP高达523.25门/秒，分别是ACO（5.59门/秒）和MIG-ABC（0.94门/秒）的93.6倍和556倍。在解的质量方面，MSCAES的UOV达到0.4289，较次优的ACO（0.3416）提升了25.56%，并在UTE、UCL及URO三个维度的均衡性上全面领先，显示出该算法优异的资源均衡调控能力。

MSCAES的性能优势源于其三阶段协同架构。第一阶段（HGIS-SR）通过优先级排序预先处理高约束课程，为全局优化划定稳定边界。第二阶段（HGI-CAMAR）的冲突感知变异算子与多目标适应度函数，在收缩后的解空间内进行高效探索与均衡优化。而至关重要的第三阶段（GRCR-DLR），则是通过对失败课程的难度评估与动态资源"缝隙"搜索，精准修复了大量疑难课程，这是其成功率远超其他算法的核心原因。该架构实现了求解效率与质量的协同优化。

### 4.3 低约束基准数据集（Regular-795）

当系统约束放宽，资源变得相对充裕时，对算法的主要考验从“能否解决”转变为“能否高效且优质地解决”，从而快速收敛，并利用富余资源进行进一步的课表质量提升。表4-2展示了各算法在Regular-795下的性能对比。

<center>**表 4-****2**** 各算法在低约束基准数据集（Regular-795）的效率指标****和****课表质量指标对比**</center>

| **算法名称** | **排课成功率**<br>**（SC，%）** | **运行时间**<br>**（RT，秒）** | **处理效率**<br>**（EP，门 / 秒）** | **教师均匀性**<br>**（UTE）** | **班级均匀性**<br>**（UCL）** | **教室均匀性**<br>**（URO）** | **综合均匀性得分**<br>**（UOV）** |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Greedy | 76.60 | 0.01 | 64497.80 | 0.1682 | 0.3402 | 0.2803 | 0.2594 |  |
| GA | 91.57 | 150.10 | 5.30 | 0.1865 | 0.4692 | 0.3139 | 0.3251 |  |
| GGHA | 91.82 | 2.90 | 274.32 | 0.1625 | 0.3407 | 0.2509 | 0.2515 |  |
| ACO | 90.69 | 110.19 | 7.21 | 0.1889 | 0.4286 | 0.2937 | 0.3057 |  |
| MIG-ABC | 94.09 | 764.51 | 1.04 | 0.1881 | 0.4515 | 0.3080 | 0.3175 |  |
| MSCAES | 99.50 | 2.94 | 269.95 | 0.2384 | 0.5010 | 0.3599 | 0.3677 |  |

由表4-2可知，MSCAES 在该数据集上实现了近乎完美的99.50%%的SC，同时RT保持在 2.94秒的优异水平。这充分展示了其自适应参数调整机制的有效性：算法能识别环境的约束强度，自动调整进化算子参数，避免在简单问题上“过度搜索”，从而实现快速收敛。相比之下，GA 和 MIG-ABC 采用了固定参数，导致了不必要的计算开销，RT分别高达 150.10 秒和 764.51 秒。

约束的放宽为提升课表质量提供了空间。MSCAES不仅能找到可行解，更能向优质解演进。其UOV达到0.3677，较次优的 GA (0.3251) 提升了 13.10%。特别是在UCL指标上，MSCAES达到0.5010，表现突出，证明其多目标适应度函数能有效利用更宽松的资源，引导搜索向帕累托最优前沿逼近，为班级规划出分布更均匀、节奏更合理的课表。

### 4.4 真实教学数据集（Real-2152）

为最终检验算法的工程实用性，采用源自真实教务系统的 Real-2152 数据集进行测试，以评估其在混合约束、噪声数据和不规则需求下的综合应用效能。表4-3展示了各算法在Real-2152上的性能对比。

<center>**表 4-****3**** 各算法在真实教学数据集（Real-2152）的效率指标****和****课表质量指标对比**</center>

| **算法名称** | **排课成功率**<br>**（SC，%）** | **运行时间**<br>**（RT，秒）** | **处理效率**<br>**（EP，门 / 秒）** | **教师均匀性**<br>**（UTE）** | **班级均匀性**<br>**（UCL）** | **教室均匀性**<br>**（URO）** | **综合均匀性得分**<br>**（UOV）** |  |
| --- | --- | --- | --- | --- | --- | --- | --- | --- |
| Greedy | 79.16 | 0.08 | 25578.14 | 0.2339 | 0.2869 | 0.3393 | 0.2762 |  |
| GA | 81.46 | 662.07 | 3.25 | 0.2404 | 0.3833 | 0.3841 | 0.3263 |  |
| GGHA | 85.67 | 6.01 | 357.82 | 0.1858 | 0.3343 | 0.3313 | 0.2743 |  |
| ACO | 89.78 | 526.49 | 4.09 | 0.2366 | 0.3875 | 0.4006 | 0.3298 |  |
| MIG-ABC | 89.92 | 2637.75 | 0.82 | 0.2552 | 0.3927 | 0.3889 | 0.3369 |  |
| MSCAES | 97.40 | 4.24 | 507.85 | 0.3409 | 0.4841 | 0.4702 | 0.4241 |  |

实验结果表明（表4-3），MSCAES在真实复杂场景下实现了效率与质量的协同最优化。其SC高达97.40%，与次优算法相比拉开了超过7个百分点的显著差距。同时，RT仅为4.24秒，与以速度见长的混合算法处于同一量级，成功摆脱了传统元启发式算法“时间换成功率”的困境。在质量维度上，其UOV达到0.4241，较次优算法提升了25.88%，生成的课表在教师、班级、教室三方面资源的均衡分配上具有高度实用价值。

综合来看，MSCAES在成功率、运行时间和课表质量三个核心维度上实现了"三位一体"的领先。算法在高约束和低约束场景下验证的鲁棒性与适应性，在真实世界数据集中得到最终印证，标志着其实现了从寻找"可行解"到生成"优质解"的关键演进，展现了其作为一种先进解决方案的巨大工程潜力

### 4.5 分析与讨论

基于三个数据集的实验结果，MSCAES在排课成功率、运行效率与课表质量上，均体现出优越性，验证了多策略融合架构的有效性<sup>[1,31]</sup>。

![Image](images/image_003.png)

<center>图4-1各算法在不同数据集上排课成功率对比</center>

图4-1显示MSCAES在所有数据集上均获得了最高的SC，在高约束数据集（97.77%）和真实数据集（97.40%）中较次优算法提升了超过7个百分点。这一成果验证了其三阶段“分而治之”架构的有效性：HGIS-SR 优先处理强约束课程，为后续全局优化提供了一个稳定且高质量的基础；HGI-CAMAR在简化的解空间中进行全局搜索；GRCR-DLR通过课程难度评估与资源松弛匹配修复冲突，成功救回了在其他算法中因陷入局部最优而被放弃的课程，从而将成功率推向了理论极限。

![Image](images/image_002.png)

<center>图4-2各算法在不同数据集上运行时间对比</center>

![Image](images/image_001.png)

<center>图4-3 各算法在不同数据集上处理效率对比</center>

图4-2和图4-3揭示了算法在“速度”与“质量”间的权衡。纯贪心算法的运行时间虽然短，但成功率仅为76-79%，实用价值很有限。传统元启发式算法如MIG-ABC和GA，虽能获得较高成功率，但运行时间达数百至数千秒，难以满足实际应用需求。MSCAES则实现了二者的最佳平衡，其在真实数据集下仅需4.24秒即可达到97.40%的成功率。其高效性源于启发式引导初始化避免盲目搜索、冲突感知进化算子减少无效探索、自适应参数调节提升收敛速度以及并行计算缩短整体时间。

![Image](images/image_005.png)

<center>图4-4 不同数据集上排课算法的处理效率对比</center>

图4-4显示，MSCAES在UOV得分上全面领先，较次优算法提升了25%以上。该优势体现在将均衡性作为核心优化目标：HGI-CAMAR阶段的适应度函数同时优化硬约束冲突和软约束均匀性，这是现代元启发式算法设计的趋势<sup>[15,32]</sup>；HGIS-SR和GRCR-DLR阶段的资源选择评分模型中，内嵌了负载均衡因子，使其蕴含了对全局均衡性的考量。

MSCAES的成功并非单一技术的突破，而是系统性架构创新与多策略深度融合的成果。它通过“约束消解-全局优化-鲁棒修复”的三阶段分解策略，在求解成功率、运行效率和课表质量三个维度上实现了协同优化，为NP-hard组合优化问题提供了有效的解决方案。

## 5 结论

针对高校排课问题固有的高维度、多约束、NP难特性，本研究提出并实现了一种MSCAES算法。该算法通过创新的三阶段协同架构，将复杂的排课任务分解为局部约束消解、全局搜索优化与鲁棒性增强三个环节，并融合了启发式贪心、自适应遗传算法与动态修复等多种策略，实现了排课成功率与资源分配均衡性的协同优化。

本研究的核心贡献包括：

提出了一种高效的三阶段协同优化框架。构建了“HGIS-SR→ HGI-CAMAR→ GRCR-DLR”的级联式解法。该框架通过任务分解与优势互补，有效克服了单一算法在处理复杂约束时的局限性，实现了对排课问题全流程的系统化覆盖。这种架构思想与多策略融合及超启发式研究的趋势一致<sup>[33,34]</sup>。

设计并验证了多项关键技术创新。引入融合多维属性的课程优先级函数、时空资源联合表征的二元组染色体编码、兼顾冲突与均衡性的多目标适应度函数，以及基于课程难度评估的分层修复机制。这些创新组件的有机结合，显著提升了算法的搜索效率与解的质量。

通过全面的实验验证了算法的卓越性能。在覆盖高约束、低约束和真实业务场景的三类数据集上，MSCAES相较于五种主流排课算法表现出明显优势。实验结果表明，该算法在排课成功率、运行效率和课表质量三个维度实现了良好性能，为高效教务管理提供了使用的智能化解决方案。

MSCAES为解决大规模、高耦合的课程调度问题提供了一个高效、鲁棒且实用的解决方案，在理论上探索了多策略混合优化的新范式，在实践上为高校教务管理提供了有效的技术支撑。未来研究可进一步探索算法在动态环境下的适应性和扩展性。

# 参考文献​

[1] Abdipoor S, Yaakob R, Goh S L, et al. Meta-heuristic approaches for the University Course Timetabling Problem[J]. Intelligent Systems with Applications, 2023, 19: 200253.

[2] Chen M C, Sze S N, Goh S L, et al. A Survey of University Course Timetabling Problem: Perspectives, Trends and Opportunities[J]. IEEE Access, 2021, 9: 106515–106529.

[3] Glover F, Laguna M: Tabu search, Modern heuristic techniques for combinatorial problems, 1993: 70–150.

[4] Kirkpatrick S, Gelatt Jr C D, Vecchi M P. Optimization by simulated annealing[J]. science, 1983, 220(4598): 671–680.

[5] Burke E K, Petrovic S. Recent research directions in automated timetabling[J]. European Journal of Operational Research, 2002, 140(2): 266–280.

[6] Lewis R. A survey of metaheuristic-based techniques for university timetabling problems[J]. OR spectrum, 2008, 30(1): 167–190.

[7] Burke E K, Mareček J, Parkes A J, et al. A supernodal formulation of vertex colouring with applications in course timetabling[J]. Annals of Operations Research, 2010, 179: 105–130.

[8] Henry Obit J. Developing novel meta-heuristic, hyper-heuristic and cooperative search for course timetabling problems, 2010.

[9] Abramson D. Constructing school timetables using simulated annealing: sequential and parallel algorithms[J]. Management science, 1991, 37(1): 98–113.

[10] Glover F. Tabu search—part I[J]. ORSA Journal on computing, 1989, 1(3): 190–206.

[11] Costa D. A tabu search algorithm for computing an operational timetable[J]. European Journal of Operational Research, 1994, 76(1): 98–110.

[12] Hertz A. Tabu search for large scale timetabling problems[J]. European journal of operational research, 1991, 54(1): 39–47.

[13] 刘莉, 栗超. 基于遗传算法的智能排课系统的设计[J]. 成都工业学院学报, 2023, 26(06): 52–55.

[14] Holland J H. Adaptation in Natural and Artificial Systems: An Introductory Analysis with Applications to Biology, Control, and Artificial Intelligence[M]. The MIT Press, 1992.

[15] Han X, Wang D. Gradual Optimization of University Course Scheduling Problem Using Genetic Algorithm and Dynamic Programming[J]. Algorithms, 2025, 18(3): 158.

[16] Moscato P. On Evolution, Search, Optimization, Genetic Algorithms and Martial Arts - Towards Memetic Algorithms[J]. Caltech Concurrent Computation Program, 2000.

[17] Burke E K, Newall J P, Weare R F. A memetic algorithm for university exam timetabling[C]. Practice and Theory of Automated Timetabling: First International Conference Edinburgh, UK, August 29–September 1, 1995 Selected Papers 1, 1996: 241–250.

[18] Ong Y, Lim M-H, Zhu N, et al. Classification of adaptive memetic algorithms: A comparative study[J]. IEEE transactions on systems, man, and cybernetics. Part B, Cybernetics : a publication of the IEEE Systems, Man, and Cybernetics Society, 2006, 36: 141–52.

[19] Ghaffar A, Din I U, Tariq A, et al. Hybridization and artificial intelligence in optimizing university examination timetabling problem: A systematic review[J]. Review of Education, 2025, 13(2): e70071.

[20] Burke E, Kendall G, Newall J, et al. Hyper-heuristics: An emerging direction in modern search technology[J]. Handbook of metaheuristics, 2003: 457–474.

[21] Burke E K, Gendreau M, Hyde M, et al. Hyper-heuristics: A survey of the state of the art[J]. Journal of the Operational Research Society, 2013, 64(12): 1695–1724.

[22] Qu R, Burke E K. Hybridizations within a graph-based hyper-heuristic framework for university timetabling problems[J]. Journal of the Operational Research Society, 2009, 60(9): 1273–1285.

[23] Soria-Alcaraz J A, Ochoa G, Swan J, et al. Effective learning hyper-heuristics for the course timetabling problem[J]. European Journal of Operational Research, 2014, 238(1): 77–86.

[24] Burke E K, Mccollum B, Meisels A, et al. A graph-based hyper-heuristic for educational timetabling problems[J]. European Journal of Operational Research, 2007, 176(1): 177–192.

[25] Song T, Chen M, Xu Y, et al. Competition-guided multi-neighborhood local search algorithm for the university course timetabling problem[J]. Applied Soft Computing, 2021, 110: 107624.

[26] Kohshori M S, Abadeh M S. Hybrid genetic algorithms for university course timetabling[J]. International Journal of Computer Science Issues (IJCSI), 2012, 9(2): 446.

[27] Burke E K, Newall J P. A multistage evolutionary algorithm for the timetable problem[J]. IEEE transactions on evolutionary computation, 1999, 3(1): 63–74.

[28] 韦芳萍. 基于蚁群算法的高校排课问题的研究[J]. 电脑编程技巧与维护, 2023(07): 32–34+44.

[29] Socha K, Knowles J, Sampels M. A max-min ant system for the university course timetabling problem[C]. Ant Algorithms: Third International Workshop, ANTS 2002 Brussels, Belgium, September 12–14, 2002 Proceedings 3, 2002: 1–13.

[30] 周新宇, 刘颖, 吴艳林, et al. 基于多元信息引导的人工蜂群算法[J]. 电子学报, 2024, 52(04): 1349–1363.

[31] Abdullah S, Turabieh H. On the use of multi neighbourhood structures within a Tabu-based memetic approach to university timetabling problems[J]. information sciences, 2012, 191: 146–168.

[32] Zitzler E, Laumanns M, Thiele L. SPEA2: Improving the strength Pareto evolutionary algorithm[J]. TIK report, 2001, 103.

[33] Teoh C K, Wibowo A, Ngadiman M S. Review of state of the art for metaheuristic techniques in Academic Scheduling Problems[J]. Artificial Intelligence Review, 2015, 44: 1–21.

[34] Bashab A, Ibrahim A O, Abedelgabar E E, et al. A systematic mapping study on solving university timetabling problems using meta-heuristic algorithms[J]. Neural Computing and Applications, 2020, 32: 17397–17432.
