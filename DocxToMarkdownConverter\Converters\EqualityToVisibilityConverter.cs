using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts equality comparison to visibility
/// </summary>
public class EqualityToVisibilityConverter : IValueConverter
{
    public static readonly EqualityToVisibilityConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (parameter == null)
            return Visibility.Collapsed;

        var parameterValue = parameter.ToString();
        var valueString = value?.ToString();

        bool isEqual = string.Equals(valueString, parameterValue, StringComparison.OrdinalIgnoreCase);
        
        return isEqual ? Visibility.Visible : Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}