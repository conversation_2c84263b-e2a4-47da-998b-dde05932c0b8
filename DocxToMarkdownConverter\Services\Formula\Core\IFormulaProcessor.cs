using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Core;

/// <summary>
/// 数学公式处理器主接口
/// </summary>
public interface IFormulaProcessor
{
    /// <summary>
    /// 异步处理数学公式
    /// </summary>
    /// <param name="request">处理请求</param>
    /// <returns>处理结果</returns>
    Task<FormulaProcessingResult> ProcessAsync(FormulaProcessingRequest request);
    
    /// <summary>
    /// 检查是否可以处理指定元素
    /// </summary>
    /// <param name="element">要检查的元素</param>
    /// <returns>是否可以处理</returns>
    Task<bool> CanProcessAsync(object element);
    
    /// <summary>
    /// 从容器中提取所有公式
    /// </summary>
    /// <param name="container">容器元素</param>
    /// <returns>提取的公式元素集合</returns>
    Task<IEnumerable<FormulaElement>> ExtractFormulasAsync(object container);
}

/// <summary>
/// 公式检测器接口
/// </summary>
public interface IFormulaDetector
{
    /// <summary>
    /// 检查元素是否包含公式
    /// </summary>
    /// <param name="element">要检查的元素</param>
    /// <returns>是否包含公式</returns>
    Task<bool> ContainsFormulasAsync(object element);
    
    /// <summary>
    /// 检测公式并返回详细信息
    /// </summary>
    /// <param name="element">要检测的元素</param>
    /// <returns>检测结果</returns>
    Task<FormulaDetectionResult> DetectAsync(object element);
    
    /// <summary>
    /// 获取公式类型
    /// </summary>
    /// <param name="element">公式元素</param>
    /// <returns>公式类型</returns>
    FormulaType GetFormulaType(object element);
}

/// <summary>
/// 公式提取器接口
/// </summary>
public interface IFormulaExtractor
{
    /// <summary>
    /// 从容器中提取所有公式
    /// </summary>
    /// <param name="container">容器元素</param>
    /// <returns>提取的公式元素集合</returns>
    Task<IEnumerable<FormulaElement>> ExtractAsync(object container);
    
    /// <summary>
    /// 提取单个公式元素
    /// </summary>
    /// <param name="element">公式元素</param>
    /// <returns>提取的公式元素</returns>
    Task<FormulaElement> ExtractSingleAsync(object element);
}

/// <summary>
/// 公式解析器接口
/// </summary>
public interface IFormulaParser
{
    /// <summary>
    /// 解析公式元素
    /// </summary>
    /// <param name="element">要解析的公式元素</param>
    /// <returns>解析结果</returns>
    Task<FormulaParseResult> ParseAsync(FormulaElement element);
    
    /// <summary>
    /// 检查是否可以解析指定类型的公式
    /// </summary>
    /// <param name="type">公式类型</param>
    /// <returns>是否可以解析</returns>
    bool CanParse(FormulaType type);
    
    /// <summary>
    /// 支持的公式类型
    /// </summary>
    FormulaType[] SupportedTypes { get; }
}

/// <summary>
/// 公式转换器接口
/// </summary>
public interface IFormulaConverter
{
    /// <summary>
    /// 转换公式结构为指定格式
    /// </summary>
    /// <param name="structure">公式结构</param>
    /// <param name="format">输出格式</param>
    /// <returns>转换后的字符串</returns>
    Task<string> ConvertAsync(FormulaStructure structure, FormulaOutputFormat format);
    
    /// <summary>
    /// 检查是否支持指定格式
    /// </summary>
    /// <param name="format">输出格式</param>
    /// <returns>是否支持</returns>
    bool SupportsFormat(FormulaOutputFormat format);
    
    /// <summary>
    /// 支持的输出格式
    /// </summary>
    FormulaOutputFormat[] SupportedFormats { get; }
}

/// <summary>
/// 公式后处理器接口
/// </summary>
public interface IFormulaPostProcessor
{
    /// <summary>
    /// 检查是否可以处理指定选项
    /// </summary>
    /// <param name="options">格式化选项</param>
    /// <returns>是否可以处理</returns>
    bool CanProcess(FormulaFormattingOptions options);
    
    /// <summary>
    /// 处理公式字符串
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="options">格式化选项</param>
    /// <returns>处理后的字符串</returns>
    Task<string> ProcessAsync(string input, FormulaFormattingOptions options);
    
    /// <summary>
    /// 处理优先级（数值越小优先级越高）
    /// </summary>
    int Priority { get; }
}

/// <summary>
/// 公式格式化器接口
/// </summary>
public interface IFormulaFormatter
{
    /// <summary>
    /// 格式化公式输出
    /// </summary>
    /// <param name="rawOutput">原始输出</param>
    /// <param name="options">格式化选项</param>
    /// <returns>格式化后的输出</returns>
    Task<string> FormatAsync(string rawOutput, FormulaFormattingOptions options);
}

/// <summary>
/// 公式类型检测器接口
/// </summary>
public interface IFormulaTypeDetector
{
    /// <summary>
    /// 检测到的公式类型
    /// </summary>
    FormulaType DetectedType { get; }
    
    /// <summary>
    /// 检查是否可以检测指定元素
    /// </summary>
    /// <param name="element">要检测的元素</param>
    /// <returns>是否可以检测</returns>
    bool CanDetect(object element);
    
    /// <summary>
    /// 异步检测元素
    /// </summary>
    /// <param name="element">要检测的元素</param>
    /// <returns>检测结果</returns>
    Task<bool> DetectAsync(object element);
}

/// <summary>
/// 公式缓存服务接口
/// </summary>
public interface IFormulaCacheService
{
    /// <summary>
    /// 获取或创建缓存项
    /// </summary>
    /// <typeparam name="T">缓存项类型</typeparam>
    /// <param name="key">缓存键</param>
    /// <param name="factory">创建工厂方法</param>
    /// <param name="expiration">过期时间</param>
    /// <returns>缓存项</returns>
    Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null);
    
    /// <summary>
    /// 使缓存失效
    /// </summary>
    /// <param name="pattern">缓存键模式</param>
    /// <returns>异步任务</returns>
    Task InvalidateAsync(string pattern);
    
    /// <summary>
    /// 清除所有缓存
    /// </summary>
    /// <returns>异步任务</returns>
    Task ClearAsync();
}
