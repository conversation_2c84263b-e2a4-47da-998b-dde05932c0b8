using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using Microsoft.Extensions.Logging;
using Timer = System.Threading.Timer;
using Application = System.Windows.Application;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// UI线程优化服务实现
/// </summary>
public class UIThreadOptimizationService : IUIThreadOptimizationService
{
    private readonly ILogger<UIThreadOptimizationService> _logger;
    private readonly Dispatcher _dispatcher;
    private readonly Timer _responsivenessTimer;
    
    private TimeSpan _uiUpdateThrottle = TimeSpan.FromMilliseconds(33); // 30 FPS for better performance
    private DateTime _lastUIUpdate = DateTime.MinValue;
    private int _recommendedBatchSize = 25; // Smaller batches for better responsiveness
    private readonly object _lockObject = new();

    public event EventHandler<UIResponsivenessEventArgs>? UIResponsivenessWarning;

    public UIThreadOptimizationService(ILogger<UIThreadOptimizationService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _dispatcher = Application.Current?.Dispatcher ?? Dispatcher.CurrentDispatcher;
        
        // 创建响应性监控定时器
        _responsivenessTimer = new Timer(CheckResponsivenessCallbackWrapper, null,
            TimeSpan.FromSeconds(1), TimeSpan.FromSeconds(1));
    }

    public async Task<T> ExecuteOnBackgroundThreadAsync<T>(Func<Task<T>> task, CancellationToken cancellationToken = default)
    {
        return await Task.Run(async () =>
        {
            try
            {
                return await task().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "后台任务执行失败");
                throw;
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    public async Task ExecuteOnBackgroundThreadAsync(Func<Task> task, CancellationToken cancellationToken = default)
    {
        await Task.Run(async () =>
        {
            try
            {
                await task().ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "后台任务执行失败");
                throw;
            }
        }, cancellationToken).ConfigureAwait(false);
    }

    public async Task<T> ExecuteOnUIThreadAsync<T>(Func<T> task)
    {
        if (_dispatcher.CheckAccess())
        {
            return task();
        }

        return await _dispatcher.InvokeAsync(task, DispatcherPriority.Normal);
    }

    public async Task ExecuteOnUIThreadAsync(Action task)
    {
        if (_dispatcher.CheckAccess())
        {
            task();
            return;
        }

        await _dispatcher.InvokeAsync(task, DispatcherPriority.Normal);
    }

    public async Task ExecuteBatchAsync<T>(
        IEnumerable<T> items, 
        Func<T, Task> processor,
        IProgress<BatchProgress>? progress = null, 
        CancellationToken cancellationToken = default)
    {
        var itemList = items.ToList();
        var totalItems = itemList.Count;
        var processedItems = 0;
        var startTime = DateTime.Now;

        _logger.LogInformation("开始批量处理 {TotalItems} 个项目", totalItems);

        var batchSize = GetRecommendedBatchSize();
        var batches = itemList.Chunk(batchSize);

        foreach (var batch in batches)
        {
            cancellationToken.ThrowIfCancellationRequested();

            // 在后台线程处理批次
            await ExecuteOnBackgroundThreadAsync(async () =>
            {
                var tasks = batch.Select(async item =>
                {
                    await processor(item).ConfigureAwait(false);
                    Interlocked.Increment(ref processedItems);
                });

                await Task.WhenAll(tasks).ConfigureAwait(false);
            }, cancellationToken).ConfigureAwait(false);

            // 在UI线程更新进度
            if (progress != null && ShouldUpdateUI())
            {
                await ExecuteOnUIThreadAsync(() =>
                {
                    var elapsed = DateTime.Now - startTime;
                    var remaining = processedItems > 0 
                        ? TimeSpan.FromMilliseconds(elapsed.TotalMilliseconds * (totalItems - processedItems) / processedItems)
                        : TimeSpan.Zero;

                    progress.Report(new BatchProgress
                    {
                        ProcessedItems = processedItems,
                        TotalItems = totalItems,
                        ElapsedTime = elapsed,
                        EstimatedRemainingTime = remaining,
                        CurrentItem = $"批次 {processedItems / batchSize + 1}"
                    });
                });
            }

            // 让出UI线程，保持响应性
            await Task.Delay(1, cancellationToken);
        }

        _logger.LogInformation("批量处理完成，处理了 {ProcessedItems} 个项目，耗时 {Duration:F2} 秒", 
            processedItems, (DateTime.Now - startTime).TotalSeconds);
    }

    public async Task ExecuteLongRunningTaskAsync(
        Func<IProgress<double>, CancellationToken, Task> task,
        IProgress<double>? progress = null,
        CancellationToken cancellationToken = default)
    {
        var internalProgress = new Progress<double>(value =>
        {
            if (ShouldUpdateUI())
            {
#pragma warning disable CS4014 // 故意不等待UI更新以避免阻塞
                _ = ExecuteOnUIThreadAsync(() => progress?.Report(value));
#pragma warning restore CS4014
            }
        });

        await ExecuteOnBackgroundThreadAsync(async () =>
        {
            await task(internalProgress, cancellationToken).ConfigureAwait(false);
        }, cancellationToken).ConfigureAwait(false);
    }

    public async Task<UIResponsivenessInfo> CheckUIResponsiveness()
    {
        var info = new UIResponsivenessInfo
        {
            Timestamp = DateTime.Now,
            CurrentPriority = DispatcherPriority.Normal
        };

        try
        {
            if (_dispatcher.CheckAccess())
            {
                // 在UI线程上检查
                var stopwatch = Stopwatch.StartNew();
                _dispatcher.Invoke(() => { }, DispatcherPriority.Background);
                stopwatch.Stop();

                info.LastUIUpdateTime = stopwatch.Elapsed;
                info.IsResponsive = stopwatch.ElapsedMilliseconds < 100; // 100ms 阈值
                info.Status = info.IsResponsive ? "响应正常" : "响应缓慢";
            }
            else
            {
                // 从后台线程检查
                var responseTime = TimeSpan.Zero;
                var tcs = new TaskCompletionSource<bool>();

#pragma warning disable CS4014 // 故意不等待BeginInvoke调用，这是fire-and-forget操作
                _dispatcher.BeginInvoke(() =>
                {
                    tcs.SetResult(true);
                }, DispatcherPriority.Background);
#pragma warning restore CS4014

                var timeout = Task.Delay(1000); // 1秒超时
                var completed = await Task.WhenAny(tcs.Task, timeout).ConfigureAwait(false);

                if (completed == tcs.Task)
                {
                    info.IsResponsive = true;
                    info.Status = "响应正常";
                }
                else
                {
                    info.IsResponsive = false;
                    info.Status = "响应超时";
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "检查UI响应性时发生错误");
            info.IsResponsive = false;
            info.Status = "检查失败";
        }

        return info;
    }

    public void SetUIUpdateThrottle(TimeSpan minInterval)
    {
        lock (_lockObject)
        {
            _uiUpdateThrottle = minInterval;
            _logger.LogDebug("UI更新节流间隔已设置为: {Interval}ms", minInterval.TotalMilliseconds);
        }
    }

    public int GetRecommendedBatchSize()
    {
        // 根据系统性能动态调整批处理大小
        var processorCount = Environment.ProcessorCount;
        var availableMemory = GC.GetTotalMemory(false);

        // 基础批处理大小（使用推荐值作为起点）
        var baseBatchSize = Math.Max(_recommendedBatchSize, processorCount * 5);

        // 根据内存情况调整
        if (availableMemory > 100 * 1024 * 1024) // 100MB
        {
            baseBatchSize *= 2;
        }
        else if (availableMemory < 50 * 1024 * 1024) // 50MB
        {
            baseBatchSize /= 2;
        }

        return Math.Min(Math.Max(baseBatchSize, 5), 200); // 限制在5-200之间
    }

    private bool ShouldUpdateUI()
    {
        lock (_lockObject)
        {
            var now = DateTime.Now;
            if (now - _lastUIUpdate >= _uiUpdateThrottle)
            {
                _lastUIUpdate = now;
                return true;
            }
            return false;
        }
    }

    private void CheckResponsivenessCallbackWrapper(object? state)
    {
        // Fire-and-forget async operation for timer callback
        _ = CheckResponsivenessCallbackAsync();
    }

    private async Task CheckResponsivenessCallbackAsync()
    {
        try
        {
            var info = await CheckUIResponsiveness().ConfigureAwait(false);

            if (!info.IsResponsive)
            {
                var warning = $"UI响应性警告: {info.Status}";
                _logger.LogWarning(warning);

                UIResponsivenessWarning?.Invoke(this, new UIResponsivenessEventArgs(info, warning));
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "响应性检查回调中发生错误");
        }
    }

    public void Dispose()
    {
        _responsivenessTimer?.Dispose();
    }
}

/// <summary>
/// 扩展方法，用于分块处理
/// </summary>
public static class EnumerableExtensions
{
    public static IEnumerable<T[]> Chunk<T>(this IEnumerable<T> source, int size)
    {
        var chunk = new List<T>(size);
        foreach (var item in source)
        {
            chunk.Add(item);
            if (chunk.Count == size)
            {
                yield return chunk.ToArray();
                chunk.Clear();
            }
        }
        
        if (chunk.Count > 0)
        {
            yield return chunk.ToArray();
        }
    }
}
