<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 圆角半径常量 -->
    <CornerRadius x:Key="LargeCornerRadius">12</CornerRadius>
    <CornerRadius x:Key="MediumCornerRadius">8</CornerRadius>
    <CornerRadius x:Key="SmallCornerRadius">6</CornerRadius>
    <CornerRadius x:Key="XSmallCornerRadius">4</CornerRadius>

    <!-- 圆角窗口样式 (简化版，保持性能) -->
    <Style x:Key="RoundedWindowStyle" TargetType="Window">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignPaper}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
    </Style>

    <!-- 圆角Card样式 -->
    <Style x:Key="RoundedCardStyle" TargetType="materialDesign:Card">
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp4"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="materialDesign:Card">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource MediumCornerRadius}"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="Black"
                                              Opacity="0.3"
                                              ShadowDepth="6"
                                              BlurRadius="12"/>
                        </Border.Effect>
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- 圆角按钮样式 -->
    <Style x:Key="RoundedButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
        <!-- 确保文本始终可见 -->
        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidForegroundBrush}"/>
        <Style.Triggers>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{DynamicResource MaterialDesignCheckBoxDisabled}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 圆角扁平按钮样式 -->
    <Style x:Key="RoundedFlatButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 圆角轮廓按钮样式 -->
    <Style x:Key="RoundedOutlinedButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Height" Value="36"/>
        <Setter Property="Padding" Value="16,8"/>
        <Setter Property="FontWeight" Value="Medium"/>
    </Style>

    <!-- 圆角文本框样式 -->
    <Style x:Key="RoundedTextBoxStyle" TargetType="TextBox" BasedOn="{StaticResource MaterialDesignOutlinedTextBox}">
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="12,8"/>
    </Style>

    <!-- 圆角ComboBox样式 -->
    <Style x:Key="RoundedComboBoxStyle" TargetType="ComboBox" BasedOn="{StaticResource MaterialDesignOutlinedComboBox}">
        <Setter Property="materialDesign:TextFieldAssist.TextFieldCornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Height" Value="40"/>
        <Setter Property="Padding" Value="12,8"/>
    </Style>

    <!-- 圆角Panel样式 -->
    <Style x:Key="RoundedPanelStyle" TargetType="Border">
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}"/>
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="Padding" Value="16"/>
        <Setter Property="Margin" Value="8"/>
    </Style>

</ResourceDictionary>
