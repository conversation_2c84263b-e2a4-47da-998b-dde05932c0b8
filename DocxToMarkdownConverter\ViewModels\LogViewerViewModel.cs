using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using System.Collections.ObjectModel;
using System.IO;
using System.Text;
using System.Windows.Input;

namespace DocxToMarkdownConverter.ViewModels;

/// <summary>
/// 日志查看器 ViewModel
/// </summary>
public class LogViewerViewModel : ViewModelBase, IDisposable
{
    private readonly ILoggingService _loggingService;
    private readonly object _lockObject = new object();
    private bool _disposed = false;

    // 日志条目集合
    private readonly ObservableCollection<LogEntryViewModel> _allLogEntries;
    private readonly ObservableCollection<LogEntryViewModel> _filteredLogEntries;

    // 过滤选项
    private bool _showDebugLogs = false;
    private bool _showInfoLogs = true;
    private bool _showWarningLogs = true;
    private bool _showErrorLogs = true;
    private bool _autoScrollEnabled = true;

    // 统计信息
    private int _totalLogCount;
    private int _filteredLogCount;
    private int _errorCount;
    private int _warningCount;
    private DateTime _lastUpdateTime;

    // 命令
    public ICommand ClearLogsCommand { get; }
    public ICommand SaveLogsCommand { get; }
    public ICommand ExportLogsCommand { get; }

    public LogViewerViewModel(ILoggingService? loggingService = null)
    {
        _loggingService = loggingService ?? new SimpleLoggingService(Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleLoggingService>.Instance);
        
        _allLogEntries = new ObservableCollection<LogEntryViewModel>();
        _filteredLogEntries = new ObservableCollection<LogEntryViewModel>();

        // 订阅日志事件
        _loggingService.LogEvent += OnLogEvent;

        // 初始化命令
        ClearLogsCommand = new RelayCommand(ExecuteClearLogs);
        SaveLogsCommand = new RelayCommand(ExecuteSaveLogs, CanSaveLogs);
        ExportLogsCommand = new RelayCommand(ExecuteExportLogs, CanExportLogs);

        // 初始化统计
        UpdateStatistics();
    }

    #region 属性

    public ObservableCollection<LogEntryViewModel> FilteredLogEntries => _filteredLogEntries;

    public bool ShowDebugLogs
    {
        get => _showDebugLogs;
        set
        {
            if (SetProperty(ref _showDebugLogs, value))
            {
                ApplyFilter();
            }
        }
    }

    public bool ShowInfoLogs
    {
        get => _showInfoLogs;
        set
        {
            if (SetProperty(ref _showInfoLogs, value))
            {
                ApplyFilter();
            }
        }
    }

    public bool ShowWarningLogs
    {
        get => _showWarningLogs;
        set
        {
            if (SetProperty(ref _showWarningLogs, value))
            {
                ApplyFilter();
            }
        }
    }

    public bool ShowErrorLogs
    {
        get => _showErrorLogs;
        set
        {
            if (SetProperty(ref _showErrorLogs, value))
            {
                ApplyFilter();
            }
        }
    }

    public bool AutoScrollEnabled
    {
        get => _autoScrollEnabled;
        set => SetProperty(ref _autoScrollEnabled, value);
    }

    public int TotalLogCount
    {
        get => _totalLogCount;
        set => SetProperty(ref _totalLogCount, value);
    }

    public int FilteredLogCount
    {
        get => _filteredLogCount;
        set => SetProperty(ref _filteredLogCount, value);
    }

    public int ErrorCount
    {
        get => _errorCount;
        set => SetProperty(ref _errorCount, value);
    }

    public int WarningCount
    {
        get => _warningCount;
        set => SetProperty(ref _warningCount, value);
    }

    public DateTime LastUpdateTime
    {
        get => _lastUpdateTime;
        set => SetProperty(ref _lastUpdateTime, value);
    }

    #endregion

    #region 事件处理

    private void OnLogEvent(object? sender, LogEventArgs e)
    {
        lock (_lockObject)
        {
            var msLogLevel = e.Level switch
            {
                Services.LogLevel.Debug => Microsoft.Extensions.Logging.LogLevel.Debug,
                Services.LogLevel.Info => Microsoft.Extensions.Logging.LogLevel.Information,
                Services.LogLevel.Warning => Microsoft.Extensions.Logging.LogLevel.Warning,
                Services.LogLevel.Error => Microsoft.Extensions.Logging.LogLevel.Error,
                _ => Microsoft.Extensions.Logging.LogLevel.Information
            };

            var logEntry = new LogEntryViewModel
            {
                Timestamp = e.Timestamp,
                Level = msLogLevel,
                Category = e.Category,
                Message = e.Message,
                Exception = e.Exception
            };

            // 在UI线程上添加日志条目
            System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
            {
                _allLogEntries.Add(logEntry);
                
                // 限制日志条目数量
                while (_allLogEntries.Count > 5000)
                {
                    _allLogEntries.RemoveAt(0);
                }

                ApplyFilter();
                UpdateStatistics();
                LastUpdateTime = DateTime.Now;
            });
        }
    }

    #endregion

    #region 过滤逻辑

    private void ApplyFilter()
    {
        _filteredLogEntries.Clear();

        foreach (var entry in _allLogEntries)
        {
            if (ShouldShowLogEntry(entry))
            {
                _filteredLogEntries.Add(entry);
            }
        }

        FilteredLogCount = _filteredLogEntries.Count;
    }

    private bool ShouldShowLogEntry(LogEntryViewModel entry)
    {
        return entry.Level switch
        {
            Microsoft.Extensions.Logging.LogLevel.Debug => ShowDebugLogs,
            Microsoft.Extensions.Logging.LogLevel.Information => ShowInfoLogs,
            Microsoft.Extensions.Logging.LogLevel.Warning => ShowWarningLogs,
            Microsoft.Extensions.Logging.LogLevel.Error => ShowErrorLogs,
            Microsoft.Extensions.Logging.LogLevel.Critical => ShowErrorLogs,
            _ => true
        };
    }

    #endregion

    #region 统计更新

    private void UpdateStatistics()
    {
        TotalLogCount = _allLogEntries.Count;
        ErrorCount = _allLogEntries.Count(e => e.Level == Microsoft.Extensions.Logging.LogLevel.Error || e.Level == Microsoft.Extensions.Logging.LogLevel.Critical);
        WarningCount = _allLogEntries.Count(e => e.Level == Microsoft.Extensions.Logging.LogLevel.Warning);
    }

    #endregion

    #region 命令实现

    private void ExecuteClearLogs()
    {
        lock (_lockObject)
        {
            _allLogEntries.Clear();
            _filteredLogEntries.Clear();
            UpdateStatistics();
            LastUpdateTime = DateTime.Now;
        }

        _loggingService.LogInfo("日志已清空");
    }

    private void ExecuteSaveLogs()
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "保存日志文件",
                Filter = "文本文件 (*.txt)|*.txt|JSON文件 (*.json)|*.json|所有文件 (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"logs_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                var content = GenerateLogContent(saveFileDialog.FilterIndex == 2);
                File.WriteAllText(saveFileDialog.FileName, content, Encoding.UTF8);
                _loggingService.LogInfo($"日志已保存到: {saveFileDialog.FileName}");
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "保存日志失败");
        }
    }

    private bool CanSaveLogs()
    {
        return _allLogEntries.Count > 0;
    }

    private void ExecuteExportLogs()
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "导出日志文件",
                Filter = "CSV文件 (*.csv)|*.csv|Excel文件 (*.xlsx)|*.xlsx|所有文件 (*.*)|*.*",
                DefaultExt = "csv",
                FileName = $"logs_export_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                var content = GenerateCsvContent();
                File.WriteAllText(saveFileDialog.FileName, content, Encoding.UTF8);
                _loggingService.LogInfo($"日志已导出到: {saveFileDialog.FileName}");
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "导出日志失败");
        }
    }

    private bool CanExportLogs()
    {
        return _allLogEntries.Count > 0;
    }

    #endregion

    #region 内容生成

    private string GenerateLogContent(bool asJson = false)
    {
        if (asJson)
        {
            return System.Text.Json.JsonSerializer.Serialize(_allLogEntries.Select(e => new
            {
                Timestamp = e.Timestamp,
                Level = e.Level.ToString(),
                Category = e.Category,
                Message = e.Message,
                Exception = e.Exception?.ToString()
            }), new System.Text.Json.JsonSerializerOptions { WriteIndented = true });
        }
        else
        {
            var sb = new StringBuilder();
            sb.AppendLine("DOCX to Markdown Converter - 日志文件");
            sb.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine($"总条目数: {_allLogEntries.Count}");
            sb.AppendLine(new string('=', 80));
            sb.AppendLine();

            foreach (var entry in _allLogEntries)
            {
                sb.AppendLine($"[{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{entry.Level}] [{entry.Category}] {entry.Message}");
                if (entry.Exception != null)
                {
                    sb.AppendLine($"    异常: {entry.Exception}");
                }
                sb.AppendLine();
            }

            return sb.ToString();
        }
    }

    private string GenerateCsvContent()
    {
        var sb = new StringBuilder();
        sb.AppendLine("时间戳,级别,类别,消息,异常");

        foreach (var entry in _allLogEntries)
        {
            var message = entry.Message.Replace("\"", "\"\"").Replace("\n", " ").Replace("\r", " ");
            var exception = entry.Exception?.Message?.Replace("\"", "\"\"").Replace("\n", " ").Replace("\r", " ") ?? "";
            
            sb.AppendLine($"\"{entry.Timestamp:yyyy-MM-dd HH:mm:ss.fff}\",\"{entry.Level}\",\"{entry.Category}\",\"{message}\",\"{exception}\"");
        }

        return sb.ToString();
    }

    #endregion

    #region IDisposable

    public new void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 取消订阅事件
                if (_loggingService != null)
                {
                    _loggingService.LogEvent -= OnLogEvent;
                }

                _allLogEntries.Clear();
                _filteredLogEntries.Clear();
            }
            _disposed = true;
        }
    }

    #endregion
}

/// <summary>
/// 日志条目 ViewModel
/// </summary>
public class LogEntryViewModel
{
    public DateTime Timestamp { get; set; }
    public Microsoft.Extensions.Logging.LogLevel Level { get; set; }
    public string Category { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
    
    /// <summary>
    /// 获取日志级别的显示文本
    /// </summary>
    public string LevelText => Level switch
    {
        Microsoft.Extensions.Logging.LogLevel.Debug => "调试",
        Microsoft.Extensions.Logging.LogLevel.Information => "信息",
        Microsoft.Extensions.Logging.LogLevel.Warning => "警告",
        Microsoft.Extensions.Logging.LogLevel.Error => "错误",
        Microsoft.Extensions.Logging.LogLevel.Critical => "严重",
        _ => Level.ToString()
    };
}