# DOCX to Markdown Converter 安装包构建脚本
# 使用 PowerShell 运行此脚本

param(
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [string]$OutputPath = ".\Installer",
    [switch]$SkipTests = $false,
    [switch]$CreatePortable = $true
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始构建 DOCX to Markdown Converter 安装包..." -ForegroundColor Green
Write-Host "配置: $Configuration" -ForegroundColor Yellow
Write-Host "平台: $Platform" -ForegroundColor Yellow
Write-Host "输出路径: $OutputPath" -ForegroundColor Yellow

# 获取项目根目录
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ProjectFile = Join-Path $ProjectRoot "DocxToMarkdownConverter.csproj"
$PublishPath = Join-Path $ProjectRoot "bin\$Configuration\net8.0-windows\publish"

# 创建输出目录
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

try {
    # 1. 清理之前的构建
    Write-Host "清理之前的构建..." -ForegroundColor Cyan
    if (Test-Path $PublishPath) {
        Remove-Item -Path $PublishPath -Recurse -Force
    }

    # 2. 运行测试（如果未跳过）
    if (!$SkipTests) {
        Write-Host "运行测试..." -ForegroundColor Cyan
        $TestProject = Join-Path $ProjectRoot "Tests\DocxToMarkdownConverter.Tests.csproj"
        if (Test-Path $TestProject) {
            dotnet test $TestProject --configuration $Configuration --verbosity minimal
            if ($LASTEXITCODE -ne 0) {
                throw "测试失败"
            }
        }
    }

    # 3. 发布应用程序
    Write-Host "发布应用程序..." -ForegroundColor Cyan
    $PublishArgs = @(
        "publish"
        $ProjectFile
        "--configuration", $Configuration
        "--runtime", "win-$Platform"
        "--self-contained", "true"
        "--single-file", "true"
        "--output", $PublishPath
        "/p:PublishReadyToRun=true"
        "/p:PublishTrimmed=false"
        "/p:IncludeNativeLibrariesForSelfExtract=true"
    )
    
    & dotnet @PublishArgs
    if ($LASTEXITCODE -ne 0) {
        throw "发布失败"
    }

    # 4. 创建便携版（如果启用）
    if ($CreatePortable) {
        Write-Host "创建便携版..." -ForegroundColor Cyan
        $PortablePath = Join-Path $OutputPath "DocxConverter_Portable"
        if (Test-Path $PortablePath) {
            Remove-Item -Path $PortablePath -Recurse -Force
        }
        New-Item -ItemType Directory -Path $PortablePath -Force | Out-Null
        
        # 复制主程序
        $ExeFile = Get-ChildItem -Path $PublishPath -Name "*.exe" | Select-Object -First 1
        if ($ExeFile) {
            Copy-Item -Path (Join-Path $PublishPath $ExeFile) -Destination $PortablePath
        }
        
        # 复制配置文件
        $ConfigFiles = @("appsettings.json", "*.dll", "*.pdb")
        foreach ($Pattern in $ConfigFiles) {
            $Files = Get-ChildItem -Path $PublishPath -Name $Pattern -ErrorAction SilentlyContinue
            foreach ($File in $Files) {
                Copy-Item -Path (Join-Path $PublishPath $File) -Destination $PortablePath -ErrorAction SilentlyContinue
            }
        }
        
        # 创建便携版标识文件
        "portable" | Out-File -FilePath (Join-Path $PortablePath "portable.txt") -Encoding UTF8
        
        # 创建便携版压缩包
        $PortableZip = Join-Path $OutputPath "DocxConverter_Portable_v1.0.0.zip"
        if (Test-Path $PortableZip) {
            Remove-Item $PortableZip -Force
        }
        Compress-Archive -Path "$PortablePath\*" -DestinationPath $PortableZip
        Write-Host "便携版已创建: $PortableZip" -ForegroundColor Green
    }

    # 5. 创建 MSI 安装包（需要 WiX Toolset）
    Write-Host "检查 WiX Toolset..." -ForegroundColor Cyan
    $WixPath = "${env:ProgramFiles(x86)}\WiX Toolset v3.11\bin"
    if (Test-Path $WixPath) {
        Write-Host "创建 MSI 安装包..." -ForegroundColor Cyan
        
        # 生成 WiX 源文件
        $WixFile = Join-Path $OutputPath "DocxConverter.wxs"
        $MsiFile = Join-Path $OutputPath "DocxConverter_Setup_v1.0.0.msi"
        
        # 创建 WiX 配置
        $WixContent = @"
<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">
  <Product Id="*" Name="DOCX to Markdown Converter" Language="1033" Version="*******" 
           Manufacturer="Augment Code" UpgradeCode="12345678-1234-1234-1234-123456789012">
    <Package InstallerVersion="200" Compressed="yes" InstallScope="perMachine" />
    
    <MajorUpgrade DowngradeErrorMessage="A newer version is already installed." />
    <MediaTemplate EmbedCab="yes" />
    
    <Feature Id="ProductFeature" Title="DOCX to Markdown Converter" Level="1">
      <ComponentGroupRef Id="ProductComponents" />
    </Feature>
    
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="DOCX to Markdown Converter" />
      </Directory>
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="DOCX to Markdown Converter"/>
      </Directory>
    </Directory>
    
    <ComponentGroup Id="ProductComponents" Directory="INSTALLFOLDER">
      <Component Id="MainExecutable" Guid="*">
        <File Id="DocxConverterExe" Source="$PublishPath\DocxToMarkdownConverter.exe" KeyPath="yes">
          <Shortcut Id="ApplicationStartMenuShortcut" Directory="ApplicationProgramsFolder" 
                    Name="DOCX to Markdown Converter" WorkingDirectory="INSTALLFOLDER" 
                    Icon="AppIcon.exe" IconIndex="0" Advertise="yes" />
        </File>
      </Component>
    </ComponentGroup>
    
    <Icon Id="AppIcon.exe" SourceFile="$PublishPath\DocxToMarkdownConverter.exe" />
    <Property Id="ARPPRODUCTICON" Value="AppIcon.exe" />
  </Product>
</Wix>
"@
        
        $WixContent | Out-File -FilePath $WixFile -Encoding UTF8
        
        # 编译 WiX
        $CandleExe = Join-Path $WixPath "candle.exe"
        $LightExe = Join-Path $WixPath "light.exe"
        
        if ((Test-Path $CandleExe) -and (Test-Path $LightExe)) {
            & $CandleExe $WixFile -out (Join-Path $OutputPath "DocxConverter.wixobj")
            & $LightExe (Join-Path $OutputPath "DocxConverter.wixobj") -out $MsiFile
            
            if (Test-Path $MsiFile) {
                Write-Host "MSI 安装包已创建: $MsiFile" -ForegroundColor Green
            }
        }
    } else {
        Write-Host "未找到 WiX Toolset，跳过 MSI 创建" -ForegroundColor Yellow
        Write-Host "请安装 WiX Toolset v3.11 以创建 MSI 安装包" -ForegroundColor Yellow
    }

    # 6. 创建 NSIS 安装包（如果可用）
    $NSISPath = "${env:ProgramFiles(x86)}\NSIS\makensis.exe"
    if (Test-Path $NSISPath) {
        Write-Host "创建 NSIS 安装包..." -ForegroundColor Cyan
        
        $NSISScript = Join-Path $OutputPath "DocxConverter.nsi"
        $NSISContent = @"
!define APP_NAME "DOCX to Markdown Converter"
!define APP_VERSION "1.0.0"
!define APP_PUBLISHER "Augment Code"
!define APP_EXE "DocxToMarkdownConverter.exe"

Name "`${APP_NAME}"
OutFile "DocxConverter_Setup_v`${APP_VERSION}.exe"
InstallDir "`$PROGRAMFILES64\`${APP_NAME}"
RequestExecutionLevel admin

Page directory
Page instfiles

Section "Install"
    SetOutPath "`$INSTDIR"
    File "$PublishPath\DocxToMarkdownConverter.exe"
    
    CreateDirectory "`$SMPROGRAMS\`${APP_NAME}"
    CreateShortCut "`$SMPROGRAMS\`${APP_NAME}\`${APP_NAME}.lnk" "`$INSTDIR\`${APP_EXE}"
    CreateShortCut "`$DESKTOP\`${APP_NAME}.lnk" "`$INSTDIR\`${APP_EXE}"
    
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "DisplayName" "`${APP_NAME}"
    WriteRegStr HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}" "UninstallString" "`$INSTDIR\uninstall.exe"
    WriteUninstaller "`$INSTDIR\uninstall.exe"
SectionEnd

Section "Uninstall"
    Delete "`$INSTDIR\DocxToMarkdownConverter.exe"
    Delete "`$INSTDIR\uninstall.exe"
    RMDir "`$INSTDIR"
    
    Delete "`$SMPROGRAMS\`${APP_NAME}\`${APP_NAME}.lnk"
    RMDir "`$SMPROGRAMS\`${APP_NAME}"
    Delete "`$DESKTOP\`${APP_NAME}.lnk"
    
    DeleteRegKey HKLM "Software\Microsoft\Windows\CurrentVersion\Uninstall\`${APP_NAME}"
SectionEnd
"@
        
        $NSISContent | Out-File -FilePath $NSISScript -Encoding UTF8
        
        & $NSISPath $NSISScript
        
        $NSISInstaller = Join-Path $OutputPath "DocxConverter_Setup_v1.0.0.exe"
        if (Test-Path $NSISInstaller) {
            Write-Host "NSIS 安装包已创建: $NSISInstaller" -ForegroundColor Green
        }
    } else {
        Write-Host "未找到 NSIS，跳过 NSIS 安装包创建" -ForegroundColor Yellow
    }

    # 7. 生成构建报告
    Write-Host "生成构建报告..." -ForegroundColor Cyan
    $BuildReport = @"
DOCX to Markdown Converter 构建报告
=====================================
构建时间: $(Get-Date)
配置: $Configuration
平台: $Platform

构建产物:
"@

    $OutputFiles = Get-ChildItem -Path $OutputPath -File
    foreach ($File in $OutputFiles) {
        $Size = [math]::Round($File.Length / 1MB, 2)
        $BuildReport += "`n- $($File.Name) ($Size MB)"
    }

    $BuildReport | Out-File -FilePath (Join-Path $OutputPath "BuildReport.txt") -Encoding UTF8

    Write-Host "构建完成！" -ForegroundColor Green
    Write-Host "输出目录: $OutputPath" -ForegroundColor Green
    
} catch {
    Write-Host "构建失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
