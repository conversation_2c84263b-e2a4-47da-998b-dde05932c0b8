using System;
using System.Windows;
using System.Windows.Media.Animation;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 自适应动画管理器接口
/// </summary>
public interface IAdaptiveAnimationManager
{
    /// <summary>
    /// 获取当前动画性能级别
    /// </summary>
    AnimationPerformanceLevel CurrentPerformanceLevel { get; }

    /// <summary>
    /// 创建自适应动画
    /// </summary>
    Storyboard CreateAdaptiveAnimation(FrameworkElement target, AnimationConfig config);

    /// <summary>
    /// 更新动画性能级别
    /// </summary>
    void UpdatePerformanceLevel(SystemPerformanceInfo performanceInfo);

    /// <summary>
    /// 启用或禁用动画
    /// </summary>
    void SetAnimationsEnabled(bool enabled);

    /// <summary>
    /// 获取推荐的动画持续时间
    /// </summary>
    TimeSpan GetRecommendedDuration(AnimationType animationType);

    /// <summary>
    /// 获取推荐的缓动函数
    /// </summary>
    IEasingFunction? GetRecommendedEasing(AnimationType animationType);

    /// <summary>
    /// 检查是否应该跳过动画
    /// </summary>
    bool ShouldSkipAnimation(AnimationConfig config);

    /// <summary>
    /// 性能级别变化事件
    /// </summary>
    event EventHandler<PerformanceLevelChangedEventArgs>? PerformanceLevelChanged;

    /// <summary>
    /// 获取动画性能统计
    /// </summary>
    AnimationPerformanceStats GetPerformanceStats();

    /// <summary>
    /// 重置性能统计
    /// </summary>
    void ResetPerformanceStats();
}

/// <summary>
/// 动画性能级别
/// </summary>
public enum AnimationPerformanceLevel
{
    /// <summary>
    /// 禁用动画
    /// </summary>
    Disabled,
    
    /// <summary>
    /// 低性能（简化动画）
    /// </summary>
    Low,
    
    /// <summary>
    /// 中等性能（标准动画）
    /// </summary>
    Medium,
    
    /// <summary>
    /// 高性能（完整动画）
    /// </summary>
    High
}

/// <summary>
/// 动画类型
/// </summary>
public enum AnimationType
{
    FadeIn,
    FadeOut,
    SlideIn,
    SlideOut,
    Scale,
    Rotate,
    Bounce,
    Elastic,
    Custom
}

/// <summary>
/// 动画配置
/// </summary>
public class AnimationConfig
{
    public AnimationType Type { get; set; }
    public TimeSpan? Duration { get; set; }
    public IEasingFunction? Easing { get; set; }
    public double From { get; set; }
    public double To { get; set; }
    public string PropertyName { get; set; } = string.Empty;
    public bool AutoReverse { get; set; }
    public int RepeatCount { get; set; } = 1;
    public TimeSpan Delay { get; set; }
    public AnimationPriority Priority { get; set; } = AnimationPriority.Normal;
}

/// <summary>
/// 动画优先级
/// </summary>
public enum AnimationPriority
{
    Low,
    Normal,
    High,
    Critical
}

/// <summary>
/// 系统性能信息
/// </summary>
public class SystemPerformanceInfo
{
    public double CpuUsage { get; set; }
    public double MemoryUsage { get; set; }
    public double GpuUsage { get; set; }
    public int FrameRate { get; set; }
    public bool IsLowPowerMode { get; set; }
    public bool IsBatteryPowered { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 性能级别变化事件参数
/// </summary>
public class PerformanceLevelChangedEventArgs : EventArgs
{
    public AnimationPerformanceLevel OldLevel { get; }
    public AnimationPerformanceLevel NewLevel { get; }
    public string Reason { get; }

    public PerformanceLevelChangedEventArgs(AnimationPerformanceLevel oldLevel, AnimationPerformanceLevel newLevel, string reason)
    {
        OldLevel = oldLevel;
        NewLevel = newLevel;
        Reason = reason;
    }
}

/// <summary>
/// 动画性能统计
/// </summary>
public class AnimationPerformanceStats
{
    public int TotalAnimations { get; set; }
    public int SkippedAnimations { get; set; }
    public int CompletedAnimations { get; set; }
    public double AverageFrameRate { get; set; }
    public TimeSpan TotalAnimationTime { get; set; }
    public DateTime StartTime { get; set; }
    public DateTime LastUpdate { get; set; }
    public double SkipRate => TotalAnimations > 0 ? (double)SkippedAnimations / TotalAnimations * 100 : 0;
}
