<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能AI检测助手 - 学术论文优化专家</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="js/detector_ui.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            overflow-x: hidden;
        }

        /* 左侧导航栏 */
        .sidebar {
            position: fixed;
            left: 0;
            top: 0;
            width: 250px;
            height: 100vh;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            z-index: 1000;
            transition: transform 0.3s ease;
            overflow-y: auto;
        }

        .sidebar-header {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h1 {
            font-size: 1.5rem;
            margin-bottom: 5px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }

        .sidebar-header p {
            font-size: 0.9rem;
            opacity: 0.8;
        }

        .nav-menu {
            padding: 20px 0;
        }

        .nav-item {
            display: flex;
            align-items: center;
            padding: 15px 20px;
            cursor: pointer;
            transition: all 0.3s ease;
            border-left: 3px solid transparent;
            position: relative;
        }

        .nav-item:hover {
            background: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }

        .nav-item.active {
            background: rgba(255,255,255,0.15);
            border-left-color: #fff;
        }

        .nav-item i {
            font-size: 1.2rem;
            margin-right: 15px;
            width: 20px;
            text-align: center;
        }

        .nav-item span {
            font-size: 0.95rem;
            font-weight: 500;
        }

        /* 主内容区域 */
        .main-container {
            margin-left: 250px;
            min-height: 100vh;
            background: #f5f7fa;
            transition: margin-left 0.3s ease;
        }

        .main-header {
            background: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            border-bottom: 1px solid #e9ecef;
        }

        .main-header h2 {
            color: #333;
            font-size: 1.8rem;
            margin: 0;
        }

        .main-header p {
            color: #666;
            margin: 5px 0 0 0;
            font-size: 0.95rem;
        }

        .content-area {
            padding: 30px;
        }

        .version-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            margin-left: 10px;
            animation: pulse 2s infinite;
        }

        /* 朱雀优化模式选择器样式 */
        .optimization-mode-selector {
            border: 2px solid #ff6b6b;
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
        }

        .optimization-option {
            display: block;
            cursor: pointer;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            background: white;
            transition: all 0.3s ease;
            position: relative;
        }

        .optimization-option:hover {
            border-color: #ff6b6b;
            box-shadow: 0 2px 8px rgba(255, 107, 107, 0.2);
            transform: translateY(-2px);
        }

        .optimization-option input[type="radio"] {
            position: absolute;
            opacity: 0;
            cursor: pointer;
        }

        .optimization-option input[type="radio"]:checked + .option-content {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            color: white;
        }

        .optimization-option input[type="radio"]:checked + .option-content strong {
            color: white;
        }

        .option-content {
            padding: 8px;
            border-radius: 6px;
            transition: all 0.3s ease;
        }

        .option-content strong {
            display: block;
            margin-bottom: 5px;
            font-size: 1.1em;
            color: #333;
        }

        .option-content p {
            margin: 0;
            font-size: 0.9em;
            color: #666;
            line-height: 1.4;
        }

        .optimization-option input[type="radio"]:checked + .option-content p {
            color: rgba(255, 255, 255, 0.9);
        }

        .zhuque-info {
            animation: fadeIn 0.5s ease-in-out;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        /* 朱雀优化按钮样式 */
        .btn-warning {
            background: linear-gradient(135deg, #ff6b6b, #ee5a24);
            border: none;
            color: white;
            font-weight: bold;
            position: relative;
            overflow: hidden;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #ee5a24, #ff6b6b);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(255, 107, 107, 0.3);
        }

        .btn-warning::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
            transition: left 0.5s;
        }

        .btn-warning:hover::before {
            left: 100%;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        /* 内容面板 */
        .content-panel {
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            display: none;
            animation: fadeInUp 0.3s ease;
        }

        .content-panel.active {
            display: block;
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 移动端汉堡菜单 */
        .mobile-menu-toggle {
            display: none;
            position: fixed;
            top: 20px;
            left: 20px;
            z-index: 1001;
            background: #667eea;
            color: white;
            border: none;
            padding: 12px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1.2rem;
        }

        .sidebar-overlay {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 999;
        }

        /* 高级学术架构标签页样式 */
        .advanced-tabs {
            display: flex;
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 25px;
            overflow-x: auto;
        }

        .tab-btn {
            background: none;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            color: #666;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .tab-btn:hover {
            color: #667eea;
            background: rgba(102, 126, 234, 0.05);
        }

        .tab-btn.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background: rgba(102, 126, 234, 0.1);
        }

        .advanced-tab-content {
            display: none;
            animation: fadeInUp 0.3s ease;
        }

        .advanced-tab-content.active {
            display: block;
        }

        .tab-description {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 25px;
            border-left: 4px solid #667eea;
        }

        .tab-description h4 {
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .tab-description p {
            color: #666;
            margin: 0;
            line-height: 1.6;
        }

        /* 对比矩阵样式 */
        .comparison-matrix {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .comparison-matrix th,
        .comparison-matrix td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: left;
        }

        .comparison-matrix th {
            background: #f8f9fa;
            font-weight: 600;
            color: #333;
        }

        .comparison-matrix .winner-a {
            background: #d4edda;
            color: #155724;
        }

        .comparison-matrix .winner-b {
            background: #d1ecf1;
            color: #0c5460;
        }

        .comparison-matrix .tie {
            background: #fff3cd;
            color: #856404;
        }

        /* 指标网格样式 */
        .metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }

        .metric-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            border: 2px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .metric-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .metric-value {
            font-size: 1.8rem;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .metric-label {
            font-size: 0.9rem;
            color: #666;
            margin-bottom: 8px;
        }

        .metric-description {
            font-size: 0.8rem;
            color: #999;
        }

        /* 推荐列表样式 */
        .recommendations-list {
            list-style: none;
            padding: 0;
            margin: 15px 0;
        }

        .recommendations-list li {
            background: #e7f3ff;
            padding: 15px;
            margin: 10px 0;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }

        .recommendations-list li:hover {
            background: #d4edda;
            border-left-color: #28a745;
        }

        .recommendations-list li.high-priority {
            background: #fff3cd;
            border-left-color: #ffc107;
        }

        .recommendations-list li.high-priority:hover {
            background: #f8d7da;
            border-left-color: #dc3545;
        }

        /* Ollama服务控制样式 */
        .ollama-status-card {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 5px 20px rgba(40, 167, 69, 0.3);
        }

        .status-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 15px;
        }

        .status-indicator {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background: #6c757d;
            animation: pulse 2s infinite;
        }

        .status-dot.online {
            background: #28a745;
        }

        .status-dot.offline {
            background: #dc3545;
        }

        .status-dot.connecting {
            background: #ffc107;
        }

        .status-badge {
            background: rgba(255,255,255,0.2);
            padding: 8px 15px;
            border-radius: 20px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: rgba(255,255,255,0.1);
            padding: 10px 15px;
            border-radius: 8px;
        }

        .detail-label {
            font-weight: 500;
            opacity: 0.9;
        }

        .detail-value {
            font-family: 'Courier New', monospace;
            background: rgba(255,255,255,0.2);
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.9rem;
        }

        /* 模型管理样式 */
        .model-management {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
        }

        .model-management h4 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .model-selector {
            display: flex;
            align-items: center;
            gap: 15px;
            margin-bottom: 25px;
        }

        .model-selector label {
            font-weight: 500;
            color: #555;
            white-space: nowrap;
        }

        .model-selector select {
            flex: 1;
            min-width: 200px;
        }

        .recommended-models h5 {
            color: #555;
            margin-bottom: 15px;
        }

        .model-cards {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 15px;
        }

        .model-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .model-card:hover {
            border-color: #667eea;
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
        }

        .model-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }

        .model-desc {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .model-size {
            color: #999;
            font-size: 0.8rem;
            font-weight: 500;
        }

        /* 服务控制样式 */
        .service-controls {
            background: #fff;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            border: 1px solid #e9ecef;
        }

        .service-controls h4 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .control-buttons {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        /* 性能监控样式 */
        .performance-monitor {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
        }

        .performance-monitor h4 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 配置指南样式 */
        .config-guide {
            background: #fff;
            padding: 25px;
            border-radius: 15px;
            border: 1px solid #e9ecef;
        }

        .config-guide h4 {
            color: #333;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .guide-steps {
            display: grid;
            gap: 20px;
        }

        .step-item {
            display: flex;
            align-items: flex-start;
            gap: 15px;
        }

        .step-number {
            background: #667eea;
            color: white;
            width: 30px;
            height: 30px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            font-size: 0.9rem;
            flex-shrink: 0;
        }

        .step-content h5 {
            color: #333;
            margin-bottom: 8px;
        }

        .step-content p {
            color: #666;
            margin: 0;
            line-height: 1.5;
        }

        .step-content code {
            background: #f8f9fa;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Courier New', monospace;
            color: #e83e8c;
            border: 1px solid #e9ecef;
        }

        .step-content a {
            color: #667eea;
            text-decoration: none;
        }

        .step-content a:hover {
            text-decoration: underline;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .sidebar-overlay.show {
                display: block;
            }

            .main-container {
                margin-left: 0;
            }

            .mobile-menu-toggle {
                display: block;
            }

            .main-header {
                padding-left: 70px;
            }

            .content-area {
                padding: 20px 15px;
            }

            .content-panel {
                padding: 20px;
            }

            .advanced-tabs {
                flex-direction: column;
            }

            .tab-btn {
                border-bottom: none;
                border-left: 3px solid transparent;
            }

            .tab-btn.active {
                border-left-color: #667eea;
                border-bottom-color: transparent;
            }

            .metrics-grid {
                grid-template-columns: 1fr;
            }
        }

        .panel-header {
            display: flex;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .panel-header i {
            font-size: 1.8rem;
            margin-right: 15px;
            color: #667eea;
        }

        .panel-header h3 {
            color: #333;
            font-size: 1.4rem;
            margin: 0;
        }

        .panel-description {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #555;
        }

        .form-control {
            width: 100%;
            padding: 15px;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            font-size: 14px;
            transition: border-color 0.3s ease;
            resize: vertical;
            min-height: 120px;
        }

        .form-control:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            text-align: center;
            margin: 5px;
        }

        .btn-primary {
            background: linear-gradient(45deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 2px solid #dee2e6;
        }

        .btn-secondary:hover {
            background: #e9ecef;
            transform: translateY(-2px);
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffc107, #ff8c00);
            color: white;
            border: none;
            position: relative;
            overflow: hidden;
        }

        .btn-warning:hover {
            background: linear-gradient(135deg, #ff8c00, #ffc107);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(255, 193, 7, 0.4);
        }

        .btn-warning::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn-warning:hover::before {
            left: 100%;
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
            color: white;
            border: none;
        }

        .btn-info:hover {
            background: linear-gradient(135deg, #007bff, #17a2b8);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.4);
        }

        .btn-info.active {
            background: linear-gradient(135deg, #28a745, #20c997);
            box-shadow: 0 0 10px rgba(40, 167, 69, 0.5);
        }

        .btn-success {
            background: linear-gradient(45deg, #28a745, #20c997);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.4);
        }

        .result-area {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
            display: none;
        }

        /* 新的AI检测结果展示样式 */
        .ai-detection-result {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .detection-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }

        .detection-score-main {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .detection-score-main.low { color: #28a745; }
        .detection-score-main.medium { color: #ffc107; }
        .detection-score-main.high { color: #dc3545; }

        .detection-status {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 5px;
            transition: all 0.5s ease-in-out;
        }

        .detection-status.low {
            color: #28a745;
            font-weight: 600;
        }

        .detection-status.medium {
            color: #ffc107;
            font-weight: 600;
        }

        .detection-status.high {
            color: #dc3545;
            font-weight: 600;
        }

        .detection-mode {
            font-size: 0.9rem;
            color: #999;
            background: #f8f9fa;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }

        /* 饼图样式 */
        .pie-chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 30px;
        }

        .pie-chart {
            position: relative;
            width: 200px;
            height: 200px;
        }

        .pie-chart svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .pie-chart svg circle {
            transition: stroke-dasharray 1.2s cubic-bezier(0.4, 0, 0.2, 1),
                        stroke-dashoffset 1.2s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .pie-chart-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }

        .pie-chart-score {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
        }

        .pie-chart-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }

        /* 分类展示 */
        .detection-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }

        .breakdown-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }

        .breakdown-item:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }

        .breakdown-item:active {
            transform: translateY(-1px) scale(1.01);
        }

        .breakdown-item.ai-generated {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
        }

        .breakdown-item.human-written {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #f8fff8, #e8f5e8);
        }

        .breakdown-item.suspicious {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0, #fff3cd);
        }

        .breakdown-percentage {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .breakdown-percentage.ai-generated { color: #dc3545; }
        .breakdown-percentage.human-written { color: #28a745; }
        .breakdown-percentage.suspicious { color: #ffc107; }

        .breakdown-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }

        /* 进度条样式 */
        .progress-bars {
            margin: 25px 0;
        }

        .progress-item {
            margin-bottom: 15px;
        }

        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 0.9rem;
            font-weight: 500;
        }

        .progress-bar-container {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-bar-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
            background-size: 20px 20px;
            animation: progressShine 2s linear infinite;
        }

        @keyframes progressShine {
            0% {
                background-position: -20px 0;
            }
            100% {
                background-position: 20px 0;
            }
        }

        .progress-bar-fill.ai-score {
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
        }

        .progress-bar-fill.confidence {
            background: linear-gradient(90deg, #007bff, #0056b3);
        }

        .progress-bar-fill.complexity {
            background: linear-gradient(90deg, #6f42c1, #495057);
        }

        /* 图例样式 */
        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }

        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }

        .legend-color.human { background: #28a745; }
        .legend-color.ai { background: #dc3545; }
        .legend-color.suspicious { background: #ffc107; }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .pie-chart-container {
                flex-direction: column;
                gap: 20px;
            }

            .pie-chart {
                width: 150px;
                height: 150px;
            }

            .detection-breakdown {
                grid-template-columns: 1fr;
            }

            .chart-legend {
                flex-direction: column;
                align-items: center;
                gap: 10px;
            }
        }

        .result-description {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .result-details {
            background: white;
            padding: 15px;
            border-radius: 8px;
            margin-top: 15px;
        }

        .result-details h4 {
            color: #667eea;
            margin-bottom: 10px;
        }

        .result-details ul {
            list-style: none;
            padding: 0;
        }

        .result-details li {
            padding: 5px 0;
            border-bottom: 1px solid #f8f9fa;
        }

        .result-details li:last-child {
            border-bottom: none;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 30px;
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 20px 0;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #667eea;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        .loading p {
            color: #333;
            font-size: 1.1rem;
            margin: 10px 0;
            font-weight: 500;
        }

        .loading small {
            color: #666;
            font-size: 0.9rem;
            display: block;
            margin-top: 10px;
        }

        .loading-progress {
            width: 100%;
            height: 4px;
            background: #f3f3f3;
            border-radius: 2px;
            margin: 15px 0;
            overflow: hidden;
        }

        .loading-progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 2px;
            animation: loadingProgress 3s ease-in-out infinite;
        }

        @keyframes loadingProgress {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 20px;
            width: 90%;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            animation: modalSlideIn 0.3s ease;
        }

        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }

        .close {
            position: absolute;
            right: 20px;
            top: 20px;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            color: #aaa;
            transition: color 0.3s ease;
        }

        .close:hover {
            color: #333;
        }

        .comparison-view {
            display: none;
            margin-top: 20px;
        }

        .comparison-container {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-top: 15px;
        }

        .comparison-panel {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
        }

        .comparison-panel h4 {
            margin-bottom: 10px;
            color: #667eea;
        }

        .comparison-text {
            background: white;
            padding: 10px;
            border-radius: 5px;
            min-height: 100px;
            line-height: 1.6;
        }

        .footer {
            text-align: center;
            margin-top: 40px;
            color: white;
            opacity: 0.8;
        }

        .footer a {
            color: white;
            text-decoration: none;
            margin: 0 10px;
        }

        .footer a:hover {
            text-decoration: underline;
        }

        /* LLM控制面板样式 */
        .llm-control-panel {
            margin-bottom: 20px;
        }

        .service-status .status-indicator {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
        }

        .status-dot {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
            transition: background-color 0.3s ease;
        }

        .service-controls button {
            margin-right: 8px;
            margin-bottom: 8px;
        }

        .quick-tips {
            margin-top: 15px;
        }

        /* 通知样式 */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            border-radius: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 10000;
            color: white;
            font-weight: 500;
        }

        .notification.success {
            background: #28a745;
        }

        .notification.error {
            background: #dc3545;
        }

        .notification.info {
            background: #007bff;
        }

        .notification-content {
            display: flex;
            align-items: center;
            gap: 8px;
        }

        /* 动画效果 */
        @keyframes slideInRight {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }

        @keyframes slideOutRight {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }

        /* 按钮激活状态 */
        .btn.active {
            background: #28a745 !important;
            border-color: #28a745 !important;
            color: white !important;
        }

        /* 多轮优化历程样式 */
        .optimization-history {
            margin-top: 20px;
        }

        .round-item {
            background: #f8f9fa;
            padding: 12px;
            margin: 8px 0;
            border-radius: 6px;
            border-left: 4px solid #007bff;
        }

        .round-item.final {
            border-left-color: #28a745;
            background: #e8f5e8;
        }

        .round-item.error {
            border-left-color: #dc3545;
            background: #f8d7da;
        }

        .round-item .strategy {
            display: block;
            font-size: 0.9em;
            color: #6c757d;
            margin-top: 4px;
        }

        .round-item .improvements {
            font-size: 0.85em;
            color: #495057;
            margin-top: 4px;
        }

        .round-item .error {
            color: #721c24;
            font-weight: 500;
        }

        /* 分数显示样式 */
        .score.good {
            color: #28a745;
            font-weight: bold;
        }

        .score.medium {
            color: #ffc107;
            font-weight: bold;
        }

        .score.high {
            color: #dc3545;
            font-weight: bold;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
                gap: 20px;
            }

            .header h1 {
                font-size: 2rem;
            }

            .card {
                padding: 20px;
            }

            .comparison-container {
                grid-template-columns: 1fr;
            }

            .service-controls {
                text-align: left !important;
                margin-top: 15px;
            }

            .service-controls button {
                width: 100%;
                margin-bottom: 8px;
            }

            .notification {
                right: 10px;
                left: 10px;
                width: auto;
            }
        }

        /* 安全控制样式 */
        .security-control {
            background: rgba(255, 193, 7, 0.1);
            border: 1px solid rgba(255, 193, 7, 0.3);
            border-radius: 8px;
            padding: 15px;
            margin-top: 15px;
        }

        .security-control h4 {
            color: #856404;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .security-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            margin-bottom: 10px;
        }

        .security-buttons:last-child {
            margin-bottom: 0;
        }

        .security-buttons .btn {
            flex: 1;
            min-width: 120px;
            font-size: 0.85em;
            padding: 6px 12px;
        }

        .security-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-left: 8px;
            vertical-align: middle;
        }

        .security-indicator.secure {
            background: #28a745;
            box-shadow: 0 0 4px rgba(40, 167, 69, 0.5);
        }

        .security-indicator.warning {
            background: #ffc107;
            box-shadow: 0 0 4px rgba(255, 193, 7, 0.5);
        }

        .security-indicator.danger {
            background: #dc3545;
            box-shadow: 0 0 4px rgba(220, 53, 69, 0.5);
        }

        .detail-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid rgba(0,0,0,0.1);
        }

        .detail-item:last-child {
            border-bottom: none;
        }

        .detail-label {
            font-weight: 500;
            color: #495057;
        }

        .detail-value {
            color: #28a745;
            font-weight: 500;
        }

        /* 自动化学术架构样式 */
        .auto-process-info {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            padding: 25px;
            border-radius: 12px;
            margin-bottom: 30px;
            border-left: 4px solid #007bff;
        }

        .auto-process-info h4 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.2em;
        }

        .process-steps {
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 15px;
        }

        .step-item {
            display: flex;
            align-items: center;
            flex: 1;
            min-width: 200px;
        }

        .step-number {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            flex-shrink: 0;
        }

        .step-content h5 {
            margin: 0 0 5px 0;
            color: #495057;
            font-size: 1em;
        }

        .step-content p {
            margin: 0;
            color: #6c757d;
            font-size: 0.9em;
            line-height: 1.4;
        }

        .step-arrow {
            font-size: 1.5em;
            color: #007bff;
            margin: 0 10px;
            flex-shrink: 0;
        }

        .optimization-config {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            border: 1px solid #e9ecef;
        }

        .optimization-config h5 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .btn-large {
            padding: 15px 30px;
            font-size: 1.1em;
            font-weight: 600;
        }

        /* 自动化处理状态样式 */
        .auto-process-status {
            background: #f8f9fa;
            border-radius: 12px;
            padding: 25px;
            margin: 25px 0;
            border: 1px solid #e9ecef;
        }

        .process-timeline {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .timeline-item {
            display: flex;
            align-items: flex-start;
            padding: 15px;
            background: white;
            border-radius: 8px;
            border: 1px solid #e9ecef;
            transition: all 0.3s ease;
        }

        .timeline-item.active {
            border-color: #007bff;
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.15);
        }

        .timeline-item.completed {
            border-color: #28a745;
            background: #f8fff9;
        }

        .timeline-icon {
            width: 50px;
            height: 50px;
            border-radius: 50%;
            background: #e9ecef;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            flex-shrink: 0;
            transition: all 0.3s ease;
        }

        .timeline-item.active .timeline-icon {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
        }

        .timeline-item.completed .timeline-icon {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .timeline-content {
            flex: 1;
        }

        .timeline-content h5 {
            margin: 0 0 8px 0;
            color: #495057;
            font-size: 1.1em;
        }

        .status-text {
            margin: 0 0 10px 0;
            color: #6c757d;
            font-size: 0.9em;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #007bff, #0056b3);
            transition: width 0.3s ease;
        }

        .timeline-item.completed .progress-fill {
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 100% !important;
        }

        /* 自动化结果显示样式 */
        .auto-result-container {
            background: white;
            border-radius: 12px;
            padding: 25px;
            border: 1px solid #e9ecef;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .result-header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #e9ecef;
        }

        .result-header h3 {
            color: #28a745;
            margin-bottom: 10px;
            font-size: 1.3em;
        }

        .result-summary {
            color: #6c757d;
            font-size: 1em;
            margin: 0;
        }

        .result-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
        }

        .result-section h4 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .comparison-summary,
        .advantages-summary {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .text-advantages {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .advantage-column h5 {
            color: #495057;
            margin-bottom: 10px;
            font-size: 1em;
        }

        .advantage-score {
            background: linear-gradient(135deg, #007bff, #0056b3);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-weight: bold;
            margin-bottom: 10px;
            text-align: center;
        }

        .advantage-list {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .advantage-list li {
            padding: 5px 0;
            border-bottom: 1px solid #e9ecef;
            color: #6c757d;
        }

        .advantage-list li:last-child {
            border-bottom: none;
        }

        .fusion-result {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
        }

        .fusion-text h5,
        .fusion-metrics h5 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 1em;
        }

        .optimized-content {
            background: white;
            padding: 20px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
            line-height: 1.6;
            color: #495057;
            max-height: 300px;
            overflow-y: auto;
        }

        .fusion-metrics {
            background: white;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        .metric-value.enabled {
            color: #28a745;
            font-weight: bold;
        }

        .metric-value.disabled {
            color: #dc3545;
        }

        .result-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            padding-top: 20px;
            border-top: 2px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .process-steps {
                flex-direction: column;
                align-items: stretch;
            }

            .step-arrow {
                transform: rotate(90deg);
                margin: 10px 0;
            }

            .step-item {
                min-width: auto;
            }

            .text-advantages {
                grid-template-columns: 1fr;
            }

            .fusion-result {
                grid-template-columns: 1fr;
            }

            .result-actions {
                flex-direction: column;
                align-items: center;
            }

            .result-actions .btn {
                width: 100%;
                max-width: 300px;
            }
        }
    </style>
</head>
<body>
    <!-- 移动端汉堡菜单按钮 -->
    <button class="mobile-menu-toggle" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <!-- 侧边栏遮罩层 -->
    <div class="sidebar-overlay" onclick="closeSidebar()"></div>

    <!-- 左侧导航栏 -->
    <div class="sidebar" id="sidebar">
        <div class="sidebar-header">
            <h1><i class="fas fa-robot"></i> AI检测助手</h1>
            <p>学术论文优化专家<span class="version-badge">✅ 已优化</span></p>
        </div>

        <nav class="nav-menu">
            <div class="nav-item active" onclick="showPanel('ai-detection')">
                <i class="fas fa-search"></i>
                <span>AI内容检测</span>
            </div>
            <div class="nav-item" onclick="showPanel('unified-academic-optimization')">
                <i class="fas fa-graduation-cap"></i>
                <span>学术智能优化</span>
            </div>
            <div class="nav-item" onclick="showPanel('advanced-academic')">
                <i class="fas fa-microscope"></i>
                <span>高级学术架构</span>
            </div>
            <div class="nav-item" onclick="showPanel('multi-round')">
                <i class="fas fa-sync-alt"></i>
                <span>多轮优化</span>
            </div>
            <div class="nav-item" onclick="showPanel('llm-control')">
                <i class="fas fa-server"></i>
                <span>LLM服务控制</span>
            </div>
        </nav>
    </div>

    <!-- 主内容区域 -->
    <div class="main-container">
        <div class="main-header">
            <h2 id="main-title">AI内容检测</h2>
            <p id="main-description">专业的学术论文AI检测功能，支持多种检测算法</p>
        </div>

        <div class="content-area">

            <!-- AI内容检测面板 -->
            <div class="content-panel active" id="ai-detection-panel">
                <div class="panel-header">
                    <i class="fas fa-search"></i>
                    <div>
                        <h3>AI内容检测</h3>
                        <p class="panel-description">专业的学术论文AI检测功能，支持多种检测算法</p>
                    </div>
                </div>

                <div class="form-group">
                    <label for="detectText">请输入需要检测的文本内容：</label>
                    <textarea id="detectText" class="form-control" placeholder="在此输入您要检测的文本内容...&#10;&#10;💡 提示：&#10;• 建议输入200-1000字的文本段落&#10;• 系统已针对学术论文优化算法&#10;• 支持中英文混合文本检测"></textarea>
                </div>

                <div style="margin: 20px 0;">
                    <button class="btn btn-primary" onclick="detectAI()">
                        <i class="fas fa-search"></i> 开始检测
                    </button>
                    <button class="btn btn-info" onclick="toggleLLMMode()" id="llmModeBtn">
                        <i class="fas fa-robot"></i> <span id="llmModeText">启用LLM</span>
                    </button>
                    <button class="btn btn-secondary" onclick="clearDetection()">
                        <i class="fas fa-eraser"></i> 清空内容
                    </button>
                </div>

                <div class="loading" id="detectLoading">
                    <div class="spinner"></div>
                    <p>正在分析文本特征...</p>
                    <small>请稍候，正在使用AI算法分析您的文本内容</small>
                    <div class="loading-progress">
                        <div class="loading-progress-bar"></div>
                    </div>
                </div>

                <div class="result-area" id="detectResult">
                    <div class="ai-detection-result">
                        <!-- AI检测结果警告提示 -->
                        <div class="ai-detection-warning" id="aiDetectionWarning" style="display: none;">
                            <div class="warning-icon">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <div class="warning-content">
                                <div class="warning-title">检测到高度AI特征</div>
                                <div class="warning-description">您的文本被多个检测平台识别为AI生成，建议使用优化功能降低AI特征</div>
                            </div>
                        </div>
                        
                        <!-- 检测结果头部 -->
                        <div class="detection-header">
                            <div class="detection-score-main" id="detectionScoreMain">0%</div>
                            <div class="detection-status" id="detectionStatus">等待检测...</div>
                            <div class="detection-mode" id="detectionMode">📝 基础检测</div>
                        </div>

                        <!-- 饼图和分类展示 -->
                        <div class="pie-chart-container">
                            <div class="pie-chart">
                                <svg viewBox="0 0 100 100">
                                    <circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="8"/>
                                    <circle id="aiScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#dc3545"
                                            stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                                    <circle id="humanScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#28a745"
                                            stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                                </svg>
                                <div class="pie-chart-center">
                                    <div class="pie-chart-score" id="pieChartScore">0%</div>
                                    <div class="pie-chart-label">AI检测度</div>
                                </div>
                            </div>

                            <div class="detection-breakdown">
                                <div class="breakdown-item human-written">
                                    <div class="breakdown-percentage human-written" id="humanPercentage">0%</div>
                                    <div class="breakdown-label">人工写作</div>
                                </div>
                                <div class="breakdown-item ai-generated">
                                    <div class="breakdown-percentage ai-generated" id="aiPercentage">0%</div>
                                    <div class="breakdown-label">AI生成</div>
                                </div>
                                <div class="breakdown-item suspicious">
                                    <div class="breakdown-percentage suspicious" id="suspiciousPercentage">0%</div>
                                    <div class="breakdown-label">疑似AI</div>
                                </div>
                            </div>
                        </div>

                        <!-- 图例 -->
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-color human"></div>
                                <span>人工写作</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color ai"></div>
                                <span>AI生成</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-color suspicious"></div>
                                <span>疑似AI</span>
                            </div>
                        </div>

                        <!-- 进度条展示 -->
                        <div class="progress-bars">
                            <div class="progress-item">
                                <div class="progress-label">
                                    <span>AI检测分数</span>
                                    <span id="aiScoreLabel">0%</span>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill ai-score" id="aiScoreProgress" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="progress-item">
                                <div class="progress-label">
                                    <span>检测置信度</span>
                                    <span id="confidenceLabel">0%</span>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill confidence" id="confidenceProgress" style="width: 0%"></div>
                                </div>
                            </div>
                            <div class="progress-item">
                                <div class="progress-label">
                                    <span>文本复杂度</span>
                                    <span id="complexityLabel">0%</span>
                                </div>
                                <div class="progress-bar-container">
                                    <div class="progress-bar-fill complexity" id="complexityProgress" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <!-- 详细结果 -->
                        <div class="result-details" id="resultDetails"></div>
                        
                        <!-- 操作按钮 -->
                        <div class="action-buttons">
                            <button class="action-button optimize" onclick="copyToOptimizer()">
                                <i class="fas fa-magic"></i> 智能优化
                            </button>
                            <button class="action-button copy" onclick="copyDetectionResult()">
                                <i class="fas fa-copy"></i> 复制报告
                            </button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 统一学术智能优化面板 -->
            <div class="content-panel" id="unified-academic-optimization-panel">
                <div class="panel-header">
                    <i class="fas fa-graduation-cap"></i>
                    <div>
                        <h3>学术智能优化</h3>
                        <p class="panel-description">整合智能改写与学术优化的统一功能，三阶段优化：学术规范化 → 表达优化 → AI特征消除</p>
                    </div>
                </div>

                <div class="form-group">
                    <label for="unifiedAcademicText">学术文本内容：</label>
                    <textarea id="unifiedAcademicText" class="form-control" style="min-height: 150px;" placeholder="请输入您的学术论文段落...&#10;&#10;🎓 统一优化特色：&#10;• 保持原文核心观点不变&#10;• 维护学术严谨性和规范性&#10;• 提升表达质量和流畅性&#10;• 降低AI检测率，增强人性化特征"></textarea>
                </div>

                <!-- 统一优化模式选择 -->
                <div class="optimization-mode-selector" style="margin: 20px 0; padding: 15px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff;">
                    <h4 style="margin-bottom: 15px; color: #333;">
                        <i class="fas fa-cogs" style="color: #007bff;"></i> 优化模式选择
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 15px;">
                        <label class="optimization-option">
                            <input type="radio" name="unifiedOptimizationMode" value="conservative" checked>
                            <div class="option-content">
                                <strong>🛡️ 保守模式</strong>
                                <p>保持原文风格，轻度优化表达</p>
                            </div>
                        </label>
                        <label class="optimization-option">
                            <input type="radio" name="unifiedOptimizationMode" value="balanced">
                            <div class="option-content">
                                <strong>⚖️ 平衡模式</strong>
                                <p>平衡学术性与自然性，推荐选择</p>
                            </div>
                        </label>
                        <label class="optimization-option">
                            <input type="radio" name="unifiedOptimizationMode" value="aggressive">
                            <div class="option-content">
                                <strong>🚀 激进模式</strong>
                                <p>最大化优化效果，包含朱雀对抗</p>
                            </div>
                        </label>
                    </div>

                    <div class="optimization-info" style="margin-top: 15px; padding: 10px; background: white; border-radius: 5px; border: 1px solid #dee2e6;">
                        <h5 style="color: #007bff; margin-bottom: 8px;">
                            <i class="fas fa-info-circle"></i> 三阶段优化流程
                        </h5>
                        <div style="font-size: 0.9em; color: #666;">
                            <p><strong>统一处理流程：</strong></p>
                            <ol style="margin: 5px 0; padding-left: 20px;">
                                <li><strong>学术规范化：</strong>术语标准化、表达规范化、结构优化</li>
                                <li><strong>表达优化：</strong>语言流畅性提升、句式多样化、自然性增强</li>
                                <li><strong>AI特征消除：</strong>个人化表达、不确定性标记、生成痕迹清除</li>
                            </ol>
                            <p style="margin-top: 10px; color: #28a745;"><strong>核心保证：</strong>保持原文核心观点 + 维护学术严谨性 + 降低AI检测率</p>
                        </div>
                    </div>
                </div>

                <div style="margin: 20px 0;">
                    <button class="btn btn-primary" onclick="performUnifiedOptimization()">
                        <i class="fas fa-magic"></i> 开始学术智能优化
                    </button>
                    <button class="btn btn-secondary" onclick="clearUnifiedOptimization()" style="margin-left: 10px;">
                        <i class="fas fa-eraser"></i> 清空内容
                    </button>
                    <button class="btn btn-info" onclick="getOptimizationSuggestions()" style="margin-left: 10px;">
                        <i class="fas fa-lightbulb"></i> 优化建议
                    </button>
                </div>

                <div class="loading" id="unifiedOptimizationLoading">
                    <div class="spinner"></div>
                    <p>正在进行学术智能优化...</p>
                    <div id="optimizationProgress" style="margin-top: 10px; font-size: 0.9em; color: #666;"></div>
                </div>

                <div class="result-area" id="unifiedOptimizationResult">
                    <div class="result-details">
                        <h4><i class="fas fa-award"></i> 优化摘要</h4>
                        <div id="unifiedOptimizationSummary" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;"></div>

                        <h4><i class="fas fa-chart-line"></i> 质量指标</h4>
                        <div id="qualityMetrics" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 10px; margin: 15px 0;"></div>

                        <h4><i class="fas fa-file-alt"></i> 优化结果</h4>
                        <div id="unifiedOptimizedText" style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0; line-height: 1.6; border: 2px solid #28a745;"></div>

                        <div style="margin: 15px 0;">
                            <button class="btn btn-primary" onclick="toggleUnifiedComparisonView()">
                                <i class="fas fa-exchange-alt"></i> <span id="unifiedComparisonToggleText">显示对比视图</span>
                            </button>
                            <button class="btn btn-success" onclick="copyUnifiedOptimizationResult(event)">
                                <i class="fas fa-copy"></i> 复制优化结果
                            </button>
                            <button class="btn btn-info" onclick="showOptimizationHistory()">
                                <i class="fas fa-history"></i> 优化历程
                            </button>
                        </div>

                        <div class="comparison-view" id="unifiedComparisonView">
                            <h4><i class="fas fa-balance-scale"></i> 前后对比</h4>
                            <div class="comparison-container">
                                <div class="comparison-panel">
                                    <h4>原始文本</h4>
                                    <div class="comparison-text" id="unifiedOriginalText"></div>
                                </div>
                                <div class="comparison-panel">
                                    <h4>优化后文本</h4>
                                    <div class="comparison-text" id="unifiedOptimizedComparisonText"></div>
                                </div>
                            </div>
                        </div>

                        <div id="optimizationHistory" style="margin-top: 20px; display: none;">
                            <h4><i class="fas fa-timeline"></i> 优化历程</h4>
                            <div id="optimizationStages"></div>
                        </div>

                        <div id="unifiedOptimizationDetails" style="margin-top: 20px;"></div>
                    </div>
                </div>
            </div>

            <!-- 高级学术架构面板 -->
            <div class="content-panel" id="advanced-academic-panel">
                <div class="panel-header">
                    <i class="fas fa-microscope"></i>
                    <div>
                        <h3>高级学术架构</h3>
                        <p class="panel-description">一键式自动学术优化：动态对比 → 优势结构 → 智能融合</p>
                    </div>
                </div>

                <!-- 自动化流程说明 -->
                <div class="auto-process-info">
                    <h4><i class="fas fa-magic"></i> 自动化三步流程</h4>
                    <div class="process-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5>动态对比分析</h5>
                                <p>自动分析两段文本的结构规范性、创新性指数、证据等级</p>
                            </div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5>优势结构提取</h5>
                                <p>智能识别理论贡献、技术新颖度、论证严谨性等核心优势</p>
                            </div>
                        </div>
                        <div class="step-arrow">→</div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5>智能融合生成</h5>
                                <p>遵循JACS结构，生成高质量融合优化版本</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 自动化学术架构输入区域 -->
                <div class="auto-academic-input">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin: 20px 0;">
                        <div class="form-group">
                            <label for="autoTextA">学术文本A：</label>
                            <textarea id="autoTextA" class="form-control" placeholder="请输入第一段学术内容...&#10;&#10;🔬 系统将自动执行：&#10;• 结构规范性分析&#10;• 创新性指数评估&#10;• 证据等级检验&#10;• 优势特征提取"></textarea>
                        </div>

                        <div class="form-group">
                            <label for="autoTextB">学术文本B：</label>
                            <textarea id="autoTextB" class="form-control" placeholder="请输入第二段学术内容...&#10;&#10;⚡ 自动化流程：&#10;• 动态对比分析&#10;• 优势结构识别&#10;• 智能融合生成&#10;• 质量控制验证"></textarea>
                        </div>
                    </div>

                    <!-- 优化配置选项 -->
                    <div class="optimization-config">
                        <h5><i class="fas fa-cogs"></i> 优化配置</h5>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin: 15px 0;">
                            <label><input type="checkbox" id="autoJacsStructure" checked> JACS式紧凑结构</label>
                            <label><input type="checkbox" id="autoInnovationDensity" checked> 创新点密度≥3个/千字</label>
                            <label><input type="checkbox" id="autoEthicsCompliance" checked> 伦理规范声明</label>
                            <label><input type="checkbox" id="autoQualityControl" checked> 自动质量控制</label>
                        </div>
                    </div>

                    <div style="margin: 20px 0;">
                        <button class="btn btn-primary btn-large" onclick="startAutoAcademicOptimization()">
                            <i class="fas fa-magic"></i> 开始自动学术架构优化
                        </button>
                        <button class="btn btn-secondary" onclick="clearAutoAcademic()">
                            <i class="fas fa-eraser"></i> 清空内容
                        </button>
                    </div>
                </div>

                <!-- 自动化处理状态显示 -->
                <div class="auto-process-status" id="autoProcessStatus" style="display: none;">
                    <div class="process-timeline">
                        <div class="timeline-item" id="step1-status">
                            <div class="timeline-icon">
                                <i class="fas fa-balance-scale"></i>
                            </div>
                            <div class="timeline-content">
                                <h5>第一步：动态对比分析</h5>
                                <p class="status-text">准备中...</p>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item" id="step2-status">
                            <div class="timeline-icon">
                                <i class="fas fa-search-plus"></i>
                            </div>
                            <div class="timeline-content">
                                <h5>第二步：优势结构提取</h5>
                                <p class="status-text">等待中...</p>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>

                        <div class="timeline-item" id="step3-status">
                            <div class="timeline-icon">
                                <i class="fas fa-magic"></i>
                            </div>
                            <div class="timeline-content">
                                <h5>第三步：智能融合生成</h5>
                                <p class="status-text">等待中...</p>
                                <div class="progress-bar">
                                    <div class="progress-fill" style="width: 0%"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 综合结果显示区域 -->
                <div class="result-area" id="autoAcademicResult"></div>
            </div>

            <!-- 多轮优化面板 -->
            <div class="content-panel" id="multi-round-panel">
                <div class="panel-header">
                    <i class="fas fa-sync-alt"></i>
                    <div>
                        <h3>多轮优化</h3>
                        <p class="panel-description">自动多轮优化直到达到目标分数，智能迭代提升文本质量</p>
                    </div>
                </div>

                <div class="form-group">
                    <label for="multiRoundText">请输入需要多轮优化的文本内容：</label>
                    <textarea id="multiRoundText" class="form-control" placeholder="在此输入您要进行多轮优化的文本内容...&#10;&#10;🔄 多轮优化特色：&#10;• 自动迭代优化直到达标&#10;• 智能选择优化策略&#10;• 保持语义连贯性&#10;• 详细的优化历程记录"></textarea>
                </div>

                <div style="margin: 20px 0;">
                    <button class="btn btn-warning" onclick="startMultiRoundOptimization()">
                        <i class="fas fa-sync-alt"></i> 开始多轮优化
                    </button>
                    <button class="btn btn-secondary" onclick="clearMultiRound()">
                        <i class="fas fa-eraser"></i> 清空内容
                    </button>
                </div>

                <div class="loading" id="multiRoundLoading">
                    <div class="spinner"></div>
                    <p>正在进行多轮优化...</p>
                    <div class="progress-info">
                        <span id="currentRound">第1轮</span> |
                        <span id="currentScore">检测中...</span> |
                        <span id="currentStrategy">初始化...</span>
                    </div>
                </div>

                <div class="result-area" id="multiRoundResult"></div>
            </div>

            <!-- LLM服务控制面板 -->
            <div class="content-panel" id="llm-control-panel">
                <div class="panel-header">
                    <i class="fas fa-server"></i>
                    <div>
                        <h3>Ollama本地模型控制中心</h3>
                        <p class="panel-description">零配置连接Ollama本地大模型，支持qwen2.5系列等主流模型</p>
                    </div>
                </div>

                <!-- Ollama服务状态 -->
                <div class="ollama-status-card">
                    <div class="status-header">
                        <div class="status-indicator">
                            <div class="status-dot" id="ollamaStatusDot"></div>
                            <span class="status-text" id="ollamaStatusText">正在检查Ollama服务...</span>
                        </div>
                        <span class="status-badge" id="ollamaStatusBadge">检查中</span>
                    </div>

                    <div class="status-details" id="ollamaStatusDetails">
                        <div class="detail-item">
                            <span class="detail-label">服务地址:</span>
                            <span class="detail-value" id="ollamaUrl">http://127.0.0.1:11434</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">安全状态:</span>
                            <span class="detail-value" id="securityStatus">安全模式</span>
                            <div class="security-indicator secure" id="securityIndicator" title="安全连接"></div>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">当前模型:</span>
                            <span class="detail-value" id="currentModel">未选择</span>
                        </div>
                        <div class="detail-item">
                            <span class="detail-label">响应时间:</span>
                            <span class="detail-value" id="responseTime">-</span>
                        </div>
                    </div>
                </div>

                <!-- 模型管理 -->
                <div class="model-management">
                    <h4><i class="fas fa-brain"></i> 模型管理</h4>

                    <div class="model-selector">
                        <label for="modelSelect">选择模型:</label>
                        <select id="modelSelect" class="form-control" onchange="switchModel()">
                            <option value="">正在加载模型列表...</option>
                        </select>
                        <button class="btn btn-info" onclick="refreshModels()">
                            <i class="fas fa-sync-alt"></i> 刷新
                        </button>
                    </div>

                    <div class="recommended-models">
                        <h5>推荐模型:</h5>
                        <div class="model-cards">
                            <div class="model-card" onclick="installModel('qwen2.5:32b-instruct')">
                                <div class="model-name">qwen2.5:32b-instruct</div>
                                <div class="model-desc">高性能中文大模型，适合学术写作</div>
                                <div class="model-size">约18GB</div>
                            </div>
                            <div class="model-card" onclick="installModel('qwen2.5:14b-instruct')">
                                <div class="model-name">qwen2.5:14b-instruct</div>
                                <div class="model-desc">平衡性能与资源的中文模型</div>
                                <div class="model-size">约8GB</div>
                            </div>
                            <div class="model-card" onclick="installModel('qwen2.5:7b-instruct')">
                                <div class="model-name">qwen2.5:7b-instruct</div>
                                <div class="model-desc">轻量级中文模型，快速响应</div>
                                <div class="model-size">约4GB</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 服务控制 -->
                <div class="service-controls">
                    <h4><i class="fas fa-cogs"></i> 服务控制</h4>

                    <div class="control-buttons">
                        <button class="btn btn-success" id="connectOllamaBtn" onclick="connectOllama()">
                            <i class="fas fa-plug"></i> 连接Ollama
                        </button>
                        <button class="btn btn-primary" id="testConnectionBtn" onclick="testOllamaConnection()" disabled>
                            <i class="fas fa-vial"></i> 测试连接
                        </button>
                        <button class="btn btn-info" id="diagnoseBtn" onclick="diagnoseOllamaConnection()">
                            <i class="fas fa-stethoscope"></i> 连接诊断
                        </button>
                        <button class="btn btn-warning" id="restartOllamaBtn" onclick="restartOllama()" disabled>
                            <i class="fas fa-redo"></i> 重启服务
                        </button>
                        <button class="btn btn-secondary" id="refreshOllamaBtn" onclick="refreshOllamaStatus()">
                            <i class="fas fa-sync-alt"></i> 刷新状态
                        </button>
                    </div>

                <!-- 安全控制面板 -->
                <div class="security-control">
                    <h4><i class="fas fa-shield-alt"></i> 安全控制</h4>

                    <div class="security-buttons">
                        <button class="btn btn-warning" id="securityModeBtn" onclick="toggleSecurityMode()">
                            切换到兼容模式
                        </button>
                        <button class="btn btn-info" onclick="testSecurityConfiguration()">
                            <i class="fas fa-shield-check"></i> 安全测试
                        </button>
                        <button class="btn btn-secondary" onclick="showSecurityReport()">
                            <i class="fas fa-clipboard-list"></i> 安全报告
                        </button>
                    </div>

                    <div class="security-buttons">
                        <button class="btn btn-danger" onclick="fixCORSIssue()">
                            <i class="fas fa-wrench"></i> 修复CORS
                        </button>
                        <button class="btn btn-outline-primary" onclick="showSecurityConfig()">
                            <i class="fas fa-cog"></i> 配置指南
                        </button>
                        <button class="btn btn-outline-warning" onclick="resetSecurityConfiguration()">
                            <i class="fas fa-undo"></i> 重置配置
                        </button>
                    </div>

                    <div class="security-buttons">
                        <button class="btn btn-outline-success" onclick="exportSecurityConfiguration()">
                            <i class="fas fa-download"></i> 导出配置
                        </button>
                    </div>
                </div>
                </div>

                <!-- 性能监控 -->
                <div class="performance-monitor" id="performanceMonitor" style="display: none;">
                    <h4><i class="fas fa-chart-line"></i> 性能监控</h4>

                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-value" id="avgResponseTime">-</div>
                            <div class="metric-label">平均响应时间</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="totalRequests">0</div>
                            <div class="metric-label">总请求数</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="successRate">-</div>
                            <div class="metric-label">成功率</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value" id="tokenUsage">-</div>
                            <div class="metric-label">Token使用量</div>
                        </div>
                    </div>
                </div>

                <!-- 配置指南 -->
                <div class="config-guide">
                    <h4><i class="fas fa-book"></i> 配置指南</h4>

                    <div class="guide-steps">
                        <div class="step-item">
                            <div class="step-number">1</div>
                            <div class="step-content">
                                <h5>安装Ollama</h5>
                                <p>访问 <a href="https://ollama.ai" target="_blank">ollama.ai</a> 下载并安装Ollama</p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">2</div>
                            <div class="step-content">
                                <h5>启动服务</h5>
                                <p>在终端运行: <code>ollama serve</code></p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">3</div>
                            <div class="step-content">
                                <h5>安装模型</h5>
                                <p>运行: <code>ollama pull qwen2.5:32b-instruct</code></p>
                            </div>
                        </div>
                        <div class="step-item">
                            <div class="step-number">4</div>
                            <div class="step-content">
                                <h5>连接测试</h5>
                                <p>点击"连接Ollama"按钮测试连接</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 模块初始化器必须最先加载 -->
    <script src="js/module_initializer.js"></script>

    <!-- 核心检测模块 -->
    <script src="js/ai_detector.js"></script>
    <script src="js/zhuque_optimizer.js"></script>
    <script src="js/hybrid_detector.js"></script>
    <script src="js/ollama_manager_v2.js"></script>

    <!-- 优化器模块 -->
    <script src="js/academic_optimizer.js"></script>
    <script src="js/unified_academic_optimizer.js"></script>
    <script src="js/multi_round_optimizer.js"></script>

    <!-- 辅助模块 -->
    <script src="js/prompt_templates.js"></script>

    <!-- 主应用逻辑 -->
    <script src="js/main.js"></script>
</body>
</html>
