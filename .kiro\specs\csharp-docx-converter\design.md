# Design Document

## Overview

本设计文档描述了使用C# WPF技术重新实现DOCX到Markdown转换器的技术架构。该应用将采用MVVM架构模式，使用Material Design设计语言，并集成现代化的动画系统。核心转换功能将使用DocumentFormat.OpenXml库来处理DOCX文件，确保与原Python版本相同的转换质量。

## Architecture

### 整体架构
```
┌─────────────────────────────────────────────────────────────┐
│                    Presentation Layer                        │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Main Window   │  │  User Controls  │  │  Animations  │ │
│  │     (WPF)       │  │     (XAML)      │  │   System     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                   Application Layer                         │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   ViewModels    │  │    Commands     │  │   Services   │ │
│  │    (MVVM)       │  │   (ICommand)    │  │  (Business)  │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                    Business Layer                           │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │   Converter     │  │   Processors    │  │  Validators  │ │
│  │    Engine       │  │   (Content)     │  │   (Rules)    │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
┌─────────────────────────────────────────────────────────────┐
│                     Data Layer                              │
│  ┌─────────────────┐  ┌─────────────────┐  ┌──────────────┐ │
│  │  File System    │  │  Configuration  │  │   Logging    │ │
│  │    Access       │  │    Storage      │  │   System     │ │
│  └─────────────────┘  └─────────────────┘  └──────────────┘ │
└─────────────────────────────────────────────────────────────┘
```

### 技术栈选择
- **UI框架**: WPF (.NET 8) - Windows平台最佳的桌面应用框架
- **设计系统**: Material Design In XAML Toolkit - 现代化Material Design组件
- **DOCX处理**: DocumentFormat.OpenXml - Microsoft官方的Office文档处理库
- **动画框架**: WPF内置动画系统 + 自定义动画管理器
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **配置管理**: Microsoft.Extensions.Configuration
- **日志系统**: Microsoft.Extensions.Logging + Serilog

## Components and Interfaces

### 1. 主窗口和导航系统

#### MainWindow (WPF Window)
```csharp
public partial class MainWindow : Window
{
    public MainWindowViewModel ViewModel { get; }
    
    // 动画管理器
    private readonly IAnimationManager _animationManager;
    
    // 主题管理器
    private readonly IThemeManager _themeManager;
}
```

#### NavigationService
```csharp
public interface INavigationService
{
    Task NavigateToAsync<T>() where T : UserControl;
    Task NavigateToAsync(Type pageType);
    void GoBack();
    bool CanGoBack { get; }
}
```

### 2. 核心转换系统

#### IDocxConverter (主转换接口)
```csharp
public interface IDocxConverter
{
    Task<ConversionResult> ConvertAsync(string inputPath, string outputPath, 
        ConversionOptions options, IProgress<ConversionProgress> progress, 
        CancellationToken cancellationToken);
    
    Task<IEnumerable<ConversionResult>> ConvertBatchAsync(
        IEnumerable<ConversionTask> tasks, 
        IProgress<BatchConversionProgress> progress,
        CancellationToken cancellationToken);
}
```

#### DocumentProcessor (文档处理器)
```csharp
public class DocumentProcessor : IDocumentProcessor
{
    private readonly ITextProcessor _textProcessor;
    private readonly IImageProcessor _imageProcessor;
    private readonly ITableProcessor _tableProcessor;
    private readonly IFormulaProcessor _formulaProcessor;
    
    public async Task<string> ProcessDocumentAsync(WordprocessingDocument document, 
        ConversionOptions options);
}
```

### 3. 内容处理器

#### ITextProcessor (文本处理)
```csharp
public interface ITextProcessor
{
    string ProcessParagraph(Paragraph paragraph, ConversionOptions options);
    string ProcessRun(Run run, ConversionOptions options);
    string ProcessHeading(Paragraph paragraph, int level);
}
```

#### IImageProcessor (图片处理)
```csharp
public interface IImageProcessor
{
    Task<string> ProcessImageAsync(ImagePart imagePart, string outputDirectory);
    Task<IEnumerable<string>> ExtractImagesAsync(WordprocessingDocument document, 
        string outputDirectory);
}
```

#### ITableProcessor (表格处理)
```csharp
public interface ITableProcessor
{
    string ProcessTable(Table table, ConversionOptions options);
    string ProcessTableRow(TableRow row);
    string ProcessTableCell(TableCell cell);
}
```

### 4. UI组件系统

#### FileListControl (文件列表组件)
```csharp
public partial class FileListControl : UserControl
{
    public ObservableCollection<ConversionFileItem> Files { get; }
    
    // 拖拽支持
    private void OnDrop(object sender, DragEventArgs e);
    private void OnDragOver(object sender, DragEventArgs e);
}
```

#### ProgressControl (进度显示组件)
```csharp
public partial class ProgressControl : UserControl
{
    public double Progress { get; set; }
    public string StatusText { get; set; }
    public bool IsIndeterminate { get; set; }
}
```

### 5. 动画系统

#### IAnimationManager (动画管理器)
```csharp
public interface IAnimationManager
{
    Task PlayEntranceAnimationAsync(FrameworkElement element);
    Task PlayExitAnimationAsync(FrameworkElement element);
    Task PlayPageTransitionAsync(FrameworkElement from, FrameworkElement to);
    Task PlayProgressAnimationAsync(ProgressBar progressBar, double targetValue);
    
    void SetAnimationSpeed(double speed);
    void EnableAnimations(bool enabled);
}
```

#### AnimationSettings (动画配置)
```csharp
public class AnimationSettings
{
    public bool IsEnabled { get; set; } = true;
    public double Speed { get; set; } = 1.0;
    public TimeSpan DefaultDuration { get; set; } = TimeSpan.FromMilliseconds(300);
    public EasingFunctionBase DefaultEasing { get; set; } = new CubicEase();
}
```

## Data Models

### 核心数据模型

#### ConversionFileItem (转换文件项)
```csharp
public class ConversionFileItem : INotifyPropertyChanged
{
    public string FilePath { get; set; }
    public string FileName { get; set; }
    public long FileSize { get; set; }
    public ConversionStatus Status { get; set; }
    public double Progress { get; set; }
    public string ErrorMessage { get; set; }
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public TimeSpan? Duration => EndTime - StartTime;
}
```

#### ConversionOptions (转换选项)
```csharp
public class ConversionOptions
{
    public string OutputDirectory { get; set; }
    public bool ExtractImages { get; set; } = true;
    public bool ConvertTables { get; set; } = true;
    public bool ProcessFormulas { get; set; } = true;
    public ImageFormat ImageFormat { get; set; } = ImageFormat.Png;
    public string ImageDirectory { get; set; } = "images";
    public bool PreserveFormatting { get; set; } = true;
    public Encoding OutputEncoding { get; set; } = Encoding.UTF8;
}
```

#### ConversionResult (转换结果)
```csharp
public class ConversionResult
{
    public bool IsSuccess { get; set; }
    public string InputPath { get; set; }
    public string OutputPath { get; set; }
    public TimeSpan Duration { get; set; }
    public long InputSize { get; set; }
    public long OutputSize { get; set; }
    public int ImageCount { get; set; }
    public int TableCount { get; set; }
    public string ErrorMessage { get; set; }
    public Exception Exception { get; set; }
}
```

### ViewModels

#### MainWindowViewModel
```csharp
public class MainWindowViewModel : ViewModelBase
{
    public ObservableCollection<ConversionFileItem> Files { get; }
    public ConversionOptions Options { get; set; }
    public ConversionStatistics Statistics { get; set; }
    
    public ICommand AddFilesCommand { get; }
    public ICommand RemoveFileCommand { get; }
    public ICommand StartConversionCommand { get; }
    public ICommand PauseConversionCommand { get; }
    public ICommand OpenOutputDirectoryCommand { get; }
    public ICommand ToggleThemeCommand { get; }
}
```

#### SettingsViewModel
```csharp
public class SettingsViewModel : ViewModelBase
{
    public ConversionOptions ConversionOptions { get; set; }
    public AnimationSettings AnimationSettings { get; set; }
    public ThemeSettings ThemeSettings { get; set; }
    
    public ICommand SaveSettingsCommand { get; }
    public ICommand ResetSettingsCommand { get; }
    public ICommand SelectOutputDirectoryCommand { get; }
}
```

## Error Handling

### 异常处理策略

#### 自定义异常类型
```csharp
public class ConversionException : Exception
{
    public string FilePath { get; }
    public ConversionErrorType ErrorType { get; }
    
    public ConversionException(string filePath, ConversionErrorType errorType, 
        string message, Exception innerException = null)
        : base(message, innerException)
    {
        FilePath = filePath;
        ErrorType = errorType;
    }
}

public enum ConversionErrorType
{
    FileNotFound,
    InvalidFormat,
    PermissionDenied,
    CorruptedFile,
    ProcessingError,
    OutputError
}
```

#### 全局异常处理
```csharp
public class GlobalExceptionHandler
{
    public static void HandleException(Exception exception, string context = null)
    {
        // 记录日志
        Logger.LogError(exception, "Unhandled exception in {Context}", context);
        
        // 显示用户友好的错误消息
        ShowUserFriendlyError(exception);
        
        // 发送遥测数据（如果启用）
        TelemetryService.TrackException(exception);
    }
    
    private static void ShowUserFriendlyError(Exception exception)
    {
        var message = exception switch
        {
            ConversionException ce => GetConversionErrorMessage(ce),
            UnauthorizedAccessException => "没有足够的权限访问文件",
            FileNotFoundException => "找不到指定的文件",
            _ => "发生了未知错误，请重试"
        };
        
        MessageBox.Show(message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
    }
}
```

### 错误恢复机制
- **自动重试**: 对于临时性错误（如文件锁定），实现指数退避重试
- **部分失败处理**: 批量转换时，单个文件失败不影响其他文件
- **状态恢复**: 应用崩溃后能够恢复未完成的转换任务
- **用户反馈**: 提供详细的错误信息和建议的解决方案

## Testing Strategy

### 单元测试
```csharp
[TestClass]
public class DocumentProcessorTests
{
    private IDocumentProcessor _processor;
    private Mock<ITextProcessor> _textProcessorMock;
    
    [TestInitialize]
    public void Setup()
    {
        _textProcessorMock = new Mock<ITextProcessor>();
        _processor = new DocumentProcessor(_textProcessorMock.Object);
    }
    
    [TestMethod]
    public async Task ProcessDocumentAsync_ValidDocument_ReturnsMarkdown()
    {
        // Arrange
        var document = CreateTestDocument();
        var options = new ConversionOptions();
        
        // Act
        var result = await _processor.ProcessDocumentAsync(document, options);
        
        // Assert
        Assert.IsNotNull(result);
        Assert.IsTrue(result.Contains("# Test Header"));
    }
}
```

### 集成测试
```csharp
[TestClass]
public class ConversionIntegrationTests
{
    [TestMethod]
    public async Task ConvertAsync_RealDocxFile_ProducesValidMarkdown()
    {
        // 使用真实的DOCX文件测试完整的转换流程
        var converter = new DocxConverter();
        var inputPath = "TestFiles/sample.docx";
        var outputPath = "Output/sample.md";
        
        var result = await converter.ConvertAsync(inputPath, outputPath, 
            new ConversionOptions(), null, CancellationToken.None);
        
        Assert.IsTrue(result.IsSuccess);
        Assert.IsTrue(File.Exists(outputPath));
    }
}
```

### UI测试
```csharp
[TestClass]
public class MainWindowUITests
{
    private TestApplication _app;
    private MainWindow _mainWindow;
    
    [TestInitialize]
    public void Setup()
    {
        _app = new TestApplication();
        _mainWindow = _app.GetMainWindow();
    }
    
    [TestMethod]
    public void DragDropFile_ValidDocx_AddsToFileList()
    {
        // 模拟拖拽操作
        var fileList = _mainWindow.FindName("FileListControl") as FileListControl;
        var dragData = new DataObject(DataFormats.FileDrop, 
            new[] { "TestFiles/sample.docx" });
        
        fileList.SimulateDrop(dragData);
        
        Assert.AreEqual(1, fileList.Files.Count);
    }
}
```

### 性能测试
- **内存使用测试**: 确保处理大文件时内存使用合理
- **并发测试**: 验证多线程转换的稳定性
- **动画性能测试**: 确保动画在低端设备上也能流畅运行
- **启动时间测试**: 优化应用启动速度

### 测试覆盖率目标
- **单元测试覆盖率**: ≥ 80%
- **集成测试覆盖率**: ≥ 60%
- **UI测试覆盖率**: ≥ 40%
- **关键路径覆盖率**: 100%

## Performance Considerations

### 内存管理
- 使用`using`语句确保资源及时释放
- 对大文件实现流式处理，避免全部加载到内存
- 定期调用GC.Collect()在批量处理间隙

### 多线程优化
- UI线程与工作线程分离
- 使用Task.Run进行CPU密集型操作
- 实现取消令牌支持用户中断操作

### 动画性能
- 使用硬件加速的WPF动画
- 避免在动画期间进行复杂计算
- 实现动画质量自适应调整

### 文件I/O优化
- 使用异步文件操作
- 实现文件访问缓存机制
- 优化临时文件管理