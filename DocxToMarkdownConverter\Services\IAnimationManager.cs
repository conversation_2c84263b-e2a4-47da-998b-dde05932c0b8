using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using Button = System.Windows.Controls.Button;
using ProgressBar = System.Windows.Controls.ProgressBar;
using Point = System.Windows.Point;
using Color = System.Windows.Media.Color;

namespace DocxToMarkdownConverter.Services;

public interface IAnimationManager
{
    Task PlayEntranceAnimationAsync(FrameworkElement element);
    Task PlayExitAnimationAsync(FrameworkElement element);
    Task PlayPageTransitionAsync(FrameworkElement from, FrameworkElement to);
    Task PlayProgressAnimationAsync(ProgressBar progressBar, double targetValue);
    Task PlayButtonHoverAnimationAsync(Button button, bool isEntering);
    Task PlayScaleAnimationAsync(FrameworkElement element, double fromScale, double toScale);
    Task PlayFadeAnimationAsync(FrameworkElement element, double fromOpacity, double toOpacity);
    Task PlaySlideAnimationAsync(FrameworkElement element, Point fromPosition, Point toPosition);
    Task PlayRotateAnimationAsync(FrameworkElement element, double fromAngle, double toAngle);
    Task PlayColorAnimationAsync(SolidColorBrush brush, Color fromColor, Color toColor);
    Task PlayPulseAnimationAsync(FrameworkElement element);
    Task PlayShakeAnimationAsync(FrameworkElement element);
    void SetAnimationSpeed(double speed);
    void EnableAnimations(bool enabled);
    void AttachHoverAnimations(Button button);
    void DetachHoverAnimations(Button button);
}

