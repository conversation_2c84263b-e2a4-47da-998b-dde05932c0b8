using DocumentFormat.OpenXml.Wordprocessing;
using DocxToMarkdownConverter.Models;
using System.Text;
using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 简化的DOCX表格处理器，将表格转换为Markdown格式
///
/// 设计原则：
/// 1. 简洁性 - 移除复杂的特性处理，专注核心功能
/// 2. 高效性 - 减少不必要的循环和字符串操作
/// 3. 易维护性 - 清晰的方法职责分离和统一的错误处理
///
/// 主要简化：
/// - 移除复杂的对齐检测，统一使用左对齐
/// - 简化合并单元格处理，使用文本标记而非HTML
/// - 统一错误处理机制，减少重复的try-catch块
/// - 优化表格列数一致性处理
///
/// 输出格式：标准Markdown表格，兼容主流解析器
/// </summary>
public class TableProcessor : ITableProcessor
{
    private readonly ITextProcessor _textProcessor;
    private readonly ILogger<TableProcessor>? _logger;

    public TableProcessor(ITextProcessor textProcessor, ILogger<TableProcessor>? logger = null)
    {
        _textProcessor = textProcessor ?? throw new ArgumentNullException(nameof(textProcessor));
        _logger = logger;
    }

    /// <summary>
    /// 处理完整的表格并转换为Markdown表格格式
    /// 简化逻辑：专注于核心功能，移除复杂的特性处理
    /// </summary>
    public string ProcessTable(object table, ConversionOptions options)
    {
        // 输入验证 - 简化的单点验证
        if (table is not Table tableElement)
        {
            _logger?.LogWarning("无效的表格对象");
            return string.Empty;
        }

        // 统一的错误处理包装
        try
        {
            return ProcessTableInternal(tableElement);
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "表格处理失败");
            return "<!-- 表格处理错误 -->";
        }
    }

    /// <summary>
    /// 内部表格处理逻辑 - 优化的核心实现
    /// 确保输出格式规范且易于阅读
    /// </summary>
    private string ProcessTableInternal(Table tableElement)
    {
        var rows = tableElement.Elements<DocumentFormat.OpenXml.Wordprocessing.TableRow>().ToList();

        // 空表格检查
        if (!rows.Any())
        {
            return string.Empty;
        }

        var result = new StringBuilder();

        // 确定表格的最大列数（考虑所有行）
        var maxColumns = DetermineMaxColumns(rows);

        result.AppendLine(); // 表格前空行

        // 处理表头（第一行）
        var headerMarkdown = ProcessTableRowWithColumnCount(rows[0], maxColumns);
        if (!string.IsNullOrEmpty(headerMarkdown))
        {
            result.AppendLine(headerMarkdown);
            result.AppendLine(CreateMarkdownSeparator(maxColumns));
        }

        // 处理数据行，确保列数一致
        for (int i = 1; i < rows.Count; i++)
        {
            var rowMarkdown = ProcessTableRowWithColumnCount(rows[i], maxColumns);
            if (!string.IsNullOrEmpty(rowMarkdown))
            {
                result.AppendLine(rowMarkdown);
            }
        }

        result.AppendLine(); // 表格后空行

        _logger?.LogDebug("成功处理表格，共 {RowCount} 行，{ColumnCount} 列", rows.Count, maxColumns);
        return result.ToString().Trim();
    }

    /// <summary>
    /// 处理单个表格行 - 简化的行处理逻辑
    /// 专注于提取单元格内容并格式化为Markdown行
    /// </summary>
    public string ProcessTableRow(object row)
    {
        if (row is not DocumentFormat.OpenXml.Wordprocessing.TableRow rowElement)
        {
            return string.Empty;
        }

        var cells = rowElement.Elements<DocumentFormat.OpenXml.Wordprocessing.TableCell>().ToList();
        if (!cells.Any())
        {
            return string.Empty;
        }

        // 简化的单元格内容提取
        var cellContents = new List<string>();
        foreach (var cell in cells)
        {
            var content = ProcessTableCell(cell);
            // 确保空单元格有占位符
            cellContents.Add(string.IsNullOrWhiteSpace(content) ? " " : content.Trim());
        }

        // 生成Markdown行格式：| 单元格1 | 单元格2 | 单元格3 |
        return "| " + string.Join(" | ", cellContents) + " |";
    }

    /// <summary>
    /// 处理单个表格单元格 - 极简的单元格处理逻辑
    /// 专注于提取纯文本内容，移除复杂的格式化和合并单元格处理
    /// </summary>
    public string ProcessTableCell(object cell)
    {
        if (cell is not DocumentFormat.OpenXml.Wordprocessing.TableCell cellElement)
        {
            return string.Empty;
        }

        var content = new StringBuilder();
        var paragraphs = cellElement.Elements<Paragraph>().ToList();

        // 简化的段落内容提取
        foreach (var paragraph in paragraphs)
        {
            var options = new ConversionOptions { PreserveFormatting = false }; // 简化：不保留复杂格式
            var paragraphText = _textProcessor.ProcessParagraph(paragraph, options);

            if (!string.IsNullOrEmpty(paragraphText))
            {
                if (content.Length > 0)
                {
                    content.Append(" "); // 使用空格分隔多个段落，而不是HTML标记
                }
                content.Append(CleanCellContent(paragraphText));
            }
        }

        // 处理合并单元格 - 极简的标记方式
        // 只处理水平合并，垂直合并在Markdown中难以表示，因此忽略
        var cellProperties = cellElement.TableCellProperties;
        var gridSpan = cellProperties?.GridSpan?.Val?.Value ?? 1;
        if (gridSpan > 1)
        {
            content.Append($" [跨{gridSpan}列]"); // 使用方括号标记，更清晰
        }

        return string.IsNullOrWhiteSpace(content.ToString()) ? " " : content.ToString();
    }

    /// <summary>
    /// 创建Markdown表格分隔行 - 简化实现
    /// 使用统一的左对齐格式，移除复杂的对齐检测
    /// </summary>
    private string CreateMarkdownSeparator(int columnCount)
    {
        if (columnCount <= 0)
        {
            return "| --- |";
        }

        // 简化：所有列都使用左对齐
        var separators = new string[columnCount];
        for (int i = 0; i < columnCount; i++)
        {
            separators[i] = "---";
        }

        return "| " + string.Join(" | ", separators) + " |";
    }

    /// <summary>
    /// 确定表格的最大列数 - 分析所有行以确保一致性
    /// </summary>
    private int DetermineMaxColumns(List<DocumentFormat.OpenXml.Wordprocessing.TableRow> rows)
    {
        int maxColumns = 0;

        foreach (var row in rows)
        {
            int rowColumns = GetColumnCount(row);
            if (rowColumns > maxColumns)
            {
                maxColumns = rowColumns;
            }
        }

        return maxColumns > 0 ? maxColumns : 1;
    }

    /// <summary>
    /// 获取表格行的列数 - 考虑合并单元格的列数计算
    /// </summary>
    private int GetColumnCount(DocumentFormat.OpenXml.Wordprocessing.TableRow row)
    {
        int totalColumns = 0;
        var cells = row.Elements<DocumentFormat.OpenXml.Wordprocessing.TableCell>();

        foreach (var cell in cells)
        {
            // 考虑合并单元格的跨列数
            var cellProperties = cell.TableCellProperties;
            var gridSpan = cellProperties?.GridSpan?.Val?.Value ?? 1;
            totalColumns += (int)gridSpan;
        }

        return totalColumns > 0 ? totalColumns : 1; // 至少返回1列
    }

    /// <summary>
    /// 处理表格行并确保指定的列数 - 优化的行处理逻辑
    /// </summary>
    private string ProcessTableRowWithColumnCount(DocumentFormat.OpenXml.Wordprocessing.TableRow rowElement, int targetColumnCount)
    {
        var cells = rowElement.Elements<DocumentFormat.OpenXml.Wordprocessing.TableCell>().ToList();
        if (!cells.Any())
        {
            // 如果没有单元格，创建空行
            return "| " + string.Join(" | ", new string[targetColumnCount].Select(_ => " ")) + " |";
        }

        var cellContents = new List<string>();
        int currentColumn = 0;

        foreach (var cell in cells)
        {
            var content = ProcessTableCell(cell);
            var cleanContent = string.IsNullOrWhiteSpace(content) ? " " : content.Trim();

            // 处理合并单元格的跨列
            var cellProperties = cell.TableCellProperties;
            var gridSpan = cellProperties?.GridSpan?.Val?.Value ?? 1;

            cellContents.Add(cleanContent);
            currentColumn += (int)gridSpan;

            // 如果跨多列，添加空单元格占位
            for (int i = 1; i < gridSpan && currentColumn <= targetColumnCount; i++)
            {
                cellContents.Add(" ");
            }

            if (currentColumn >= targetColumnCount)
            {
                break;
            }
        }

        // 确保达到目标列数
        while (cellContents.Count < targetColumnCount)
        {
            cellContents.Add(" ");
        }

        // 如果超过目标列数，截断
        if (cellContents.Count > targetColumnCount)
        {
            cellContents = cellContents.Take(targetColumnCount).ToList();
        }

        return "| " + string.Join(" | ", cellContents) + " |";
    }

    /// <summary>
    /// 清理单元格内容 - 简化的文本清理逻辑
    /// 专注于处理会破坏Markdown表格格式的字符
    /// </summary>
    private string CleanCellContent(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return string.Empty;
        }

        // 简化的文本清理：只处理必要的字符转义
        return text
            .Replace("|", "\\|")     // 转义管道字符，防止破坏表格格式
            .Replace("\n", " ")      // 将换行符转换为空格，保持单行格式
            .Replace("\r", "")       // 移除回车符
            .Replace("\t", " ")      // 将制表符转换为空格
            .Trim();                 // 移除首尾空白字符
    }
}