using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using System.Windows;
using DocxToMarkdownConverter.Commands;

namespace DocxToMarkdownConverter.ViewModels;

public abstract class ViewModelBase : INotifyPropertyChanged, IDisposable
{
    private bool _isBusy;
    private string _busyMessage = string.Empty;
    private bool _disposed;

    public event PropertyChangedEventHandler? PropertyChanged;

    /// <summary>
    /// Gets or sets a value indicating whether the view model is busy performing an operation.
    /// </summary>
    public bool IsBusy
    {
        get => _isBusy;
        set => SetProperty(ref _isBusy, value);
    }

    /// <summary>
    /// Gets or sets the message to display when the view model is busy.
    /// </summary>
    public string BusyMessage
    {
        get => _busyMessage;
        set => SetProperty(ref _busyMessage, value);
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    protected void RaisePropertyChanged([CallerMemberName] string? propertyName = null)
    {
        OnPropertyChanged(propertyName);
    }

    /// <summary>
    /// Creates a RelayCommand with the specified execute action.
    /// </summary>
    protected RelayCommand CreateCommand(Action execute, Func<bool>? canExecute = null)
    {
        return new RelayCommand(execute, canExecute);
    }

    /// <summary>
    /// Creates a RelayCommand with the specified execute action and parameter.
    /// </summary>
    protected RelayCommand<T> CreateCommand<T>(Action<T?> execute, Func<T?, bool>? canExecute = null)
    {
        return new RelayCommand<T>(execute, canExecute);
    }

    /// <summary>
    /// Creates an AsyncRelayCommand with the specified execute action.
    /// </summary>
    protected AsyncRelayCommand CreateAsyncCommand(Func<Task> execute, Func<bool>? canExecute = null)
    {
        return new AsyncRelayCommand(execute, canExecute);
    }

    /// <summary>
    /// Sets the busy state with an optional message.
    /// </summary>
    protected void SetBusy(bool isBusy, string message = "")
    {
        IsBusy = isBusy;
        BusyMessage = message;
    }

    /// <summary>
    /// Executes an async operation while setting the busy state.
    /// </summary>
    protected async Task ExecuteWithBusyAsync(Func<Task> operation, string busyMessage = "Processing...")
    {
        if (IsBusy) return;

        try
        {
            SetBusy(true, busyMessage);

            // 在后台线程执行操作，避免阻塞UI
            await Task.Run(async () =>
            {
                try
                {
                    await operation().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    // 确保异常在UI线程上处理
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        throw new InvalidOperationException($"操作执行失败: {ex.Message}", ex);
                    });
                }
            }).ConfigureAwait(true); // 确保继续在UI线程上下文中
        }
        catch (Exception ex)
        {
            // 记录详细的异常信息
            System.Diagnostics.Debug.WriteLine($"ExecuteWithBusyAsync异常: {ex}");

            // 确保在UI线程上显示错误
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                throw;
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => throw ex);
            }
        }
        finally
        {
            // 确保在UI线程上重置忙碌状态
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                SetBusy(false);
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => SetBusy(false));
            }
        }
    }

    /// <summary>
    /// Executes an async operation with result while setting the busy state.
    /// </summary>
    protected async Task<T> ExecuteWithBusyAsync<T>(Func<Task<T>> operation, string busyMessage = "Processing...")
    {
        if (IsBusy) return default(T)!;

        try
        {
            SetBusy(true, busyMessage);

            // 在后台线程执行操作，避免阻塞UI
            return await Task.Run(async () =>
            {
                try
                {
                    return await operation().ConfigureAwait(false);
                }
                catch (Exception ex)
                {
                    // 确保异常在UI线程上处理
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        throw new InvalidOperationException($"操作执行失败: {ex.Message}", ex);
                    });
                    return default(T)!; // 这行不会执行，但编译器需要
                }
            }).ConfigureAwait(true); // 确保继续在UI线程上下文中
        }
        catch (Exception ex)
        {
            // 记录详细的异常信息
            System.Diagnostics.Debug.WriteLine($"ExecuteWithBusyAsync<T>异常: {ex}");

            // 确保在UI线程上显示错误
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                throw;
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => throw ex);
                return default(T)!; // 这行不会执行，但编译器需要
            }
        }
        finally
        {
            // 确保在UI线程上重置忙碌状态
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                SetBusy(false);
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => SetBusy(false));
            }
        }
    }

    /// <summary>
    /// Called when the view model is being disposed.
    /// </summary>
    protected virtual void OnDispose()
    {
        // Override in derived classes to perform cleanup
    }

    public void Dispose()
    {
        if (!_disposed)
        {
            OnDispose();
            _disposed = true;
        }
        GC.SuppressFinalize(this);
    }
}