using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Models;
using Application = System.Windows.Application;
using Color = System.Windows.Media.Color;

namespace DocxToMarkdownConverter.Services;

public class ThemeManager : IThemeManager
{
    private AppTheme _currentTheme = AppTheme.Dark;
    private readonly Application _application;
    private readonly ILogger<ThemeManager> _logger;
    private readonly ThemeSettings _settings;
    private readonly ISystemThemeMonitor _systemThemeMonitor;
    private bool _isApplyingTheme = false;

    public ThemeManager(ILogger<ThemeManager> logger, ISystemThemeMonitor systemThemeMonitor)
    {
        _application = Application.Current;
        _logger = logger;
        _systemThemeMonitor = systemThemeMonitor;
        _settings = new ThemeSettings();

        // Subscribe to system theme changes
        _systemThemeMonitor.SystemThemeChanged += OnSystemThemeChanged;

        // Initialize with system theme if auto-follow is enabled
        if (_settings.FollowSystemTheme)
        {
            _currentTheme = GetSystemTheme();
        }
    }

    public AppTheme CurrentTheme => _currentTheme;
    public ThemeSettings Settings => _settings;
    public bool IsSystemThemeSupported => Environment.OSVersion.Version.Major >= 10;

    public event EventHandler<ThemeChangedEventArgs>? ThemeChanged;
    public event EventHandler<AccentColorChangedEventArgs>? AccentColorChanged;

    public async Task ApplyThemeAsync(AppTheme theme, bool animate = true)
    {
        // Prevent re-entry to avoid deadlocks
        if (_isApplyingTheme) return;

        var oldTheme = _currentTheme;
        var targetTheme = theme == AppTheme.Auto ? GetSystemTheme() : theme;

        if (oldTheme == targetTheme) return;

        _isApplyingTheme = true;

        try
        {
            _logger.LogInformation("Applying theme change from {OldTheme} to {NewTheme} (animated: {Animate})",
                oldTheme, targetTheme, animate);

            _currentTheme = targetTheme;
            _settings.CurrentTheme = targetTheme;

            if (animate && _settings.EnableThemeTransitions)
            {
                await ApplyThemeWithAnimationAsync(targetTheme);
            }
            else
            {
                // Apply theme resources directly on UI thread
                if (_application.Dispatcher.CheckAccess())
                {
                    ApplyThemeResources(targetTheme);
                }
                else
                {
                    await _application.Dispatcher.InvokeAsync(() => ApplyThemeResources(targetTheme));
                }
            }

            ThemeChanged?.Invoke(this, new ThemeChangedEventArgs(oldTheme, targetTheme, animate));
            _logger.LogInformation("Theme successfully changed to {Theme}", targetTheme);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply theme {Theme}", targetTheme);
        }
        finally
        {
            _isApplyingTheme = false;
        }
    }

    public async Task ToggleThemeAsync()
    {
        var newTheme = _currentTheme == AppTheme.Light ? AppTheme.Dark : AppTheme.Light;
        await ApplyThemeAsync(newTheme, _settings.EnableThemeTransitions);
    }

    public void SetAccentColor(string colorHex)
    {
        // Prevent re-entry
        if (_isApplyingTheme) return;

        var oldColor = _settings.AccentColor;

        try
        {
            var color = (Color)System.Windows.Media.ColorConverter.ConvertFromString(colorHex);
            var brush = new SolidColorBrush(color);

            // Use safer dispatcher invocation
            if (_application.Dispatcher.CheckAccess())
            {
                UpdateAccentColorResources(color, brush);
            }
            else
            {
                _application.Dispatcher.BeginInvoke(new Action(() =>
                {
                    UpdateAccentColorResources(color, brush);
                }));
            }

            _settings.AccentColor = colorHex;
            AccentColorChanged?.Invoke(this, new AccentColorChangedEventArgs(oldColor, colorHex));

            _logger.LogInformation("Accent color changed from {OldColor} to {NewColor}", oldColor, colorHex);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set accent color to {ColorHex}", colorHex);
        }
    }

    private void UpdateAccentColorResources(Color color, SolidColorBrush brush)
    {
        try
        {
            // Update Material Design primary colors
            _application.Resources["PrimaryHueMidBrush"] = brush;
            _application.Resources["PrimaryHueDarkBrush"] = new SolidColorBrush(DarkenColor(color, 0.2f));
            _application.Resources["PrimaryHueLightBrush"] = new SolidColorBrush(LightenColor(color, 0.2f));

            // Ensure proper foreground colors for text visibility
            var whiteBrush = new SolidColorBrush(Colors.White);
            var blackBrush = new SolidColorBrush(Colors.Black);

            // Set foreground colors based on contrast
            _application.Resources["PrimaryHueMidForegroundBrush"] = whiteBrush;
            _application.Resources["PrimaryHueDarkForegroundBrush"] = whiteBrush;
            _application.Resources["PrimaryHueLightForegroundBrush"] = blackBrush;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to update accent color resources");
        }
    }

    public AppTheme GetSystemTheme()
    {
        return _systemThemeMonitor.GetCurrentSystemTheme();
    }

    public void StartSystemThemeMonitoring()
    {
        if (!IsSystemThemeSupported || !_settings.FollowSystemTheme) return;

        _logger.LogInformation("Starting system theme monitoring");
        _systemThemeMonitor.StartMonitoring();
    }

    public void StopSystemThemeMonitoring()
    {
        _logger.LogInformation("Stopping system theme monitoring");
        _systemThemeMonitor.StopMonitoring();
    }

    private async void OnSystemThemeChanged(object? sender, SystemThemeChangedEventArgs e)
    {
        if (_settings.FollowSystemTheme)
        {
            _logger.LogInformation("Applying system theme change from {OldTheme} to {NewTheme}", e.OldTheme, e.NewTheme);
            await ApplyThemeAsync(e.NewTheme, _settings.EnableThemeTransitions);
        }
    }

    private async Task ApplyThemeWithAnimationAsync(AppTheme theme)
    {
        Window? mainWindow = null;
        await _application.Dispatcher.InvokeAsync(() =>
        {
            mainWindow = _application.MainWindow;
        });

        if (mainWindow == null) return;

        // Create fade out animation
        var fadeOut = new DoubleAnimation
        {
            From = 1.0,
            To = 0.8,
            Duration = TimeSpan.FromMilliseconds(150),
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
        };

        // Create fade in animation
        var fadeIn = new DoubleAnimation
        {
            From = 0.8,
            To = 1.0,
            Duration = TimeSpan.FromMilliseconds(200),
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
        };

        var tcs = new TaskCompletionSource<bool>();

        fadeOut.Completed += (s, e) =>
        {
            // Apply theme resources during fade
            ApplyThemeResources(theme);
            
            // Start fade in
            fadeIn.Completed += (s2, e2) => tcs.SetResult(true);
            mainWindow.BeginAnimation(UIElement.OpacityProperty, fadeIn);
        };

        // Start fade out
        mainWindow.BeginAnimation(UIElement.OpacityProperty, fadeOut);
        
        await tcs.Task;
    }

    private void ApplyThemeResources(AppTheme theme)
    {
        try
        {
            // Remove existing theme dictionaries
            var existingThemes = _application.Resources.MergedDictionaries
                .Where(d => d.Source?.ToString().Contains("Theme.xaml") == true || 
                           d.Source?.ToString().Contains("MaterialDesignTheme") == true)
                .ToList();

            foreach (var existingTheme in existingThemes)
            {
                _application.Resources.MergedDictionaries.Remove(existingTheme);
            }

            // Add new theme dictionary
            var themeUri = theme switch
            {
                AppTheme.Dark => new Uri("Styles/DarkTheme.xaml", UriKind.Relative),
                _ => new Uri("Styles/LightTheme.xaml", UriKind.Relative)
            };

            _application.Resources.MergedDictionaries.Insert(0, new ResourceDictionary { Source = themeUri });

            _logger.LogDebug("Applied theme resources for {Theme}", theme);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to apply theme resources for {Theme}", theme);
        }
    }

    private static Color DarkenColor(Color color, float factor)
    {
        return Color.FromRgb(
            (byte)(color.R * (1 - factor)),
            (byte)(color.G * (1 - factor)),
            (byte)(color.B * (1 - factor))
        );
    }

    private static Color LightenColor(Color color, float factor)
    {
        return Color.FromRgb(
            (byte)(color.R + (255 - color.R) * factor),
            (byte)(color.G + (255 - color.G) * factor),
            (byte)(color.B + (255 - color.B) * factor)
        );
    }
}
