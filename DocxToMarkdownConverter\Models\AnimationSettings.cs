using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Media.Animation;

namespace DocxToMarkdownConverter.Models;

public class AnimationSettings : INotifyPropertyChanged
{
    private bool _isEnabled = true;
    private double _speed = 1.0;
    private TimeSpan _defaultDuration = TimeSpan.FromMilliseconds(300);
    private EasingMode _defaultEasing = EasingMode.EaseOut;
    private bool _enablePageTransitions = true;
    private bool _enableButtonAnimations = true;
    private bool _enableProgressAnimations = true;

    public bool IsEnabled
    {
        get => _isEnabled;
        set => SetProperty(ref _isEnabled, value);
    }

    public double Speed
    {
        get => _speed;
        set => SetProperty(ref _speed, Math.Max(0.1, Math.Min(3.0, value)));
    }

    public TimeSpan DefaultDuration
    {
        get => _defaultDuration;
        set => SetProperty(ref _defaultDuration, value);
    }

    public EasingMode DefaultEasing
    {
        get => _defaultEasing;
        set => SetProperty(ref _defaultEasing, value);
    }

    public bool EnablePageTransitions
    {
        get => _enablePageTransitions;
        set => SetProperty(ref _enablePageTransitions, value);
    }

    public bool EnableButtonAnimations
    {
        get => _enableButtonAnimations;
        set => SetProperty(ref _enableButtonAnimations, value);
    }

    public bool EnableProgressAnimations
    {
        get => _enableProgressAnimations;
        set => SetProperty(ref _enableProgressAnimations, value);
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}