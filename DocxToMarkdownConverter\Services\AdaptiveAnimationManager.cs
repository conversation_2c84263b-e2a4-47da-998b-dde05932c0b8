using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Media.Animation;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 自适应动画管理器实现
/// </summary>
public class AdaptiveAnimationManager : IAdaptiveAnimationManager
{
    private readonly ILogger<AdaptiveAnimationManager> _logger;
    private readonly IPerformanceMonitorService _performanceMonitor;
    
    private AnimationPerformanceLevel _currentPerformanceLevel = AnimationPerformanceLevel.High;
    private bool _animationsEnabled = true;
    private readonly AnimationPerformanceStats _stats = new();
    private readonly object _lockObject = new();

    public AnimationPerformanceLevel CurrentPerformanceLevel => _currentPerformanceLevel;

    public event EventHandler<PerformanceLevelChangedEventArgs>? PerformanceLevelChanged;

    public AdaptiveAnimationManager(
        ILogger<AdaptiveAnimationManager> logger,
        IPerformanceMonitorService performanceMonitor)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _performanceMonitor = performanceMonitor ?? throw new ArgumentNullException(nameof(performanceMonitor));
        
        _stats.StartTime = DateTime.Now;
        _stats.LastUpdate = DateTime.Now;

        // 监听性能变化
        _performanceMonitor.PerformanceAlert += OnPerformanceAlert;
    }

    public Storyboard CreateAdaptiveAnimation(FrameworkElement target, AnimationConfig config)
    {
        lock (_lockObject)
        {
            _stats.TotalAnimations++;
        }

        if (!_animationsEnabled || ShouldSkipAnimation(config))
        {
            lock (_lockObject)
            {
                _stats.SkippedAnimations++;
            }
            return new Storyboard(); // 返回空动画
        }

        var storyboard = new Storyboard();
        var animation = CreateAnimationForType(config);

        if (animation != null)
        {
            // 根据性能级别调整动画
            AdjustAnimationForPerformance(animation, config);
            
            // 设置目标
            Storyboard.SetTarget(animation, target);
            Storyboard.SetTargetProperty(animation, new PropertyPath(config.PropertyName));
            
            storyboard.Children.Add(animation);

            // 添加完成事件处理
            storyboard.Completed += (s, e) =>
            {
                lock (_lockObject)
                {
                    _stats.CompletedAnimations++;
                    _stats.LastUpdate = DateTime.Now;
                }
            };
        }

        return storyboard;
    }

    public void UpdatePerformanceLevel(SystemPerformanceInfo performanceInfo)
    {
        var newLevel = DeterminePerformanceLevel(performanceInfo);
        
        if (newLevel != _currentPerformanceLevel)
        {
            var oldLevel = _currentPerformanceLevel;
            _currentPerformanceLevel = newLevel;
            
            var reason = BuildPerformanceChangeReason(performanceInfo);
            _logger.LogInformation("动画性能级别从 {OldLevel} 变更为 {NewLevel}: {Reason}", 
                oldLevel, newLevel, reason);
            
            PerformanceLevelChanged?.Invoke(this, 
                new PerformanceLevelChangedEventArgs(oldLevel, newLevel, reason));
        }
    }

    public void SetAnimationsEnabled(bool enabled)
    {
        _animationsEnabled = enabled;
        _logger.LogInformation("动画已{Status}", enabled ? "启用" : "禁用");
    }

    public TimeSpan GetRecommendedDuration(AnimationType animationType)
    {
        var baseDuration = animationType switch
        {
            AnimationType.FadeIn or AnimationType.FadeOut => TimeSpan.FromMilliseconds(300),
            AnimationType.SlideIn or AnimationType.SlideOut => TimeSpan.FromMilliseconds(400),
            AnimationType.Scale => TimeSpan.FromMilliseconds(250),
            AnimationType.Rotate => TimeSpan.FromMilliseconds(500),
            AnimationType.Bounce => TimeSpan.FromMilliseconds(600),
            AnimationType.Elastic => TimeSpan.FromMilliseconds(800),
            _ => TimeSpan.FromMilliseconds(300)
        };

        // 根据性能级别调整持续时间
        return _currentPerformanceLevel switch
        {
            AnimationPerformanceLevel.Disabled => TimeSpan.Zero,
            AnimationPerformanceLevel.Low => TimeSpan.FromMilliseconds(baseDuration.TotalMilliseconds * 0.5),
            AnimationPerformanceLevel.Medium => TimeSpan.FromMilliseconds(baseDuration.TotalMilliseconds * 0.75),
            AnimationPerformanceLevel.High => baseDuration,
            _ => baseDuration
        };
    }

    public IEasingFunction? GetRecommendedEasing(AnimationType animationType)
    {
        return _currentPerformanceLevel switch
        {
            AnimationPerformanceLevel.Disabled => null,
            AnimationPerformanceLevel.Low => null,
            AnimationPerformanceLevel.Medium => animationType switch
            {
                AnimationType.FadeIn or AnimationType.FadeOut => new QuadraticEase(),
                AnimationType.SlideIn or AnimationType.SlideOut => new CubicEase(),
                _ => new QuadraticEase()
            },
            AnimationPerformanceLevel.High => animationType switch
            {
                AnimationType.FadeIn or AnimationType.FadeOut => new QuadraticEase(),
                AnimationType.SlideIn or AnimationType.SlideOut => new CubicEase(),
                AnimationType.Scale => new BackEase { Amplitude = 0.3 },
                AnimationType.Bounce => new BounceEase { Bounces = 2, Bounciness = 2 },
                AnimationType.Elastic => new ElasticEase { Oscillations = 3, Springiness = 5 },
                _ => new QuadraticEase()
            },
            _ => null
        };
    }

    public bool ShouldSkipAnimation(AnimationConfig config)
    {
        if (!_animationsEnabled || _currentPerformanceLevel == AnimationPerformanceLevel.Disabled)
            return true;

        // 根据优先级和性能级别决定是否跳过
        return _currentPerformanceLevel switch
        {
            AnimationPerformanceLevel.Low => config.Priority < AnimationPriority.High,
            AnimationPerformanceLevel.Medium => config.Priority < AnimationPriority.Normal,
            AnimationPerformanceLevel.High => false,
            _ => true
        };
    }

    public AnimationPerformanceStats GetPerformanceStats()
    {
        lock (_lockObject)
        {
            return new AnimationPerformanceStats
            {
                TotalAnimations = _stats.TotalAnimations,
                SkippedAnimations = _stats.SkippedAnimations,
                CompletedAnimations = _stats.CompletedAnimations,
                AverageFrameRate = _stats.AverageFrameRate,
                TotalAnimationTime = _stats.TotalAnimationTime,
                StartTime = _stats.StartTime,
                LastUpdate = _stats.LastUpdate
            };
        }
    }

    public void ResetPerformanceStats()
    {
        lock (_lockObject)
        {
            _stats.TotalAnimations = 0;
            _stats.SkippedAnimations = 0;
            _stats.CompletedAnimations = 0;
            _stats.AverageFrameRate = 0;
            _stats.TotalAnimationTime = TimeSpan.Zero;
            _stats.StartTime = DateTime.Now;
            _stats.LastUpdate = DateTime.Now;
        }
        
        _logger.LogInformation("动画性能统计已重置");
    }

    private AnimationTimeline? CreateAnimationForType(AnimationConfig config)
    {
        return config.Type switch
        {
            AnimationType.FadeIn or AnimationType.FadeOut => new DoubleAnimation
            {
                From = config.From,
                To = config.To,
                Duration = config.Duration ?? GetRecommendedDuration(config.Type),
                EasingFunction = config.Easing ?? GetRecommendedEasing(config.Type),
                AutoReverse = config.AutoReverse,
                BeginTime = config.Delay
            },
            AnimationType.SlideIn or AnimationType.SlideOut => new DoubleAnimation
            {
                From = config.From,
                To = config.To,
                Duration = config.Duration ?? GetRecommendedDuration(config.Type),
                EasingFunction = config.Easing ?? GetRecommendedEasing(config.Type),
                AutoReverse = config.AutoReverse,
                BeginTime = config.Delay
            },
            AnimationType.Scale => new DoubleAnimation
            {
                From = config.From,
                To = config.To,
                Duration = config.Duration ?? GetRecommendedDuration(config.Type),
                EasingFunction = config.Easing ?? GetRecommendedEasing(config.Type),
                AutoReverse = config.AutoReverse,
                BeginTime = config.Delay
            },
            _ => new DoubleAnimation
            {
                From = config.From,
                To = config.To,
                Duration = config.Duration ?? GetRecommendedDuration(config.Type),
                EasingFunction = config.Easing ?? GetRecommendedEasing(config.Type),
                AutoReverse = config.AutoReverse,
                BeginTime = config.Delay
            }
        };
    }

    private void AdjustAnimationForPerformance(AnimationTimeline animation, AnimationConfig config)
    {
        if (animation is DoubleAnimation doubleAnimation)
        {
            // 根据性能级别调整动画属性
            switch (_currentPerformanceLevel)
            {
                case AnimationPerformanceLevel.Low:
                    doubleAnimation.Duration = TimeSpan.FromMilliseconds(
                        doubleAnimation.Duration.TimeSpan.TotalMilliseconds * 0.5);
                    doubleAnimation.EasingFunction = null;
                    break;
                case AnimationPerformanceLevel.Medium:
                    doubleAnimation.Duration = TimeSpan.FromMilliseconds(
                        doubleAnimation.Duration.TimeSpan.TotalMilliseconds * 0.75);
                    break;
            }
        }
    }

    private AnimationPerformanceLevel DeterminePerformanceLevel(SystemPerformanceInfo performanceInfo)
    {
        // 如果是电池供电且低功耗模式，降低性能级别
        if (performanceInfo.IsBatteryPowered && performanceInfo.IsLowPowerMode)
            return AnimationPerformanceLevel.Low;

        // 根据CPU和内存使用情况确定性能级别
        var cpuThreshold = performanceInfo.CpuUsage;
        var memoryThreshold = performanceInfo.MemoryUsage;

        if (cpuThreshold > 80 || memoryThreshold > 85)
            return AnimationPerformanceLevel.Disabled;
        
        if (cpuThreshold > 60 || memoryThreshold > 70)
            return AnimationPerformanceLevel.Low;
        
        if (cpuThreshold > 40 || memoryThreshold > 50)
            return AnimationPerformanceLevel.Medium;
        
        return AnimationPerformanceLevel.High;
    }

    private string BuildPerformanceChangeReason(SystemPerformanceInfo performanceInfo)
    {
        var reasons = new List<string>();
        
        if (performanceInfo.CpuUsage > 60)
            reasons.Add($"CPU使用率高 ({performanceInfo.CpuUsage:F1}%)");
        
        if (performanceInfo.MemoryUsage > 70)
            reasons.Add($"内存使用率高 ({performanceInfo.MemoryUsage:F1}%)");
        
        if (performanceInfo.IsLowPowerMode)
            reasons.Add("低功耗模式");
        
        if (performanceInfo.IsBatteryPowered)
            reasons.Add("电池供电");

        return reasons.Count > 0 ? string.Join(", ", reasons) : "性能良好";
    }

    private void OnPerformanceAlert(object? sender, PerformanceEventArgs e)
    {
        if (e.Alert.Type == "MemoryWarning" && e.Alert.Severity >= AlertSeverity.Warning)
        {
            // 内存警告时降低动画性能级别
            var currentLevel = _currentPerformanceLevel;
            var newLevel = currentLevel switch
            {
                AnimationPerformanceLevel.High => AnimationPerformanceLevel.Medium,
                AnimationPerformanceLevel.Medium => AnimationPerformanceLevel.Low,
                AnimationPerformanceLevel.Low => AnimationPerformanceLevel.Disabled,
                _ => currentLevel
            };

            if (newLevel != currentLevel)
            {
                _currentPerformanceLevel = newLevel;
                _logger.LogWarning("由于内存警告，动画性能级别从 {OldLevel} 降低到 {NewLevel}", 
                    currentLevel, newLevel);
                
                PerformanceLevelChanged?.Invoke(this, 
                    new PerformanceLevelChangedEventArgs(currentLevel, newLevel, "内存警告"));
            }
        }
    }
}
