namespace DocxToMarkdownConverter.Services.Formula.Models;

/// <summary>
/// 公式处理选项
/// </summary>
public class FormulaProcessingOptions
{
    /// <summary>
    /// 输出格式
    /// </summary>
    public FormulaOutputFormat OutputFormat { get; set; } = FormulaOutputFormat.LaTeX;
    
    /// <summary>
    /// 是否处理内联公式
    /// </summary>
    public bool ProcessInlineFormulas { get; set; } = true;
    
    /// <summary>
    /// 是否处理显示公式
    /// </summary>
    public bool ProcessDisplayFormulas { get; set; } = true;
    
    /// <summary>
    /// 是否启用后处理
    /// </summary>
    public bool EnablePostProcessing { get; set; } = true;
    
    /// <summary>
    /// 是否启用错误恢复
    /// </summary>
    public bool EnableErrorRecovery { get; set; } = true;
    
    /// <summary>
    /// 最大复杂度限制
    /// </summary>
    public int MaxComplexity { get; set; } = 1000;
    
    /// <summary>
    /// 处理超时时间
    /// </summary>
    public TimeSpan Timeout { get; set; } = TimeSpan.FromSeconds(30);
    
    /// <summary>
    /// 处理模式
    /// </summary>
    public FormulaProcessingMode ProcessingMode { get; set; } = FormulaProcessingMode.Full;
    
    /// <summary>
    /// 显示模式
    /// </summary>
    public FormulaDisplayMode DisplayMode { get; set; } = FormulaDisplayMode.Auto;
    
    /// <summary>
    /// 是否启用缓存
    /// </summary>
    public bool EnableCaching { get; set; } = true;
    
    /// <summary>
    /// 缓存过期时间
    /// </summary>
    public TimeSpan CacheExpiration { get; set; } = TimeSpan.FromMinutes(30);
    
    /// <summary>
    /// 是否启用并行处理
    /// </summary>
    public bool EnableParallelProcessing { get; set; } = true;
    
    /// <summary>
    /// 最大并行度
    /// </summary>
    public int MaxDegreeOfParallelism { get; set; } = Environment.ProcessorCount;
    
    /// <summary>
    /// 自定义符号映射
    /// </summary>
    public Dictionary<string, string> CustomSymbolMap { get; set; } = new();
    
    /// <summary>
    /// 自定义函数映射
    /// </summary>
    public Dictionary<string, string> CustomFunctionMap { get; set; } = new();
    
    /// <summary>
    /// 是否保留原始结构信息
    /// </summary>
    public bool PreserveStructureInfo { get; set; } = false;
    
    /// <summary>
    /// 是否生成调试信息
    /// </summary>
    public bool GenerateDebugInfo { get; set; } = false;
}

/// <summary>
/// 公式格式化选项
/// </summary>
public class FormulaFormattingOptions
{
    /// <summary>
    /// 是否标准化符号
    /// </summary>
    public bool NormalizeSymbols { get; set; } = true;
    
    /// <summary>
    /// 是否修复间距
    /// </summary>
    public bool FixSpacing { get; set; } = true;
    
    /// <summary>
    /// 是否优化输出
    /// </summary>
    public bool OptimizeOutput { get; set; } = true;
    
    /// <summary>
    /// 是否简化复杂结构
    /// </summary>
    public bool SimplifyComplexStructures { get; set; } = false;
    
    /// <summary>
    /// 最大内联公式长度
    /// </summary>
    public int MaxInlineFormulaLength { get; set; } = 50;
    
    /// <summary>
    /// 是否在运算符周围添加空格
    /// </summary>
    public bool AddSpacingAroundOperators { get; set; } = true;
    
    /// <summary>
    /// 是否使用Unicode符号
    /// </summary>
    public bool UseUnicodeSymbols { get; set; } = false;
    
    /// <summary>
    /// 是否保留注释
    /// </summary>
    public bool PreserveComments { get; set; } = true;
    
    /// <summary>
    /// 自定义符号映射
    /// </summary>
    public Dictionary<string, string> CustomSymbolMap { get; set; } = new();
    
    /// <summary>
    /// 换行策略
    /// </summary>
    public LineBreakStrategy LineBreakStrategy { get; set; } = LineBreakStrategy.Auto;
    
    /// <summary>
    /// 最大行长度
    /// </summary>
    public int MaxLineLength { get; set; } = 80;
    
    /// <summary>
    /// 缩进字符串
    /// </summary>
    public string IndentString { get; set; } = "  ";
    
    /// <summary>
    /// 是否美化输出
    /// </summary>
    public bool BeautifyOutput { get; set; } = true;
}

/// <summary>
/// 公式检测选项
/// </summary>
public class FormulaDetectionOptions
{
    /// <summary>
    /// 是否检测内联公式
    /// </summary>
    public bool DetectInlineFormulas { get; set; } = true;
    
    /// <summary>
    /// 是否检测显示公式
    /// </summary>
    public bool DetectDisplayFormulas { get; set; } = true;
    
    /// <summary>
    /// 是否检测分段函数
    /// </summary>
    public bool DetectPiecewiseFunctions { get; set; } = true;
    
    /// <summary>
    /// 是否检测矩阵
    /// </summary>
    public bool DetectMatrices { get; set; } = true;
    
    /// <summary>
    /// 最小复杂度阈值
    /// </summary>
    public FormulaComplexity MinComplexityThreshold { get; set; } = FormulaComplexity.Simple;
    
    /// <summary>
    /// 最大检测深度
    /// </summary>
    public int MaxDetectionDepth { get; set; } = 10;
    
    /// <summary>
    /// 检测超时时间
    /// </summary>
    public TimeSpan DetectionTimeout { get; set; } = TimeSpan.FromSeconds(5);
}

/// <summary>
/// 公式解析选项
/// </summary>
public class FormulaParsingOptions
{
    /// <summary>
    /// 是否严格解析
    /// </summary>
    public bool StrictParsing { get; set; } = false;
    
    /// <summary>
    /// 是否允许部分解析
    /// </summary>
    public bool AllowPartialParsing { get; set; } = true;
    
    /// <summary>
    /// 最大解析深度
    /// </summary>
    public int MaxParsingDepth { get; set; } = 20;
    
    /// <summary>
    /// 解析超时时间
    /// </summary>
    public TimeSpan ParsingTimeout { get; set; } = TimeSpan.FromSeconds(10);
    
    /// <summary>
    /// 是否保留空白
    /// </summary>
    public bool PreserveWhitespace { get; set; } = false;
    
    /// <summary>
    /// 是否验证结构
    /// </summary>
    public bool ValidateStructure { get; set; } = true;
}

/// <summary>
/// 公式转换选项
/// </summary>
public class FormulaConversionOptions
{
    /// <summary>
    /// 目标格式
    /// </summary>
    public FormulaOutputFormat TargetFormat { get; set; } = FormulaOutputFormat.LaTeX;
    
    /// <summary>
    /// 是否压缩输出
    /// </summary>
    public bool CompressOutput { get; set; } = false;
    
    /// <summary>
    /// 是否验证输出
    /// </summary>
    public bool ValidateOutput { get; set; } = true;
    
    /// <summary>
    /// 转换超时时间
    /// </summary>
    public TimeSpan ConversionTimeout { get; set; } = TimeSpan.FromSeconds(15);
    
    /// <summary>
    /// 是否使用兼容模式
    /// </summary>
    public bool UseCompatibilityMode { get; set; } = true;
    
    /// <summary>
    /// 目标平台
    /// </summary>
    public string TargetPlatform { get; set; } = "generic";
}

/// <summary>
/// 换行策略枚举
/// </summary>
public enum LineBreakStrategy
{
    /// <summary>
    /// 自动换行
    /// </summary>
    Auto = 0,
    
    /// <summary>
    /// 不换行
    /// </summary>
    None = 1,
    
    /// <summary>
    /// 在运算符处换行
    /// </summary>
    AtOperators = 2,
    
    /// <summary>
    /// 在逗号处换行
    /// </summary>
    AtCommas = 3,
    
    /// <summary>
    /// 强制换行
    /// </summary>
    Force = 4
}

/// <summary>
/// 公式配置验证器
/// </summary>
public static class FormulaOptionsValidator
{
    /// <summary>
    /// 验证处理选项
    /// </summary>
    /// <param name="options">处理选项</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateProcessingOptions(FormulaProcessingOptions options)
    {
        var result = new ValidationResult();
        
        if (options.MaxComplexity <= 0)
        {
            result.AddError("MaxComplexity must be greater than 0");
        }
        
        if (options.Timeout <= TimeSpan.Zero)
        {
            result.AddError("Timeout must be greater than zero");
        }
        
        if (options.MaxDegreeOfParallelism <= 0)
        {
            result.AddError("MaxDegreeOfParallelism must be greater than 0");
        }
        
        return result;
    }
    
    /// <summary>
    /// 验证格式化选项
    /// </summary>
    /// <param name="options">格式化选项</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateFormattingOptions(FormulaFormattingOptions options)
    {
        var result = new ValidationResult();
        
        if (options.MaxInlineFormulaLength <= 0)
        {
            result.AddError("MaxInlineFormulaLength must be greater than 0");
        }
        
        if (options.MaxLineLength <= 0)
        {
            result.AddError("MaxLineLength must be greater than 0");
        }
        
        if (string.IsNullOrEmpty(options.IndentString))
        {
            result.AddWarning("IndentString is empty, using default");
            options.IndentString = "  ";
        }
        
        return result;
    }
}

/// <summary>
/// 验证结果
/// </summary>
public class ValidationResult
{
    /// <summary>
    /// 是否有效
    /// </summary>
    public bool IsValid => !Errors.Any();
    
    /// <summary>
    /// 错误列表
    /// </summary>
    public List<string> Errors { get; } = new();
    
    /// <summary>
    /// 警告列表
    /// </summary>
    public List<string> Warnings { get; } = new();
    
    /// <summary>
    /// 添加错误
    /// </summary>
    /// <param name="error">错误消息</param>
    public void AddError(string error)
    {
        Errors.Add(error);
    }
    
    /// <summary>
    /// 添加警告
    /// </summary>
    /// <param name="warning">警告消息</param>
    public void AddWarning(string warning)
    {
        Warnings.Add(warning);
    }
}
