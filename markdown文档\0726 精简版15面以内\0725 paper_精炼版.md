3.4 阶段二：冲突感知自适应遗传算法

为对剩余常规课程进行全局优化，本阶段采用冲突感知自适应遗传算法，通过二元组染色体编码、多目标适应度函数、冲突感知进化算子、自适应参数调节四项关键改进，实现教师、班级、教室三维资源的协调优化。

3.4.1 染色体编码与初始种群生成

设计二元组染色体编码方案，将所有课程的排课决策表示为n×2矩阵，每行基因[(r1 , t1),(r2 , t2),…,(rn , tn)]对应课程的"教室-时间槽"分配。合班授课课程各班级共享同一组(ri , ti)，保证时空分配一致性。

初始种群生成采用启发式评分策略：

（1）时间槽选择基于多维度综合评估。时间槽总得分*Score(s)*的表达式如下：

$S_{core}(s) = P_{balance}(s)∙P_{reuse}(s)∙P_{period}(s)$

（15）

其中，P*balance(s)为天数均衡性得分，P**reuse(s)为时间槽复用奖励得分，P**period(s)*为时段偏好得分。

（2）教室选择采用多维度综合评分策略。教室综合得分*Score(r)*表达式如下：

$$\text{Score}(r) = f_{\text{reuse}}(r) \times \frac{\delta_1}{1 + \delta_2 N(r)} \times \begin{cases}
\delta_3 & \text{if } C_{\min} \leq \text{cap}(r) \leq C_{\text{ideal}} \\
\delta_3 \times \frac{C_{\text{ideal}}}{\text{cap}(r)} & \text{if } \text{cap}(r) > C_{\text{ideal}} \\
0 & \text{otherwise}
\end{cases}$$
（16）

其中，f*reuse(r)*为教室复用奖励得分，N(r)为教室r的历史使用次数，δ1为均衡性基准系数，δ2为负载调节参数，δ3为容量匹配基准分。

3.4.2 多目标适应度函数

为实现冲突最小化与课程分布均衡化的双重优化目标，构建多目标适应度函数。综合适应度函数F**total表达式如下：

$$F_{\text{total}} = \begin{cases}
\theta + \rho \sum_{x \in \{\text{teacher}, \text{class}, \text{classroom}\}} \omega_x \exp\left(-\tau \frac{\sigma_x}{\mu_x}\right) & \text{if } N_{\text{conflict}} = 0 \\
\frac{\theta}{1 + \varepsilon N_{\text{conflict}}} \left(1 + \iota \sum_{x \in \{\text{teacher}, \text{class}, \text{classroom}\}} \omega_x \exp\left(-\tau \frac{\sigma_x}{\mu_x}\right)\right) & \text{if } N_{\text{conflict}} > 0
\end{cases}$$

（19）

其中，Nconflict为冲突数量，σx和µx分别为实体x（教室、班级或教室）每日课程数量的标准值和平均值，θ为无冲突基准分，ρ为均匀性奖励系数，ε为冲突惩罚系数，ι为均匀性权重，τ为均匀性敏感度参数，ωteacher、ωclass、ωclassroom为各实体权重系数。

3.4.3 进化操作优化

构建自适应进化操作框架，通过精英保留选择、动态交叉变异和冲突感知修复实现高效进化搜索。

（1）选择机制：采用精英保留锦标赛选择。保留前25%的最优个体直接进入下一代，锦标赛规模τ根据种群规模N**p动态调整：

$$\tau = \max\left(\tau_{\min}, \min\left(\left\lfloor\frac{N_p}{\beta_t}\right\rfloor, \tau_{\max}\right)\right)$$

（24）

其中，τmin、τmax分别为锦标赛规模的下界和上界，βt为规模调节因子。

（2）交叉操作：基于染色体有效基因位长度Lg，动态确定交叉点数n**c：

$$n_c = \begin{cases}
1 & \text{if } L_g \leq \lambda_1 \\
\min\left(\left\lfloor\frac{L_g}{\lambda_2}\right\rfloor, \xi_1\right) & \text{if } \lambda_1 < L_g \leq \lambda_3 \\
\min\left(\left\lfloor\frac{L_g}{\lambda_4}\right\rfloor, \xi_2\right) & \text{if } L_g > \lambda_3
\end{cases}$$

（25）

其中，λi为长度阈值参数，ξi为交叉点数上界。

（3）变异策略：引入冲突感知变异机制，优先修复高冲突基因位。变异基数nm与容量弹性因子µ的表达式为：

$$\begin{aligned}
n_m &= \begin{cases}
\min(|C_p|, \max(\eta_{\min}, \min(\eta_{\max}, \lfloor|C_p|/\gamma\rfloor))) & \text{if } C_p \neq \emptyset \text{ and } \rho < \theta \\
\max(\eta_{\min}, \min(\eta_{\max}, \lfloor|V|/\delta\rfloor)) & \text{otherwise}
\end{cases} \\
\mu &= \mu_0 + \kappa \cdot (\phi - p)
\end{aligned}$$

（27）

其中，Cp为冲突基因位集合，|V|为有效基因位总数，ρ为随机数，θ为概率阈值，ηmin、ηmax为变异数量边界，γ、δ为调节参数，µ0为基础弹性系数，κ为调节强度，φ为参考值，p为当前优先级。

3.4.4 遗传主函数优化策略

构建冲突感知的自适应参数调整框架，集成并行计算与早期终止机制：

（1）参数自适应调整机制：基于种群冲突状态动态调整进化参数。交叉率p**c、变异率p**m与冲突率r**conf的联合调节表达式为：

$$\begin{aligned}
p_c &= \min(\alpha_{\max}, \alpha_{\text{base}} \cdot (1 + \beta \cdot r_{\text{conf}})) \\
p_m &= \max(\gamma_{\min}, \gamma_{\text{init}} \cdot (1 + r_{\text{conf}})) \\
r_{\text{conf}} &= 1 - \frac{\bar{f}}{\sigma_f}
\end{aligned}$$

（30）

其中，αmax、γmin分别为交叉率上界和变异率下界，αbase、γinit为基础参数，β为调节强度，$\bar{f}$为种群平均适应度，σf为适应度标准化因子。

（2）并行计算与终止策略：采用分块并行处理机制优化适应度评估。分块规模Χ与终止条件Ω的表达式为：

$$\begin{aligned}
\chi &= \max\left(\chi_{\min}, \left\lfloor\frac{N_p}{W_{\max}}\right\rfloor\right) \\
\Omega &= (G_{\text{stag}} \geq \psi) \lor (\bar{f}_{\text{best}} \geq \varphi)
\end{aligned}$$

其中，Χmin为最小分块大小，Wmax为最大工作线程数，Gstag为连续无改进代数，ψ为停滞阈值，$\bar{f}_{\text{best}}$为最优适应度，φ为收敛阈值。

本阶段通过冲突感知自适应遗传算法实现全局优化。染色体编码与初始种群生成确保解的多样性和质量，多目标适应度函数实现冲突最小化与均衡性优化的统一，进化操作优化和参数调节策略显著提升算法的收敛效率和解质量。