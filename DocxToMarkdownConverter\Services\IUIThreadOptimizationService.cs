using System;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Threading;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// UI线程优化服务接口
/// </summary>
public interface IUIThreadOptimizationService
{
    /// <summary>
    /// 在后台线程执行任务，不阻塞UI
    /// </summary>
    Task<T> ExecuteOnBackgroundThreadAsync<T>(Func<Task<T>> task, CancellationToken cancellationToken = default);

    /// <summary>
    /// 在后台线程执行任务，不阻塞UI
    /// </summary>
    Task ExecuteOnBackgroundThreadAsync(Func<Task> task, CancellationToken cancellationToken = default);

    /// <summary>
    /// 在UI线程执行任务
    /// </summary>
    Task<T> ExecuteOnUIThreadAsync<T>(Func<T> task);

    /// <summary>
    /// 在UI线程执行任务
    /// </summary>
    Task ExecuteOnUIThreadAsync(Action task);

    /// <summary>
    /// 批量执行任务，自动管理UI响应性
    /// </summary>
    Task ExecuteBatchAsync<T>(IEnumerable<T> items, Func<T, Task> processor, 
        IProgress<BatchProgress>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 长时间运行的任务，定期让出UI线程
    /// </summary>
    Task ExecuteLongRunningTaskAsync(Func<IProgress<double>, CancellationToken, Task> task,
        IProgress<double>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查UI线程响应性
    /// </summary>
    Task<UIResponsivenessInfo> CheckUIResponsiveness();

    /// <summary>
    /// 设置UI更新频率限制
    /// </summary>
    void SetUIUpdateThrottle(TimeSpan minInterval);

    /// <summary>
    /// 获取推荐的批处理大小
    /// </summary>
    int GetRecommendedBatchSize();

    /// <summary>
    /// UI响应性警告事件
    /// </summary>
    event EventHandler<UIResponsivenessEventArgs>? UIResponsivenessWarning;
}

/// <summary>
/// 批处理进度
/// </summary>
public class BatchProgress
{
    public int ProcessedItems { get; set; }
    public int TotalItems { get; set; }
    public double ProgressPercentage => TotalItems > 0 ? (double)ProcessedItems / TotalItems * 100 : 0;
    public string CurrentItem { get; set; } = string.Empty;
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedRemainingTime { get; set; }
}

/// <summary>
/// UI响应性信息
/// </summary>
public class UIResponsivenessInfo
{
    public bool IsResponsive { get; set; }
    public TimeSpan LastUIUpdateTime { get; set; }
    public double UIThreadUsagePercentage { get; set; }
    public int PendingUIOperations { get; set; }
    public DispatcherPriority CurrentPriority { get; set; }
    public DateTime Timestamp { get; set; }
    public string Status { get; set; } = string.Empty;
}

/// <summary>
/// UI响应性事件参数
/// </summary>
public class UIResponsivenessEventArgs : EventArgs
{
    public UIResponsivenessInfo ResponsivenessInfo { get; }
    public string Warning { get; }

    public UIResponsivenessEventArgs(UIResponsivenessInfo info, string warning)
    {
        ResponsivenessInfo = info;
        Warning = warning;
    }
}

/// <summary>
/// 任务优先级
/// </summary>
public enum TaskPriority
{
    Low,
    Normal,
    High,
    Critical
}

/// <summary>
/// UI更新策略
/// </summary>
public enum UIUpdateStrategy
{
    /// <summary>
    /// 立即更新
    /// </summary>
    Immediate,
    
    /// <summary>
    /// 节流更新
    /// </summary>
    Throttled,
    
    /// <summary>
    /// 批量更新
    /// </summary>
    Batched,
    
    /// <summary>
    /// 自适应更新
    /// </summary>
    Adaptive
}
