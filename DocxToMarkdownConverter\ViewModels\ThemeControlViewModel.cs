using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.ViewModels;

public class ThemeControlViewModel : INotifyPropertyChanged
{
    private readonly IThemeManager _themeManager;

    public ThemeControlViewModel(IThemeManager themeManager)
    {
        _themeManager = themeManager;

        // Initialize commands with safer async handling
        ToggleThemeCommand = new RelayCommand(() => SafeExecuteAsync(ToggleThemeAsync));
        ResetThemeCommand = new RelayCommand(() => SafeExecuteAsync(ResetThemeAsync));
        SetPrimaryColorCommand = new RelayCommand<string>(SetPrimaryColor);

        // Subscribe to theme changes
        _themeManager.ThemeChanged += OnThemeChanged;
        _themeManager.AccentColorChanged += OnAccentColorChanged;

        // Initialize properties with timeout protection
        try
        {
            var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
            Task.Run(() =>
            {
                try
                {
                    if (!cts.Token.IsCancellationRequested)
                    {
                        System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                        {
                            try
                            {
                                UpdateThemeProperties();
                            }
                            catch (Exception ex)
                            {
                                System.Diagnostics.Debug.WriteLine($"Failed to update theme properties on UI thread: {ex.Message}");
                            }
                        }, System.Windows.Threading.DispatcherPriority.Background);
                    }
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"Failed to schedule theme properties update: {ex.Message}");
                }
            }, cts.Token);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to initialize theme properties: {ex.Message}");
        }
    }

    private void SafeExecuteAsync(Func<Task> asyncAction)
    {
        System.Windows.Application.Current.Dispatcher.BeginInvoke(new Action(async () =>
        {
            try
            {
                await asyncAction();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Theme operation failed: {ex.Message}");
            }
        }));
    }

    #region Properties

    public bool IsLightTheme
    {
        get => _themeManager.CurrentTheme == AppTheme.Light;
        set
        {
            if (value && _themeManager.CurrentTheme != AppTheme.Light)
            {
                SafeExecuteAsync(() => _themeManager.ApplyThemeAsync(AppTheme.Light));
            }
        }
    }

    public bool IsDarkTheme
    {
        get => _themeManager.CurrentTheme == AppTheme.Dark;
        set
        {
            if (value && _themeManager.CurrentTheme != AppTheme.Dark)
            {
                SafeExecuteAsync(() => _themeManager.ApplyThemeAsync(AppTheme.Dark));
            }
        }
    }

    public bool IsAutoTheme
    {
        get => _themeManager.Settings.CurrentTheme == AppTheme.Auto;
        set
        {
            if (value && _themeManager.Settings.CurrentTheme != AppTheme.Auto)
            {
                SafeExecuteAsync(() => _themeManager.ApplyThemeAsync(AppTheme.Auto));
            }
        }
    }

    public bool IsSystemThemeSupported => _themeManager.IsSystemThemeSupported;

    public bool EnableThemeTransitions
    {
        get => _themeManager.Settings.EnableThemeTransitions;
        set
        {
            if (_themeManager.Settings.EnableThemeTransitions != value)
            {
                _themeManager.Settings.EnableThemeTransitions = value;
                OnPropertyChanged();
            }
        }
    }

    public bool FollowSystemTheme
    {
        get => _themeManager.Settings.FollowSystemTheme;
        set
        {
            if (_themeManager.Settings.FollowSystemTheme != value)
            {
                _themeManager.Settings.FollowSystemTheme = value;
                OnPropertyChanged();

                if (value)
                {
                    _themeManager.StartSystemThemeMonitoring();
                    _ = Task.Run(async () => await _themeManager.ApplyThemeAsync(AppTheme.Auto));
                }
                else
                {
                    _themeManager.StopSystemThemeMonitoring();
                }
            }
        }
    }

    public bool EnableAnimations
    {
        get => _themeManager.Settings.EnableAnimations;
        set
        {
            if (_themeManager.Settings.EnableAnimations != value)
            {
                _themeManager.Settings.EnableAnimations = value;
                OnPropertyChanged();
            }
        }
    }



    public string CurrentThemeDisplayName => _themeManager.CurrentTheme switch
    {
        AppTheme.Light => "浅色主题",
        AppTheme.Dark => "深色主题",
        AppTheme.Auto => "自动主题",
        _ => "未知主题"
    };

    #endregion

    #region Commands

    public ICommand ToggleThemeCommand { get; }
    public ICommand ResetThemeCommand { get; }
    public ICommand SetPrimaryColorCommand { get; }

    #endregion

    #region Command Implementations

    private async Task ToggleThemeAsync()
    {
        try
        {
            await _themeManager.ToggleThemeAsync();
        }
        catch (Exception ex)
        {
            // Handle error silently for now
            System.Diagnostics.Debug.WriteLine($"Failed to toggle theme: {ex.Message}");
        }
    }

    private async Task ResetThemeAsync()
    {
        try
        {
            // Reset to default dark theme
            await _themeManager.ApplyThemeAsync(AppTheme.Dark);
            
            // Reset settings to defaults
            _themeManager.Settings.EnableThemeTransitions = true;
            _themeManager.Settings.EnableAnimations = true;
            _themeManager.Settings.FollowSystemTheme = false;
            _themeManager.SetAccentColor("#0078D4"); // Blue
            
            UpdateThemeProperties();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to reset theme settings: {ex.Message}");
        }
    }

    private void SetPrimaryColor(string? colorName)
    {
        if (string.IsNullOrEmpty(colorName)) return;

        try
        {
            var colorHex = colorName switch
            {
                "DeepPurple" => "#673AB7",
                "Blue" => "#2196F3",
                "Teal" => "#009688",
                "Green" => "#4CAF50",
                "Orange" => "#FF9800",
                "Red" => "#F44336",
                _ => "#673AB7"
            };

            // Execute on UI thread
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                _themeManager.SetAccentColor(colorHex);
            }
            else
            {
                System.Windows.Application.Current.Dispatcher.BeginInvoke(new Action(() =>
                {
                    _themeManager.SetAccentColor(colorHex);
                }));
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to set primary color to {colorName}: {ex.Message}");
        }
    }

    #endregion

    #region Event Handlers

    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        UpdateThemeProperties();
    }

    private void OnAccentColorChanged(object? sender, AccentColorChangedEventArgs e)
    {
        // Handle accent color change
    }

    private void UpdateThemeProperties()
    {
        OnPropertyChanged(nameof(IsLightTheme));
        OnPropertyChanged(nameof(IsDarkTheme));
        OnPropertyChanged(nameof(IsAutoTheme));
        OnPropertyChanged(nameof(CurrentThemeDisplayName));
        OnPropertyChanged(nameof(EnableThemeTransitions));
        OnPropertyChanged(nameof(FollowSystemTheme));
        OnPropertyChanged(nameof(EnableAnimations));
    }

    #endregion

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion
}