<UserControl x:Class="DocxToMarkdownConverter.Controls.PerformanceSettingsControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:DocxToMarkdownConverter.Models"
             xmlns:converters="clr-namespace:DocxToMarkdownConverter.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="500">

    <UserControl.Resources>
        <!-- Converters -->
        <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
        <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
    </UserControl.Resources>

    <StackPanel Margin="16" Style="{StaticResource OptimizedSettingsStackPanel}"
                ScrollViewer.CanContentScroll="False"
                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                ScrollViewer.VerticalScrollBarVisibility="Disabled">


            <!-- 性能级别选择 -->
            <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                <StackPanel>
                    <TextBlock Text="性能级别"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               Foreground="{DynamicResource AppTextPrimaryBrush}"
                               Margin="0,0,0,12"/>

                    <TextBlock Text="选择适合您设备的性能级别，或让系统自动检测最佳设置。"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource AppTextSecondaryBrush}"
                               TextWrapping="Wrap"
                               Margin="0,0,0,16"/>

                    <StackPanel>
                        <RadioButton Content="自动检测（推荐）"
                                     IsChecked="{Binding PerformanceSettings.PerformanceLevel,
                                                 Converter={StaticResource EnumToBooleanConverter},
                                                 ConverterParameter={x:Static models:PerformanceLevel.Auto}}"
                                     Style="{StaticResource MaterialDesignRadioButton}"
                                     Foreground="{DynamicResource AppTextPrimaryBrush}"
                                     Margin="0,0,0,8"/>

                        <RadioButton Content="高性能 - 启用所有动画和效果"
                                     IsChecked="{Binding PerformanceSettings.PerformanceLevel,
                                                 Converter={StaticResource EnumToBooleanConverter},
                                                 ConverterParameter={x:Static models:PerformanceLevel.High}}"
                                     Style="{StaticResource MaterialDesignRadioButton}"
                                     Foreground="{DynamicResource AppTextPrimaryBrush}"
                                     Margin="0,0,0,8"/>

                        <RadioButton Content="中等性能 - 基本动画，无复杂效果"
                                     IsChecked="{Binding PerformanceSettings.PerformanceLevel,
                                                 Converter={StaticResource EnumToBooleanConverter},
                                                 ConverterParameter={x:Static models:PerformanceLevel.Medium}}"
                                     Style="{StaticResource MaterialDesignRadioButton}"
                                     Foreground="{DynamicResource AppTextPrimaryBrush}"
                                     Margin="0,0,0,8"/>

                        <RadioButton Content="低性能 - 禁用动画以提升响应速度"
                                     IsChecked="{Binding PerformanceSettings.PerformanceLevel,
                                                 Converter={StaticResource EnumToBooleanConverter},
                                                 ConverterParameter={x:Static models:PerformanceLevel.Low}}"
                                     Style="{StaticResource MaterialDesignRadioButton}"
                                     Foreground="{DynamicResource AppTextPrimaryBrush}"/>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- 动画设置 -->
            <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                <StackPanel>
                    <TextBlock Text="动画设置"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               Foreground="{DynamicResource AppTextPrimaryBrush}"
                               Margin="0,0,0,12"/>

                    <CheckBox Content="启用动画"
                              IsChecked="{Binding PerformanceSettings.EnableAnimations}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              Margin="0,0,0,12"/>

                    <Grid Margin="0,0,0,12">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0"
                                   Text="动画速度:"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource AppTextPrimaryBrush}"
                                   Margin="0,0,12,0"/>

                        <Slider Grid.Column="1"
                                Value="{Binding PerformanceSettings.AnimationSpeed}"
                                Minimum="0.1"
                                Maximum="3.0"
                                TickFrequency="0.1"
                                IsSnapToTickEnabled="True"
                                Style="{StaticResource MaterialDesignSlider}"
                                IsEnabled="{Binding PerformanceSettings.EnableAnimations}"
                                ScrollViewer.CanContentScroll="False"
                                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                ScrollViewer.VerticalScrollBarVisibility="Disabled"/>

                        <TextBlock Grid.Column="2"
                                   Text="{Binding PerformanceSettings.AnimationSpeed, StringFormat={}{0:F1}x}"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource AppTextSecondaryBrush}"
                                   MinWidth="40"
                                   Margin="12,0,0,0"/>
                    </Grid>

                    <CheckBox Content="页面切换动画"
                              IsChecked="{Binding PerformanceSettings.EnablePageTransitions}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              IsEnabled="{Binding PerformanceSettings.EnableAnimations}"
                              Margin="0,0,0,8"/>

                    <CheckBox Content="按钮悬停动画"
                              IsChecked="{Binding PerformanceSettings.EnableButtonAnimations}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              IsEnabled="{Binding PerformanceSettings.EnableAnimations}"
                              Margin="0,0,0,8"/>

                    <CheckBox Content="进度条动画"
                              IsChecked="{Binding PerformanceSettings.EnableProgressAnimations}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              IsEnabled="{Binding PerformanceSettings.EnableAnimations}"
                              Margin="0,0,0,8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 视觉效果设置 -->
            <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                <StackPanel>
                    <TextBlock Text="视觉效果"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               Foreground="{DynamicResource AppTextPrimaryBrush}"
                               Margin="0,0,0,12"/>

                    <CheckBox Content="玻璃磨砂效果"
                              IsChecked="{Binding PerformanceSettings.EnableGlassEffects}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              Margin="0,0,0,8"/>

                    <CheckBox Content="阴影效果"
                              IsChecked="{Binding PerformanceSettings.EnableShadowEffects}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              Margin="0,0,0,8"/>

                    <CheckBox Content="硬件加速"
                              IsChecked="{Binding PerformanceSettings.UseHardwareAcceleration}"
                              Style="{StaticResource MaterialDesignCheckBox}"
                              Foreground="{DynamicResource AppTextPrimaryBrush}"
                              ToolTip="启用GPU硬件加速以提升渲染性能"
                              Margin="0,0,0,8"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 高级设置 -->
            <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                <StackPanel>
                    <TextBlock Text="高级设置"
                               Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                               Foreground="{DynamicResource AppTextPrimaryBrush}"
                               Margin="0,0,0,12"/>

                    <Grid Margin="0,0,0,16">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <TextBlock Grid.Column="0"
                                   Text="最大帧率:"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource AppTextPrimaryBrush}"
                                   Margin="0,0,12,0"/>

                        <Slider Grid.Column="1"
                                Value="{Binding PerformanceSettings.MaxAnimationFps}"
                                Minimum="10"
                                Maximum="120"
                                TickFrequency="10"
                                IsSnapToTickEnabled="True"
                                Style="{StaticResource MaterialDesignSlider}"
                                ScrollViewer.CanContentScroll="False"
                                ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                ScrollViewer.VerticalScrollBarVisibility="Disabled"/>

                        <TextBlock Grid.Column="2"
                                   Text="{Binding PerformanceSettings.MaxAnimationFps, StringFormat={}{0} FPS}"
                                   VerticalAlignment="Center"
                                   Foreground="{DynamicResource AppTextSecondaryBrush}"
                                   MinWidth="60"
                                   Margin="12,0,0,0"/>
                    </Grid>

                    <!-- 按钮容器 -->
                    <StackPanel Orientation="Horizontal"
                                HorizontalAlignment="Left"
                                Margin="0,16,0,0">

                        <!-- 保存设置按钮 -->
                        <Button Command="{Binding SavePerformanceSettingsCommand}"
                                Padding="16,8"
                                MinWidth="140"
                                Margin="0,0,12,0"
                                ToolTip="保存当前性能设置到配置文件"
                                Style="{StaticResource MaterialDesignRaisedButton}"
                                Background="{DynamicResource PrimaryHueMidBrush}"
                                Foreground="{DynamicResource PrimaryHueMidForegroundBrush}"
                                BorderBrush="{DynamicResource PrimaryHueMidBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="ContentSave"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="保存设置" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>

                        <!-- 重置为默认设置按钮 -->
                        <Button Command="{Binding ResetPerformanceSettingsCommand}"
                                Padding="16,8"
                                MinWidth="140"
                                ToolTip="将所有性能设置重置为推荐的默认值"
                                Style="{StaticResource MaterialDesignOutlinedButton}"
                                Foreground="{DynamicResource AppTextPrimaryBrush}"
                                BorderBrush="{DynamicResource AppBorderBrush}">
                            <StackPanel Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Restore"
                                                       Width="16" Height="16"
                                                       VerticalAlignment="Center"
                                                       Margin="0,0,8,0"/>
                                <TextBlock Text="重置为默认设置" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Button>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>

            <!-- 性能提示 -->
            <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                <StackPanel>
                    <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                        <materialDesign:PackIcon Kind="Lightbulb"
                                               Width="20" Height="20"
                                               Foreground="{DynamicResource SecondaryHueMidBrush}"
                                               VerticalAlignment="Top"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="性能提示"
                                   Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                   Foreground="{DynamicResource AppTextPrimaryBrush}"/>
                    </StackPanel>

                    <TextBlock TextWrapping="Wrap"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource AppTextSecondaryBrush}">
                        <Run Text="• 如果应用程序运行缓慢，请尝试降低性能级别"/>
                        <LineBreak/>
                        <Run Text="• 在低性能设备上建议禁用玻璃效果和阴影"/>
                        <LineBreak/>
                        <Run Text="• 硬件加速可以显著提升动画性能"/>
                        <LineBreak/>
                        <Run Text="• 自动检测模式会根据您的系统配置选择最佳设置"/>
                    </TextBlock>
                </StackPanel>
            </materialDesign:Card>
    </StackPanel>
</UserControl>
