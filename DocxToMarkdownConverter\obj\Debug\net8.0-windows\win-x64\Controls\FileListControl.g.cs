﻿#pragma checksum "..\..\..\..\..\Controls\FileListControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "130CE33817F4C5DAD4AFCE93B97F0625EEAE7DD7"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DocxToMarkdownConverter.Behaviors;
using DocxToMarkdownConverter.Converters;
using DocxToMarkdownConverter.Models;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocxToMarkdownConverter.Controls {
    
    
    /// <summary>
    /// FileListControl
    /// </summary>
    public partial class FileListControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 190 "..\..\..\..\..\Controls\FileListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border DragDropOverlay;
        
        #line default
        #line hidden
        
        
        #line 220 "..\..\..\..\..\Controls\FileListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Shapes.Ellipse PulseCircle;
        
        #line default
        #line hidden
        
        
        #line 265 "..\..\..\..\..\Controls\FileListControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ListView FileListView;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocxToMarkdownConverter-v3.2.0;V3.2.0.0;component/controls/filelistcontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Controls\FileListControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "*******")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            
            #line 13 "..\..\..\..\..\Controls\FileListControl.xaml"
            ((DocxToMarkdownConverter.Controls.FileListControl)(target)).Drop += new System.Windows.DragEventHandler(this.OnDrop);
            
            #line default
            #line hidden
            
            #line 14 "..\..\..\..\..\Controls\FileListControl.xaml"
            ((DocxToMarkdownConverter.Controls.FileListControl)(target)).DragOver += new System.Windows.DragEventHandler(this.OnDragOver);
            
            #line default
            #line hidden
            
            #line 15 "..\..\..\..\..\Controls\FileListControl.xaml"
            ((DocxToMarkdownConverter.Controls.FileListControl)(target)).DragEnter += new System.Windows.DragEventHandler(this.OnDragEnter);
            
            #line default
            #line hidden
            
            #line 16 "..\..\..\..\..\Controls\FileListControl.xaml"
            ((DocxToMarkdownConverter.Controls.FileListControl)(target)).DragLeave += new System.Windows.DragEventHandler(this.OnDragLeave);
            
            #line default
            #line hidden
            return;
            case 2:
            this.DragDropOverlay = ((System.Windows.Controls.Border)(target));
            return;
            case 3:
            this.PulseCircle = ((System.Windows.Shapes.Ellipse)(target));
            return;
            case 4:
            this.FileListView = ((System.Windows.Controls.ListView)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

