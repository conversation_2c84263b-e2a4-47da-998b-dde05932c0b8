<Application x:Class="DocxToMarkdownConverter.App"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:converters="clr-namespace:DocxToMarkdownConverter.Converters">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <!-- Default Dark Theme - Will be replaced by ThemeManager -->
                <ResourceDictionary Source="Styles/DarkTheme.xaml" />

                <!-- Rounded Design System -->
                <ResourceDictionary Source="Styles/RoundedDesignSystem.xaml" />

                <!-- Glass Morphism Effects -->
                <ResourceDictionary Source="Styles/GlassMorphismEffects.xaml" />

                <!-- Application Styles -->
                <ResourceDictionary Source="Styles/ApplicationStyles.xaml" />

                <!-- Animation Styles -->
                <ResourceDictionary Source="Styles/AnimationStyles.xaml" />

                <!-- Default Localization Resources (Chinese) -->
                <ResourceDictionary Source="Resources/Strings.zh-CN.xaml" />
            </ResourceDictionary.MergedDictionaries>

            <!-- Global Converters -->
            <converters:CountToVisibilityConverter x:Key="CountToVisibilityConverter"/>
            <converters:CountToBooleanConverter x:Key="CountToBooleanConverter"/>
            <converters:ComparisonConverter x:Key="ComparisonConverter"/>
            <converters:InverseBooleanConverter x:Key="InverseBooleanConverter"/>
            <converters:LocalizedStatusConverter x:Key="LocalizedStatusConverter"/>
            <converters:EnumToBooleanConverter x:Key="EnumToBooleanConverter"/>
            <converters:FileSizeConverter x:Key="FileSizeConverter"/>

            <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

            <!-- Global Theme-Aware Styles -->
            <Style x:Key="ThemedWindowStyle" TargetType="Window">
                <Setter Property="Background" Value="{DynamicResource AppBackgroundBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource AppTextPrimaryBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
            </Style>
            
            <Style x:Key="ThemedCardStyle" TargetType="Border">
                <Setter Property="Background" Value="{DynamicResource AppCardBrush}"/>
                <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
                <Setter Property="Effect" Value="{DynamicResource CardShadowEffect}"/>
                <Setter Property="CornerRadius" Value="8"/>
                <Setter Property="Padding" Value="16"/>
                <Setter Property="Margin" Value="8"/>
            </Style>
            
            <Style x:Key="ThemedTextBlockStyle" TargetType="TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource AppTextPrimaryBrush}"/>
                <Setter Property="FontFamily" Value="Segoe UI"/>
            </Style>
            
            <Style x:Key="ThemedSecondaryTextStyle" TargetType="TextBlock" BasedOn="{StaticResource ThemedTextBlockStyle}">
                <Setter Property="Foreground" Value="{DynamicResource AppTextSecondaryBrush}"/>
                <Setter Property="FontSize" Value="12"/>
            </Style>
        </ResourceDictionary>
    </Application.Resources>
</Application>