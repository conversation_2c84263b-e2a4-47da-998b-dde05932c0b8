using Microsoft.Extensions.Logging;
using Serilog;
using Serilog.Events;
using System.IO;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 日志配置服务
/// </summary>
public static class LoggingConfiguration
{
    /// <summary>
    /// 配置 Serilog 日志记录
    /// </summary>
    public static void ConfigureSerilog()
    {
        var logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
        Directory.CreateDirectory(logDirectory);

        var logFilePath = Path.Combine(logDirectory, "docx-converter-.log");

        Log.Logger = new LoggerConfiguration()
            .MinimumLevel.Debug()
            .MinimumLevel.Override("Microsoft", LogEventLevel.Information)
            .MinimumLevel.Override("System", LogEventLevel.Information)
            .Enrich.FromLogContext()
            .Enrich.WithProperty("Application", "DocxToMarkdownConverter")
            .WriteTo.Console(
                outputTemplate: "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj}{NewLine}{Exception}")
            .WriteTo.File(
                logFilePath,
                rollingInterval: RollingInterval.Day,
                retainedFileCountLimit: 7,
                outputTemplate: "[{Timestamp:yyyy-MM-dd HH:mm:ss.fff} {Level:u3}] [{SourceContext}] {Message:lj}{NewLine}{Exception}",
                shared: true)
            .CreateLogger();
    }

    /// <summary>
    /// 创建 Microsoft.Extensions.Logging 的 LoggerFactory
    /// </summary>
    public static ILoggerFactory CreateLoggerFactory()
    {
        return LoggerFactory.Create(builder =>
        {
            builder
                .AddSerilog(dispose: true)
                .SetMinimumLevel(Microsoft.Extensions.Logging.LogLevel.Debug);
        });
    }

    /// <summary>
    /// 关闭日志记录
    /// </summary>
    public static void CloseAndFlush()
    {
        Log.CloseAndFlush();
    }
}

/// <summary>
/// 日志级别映射
/// </summary>
public static class LogLevelMapping
{
    /// <summary>
    /// 将 Serilog LogEventLevel 映射到 Microsoft.Extensions.Logging.LogLevel
    /// </summary>
    public static Microsoft.Extensions.Logging.LogLevel ToMicrosoftLogLevel(this LogEventLevel level)
    {
        return level switch
        {
            LogEventLevel.Debug => Microsoft.Extensions.Logging.LogLevel.Debug,
            LogEventLevel.Information => Microsoft.Extensions.Logging.LogLevel.Information,
            LogEventLevel.Warning => Microsoft.Extensions.Logging.LogLevel.Warning,
            LogEventLevel.Error => Microsoft.Extensions.Logging.LogLevel.Error,
            LogEventLevel.Fatal => Microsoft.Extensions.Logging.LogLevel.Critical,
            _ => Microsoft.Extensions.Logging.LogLevel.Information
        };
    }

    /// <summary>
    /// 将 Microsoft.Extensions.Logging.LogLevel 映射到 Serilog LogEventLevel
    /// </summary>
    public static LogEventLevel ToSerilogLogLevel(this Microsoft.Extensions.Logging.LogLevel level)
    {
        return level switch
        {
            Microsoft.Extensions.Logging.LogLevel.Debug => LogEventLevel.Debug,
            Microsoft.Extensions.Logging.LogLevel.Information => LogEventLevel.Information,
            Microsoft.Extensions.Logging.LogLevel.Warning => LogEventLevel.Warning,
            Microsoft.Extensions.Logging.LogLevel.Error => LogEventLevel.Error,
            Microsoft.Extensions.Logging.LogLevel.Critical => LogEventLevel.Fatal,
            _ => LogEventLevel.Information
        };
    }
}