/**
 * AI检测助手本地Web服务器
 * 支持本地域名部署和静态文件服务
 */

const http = require('http');
const fs = require('fs');
const path = require('path');
const url = require('url');

class AIDetectorServer {
    constructor(options = {}) {
        this.config = {
            port: options.port || 3000,
            host: options.host || 'localhost',
            domain: options.domain || 'ai-detector.local',
            staticDir: options.staticDir || __dirname,
            ...options
        };
        
        // MIME类型映射
        this.mimeTypes = {
            '.html': 'text/html',
            '.js': 'application/javascript',
            '.css': 'text/css',
            '.json': 'application/json',
            '.png': 'image/png',
            '.jpg': 'image/jpeg',
            '.gif': 'image/gif',
            '.svg': 'image/svg+xml',
            '.ico': 'image/x-icon',
            '.woff': 'font/woff',
            '.woff2': 'font/woff2',
            '.ttf': 'font/ttf',
            '.eot': 'application/vnd.ms-fontobject'
        };
    }

    /**
     * 启动服务器
     */
    start() {
        this.server = http.createServer((req, res) => {
            this.handleRequest(req, res);
        });

        this.server.listen(this.config.port, this.config.host, () => {
            console.log(`🚀 AI检测助手服务器已启动`);
            console.log(`📡 访问地址: http://${this.config.domain}:${this.config.port}`);
            console.log(`🏠 本地地址: http://${this.config.host}:${this.config.port}`);
            console.log(`📁 静态目录: ${this.config.staticDir}`);
            console.log(`\n💡 配置hosts文件以使用自定义域名:`);
            console.log(`   127.0.0.1 ${this.config.domain}`);
        });

        // 错误处理
        this.server.on('error', (err) => {
            if (err.code === 'EADDRINUSE') {
                console.error(`❌ 端口 ${this.config.port} 已被占用`);
                console.log(`💡 尝试使用其他端口: node server.js --port 3001`);
            } else {
                console.error('❌ 服务器错误:', err);
            }
        });

        return this.server;
    }

    /**
     * 处理HTTP请求
     */
    handleRequest(req, res) {
        const parsedUrl = url.parse(req.url, true);
        let pathname = parsedUrl.pathname;

        // 默认页面
        if (pathname === '/') {
            pathname = '/index.html';
        }

        // 构建文件路径
        const filePath = path.join(this.config.staticDir, pathname);
        const ext = path.extname(filePath).toLowerCase();

        // 安全检查：防止目录遍历攻击
        if (!this.isPathSafe(filePath)) {
            this.sendError(res, 403, 'Forbidden');
            return;
        }

        // 检查文件是否存在
        fs.access(filePath, fs.constants.F_OK, (err) => {
            if (err) {
                this.sendError(res, 404, 'File Not Found');
                return;
            }

            // 读取并发送文件
            fs.readFile(filePath, (err, data) => {
                if (err) {
                    this.sendError(res, 500, 'Internal Server Error');
                    return;
                }

                // 设置响应头
                const mimeType = this.mimeTypes[ext] || 'application/octet-stream';
                res.writeHead(200, {
                    'Content-Type': mimeType,
                    'Cache-Control': 'no-cache',
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type'
                });

                res.end(data);
            });
        });
    }

    /**
     * 检查路径安全性
     */
    isPathSafe(filePath) {
        const resolvedPath = path.resolve(filePath);
        const staticPath = path.resolve(this.config.staticDir);
        return resolvedPath.startsWith(staticPath);
    }

    /**
     * 发送错误响应
     */
    sendError(res, statusCode, message) {
        res.writeHead(statusCode, { 'Content-Type': 'text/html' });
        res.end(`
            <!DOCTYPE html>
            <html>
            <head>
                <title>错误 ${statusCode}</title>
                <style>
                    body { font-family: Arial, sans-serif; text-align: center; padding: 50px; }
                    h1 { color: #e74c3c; }
                </style>
            </head>
            <body>
                <h1>错误 ${statusCode}</h1>
                <p>${message}</p>
                <a href="/">返回首页</a>
            </body>
            </html>
        `);
    }

    /**
     * 停止服务器
     */
    stop() {
        if (this.server) {
            this.server.close(() => {
                console.log('🛑 服务器已停止');
            });
        }
    }
}

// 命令行参数解析
function parseArgs() {
    const args = process.argv.slice(2);
    const options = {};
    
    for (let i = 0; i < args.length; i += 2) {
        const key = args[i].replace('--', '');
        const value = args[i + 1];
        
        switch (key) {
            case 'port':
                options.port = parseInt(value);
                break;
            case 'host':
                options.host = value;
                break;
            case 'domain':
                options.domain = value;
                break;
        }
    }
    
    return options;
}

// 主程序
if (require.main === module) {
    const options = parseArgs();
    const server = new AIDetectorServer(options);
    
    // 启动服务器
    server.start();
    
    // 优雅关闭
    process.on('SIGINT', () => {
        console.log('\n🛑 正在关闭服务器...');
        server.stop();
        process.exit(0);
    });
}

module.exports = AIDetectorServer;
