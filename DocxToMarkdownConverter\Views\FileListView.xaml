<UserControl x:Class="DocxToMarkdownConverter.Views.FileListView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:DocxToMarkdownConverter.Controls"
             mc:Ignorable="d" 
             d:DesignHeight="450" d:DesignWidth="800">

    <Grid Margin="24">
        <Grid.RenderTransform>
            <TranslateTransform Y="0"/>
        </Grid.RenderTransform>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- <PERSON> Header -->
        <StackPanel Grid.Row="0" Margin="0,0,0,24">
            <TextBlock Text="{DynamicResource Files.Title}"
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,8"/>
            <TextBlock Text="{DynamicResource Files.Description}"
                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                       TextWrapping="Wrap"/>
        </StackPanel>

        <!-- File List Control -->
        <controls:FileListControl Grid.Row="1" 
                                  DataContext="{Binding}"
                                  x:Name="FileListControl"/>
    </Grid>
</UserControl>