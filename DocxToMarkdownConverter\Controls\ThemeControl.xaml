<UserControl x:Class="DocxToMarkdownConverter.Controls.ThemeControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <StackPanel>
        <!-- 主题模式选择 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="{DynamicResource Theme.Mode}"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <StackPanel Orientation="Horizontal" Margin="0,0,0,8">
                    <RadioButton x:Name="LightThemeRadio"
                                 Content="{DynamicResource Theme.Light}"
                                 IsChecked="{Binding IsLightTheme, Mode=TwoWay}"
                                 Style="{StaticResource MaterialDesignRadioButton}"
                                 Foreground="{DynamicResource AppTextPrimaryBrush}"
                                 Margin="0,0,16,0"/>

                    <RadioButton x:Name="DarkThemeRadio"
                                 Content="{DynamicResource Theme.Dark}"
                                 IsChecked="{Binding IsDarkTheme, Mode=TwoWay}"
                                 Style="{StaticResource MaterialDesignRadioButton}"
                                 Foreground="{DynamicResource AppTextPrimaryBrush}"
                                 Margin="0,0,16,0"/>

                    <RadioButton x:Name="AutoThemeRadio"
                                 Content="{DynamicResource Theme.Auto}"
                                 IsChecked="{Binding IsAutoTheme, Mode=TwoWay}"
                                 Style="{StaticResource MaterialDesignRadioButton}"
                                 Foreground="{DynamicResource AppTextPrimaryBrush}"
                                 IsEnabled="{Binding IsSystemThemeSupported}"/>
                </StackPanel>

                <CheckBox Content="{DynamicResource Theme.FollowSystem}"
                          IsChecked="{Binding FollowSystemTheme, Mode=TwoWay}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                          IsEnabled="{Binding IsSystemThemeSupported}"
                          Margin="0,0,0,8"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 主色调选择 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="主色调"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <WrapPanel Margin="0,0,0,8">
                    <Button x:Name="DeepPurpleButton"
                            Background="#673AB7"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="DeepPurple"
                            ToolTip="深紫色"/>

                    <Button x:Name="BlueButton"
                            Background="#2196F3"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="Blue"
                            ToolTip="蓝色"/>

                    <Button x:Name="TealButton"
                            Background="#009688"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="Teal"
                            ToolTip="青色"/>

                    <Button x:Name="GreenButton"
                            Background="#4CAF50"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="Green"
                            ToolTip="绿色"/>

                    <Button x:Name="OrangeButton"
                            Background="#FF9800"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="Orange"
                            ToolTip="橙色"/>

                    <Button x:Name="RedButton"
                            Background="#F44336"
                            Width="32" Height="32"
                            Margin="0,0,8,8"
                            Style="{StaticResource MaterialDesignFloatingActionMiniButton}"
                            Command="{Binding SetPrimaryColorCommand}"
                            CommandParameter="Red"
                            ToolTip="红色"/>
                </WrapPanel>
            </StackPanel>
        </materialDesign:Card>

        <!-- 主题选项 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="主题选项"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <CheckBox Content="{DynamicResource Theme.EnableTransitions}"
                          IsChecked="{Binding EnableThemeTransitions, Mode=TwoWay}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                          Margin="0,0,0,8"/>

                <CheckBox Content="{DynamicResource Theme.EnableAnimations}"
                          IsChecked="{Binding EnableAnimations, Mode=TwoWay}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                          Margin="0,0,0,8"
                          ToolTip="{DynamicResource Theme.AnimationTooltip}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 主题预览和操作 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="主题预览"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <Border Background="{DynamicResource AppCardBrush}"
                        BorderBrush="{DynamicResource AppBorderBrush}"
                        BorderThickness="1"
                        CornerRadius="8"
                        Padding="12"
                        Height="80"
                        Margin="0,0,0,12">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0"
                                   Text="示例卡片"
                                   Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                   Foreground="{DynamicResource AppTextPrimaryBrush}"/>

                        <TextBlock Grid.Row="1"
                                   Text="这是一个主题预览示例，显示当前主题的颜色效果。"
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                   Foreground="{DynamicResource AppTextSecondaryBrush}"
                                   TextWrapping="Wrap"
                                   VerticalAlignment="Center"/>
                    </Grid>
                </Border>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Left">
                    <Button Content="{DynamicResource Theme.Toggle}"
                            Command="{Binding ToggleThemeCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="{DynamicResource AppTextPrimaryBrush}"
                            BorderBrush="{DynamicResource AppBorderBrush}"
                            Padding="16,8"
                            MinWidth="120"
                            Margin="0,0,12,0"/>

                    <Button Content="{DynamicResource Theme.Reset}"
                            Command="{Binding ResetThemeCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="{DynamicResource AppTextPrimaryBrush}"
                            BorderBrush="{DynamicResource AppBorderBrush}"
                            Padding="16,8"
                            MinWidth="120"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </StackPanel>
</UserControl>