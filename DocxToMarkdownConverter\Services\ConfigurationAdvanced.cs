using DocxToMarkdownConverter.Models;
using System.IO;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using System.Collections.Concurrent;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// Advanced configuration functionality for ConfigurationService
/// </summary>
public partial class ConfigurationService
{
    #region Configuration Monitoring

    public void StartWatchingConfiguration<T>()
    {
        var settingsType = typeof(T);
        
        if (_watchers.ContainsKey(settingsType))
        {
            _logger?.LogWarning($"Already watching configuration for {settingsType.Name}");
            return;
        }

        try
        {
            var filePath = GetSettingsFilePath<T>();
            var directory = Path.GetDirectoryName(filePath);
            var fileName = Path.GetFileName(filePath);

            if (string.IsNullOrEmpty(directory) || string.IsNullOrEmpty(fileName))
            {
                _logger?.LogError($"Invalid file path for {settingsType.Name} configuration watching");
                return;
            }

            var watcher = new FileSystemWatcher(directory, fileName)
            {
                NotifyFilter = NotifyFilters.LastWrite | NotifyFilters.Size,
                EnableRaisingEvents = true
            };

            watcher.Changed += async (sender, e) =>
            {
                try
                {
                    // Debounce multiple rapid changes
                    await Task.Delay(500).ConfigureAwait(false);
                    
                    _logger?.LogInfo($"Configuration file changed: {e.FullPath}");
                    
                    // Notify configuration changed
                    ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(settingsType)
                    {
                        PropertyName = "FileChanged"
                    });
                }
                catch (Exception ex)
                {
                    _logger?.LogError($"Error handling configuration file change: {ex.Message}", ex);
                }
            };

            _watchers[settingsType] = watcher;
            _logger?.LogInfo($"Started watching configuration for {settingsType.Name}");
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to start watching configuration for {settingsType.Name}: {ex.Message}", ex);
        }
    }

    public void StopWatchingConfiguration<T>()
    {
        var settingsType = typeof(T);
        
        if (_watchers.TryGetValue(settingsType, out var watcher))
        {
            try
            {
                watcher.EnableRaisingEvents = false;
                watcher.Dispose();
                _watchers.Remove(settingsType);
                _logger?.LogInfo($"Stopped watching configuration for {settingsType.Name}");
            }
            catch (Exception ex)
            {
                _logger?.LogError($"Error stopping configuration watcher for {settingsType.Name}: {ex.Message}", ex);
            }
        }
    }

    #endregion

    #region Advanced Operations

    public async Task<Dictionary<string, object>> GetAllSettingsAsync()
    {
        var allSettings = new Dictionary<string, object>();

        try
        {
            // Load all known settings types
            var settingsTypes = new[]
            {
                typeof(ApplicationSettings),
                typeof(ConversionOptions),
                typeof(AnimationSettings),
                typeof(ThemeSettings),
                typeof(ConversionStatistics)
            };

            foreach (var type in settingsTypes)
            {
                try
                {
                    var method = typeof(ConfigurationService).GetMethod(nameof(LoadSettingsAsync))?.MakeGenericMethod(type);
                    if (method != null)
                    {
                        var task = (Task<object>)method.Invoke(this, Array.Empty<object>())!;
                        var settings = await task;
                        allSettings[type.Name] = settings;
                    }
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Failed to load {type.Name} settings: {ex.Message}");
                    allSettings[type.Name] = Activator.CreateInstance(type)!;
                }
            }

            _logger?.LogInfo("Successfully loaded all settings");
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to load all settings: {ex.Message}", ex);
        }

        return allSettings;
    }

    public async Task<bool> MergeSettingsAsync<T>(T baseSettings, T overrideSettings) where T : new()
    {
        try
        {
            if (baseSettings == null || overrideSettings == null)
            {
                _logger?.LogError("Cannot merge null settings");
                return false;
            }

            var type = typeof(T);
            var properties = type.GetProperties(System.Reflection.BindingFlags.Public | System.Reflection.BindingFlags.Instance)
                .Where(p => p.CanRead && p.CanWrite);

            foreach (var property in properties)
            {
                var overrideValue = property.GetValue(overrideSettings);
                
                // Only override if the value is not null/default
                if (overrideValue != null && !IsDefaultValue(overrideValue, property.PropertyType))
                {
                    property.SetValue(baseSettings, overrideValue);
                }
            }

            // Save the merged settings
            await SaveSettingsAsync(baseSettings);
            
            _logger?.LogInfo($"Successfully merged {typeof(T).Name} settings");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to merge {typeof(T).Name} settings: {ex.Message}", ex);
            return false;
        }
    }

    public async Task<string> GetSettingsHashAsync<T>() where T : class, new()
    {
        try
        {
            var settings = await LoadSettingsAsync<T>();
            var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                WriteIndented = false
            });

            using var sha256 = SHA256.Create();
            var hashBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(json));
            return Convert.ToBase64String(hashBytes);
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to compute hash for {typeof(T).Name} settings: {ex.Message}", ex);
            return string.Empty;
        }
    }

    #endregion

    #region Utility Methods

    private bool IsDefaultValue(object value, Type type)
    {
        if (value == null)
            return true;

        if (type.IsValueType)
        {
            var defaultValue = Activator.CreateInstance(type);
            return value.Equals(defaultValue);
        }

        if (type == typeof(string))
        {
            return string.IsNullOrEmpty((string)value);
        }

        return false;
    }

    #endregion

    #region Cleanup and Disposal

    public void Dispose()
    {
        try
        {
            // Stop all watchers
            foreach (var watcher in _watchers.Values)
            {
                try
                {
                    watcher.EnableRaisingEvents = false;
                    watcher.Dispose();
                }
                catch (Exception ex)
                {
                    _logger?.LogWarning($"Error disposing configuration watcher: {ex.Message}");
                }
            }
            _watchers.Clear();

            _logger?.LogInfo("Configuration service disposed");
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Error during configuration service disposal: {ex.Message}", ex);
        }
    }

    #endregion
}

/// <summary>
/// Extension methods for File operations
/// </summary>
public static class FileExtensions
{
    public static async Task CopyAsync(string sourceFile, string destinationFile, bool overwrite = false)
    {
        const int bufferSize = 4096;
        
        using var sourceStream = new FileStream(sourceFile, FileMode.Open, FileAccess.Read, FileShare.Read, bufferSize, useAsync: true);
        using var destinationStream = new FileStream(destinationFile, overwrite ? FileMode.Create : FileMode.CreateNew, FileAccess.Write, FileShare.None, bufferSize, useAsync: true);
        
        await sourceStream.CopyToAsync(destinationStream);
    }
}
