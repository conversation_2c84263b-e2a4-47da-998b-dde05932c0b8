using System.Windows;
using Microsoft.Extensions.Logging;
using UserControl = System.Windows.Controls.UserControl;

namespace DocxToMarkdownConverter.Services;

public interface INavigationService
{
    // Navigation methods
    Task NavigateToAsync<T>() where T : UserControl;
    Task NavigateToAsync(Type pageType);
    Task NavigateToAsync<T>(object? parameter) where T : UserControl;
    Task NavigateToAsync(Type pageType, object? parameter);

    // History navigation
    void GoBack();
    void GoForward();
    void ClearHistory();

    // Navigation state
    bool CanGoBack { get; }
    bool CanGoForward { get; }
    Type? CurrentPageType { get; }
    UserControl? CurrentPage { get; }
    object? CurrentParameter { get; }

    // Navigation history
    IReadOnlyList<NavigationHistoryEntry> NavigationHistory { get; }
    int CurrentIndex { get; }

    // Page lifecycle management
    Task<bool> CanNavigateAwayAsync();
    void RefreshCurrentPage();

    // Events
    event EventHandler<NavigationEventArgs>? Navigated;
    event EventHandler<NavigationFailedEventArgs>? NavigationFailed;
    event EventHandler<NavigatingEventArgs>? Navigating;
}

public class NavigationEventArgs : EventArgs
{
    public Type? PageType { get; set; }
    public UserControl? Page { get; set; }
    public object? Parameter { get; set; }
    public NavigationMode Mode { get; set; }
}

public class NavigationFailedEventArgs : EventArgs
{
    public Type? PageType { get; set; }
    public Exception? Exception { get; set; }
    public string? ErrorMessage { get; set; }
    public object? Parameter { get; set; }
}

public class NavigatingEventArgs : EventArgs
{
    public Type? FromPageType { get; set; }
    public Type? ToPageType { get; set; }
    public object? Parameter { get; set; }
    public NavigationMode Mode { get; set; }
    public bool Cancel { get; set; }
}

public class NavigationHistoryEntry
{
    public Type PageType { get; set; } = null!;
    public object? Parameter { get; set; }
    public DateTime NavigatedAt { get; set; }
    public string? Title { get; set; }
}

public enum NavigationMode
{
    New,
    Back,
    Forward,
    Refresh
}

public class NavigationService : INavigationService
{
    private readonly List<NavigationHistoryEntry> _navigationHistory = new();
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<NavigationService>? _logger;
    private int _currentIndex = -1;
    private UserControl? _currentPage;
    private readonly Dictionary<Type, UserControl> _pageCache = new();
    private readonly object _lockObject = new();

    public NavigationService(IServiceProvider serviceProvider, ILogger<NavigationService>? logger = null)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }

    public bool CanGoBack => _currentIndex > 0;
    public bool CanGoForward => _currentIndex < _navigationHistory.Count - 1;
    public Type? CurrentPageType => _currentIndex >= 0 && _currentIndex < _navigationHistory.Count
        ? _navigationHistory[_currentIndex].PageType
        : null;
    public UserControl? CurrentPage => _currentPage;
    public object? CurrentParameter => _currentIndex >= 0 && _currentIndex < _navigationHistory.Count
        ? _navigationHistory[_currentIndex].Parameter
        : null;
    public IReadOnlyList<NavigationHistoryEntry> NavigationHistory => _navigationHistory.AsReadOnly();
    public int CurrentIndex => _currentIndex;

    public event EventHandler<NavigationEventArgs>? Navigated;
    public event EventHandler<NavigationFailedEventArgs>? NavigationFailed;
    public event EventHandler<NavigatingEventArgs>? Navigating;

    public void GoBack()
    {
        if (!CanGoBack) return;

        lock (_lockObject)
        {
            _currentIndex--;
            var entry = _navigationHistory[_currentIndex];

            if (TryNavigateToHistoryEntry(entry, NavigationMode.Back))
            {
                _logger?.LogInformation("Navigated back to {PageType}", entry.PageType.Name);
            }
        }
    }

    public void GoForward()
    {
        if (!CanGoForward) return;

        lock (_lockObject)
        {
            _currentIndex++;
            var entry = _navigationHistory[_currentIndex];

            if (TryNavigateToHistoryEntry(entry, NavigationMode.Forward))
            {
                _logger?.LogInformation("Navigated forward to {PageType}", entry.PageType.Name);
            }
        }
    }

    public void ClearHistory()
    {
        lock (_lockObject)
        {
            _navigationHistory.Clear();
            _pageCache.Clear();
            _currentIndex = -1;
            _currentPage = null;
            _logger?.LogInformation("Navigation history cleared");
        }
    }

    public async Task<bool> CanNavigateAwayAsync()
    {
        if (_currentPage is INavigationAware navigationAware)
        {
            return await navigationAware.CanNavigateAwayAsync();
        }
        return true;
    }

    public void RefreshCurrentPage()
    {
        if (_currentIndex >= 0 && _currentIndex < _navigationHistory.Count)
        {
            var entry = _navigationHistory[_currentIndex];
            TryNavigateToHistoryEntry(entry, NavigationMode.Refresh);
            _logger?.LogInformation("Refreshed current page {PageType}", entry.PageType.Name);
        }
    }

    public async Task NavigateToAsync<T>() where T : UserControl
    {
        await NavigateToAsync(typeof(T));
    }

    public async Task NavigateToAsync<T>(object? parameter) where T : UserControl
    {
        await NavigateToAsync(typeof(T), parameter);
    }

    public async Task NavigateToAsync(Type pageType)
    {
        await NavigateToAsync(pageType, null);
    }

    public async Task NavigateToAsync(Type pageType, object? parameter)
    {
        try
        {
            // Check if we can navigate away from current page
            if (!await CanNavigateAwayAsync())
            {
                _logger?.LogInformation("Navigation cancelled by current page");
                return;
            }

            // Fire navigating event
            var navigatingArgs = new NavigatingEventArgs
            {
                FromPageType = CurrentPageType,
                ToPageType = pageType,
                Parameter = parameter,
                Mode = NavigationMode.New
            };

            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                Navigating?.Invoke(this, navigatingArgs);
            });

            if (navigatingArgs.Cancel)
            {
                _logger?.LogInformation("Navigation cancelled by event handler");
                return;
            }

            // 确保UI操作在UI线程上执行
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                lock (_lockObject)
                {
                    // Get or create page instance
                    var page = GetOrCreatePage(pageType);
                    if (page == null)
                    {
                        var errorMessage = $"Failed to resolve page type: {pageType.Name}";
                        _logger?.LogError(errorMessage);

                        NavigationFailed?.Invoke(this, new NavigationFailedEventArgs
                        {
                            PageType = pageType,
                            ErrorMessage = errorMessage,
                            Parameter = parameter
                        });
                        return;
                    }

                    // Remove forward history if navigating to new page
                    if (_currentIndex < _navigationHistory.Count - 1)
                    {
                        _navigationHistory.RemoveRange(_currentIndex + 1, _navigationHistory.Count - _currentIndex - 1);
                    }

                    // Add new entry to history
                    var historyEntry = new NavigationHistoryEntry
                    {
                        PageType = pageType,
                        Parameter = parameter,
                        NavigatedAt = DateTime.Now,
                        Title = GetPageTitle(pageType)
                    };

                    _navigationHistory.Add(historyEntry);
                    _currentIndex = _navigationHistory.Count - 1;
                    _currentPage = page;

                    // Notify page of navigation
                    if (page is INavigationAware navigationAware)
                    {
                        navigationAware.OnNavigatedTo(parameter);
                    }

                    Navigated?.Invoke(this, new NavigationEventArgs
                    {
                        PageType = pageType,
                        Page = page,
                        Parameter = parameter,
                        Mode = NavigationMode.New
                    });

                    _logger?.LogInformation("Navigated to {PageType}", pageType.Name);
                }
            });
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Navigation failed for {PageType}", pageType.Name);

            NavigationFailed?.Invoke(this, new NavigationFailedEventArgs
            {
                PageType = pageType,
                Exception = ex,
                ErrorMessage = ex.Message,
                Parameter = parameter
            });
        }
    }

    private UserControl? GetOrCreatePage(Type pageType)
    {
        if (_pageCache.TryGetValue(pageType, out var cachedPage))
        {
            return cachedPage;
        }

        if (_serviceProvider.GetService(pageType) is UserControl page)
        {
            _pageCache[pageType] = page;
            return page;
        }

        return null;
    }

    private bool TryNavigateToHistoryEntry(NavigationHistoryEntry entry, NavigationMode mode)
    {
        try
        {
            var page = GetOrCreatePage(entry.PageType);
            if (page == null) return false;

            _currentPage = page;

            // Notify page of navigation
            if (page is INavigationAware navigationAware)
            {
                System.Windows.Application.Current.Dispatcher.Invoke(() =>
                {
                    navigationAware.OnNavigatedTo(entry.Parameter);
                });
            }

            System.Windows.Application.Current.Dispatcher.Invoke(() =>
            {
                Navigated?.Invoke(this, new NavigationEventArgs
                {
                    PageType = entry.PageType,
                    Page = page,
                    Parameter = entry.Parameter,
                    Mode = mode
                });
            });

            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to navigate to history entry {PageType}", entry.PageType.Name);
            return false;
        }
    }

    private static string GetPageTitle(Type pageType)
    {
        return pageType.Name.Replace("View", "").Replace("Page", "");
    }
}

// Interface for pages that need to participate in navigation lifecycle
public interface INavigationAware
{
    void OnNavigatedTo(object? parameter);
    void OnNavigatedFrom();
    Task<bool> CanNavigateAwayAsync();
}