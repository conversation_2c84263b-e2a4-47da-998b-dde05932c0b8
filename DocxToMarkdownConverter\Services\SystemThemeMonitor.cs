using Microsoft.Extensions.Logging;
using Microsoft.Win32;
using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Services;

public interface ISystemThemeMonitor : IDisposable
{
    event EventHandler<SystemThemeChangedEventArgs>? SystemThemeChanged;
    void StartMonitoring();
    void StopMonitoring();
    AppTheme GetCurrentSystemTheme();
    bool IsSupported { get; }
}

public class SystemThemeChangedEventArgs : EventArgs
{
    public AppTheme NewTheme { get; }
    public AppTheme OldTheme { get; }

    public SystemThemeChangedEventArgs(AppTheme oldTheme, AppTheme newTheme)
    {
        OldTheme = oldTheme;
        NewTheme = newTheme;
    }
}

public class SystemThemeMonitor : ISystemThemeMonitor, IDisposable
{
    private readonly ILogger<SystemThemeMonitor> _logger;
    private readonly System.Threading.Timer _monitorTimer;
    private AppTheme _lastKnownTheme;
    private bool _isMonitoring;

    public SystemThemeMonitor(ILogger<SystemThemeMonitor> logger)
    {
        _logger = logger;
        _lastKnownTheme = GetCurrentSystemTheme();
        
        // Create timer but don't start it yet
        _monitorTimer = new System.Threading.Timer(CheckSystemTheme, null, Timeout.Infinite, Timeout.Infinite);
    }

    public bool IsSupported => Environment.OSVersion.Version.Major >= 10;

    public event EventHandler<SystemThemeChangedEventArgs>? SystemThemeChanged;

    public void StartMonitoring()
    {
        if (!IsSupported)
        {
            _logger.LogWarning("System theme monitoring is not supported on this OS version");
            return;
        }

        if (_isMonitoring)
        {
            _logger.LogDebug("System theme monitoring is already running");
            return;
        }

        _isMonitoring = true;
        _lastKnownTheme = GetCurrentSystemTheme();
        
        // Check every 2 seconds for theme changes
        _monitorTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(2));
        
        _logger.LogInformation("Started system theme monitoring. Current theme: {Theme}", _lastKnownTheme);
    }

    public void StopMonitoring()
    {
        if (!_isMonitoring) return;

        _isMonitoring = false;
        _monitorTimer.Change(Timeout.Infinite, Timeout.Infinite);
        
        _logger.LogInformation("Stopped system theme monitoring");
    }

    public AppTheme GetCurrentSystemTheme()
    {
        if (!IsSupported) return AppTheme.Light;

        try
        {
            using var key = Registry.CurrentUser.OpenSubKey(@"Software\Microsoft\Windows\CurrentVersion\Themes\Personalize");
            var value = key?.GetValue("AppsUseLightTheme");
            
            var theme = value is int intValue && intValue == 0 ? AppTheme.Dark : AppTheme.Light;
            _logger.LogDebug("Retrieved system theme: {Theme} (registry value: {Value})", theme, value);
            
            return theme;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to get system theme from registry, defaulting to Light");
            return AppTheme.Light;
        }
    }

    private void CheckSystemTheme(object? state)
    {
        if (!_isMonitoring) return;

        try
        {
            var currentTheme = GetCurrentSystemTheme();
            
            if (currentTheme != _lastKnownTheme)
            {
                _logger.LogInformation("System theme changed from {OldTheme} to {NewTheme}", _lastKnownTheme, currentTheme);
                
                var args = new SystemThemeChangedEventArgs(_lastKnownTheme, currentTheme);
                _lastKnownTheme = currentTheme;
                
                // Invoke on UI thread
                System.Windows.Application.Current?.Dispatcher.BeginInvoke(() =>
                {
                    SystemThemeChanged?.Invoke(this, args);
                });
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking system theme");
        }
    }

    public void Dispose()
    {
        StopMonitoring();
        _monitorTimer?.Dispose();
        _logger.LogDebug("SystemThemeMonitor disposed");
    }
}