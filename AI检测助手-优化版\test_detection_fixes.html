<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI检测功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        
        /* 引入主应用的样式 */
        .ai-detection-result {
            background: white;
            border-radius: 15px;
            padding: 25px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin: 20px 0;
        }
        
        .detection-header {
            text-align: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #f8f9fa;
        }
        
        .detection-score-main {
            font-size: 2.5rem;
            font-weight: bold;
            margin-bottom: 10px;
            transition: all 0.5s ease-in-out;
        }
        
        .detection-score-main.low { color: #28a745; }
        .detection-score-main.medium { color: #ffc107; }
        .detection-score-main.high { color: #dc3545; }
        
        .detection-status {
            font-size: 1.1rem;
            color: #666;
            margin-bottom: 5px;
            transition: all 0.5s ease-in-out;
        }
        
        .detection-status.low { color: #28a745; font-weight: 600; }
        .detection-status.medium { color: #ffc107; font-weight: 600; }
        .detection-status.high { color: #dc3545; font-weight: 600; }
        
        .detection-mode {
            font-size: 0.9rem;
            color: #999;
            background: #f8f9fa;
            padding: 5px 15px;
            border-radius: 20px;
            display: inline-block;
        }
        
        .pie-chart-container {
            display: flex;
            justify-content: center;
            align-items: center;
            margin: 30px 0;
            gap: 30px;
        }
        
        .pie-chart {
            position: relative;
            width: 200px;
            height: 200px;
        }
        
        .pie-chart svg {
            width: 100%;
            height: 100%;
            transform: rotate(-90deg);
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }
        
        .pie-chart svg circle {
            transition: stroke-dasharray 1.2s cubic-bezier(0.4, 0, 0.2, 1), 
                        stroke-dashoffset 1.2s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .pie-chart-center {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
        }
        
        .pie-chart-score {
            font-size: 1.8rem;
            font-weight: bold;
            color: #333;
        }
        
        .pie-chart-label {
            font-size: 0.9rem;
            color: #666;
            margin-top: 5px;
        }
        
        .detection-breakdown {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin: 25px 0;
        }
        
        .breakdown-item {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #dee2e6;
            transition: all 0.3s ease;
        }
        
        .breakdown-item:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
        }
        
        .breakdown-item.ai-generated {
            border-left-color: #dc3545;
            background: linear-gradient(135deg, #fff5f5, #ffe8e8);
        }
        
        .breakdown-item.human-written {
            border-left-color: #28a745;
            background: linear-gradient(135deg, #f8fff8, #e8f5e8);
        }
        
        .breakdown-item.suspicious {
            border-left-color: #ffc107;
            background: linear-gradient(135deg, #fffbf0, #fff3cd);
        }
        
        .breakdown-percentage {
            font-size: 1.5rem;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .breakdown-percentage.ai-generated { color: #dc3545; }
        .breakdown-percentage.human-written { color: #28a745; }
        .breakdown-percentage.suspicious { color: #ffc107; }
        
        .breakdown-label {
            font-size: 0.9rem;
            color: #666;
            font-weight: 500;
        }
        
        .progress-bars {
            margin: 25px 0;
        }
        
        .progress-item {
            margin-bottom: 15px;
        }
        
        .progress-label {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .progress-bar-container {
            width: 100%;
            height: 8px;
            background: #e9ecef;
            border-radius: 4px;
            overflow: hidden;
        }
        
        .progress-bar-fill {
            height: 100%;
            border-radius: 4px;
            transition: width 1s cubic-bezier(0.4, 0, 0.2, 1);
            position: relative;
        }
        
        .progress-bar-fill.ai-score {
            background: linear-gradient(90deg, #28a745, #ffc107, #dc3545);
        }
        
        .progress-bar-fill.confidence {
            background: linear-gradient(90deg, #007bff, #0056b3);
        }
        
        .progress-bar-fill.complexity {
            background: linear-gradient(90deg, #6f42c1, #495057);
        }
        
        .chart-legend {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-top: 15px;
            flex-wrap: wrap;
        }
        
        .legend-item {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 0.9rem;
        }
        
        .legend-color {
            width: 16px;
            height: 16px;
            border-radius: 50%;
        }
        
        .legend-color.human { background: #28a745; }
        .legend-color.ai { background: #dc3545; }
        .legend-color.suspicious { background: #ffc107; }
    </style>
</head>
<body>
    <h1>AI检测功能修复测试</h1>
    
    <!-- 测试1: DOM元素检查 -->
    <div class="test-section">
        <h2>测试1: DOM元素完整性检查</h2>
        <p>检查所有必需的DOM元素是否存在</p>
        
        <button class="test-button" onclick="testDOMElements()">
            检查DOM元素
        </button>
        
        <div id="domTestResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 测试2: 检测结果展示 -->
    <div class="test-section">
        <h2>测试2: 检测结果展示测试</h2>
        <p>测试不同分数的检测结果展示效果</p>
        
        <button class="test-button" onclick="testDetectionResult(25)">
            测试低分数 (25%)
        </button>
        <button class="test-button" onclick="testDetectionResult(55)">
            测试中等分数 (55%)
        </button>
        <button class="test-button" onclick="testDetectionResult(85)">
            测试高分数 (85%)
        </button>
        
        <div id="detectResult" style="display: none;">
            <div class="ai-detection-result">
                <!-- 检测结果头部 -->
                <div class="detection-header">
                    <div class="detection-score-main" id="detectionScoreMain">0%</div>
                    <div class="detection-status" id="detectionStatus">等待检测...</div>
                    <div class="detection-mode" id="detectionMode">📝 基础检测</div>
                </div>

                <!-- 饼图和分类展示 -->
                <div class="pie-chart-container">
                    <div class="pie-chart">
                        <svg viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="8"/>
                            <circle id="aiScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#dc3545" 
                                    stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                            <circle id="humanScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#28a745" 
                                    stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                        </svg>
                        <div class="pie-chart-center">
                            <div class="pie-chart-score" id="pieChartScore">0%</div>
                            <div class="pie-chart-label">AI检测度</div>
                        </div>
                    </div>

                    <div class="detection-breakdown">
                        <div class="breakdown-item human-written">
                            <div class="breakdown-percentage human-written" id="humanPercentage">0%</div>
                            <div class="breakdown-label">人工写作</div>
                        </div>
                        <div class="breakdown-item ai-generated">
                            <div class="breakdown-percentage ai-generated" id="aiPercentage">0%</div>
                            <div class="breakdown-label">AI生成</div>
                        </div>
                        <div class="breakdown-item suspicious">
                            <div class="breakdown-percentage suspicious" id="suspiciousPercentage">0%</div>
                            <div class="breakdown-label">疑似AI</div>
                        </div>
                    </div>
                </div>

                <!-- 图例 -->
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color human"></div>
                        <span>人工写作</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color ai"></div>
                        <span>AI生成</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color suspicious"></div>
                        <span>疑似AI</span>
                    </div>
                </div>

                <!-- 进度条展示 -->
                <div class="progress-bars">
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>AI检测分数</span>
                            <span id="aiScoreLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill ai-score" id="aiScoreProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>检测置信度</span>
                            <span id="confidenceLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill confidence" id="confidenceProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>文本复杂度</span>
                            <span id="complexityLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill complexity" id="complexityProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/ai_detector.js"></script>
    <script src="js/academic_optimizer.js"></script>
    <script src="js/unified_academic_optimizer.js"></script>
    <script src="js/zhuque_optimizer.js"></script>
    <script src="js/hybrid_detector.js"></script>
    <script src="js/multi_round_optimizer.js"></script>
    <script src="js/prompt_templates.js"></script>
    <script src="js/ollama_manager_v2.js"></script>
    <script src="js/main.js"></script>

    <script>
        function log(message, type = 'info') {
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }

        // 测试DOM元素完整性
        function testDOMElements() {
            const resultDiv = document.getElementById('domTestResult');
            resultDiv.style.display = 'block';
            
            const requiredElements = [
                'detectionScoreMain',
                'detectionStatus', 
                'detectionMode',
                'pieChartScore',
                'aiScoreCircle',
                'humanScoreCircle',
                'humanPercentage',
                'aiPercentage',
                'suspiciousPercentage',
                'aiScoreLabel',
                'aiScoreProgress',
                'confidenceLabel',
                'confidenceProgress',
                'complexityLabel',
                'complexityProgress'
            ];
            
            let results = [];
            let allFound = true;
            
            requiredElements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    results.push(`✅ ${id}: 找到`);
                } else {
                    results.push(`❌ ${id}: 未找到`);
                    allFound = false;
                }
            });
            
            const summary = allFound ? 
                '<div class="success">✅ 所有必需的DOM元素都已找到！</div>' :
                '<div class="error">❌ 部分DOM元素缺失，可能导致功能异常</div>';
            
            resultDiv.innerHTML = summary + '\n\n详细检查结果:\n' + results.join('\n');
        }

        // 测试检测结果展示
        function testDetectionResult(score) {
            log(`开始测试检测结果展示，分数: ${score}%`, 'info');
            
            // 模拟检测结果数据
            const mockResult = {
                aiProbability: score,
                score: score,
                confidence: score > 70 ? 'high' : score > 40 ? 'medium' : 'low',
                mode: 'zhuque_enhanced',
                recommendation: `基于${score}%的AI检测分数的建议`,
                zhuqueAnalysis: {
                    analysis: {
                        perplexity: { score: score + Math.random() * 10 - 5 },
                        structural: { score: score + Math.random() * 10 - 5 },
                        semantic: { score: score + Math.random() * 10 - 5 }
                    }
                }
            };

            try {
                // 调用显示函数
                displayDetectionResult(mockResult);
                log(`✅ 检测结果展示测试完成，分数: ${score}%`, 'success');
            } catch (error) {
                log(`❌ 检测结果展示测试失败: ${error.message}`, 'error');
                console.error('详细错误:', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('AI检测功能修复测试页面加载完成', 'info');
            
            // 检查关键函数是否存在
            const functions = [
                'displayDetectionResult',
                'updateMainScore',
                'updatePieChart',
                'updateBreakdown',
                'updateProgressBars',
                'updateDetectionMode',
                'updateDetectionStatus',
                'animateNumber'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ ${funcName} 函数已加载`, 'success');
                } else {
                    log(`❌ ${funcName} 函数未找到`, 'error');
                }
            });
        });
    </script>
</body>
</html>
