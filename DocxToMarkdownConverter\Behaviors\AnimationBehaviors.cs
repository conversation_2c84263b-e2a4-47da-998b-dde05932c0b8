using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.DependencyInjection;

namespace DocxToMarkdownConverter.Behaviors;

/// <summary>
/// Attached behaviors for automatic animation management
/// </summary>
public static class AnimationBehaviors
{
    #region AutoAttachHoverAnimations Attached Property

    public static readonly DependencyProperty AutoAttachHoverAnimationsProperty =
        DependencyProperty.RegisterAttached(
            "AutoAttachHoverAnimations",
            typeof(bool),
            typeof(AnimationBehaviors),
            new PropertyMetadata(false, OnAutoAttachHoverAnimationsChanged));

    public static bool GetAutoAttachHoverAnimations(DependencyObject obj)
    {
        return (bool)obj.GetValue(AutoAttachHoverAnimationsProperty);
    }

    public static void SetAutoAttachHoverAnimations(DependencyObject obj, bool value)
    {
        obj.SetValue(AutoAttachHoverAnimationsProperty, value);
    }

    private static void OnAutoAttachHoverAnimationsChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && (bool)e.NewValue)
        {
            element.Loaded += OnElementLoaded;
        }
        else if (d is FrameworkElement elementToDetach && !(bool)e.NewValue)
        {
            elementToDetach.Loaded -= OnElementLoaded;
        }
    }

    private static void OnElementLoaded(object sender, RoutedEventArgs e)
    {
        if (sender is FrameworkElement element)
        {
            AttachHoverAnimationsToButtons(element);
        }
    }

    #endregion

    #region EnableEntranceAnimation Attached Property

    public static readonly DependencyProperty EnableEntranceAnimationProperty =
        DependencyProperty.RegisterAttached(
            "EnableEntranceAnimation",
            typeof(bool),
            typeof(AnimationBehaviors),
            new PropertyMetadata(false, OnEnableEntranceAnimationChanged));

    public static bool GetEnableEntranceAnimation(DependencyObject obj)
    {
        return (bool)obj.GetValue(EnableEntranceAnimationProperty);
    }

    public static void SetEnableEntranceAnimation(DependencyObject obj, bool value)
    {
        obj.SetValue(EnableEntranceAnimationProperty, value);
    }

    private static void OnEnableEntranceAnimationChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is FrameworkElement element && (bool)e.NewValue)
        {
            element.Loaded += OnElementLoadedForEntrance;
        }
        else if (d is FrameworkElement elementToDetach && !(bool)e.NewValue)
        {
            elementToDetach.Loaded -= OnElementLoadedForEntrance;
        }
    }

    private static async void OnElementLoadedForEntrance(object sender, RoutedEventArgs e)
    {
        if (sender is FrameworkElement element)
        {
            var animationManager = GetAnimationManager();
            if (animationManager != null)
            {
                await animationManager.PlayEntranceAnimationAsync(element);
            }
        }
    }

    #endregion

    #region EnableProgressAnimation Attached Property

    public static readonly DependencyProperty EnableProgressAnimationProperty =
        DependencyProperty.RegisterAttached(
            "EnableProgressAnimation",
            typeof(bool),
            typeof(AnimationBehaviors),
            new PropertyMetadata(false, OnEnableProgressAnimationChanged));

    public static bool GetEnableProgressAnimation(DependencyObject obj)
    {
        return (bool)obj.GetValue(EnableProgressAnimationProperty);
    }

    public static void SetEnableProgressAnimation(DependencyObject obj, bool value)
    {
        obj.SetValue(EnableProgressAnimationProperty, value);
    }

    private static void OnEnableProgressAnimationChanged(DependencyObject d, DependencyPropertyChangedEventArgs e)
    {
        if (d is System.Windows.Controls.ProgressBar progressBar && (bool)e.NewValue)
        {
            progressBar.ValueChanged += OnProgressBarValueChanged;
        }
        else if (d is System.Windows.Controls.ProgressBar progressBarToDetach && !(bool)e.NewValue)
        {
            progressBarToDetach.ValueChanged -= OnProgressBarValueChanged;
        }
    }

    private static async void OnProgressBarValueChanged(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (sender is System.Windows.Controls.ProgressBar progressBar)
        {
            var animationManager = GetAnimationManager();
            if (animationManager != null)
            {
                await animationManager.PlayProgressAnimationAsync(progressBar, e.NewValue);
            }
        }
    }

    #endregion

    #region Helper Methods

    private static void AttachHoverAnimationsToButtons(DependencyObject parent)
    {
        var animationManager = GetAnimationManager();
        if (animationManager == null) return;

        AttachAnimationsToChildButtons(parent, animationManager);
    }

    private static void AttachAnimationsToChildButtons(DependencyObject parent, IAnimationManager animationManager)
    {
        // 使用队列而不是递归，避免深层嵌套时的性能问题
        var queue = new Queue<DependencyObject>();
        queue.Enqueue(parent);

        var processedCount = 0;
        const int maxProcessedElements = 100; // 限制处理的元素数量

        while (queue.Count > 0 && processedCount < maxProcessedElements)
        {
            var current = queue.Dequeue();
            processedCount++;

            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(current); i++)
            {
                var child = VisualTreeHelper.GetChild(current, i);

                if (child is System.Windows.Controls.Button button)
                {
                    animationManager.AttachHoverAnimations(button);
                }
                else if (child is System.Windows.Controls.Panel || child is ContentControl) // 只遍历容器控件
                {
                    queue.Enqueue(child);
                }
            }
        }
    }

    private static IAnimationManager? GetAnimationManager()
    {
        try
        {
            // Get the service provider from the application
            if (System.Windows.Application.Current is App app)
            {
                var serviceProviderField = typeof(App).GetField("_serviceProvider",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                
                if (serviceProviderField?.GetValue(app) is ServiceProvider serviceProvider)
                {
                    return serviceProvider.GetService<IAnimationManager>();
                }
            }
        }
        catch
        {
            // Fallback: try to get from main window if available
            if (System.Windows.Application.Current.MainWindow is Views.MainWindow mainWindow)
            {
                var field = typeof(Views.MainWindow).GetField("_animationManager",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
                return field?.GetValue(mainWindow) as IAnimationManager;
            }
        }

        return null;
    }

    #endregion
}