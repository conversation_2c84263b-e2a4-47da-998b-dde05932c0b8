// 多轮优化器 - 自动迭代优化直到达到目标
class MultiRoundOptimizer {
    constructor() {
        this.targetScore = 15;        // 目标AI检测分数
        this.maxRounds = 5;           // 最大优化轮数
        this.convergenceThreshold = 3; // 收敛阈值（分数变化小于此值认为收敛）
        this.improvementThreshold = 5; // 改进阈值（每轮至少改进5分）
        
        // 优化策略配置
        this.strategies = [
            'basic',           // 基础优化
            'academic',        // 学术优化
            'aggressive',      // 激进优化
            'semantic',        // 语义保持优化
            'hybrid'           // 混合策略
        ];
        
        this.currentStrategy = 0;
    }

    // 主优化方法
    async optimize(originalText, options = {}) {
        const {
            targetScore = this.targetScore,
            maxRounds = this.maxRounds,
            preserveSemantics = true,
            enableLLM = true
        } = options;

        const optimizationHistory = [];
        let currentText = originalText;
        let currentScore = null;
        let round = 0;

        // 初始检测
        try {
            const initialDetection = await this.detectText(currentText, enableLLM);
            currentScore = initialDetection.score;
            
            optimizationHistory.push({
                round: 0,
                text: currentText,
                score: currentScore,
                strategy: 'initial',
                improvements: ['初始文本'],
                processingTime: initialDetection.processingTime || 0
            });

            // 如果已经达到目标，直接返回
            if (currentScore <= targetScore) {
                return this.createSuccessResult(optimizationHistory, '文本已达到目标分数');
            }

        } catch (error) {
            return this.createErrorResult(error, '初始检测失败');
        }

        // 多轮优化循环
        while (round < maxRounds && currentScore > targetScore) {
            round++;
            const startTime = Date.now();

            try {
                // 选择优化策略
                const strategy = this.selectStrategy(round, currentScore, optimizationHistory);
                
                // 执行优化
                const optimizationResult = await this.executeOptimization(
                    currentText, 
                    strategy, 
                    targetScore,
                    enableLLM
                );

                // 检测优化后的文本
                const detectionResult = await this.detectText(optimizationResult.optimizedText, enableLLM);
                const newScore = detectionResult.score;

                // 评估优化效果
                const improvement = currentScore - newScore;
                const processingTime = Date.now() - startTime;

                // 记录本轮结果
                const roundResult = {
                    round: round,
                    text: optimizationResult.optimizedText,
                    score: newScore,
                    strategy: strategy,
                    improvements: optimizationResult.improvements,
                    improvement: improvement,
                    processingTime: processingTime,
                    convergence: Math.abs(improvement) < this.convergenceThreshold
                };

                optimizationHistory.push(roundResult);

                // 检查是否达到目标
                if (newScore <= targetScore) {
                    return this.createSuccessResult(optimizationHistory, `第${round}轮优化成功达到目标`);
                }

                // 检查是否有改进
                if (improvement < this.improvementThreshold && round > 1) {
                    // 尝试更激进的策略
                    if (this.currentStrategy < this.strategies.length - 1) {
                        this.currentStrategy++;
                        console.warn(`改进不足，切换到策略: ${this.strategies[this.currentStrategy]}`);
                    } else {
                        return this.createPartialResult(optimizationHistory, '优化效果不佳，已尝试所有策略');
                    }
                }

                // 检查收敛
                if (roundResult.convergence && round >= 3) {
                    return this.createPartialResult(optimizationHistory, '优化已收敛，无法进一步改进');
                }

                // 更新当前文本和分数
                currentText = optimizationResult.optimizedText;
                currentScore = newScore;

            } catch (error) {
                console.error(`第${round}轮优化失败:`, error);
                
                // 记录失败的轮次
                optimizationHistory.push({
                    round: round,
                    error: error.message,
                    strategy: this.strategies[this.currentStrategy],
                    processingTime: Date.now() - startTime
                });

                // 如果是最后一轮，返回部分结果
                if (round === maxRounds) {
                    return this.createPartialResult(optimizationHistory, '最后一轮优化失败');
                }
            }
        }

        // 达到最大轮数
        if (round >= maxRounds) {
            return this.createPartialResult(optimizationHistory, `已达到最大优化轮数(${maxRounds})`);
        }

        return this.createPartialResult(optimizationHistory, '优化过程异常结束');
    }

    // 选择优化策略
    selectStrategy(round, currentScore, history) {
        // 第一轮使用基础策略
        if (round === 1) {
            return 'basic';
        }

        // 根据当前分数选择策略
        if (currentScore > 70) {
            return 'aggressive';  // 高分数使用激进策略
        } else if (currentScore > 40) {
            return 'academic';    // 中等分数使用学术策略
        } else if (currentScore > 25) {
            return 'semantic';    // 接近目标使用语义保持策略
        } else {
            return 'hybrid';      // 最后使用混合策略
        }
    }

    // 执行优化
    async executeOptimization(text, strategy, targetScore, enableLLM) {
        switch (strategy) {
            case 'basic':
                return await this.basicOptimization(text);
            
            case 'academic':
                return await this.academicOptimization(text);
            
            case 'aggressive':
                return await this.aggressiveOptimization(text, enableLLM);
            
            case 'semantic':
                return await this.semanticOptimization(text);
            
            case 'hybrid':
                return await this.hybridOptimization(text, targetScore, enableLLM);
            
            default:
                throw new Error(`未知的优化策略: ${strategy}`);
        }
    }

    // 基础优化
    async basicOptimization(text) {
        const result = textOptimizer.optimize(text);
        return {
            optimizedText: result.optimizedText,
            improvements: result.improvements || ['基础同义词替换', '句式调整']
        };
    }

    // 学术优化
    async academicOptimization(text) {
        const result = academicOptimizer.optimize(text);
        return {
            optimizedText: result.optimizedText,
            improvements: result.improvements || ['学术表达优化', '专业术语调整']
        };
    }

    // 激进优化
    async aggressiveOptimization(text, enableLLM) {
        if (enableLLM && llmService.isAvailable) {
            try {
                const result = await llmService.optimizeWithLLM(text, this.targetScore);
                return {
                    optimizedText: result.optimizedText,
                    improvements: result.improvements || ['LLM激进优化']
                };
            } catch (error) {
                console.warn('LLM激进优化失败，降级到规则优化:', error.message);
            }
        }

        // 降级到规则优化
        const basicResult = textOptimizer.optimize(text);
        const academicResult = academicOptimizer.optimize(basicResult.optimizedText);
        
        return {
            optimizedText: academicResult.optimizedText,
            improvements: ['激进规则优化', '多层次改写']
        };
    }

    // 语义保持优化
    async semanticOptimization(text) {
        // 更保守的优化，重点保持语义
        const options = {
            synonymReplacement: true,
            templateReplacement: false,  // 不替换模板
            technicalEnhancement: false, // 不增强术语
            structureOptimization: true,
            rigorEnhancement: true
        };

        const result = academicOptimizer.optimize(text, options);
        return {
            optimizedText: result.optimizedText,
            improvements: ['语义保持优化', '保守式改写']
        };
    }

    // 混合优化
    async hybridOptimization(text, targetScore, enableLLM) {
        let currentText = text;
        const improvements = [];

        // 1. 先进行学术优化
        const academicResult = academicOptimizer.optimize(currentText);
        currentText = academicResult.optimizedText;
        improvements.push('学术优化');

        // 2. 再进行基础优化
        const basicResult = textOptimizer.optimize(currentText);
        currentText = basicResult.optimizedText;
        improvements.push('基础优化');

        // 3. 如果启用LLM，进行最终优化
        if (enableLLM && llmService.isAvailable) {
            try {
                const llmResult = await llmService.optimizeWithLLM(currentText, targetScore);
                currentText = llmResult.optimizedText;
                improvements.push('LLM最终优化');
            } catch (error) {
                console.warn('LLM最终优化失败:', error.message);
                improvements.push('LLM优化失败，保持规则优化结果');
            }
        }

        return {
            optimizedText: currentText,
            improvements: improvements
        };
    }

    // 检测文本
    async detectText(text, enableLLM) {
        if (enableLLM && typeof hybridDetector !== 'undefined') {
            return await hybridDetector.detect(text);
        } else {
            return aiDetector.detectAI(text);
        }
    }

    // 创建成功结果
    createSuccessResult(history, message) {
        const finalResult = history[history.length - 1];
        return {
            success: true,
            finalScore: finalResult.score,
            finalText: finalResult.text,
            rounds: history.length - 1,
            message: message,
            history: history,
            totalProcessingTime: history.reduce((sum, h) => sum + (h.processingTime || 0), 0),
            averageImprovement: this.calculateAverageImprovement(history)
        };
    }

    // 创建部分成功结果
    createPartialResult(history, message) {
        const finalResult = history[history.length - 1];
        return {
            success: false,
            finalScore: finalResult.score,
            finalText: finalResult.text,
            rounds: history.length - 1,
            message: message,
            history: history,
            totalProcessingTime: history.reduce((sum, h) => sum + (h.processingTime || 0), 0),
            averageImprovement: this.calculateAverageImprovement(history)
        };
    }

    // 创建错误结果
    createErrorResult(error, message) {
        return {
            success: false,
            error: error.message,
            message: message,
            history: [],
            rounds: 0
        };
    }

    // 计算平均改进
    calculateAverageImprovement(history) {
        if (history.length < 2) return 0;
        
        const improvements = history.slice(1).map(h => h.improvement || 0);
        return improvements.reduce((sum, imp) => sum + imp, 0) / improvements.length;
    }

    // 获取优化建议
    getOptimizationSuggestions(score) {
        if (score > 80) {
            return [
                '文本AI特征非常明显，建议大幅改写',
                '使用更多样化的表达方式',
                '增加人工写作的自然变化'
            ];
        } else if (score > 60) {
            return [
                '存在较多AI特征，需要重点优化',
                '调整句式结构和词汇选择',
                '增强表达的自然性'
            ];
        } else if (score > 30) {
            return [
                '轻微AI特征，适当优化即可',
                '保持现有风格，微调表达',
                '注意保持语义完整性'
            ];
        } else {
            return [
                'AI特征较少，可以放心使用',
                '如需进一步优化，建议保守调整',
                '重点保持原文质量'
            ];
        }
    }
}

// 创建全局实例
const multiRoundOptimizer = new MultiRoundOptimizer();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { MultiRoundOptimizer };
}
