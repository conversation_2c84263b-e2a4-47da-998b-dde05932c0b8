<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>功能修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
    </style>
</head>
<body>
    <h1>AI检测助手功能修复测试</h1>
    
    <!-- 测试1: 复制功能 -->
    <div class="test-section">
        <h2>测试1: 复制功能修复</h2>
        <p>测试学术智能优化的复制功能是否正常工作</p>
        
        <div>
            <!-- 模拟优化结果 -->
            <div id="unifiedOptimizedText" style="background: white; padding: 15px; border-radius: 8px; margin: 10px 0; line-height: 1.6; border: 2px solid #28a745;">
                这是一段测试用的优化后文本内容。人工智能技术在学术研究领域的应用日益广泛，为研究者提供了强有力的工具支持。通过深度学习算法，我们能够更好地分析复杂的数据模式，从而获得更加准确的研究结论。
            </div>
            
            <button class="test-button" onclick="testCopyFunction(event)">
                <i class="fas fa-copy"></i> 测试复制功能
            </button>
        </div>
        
        <div id="copyTestResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 测试2: AI检测结果展示 -->
    <div class="test-section">
        <h2>测试2: AI检测结果展示优化</h2>
        <p>测试新的AI检测结果展示界面</p>
        
        <button class="test-button" onclick="testDetectionDisplay()">
            测试AI检测结果展示
        </button>
        
        <div id="detectResult" style="display: none;">
            <!-- 这里会显示新的检测结果界面 -->
        </div>
    </div>
    
    <!-- 测试3: 通知系统 -->
    <div class="test-section">
        <h2>测试3: 通知系统</h2>
        <p>测试新的通知系统是否正常工作</p>
        
        <button class="test-button" onclick="testNotifications('success')">
            测试成功通知
        </button>
        <button class="test-button" onclick="testNotifications('error')">
            测试错误通知
        </button>
        <button class="test-button" onclick="testNotifications('info')">
            测试信息通知
        </button>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/js/all.min.js"></script>
    <script src="js/ai_detector.js"></script>
    <script src="js/academic_optimizer.js"></script>
    <script src="js/unified_academic_optimizer.js"></script>
    <script src="js/zhuque_optimizer.js"></script>
    <script src="js/hybrid_detector.js"></script>
    <script src="js/multi_round_optimizer.js"></script>
    <script src="js/prompt_templates.js"></script>
    <script src="js/ollama_manager_v2.js"></script>
    <script src="js/main.js"></script>

    <script>
        function log(message, type = 'info') {
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }

        // 测试复制功能
        function testCopyFunction(event) {
            const resultDiv = document.getElementById('copyTestResult');
            resultDiv.style.display = 'block';
            resultDiv.innerHTML = '<div class="info">正在测试复制功能...</div>';
            
            try {
                // 调用修复后的复制函数
                copyUnifiedOptimizationResult(event);
                
                setTimeout(() => {
                    resultDiv.innerHTML = '<div class="success">✅ 复制功能测试完成！请检查：\n1. 按钮是否显示"复制中..."状态\n2. 是否显示成功通知\n3. 按钮是否恢复原状\n4. 剪贴板是否包含正确内容</div>';
                }, 1000);
                
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 复制功能测试失败: ${error.message}</div>`;
            }
        }

        // 测试AI检测结果展示
        function testDetectionDisplay() {
            // 模拟检测结果数据
            const mockResult = {
                aiProbability: 65,
                score: 65,
                confidence: 0.85,
                mode: 'zhuque_enhanced',
                recommendation: '建议使用学术专业优化功能进行深度改写',
                zhuqueAnalysis: {
                    analysis: {
                        perplexity: { score: 72, details: { adjustedPerplexity: 3.45 } },
                        structural: { score: 68, details: { consistencyRatio: 0.78 } },
                        semantic: { score: 61, details: { lexicalDiversity: 0.65 } },
                        frequency: { score: 59, details: { entropy: 4.23 } }
                    },
                    technicalDetails: {
                        method: '朱雀四维度分析',
                        features: ['困惑度', '结构化', '语义一致性', '频域特征'],
                        confidence_interval: [0.58, 0.72]
                    }
                },
                evidence: [
                    '检测到典型的AI生成句式结构',
                    '词汇使用模式符合AI生成特征',
                    '句子长度分布过于规律'
                ],
                details: [
                    '困惑度分析显示文本复杂度偏低',
                    '语义一致性过高，缺乏人类写作的自然变化',
                    '频域分析发现字符分布异常'
                ]
            };

            // 调用显示函数
            displayDetectionResult(mockResult);
            
            log('AI检测结果展示测试完成', 'success');
        }

        // 测试通知系统
        function testNotifications(type) {
            const messages = {
                success: '这是一个成功通知示例',
                error: '这是一个错误通知示例',
                info: '这是一个信息通知示例'
            };
            
            showNotification(messages[type], type);
            log(`${type} 通知测试完成`, 'info');
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('功能修复测试页面加载完成', 'info');
            
            // 检查关键函数是否存在
            const functions = [
                'copyUnifiedOptimizationResult',
                'displayDetectionResult', 
                'showNotification',
                'updateMainScore',
                'updatePieChart',
                'updateBreakdown',
                'updateProgressBars'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ ${funcName} 函数已加载`, 'success');
                } else {
                    log(`❌ ${funcName} 函数未找到`, 'error');
                }
            });
        });
    </script>
</body>
</html>
