namespace DocxToMarkdownConverter.Services.Formula.Models;

/// <summary>
/// 公式类型枚举
/// </summary>
public enum FormulaType
{
    /// <summary>
    /// 未知类型
    /// </summary>
    Unknown = 0,
    
    /// <summary>
    /// Office Math公式
    /// </summary>
    OfficeMath = 1,
    
    /// <summary>
    /// 分数
    /// </summary>
    Fraction = 2,
    
    /// <summary>
    /// 根式
    /// </summary>
    Radical = 3,
    
    /// <summary>
    /// 上标
    /// </summary>
    Superscript = 4,
    
    /// <summary>
    /// 下标
    /// </summary>
    Subscript = 5,
    
    /// <summary>
    /// 上下标组合
    /// </summary>
    SubSuperscript = 6,
    
    /// <summary>
    /// N元运算符（求和、积分等）
    /// </summary>
    Nary = 7,
    
    /// <summary>
    /// 分隔符
    /// </summary>
    Delimiter = 8,
    
    /// <summary>
    /// 矩阵
    /// </summary>
    Matrix = 9,
    
    /// <summary>
    /// 分段函数
    /// </summary>
    PiecewiseFunction = 10,
    
    /// <summary>
    /// 数学函数
    /// </summary>
    MathFunction = 11,
    
    /// <summary>
    /// 重音符号
    /// </summary>
    Accent = 12,
    
    /// <summary>
    /// 边框
    /// </summary>
    BorderBox = 13,
    
    /// <summary>
    /// 组合字符
    /// </summary>
    GroupCharacter = 14,
    
    /// <summary>
    /// 文本
    /// </summary>
    Text = 15,
    
    /// <summary>
    /// 运行（Run）
    /// </summary>
    Run = 16,
    
    /// <summary>
    /// 复合公式
    /// </summary>
    Composite = 17
}

/// <summary>
/// 公式输出格式枚举
/// </summary>
public enum FormulaOutputFormat
{
    /// <summary>
    /// LaTeX格式
    /// </summary>
    LaTeX = 0,
    
    /// <summary>
    /// MathML格式
    /// </summary>
    MathML = 1,
    
    /// <summary>
    /// AsciiMath格式
    /// </summary>
    AsciiMath = 2,
    
    /// <summary>
    /// 纯文本格式
    /// </summary>
    PlainText = 3,
    
    /// <summary>
    /// Unicode数学符号
    /// </summary>
    Unicode = 4
}

/// <summary>
/// 公式复杂度枚举
/// </summary>
public enum FormulaComplexity
{
    /// <summary>
    /// 简单
    /// </summary>
    Simple = 0,
    
    /// <summary>
    /// 中等
    /// </summary>
    Medium = 1,
    
    /// <summary>
    /// 复杂
    /// </summary>
    Complex = 2,
    
    /// <summary>
    /// 非常复杂
    /// </summary>
    VeryComplex = 3
}

/// <summary>
/// 公式错误严重程度枚举
/// </summary>
public enum FormulaErrorSeverity
{
    /// <summary>
    /// 信息
    /// </summary>
    Info = 0,
    
    /// <summary>
    /// 警告
    /// </summary>
    Warning = 1,
    
    /// <summary>
    /// 错误
    /// </summary>
    Error = 2,
    
    /// <summary>
    /// 严重错误
    /// </summary>
    Critical = 3
}

/// <summary>
/// 分数类型枚举
/// </summary>
public enum FractionType
{
    /// <summary>
    /// 标准分数线
    /// </summary>
    Bar = 0,
    
    /// <summary>
    /// 斜线分数
    /// </summary>
    Skewed = 1,
    
    /// <summary>
    /// 线性分数（a/b形式）
    /// </summary>
    Linear = 2,
    
    /// <summary>
    /// 无分数线
    /// </summary>
    NoBar = 3
}

/// <summary>
/// 矩阵对齐方式枚举
/// </summary>
public enum MatrixAlignment
{
    /// <summary>
    /// 居中对齐
    /// </summary>
    Center = 0,
    
    /// <summary>
    /// 左对齐
    /// </summary>
    Left = 1,
    
    /// <summary>
    /// 右对齐
    /// </summary>
    Right = 2,
    
    /// <summary>
    /// 顶部对齐
    /// </summary>
    Top = 3,
    
    /// <summary>
    /// 底部对齐
    /// </summary>
    Bottom = 4
}

/// <summary>
/// N元运算符类型枚举
/// </summary>
public enum NaryOperatorType
{
    /// <summary>
    /// 求和
    /// </summary>
    Sum = 0,
    
    /// <summary>
    /// 乘积
    /// </summary>
    Product = 1,
    
    /// <summary>
    /// 余积
    /// </summary>
    Coproduct = 2,
    
    /// <summary>
    /// 积分
    /// </summary>
    Integral = 3,
    
    /// <summary>
    /// 环积分
    /// </summary>
    ContourIntegral = 4,
    
    /// <summary>
    /// 二重积分
    /// </summary>
    DoubleIntegral = 5,
    
    /// <summary>
    /// 三重积分
    /// </summary>
    TripleIntegral = 6,
    
    /// <summary>
    /// 并集
    /// </summary>
    Union = 7,
    
    /// <summary>
    /// 交集
    /// </summary>
    Intersection = 8,
    
    /// <summary>
    /// 自定义
    /// </summary>
    Custom = 9
}

/// <summary>
/// 分隔符类型枚举
/// </summary>
public enum DelimiterType
{
    /// <summary>
    /// 圆括号
    /// </summary>
    Parentheses = 0,
    
    /// <summary>
    /// 方括号
    /// </summary>
    Brackets = 1,
    
    /// <summary>
    /// 花括号
    /// </summary>
    Braces = 2,
    
    /// <summary>
    /// 绝对值
    /// </summary>
    AbsoluteValue = 3,
    
    /// <summary>
    /// 范数
    /// </summary>
    Norm = 4,
    
    /// <summary>
    /// 角括号
    /// </summary>
    AngleBrackets = 5,
    
    /// <summary>
    /// 向上取整
    /// </summary>
    Ceiling = 6,
    
    /// <summary>
    /// 向下取整
    /// </summary>
    Floor = 7,
    
    /// <summary>
    /// 自定义
    /// </summary>
    Custom = 8
}

/// <summary>
/// 公式显示模式枚举
/// </summary>
public enum FormulaDisplayMode
{
    /// <summary>
    /// 自动检测
    /// </summary>
    Auto = 0,
    
    /// <summary>
    /// 内联模式
    /// </summary>
    Inline = 1,
    
    /// <summary>
    /// 显示模式（块级）
    /// </summary>
    Display = 2
}

/// <summary>
/// 公式处理模式枚举
/// </summary>
public enum FormulaProcessingMode
{
    /// <summary>
    /// 完整处理
    /// </summary>
    Full = 0,
    
    /// <summary>
    /// 快速处理
    /// </summary>
    Fast = 1,
    
    /// <summary>
    /// 仅检测
    /// </summary>
    DetectionOnly = 2,
    
    /// <summary>
    /// 仅提取
    /// </summary>
    ExtractionOnly = 3
}
