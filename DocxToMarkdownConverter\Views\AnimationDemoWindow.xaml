<Window x:Class="DocxToMarkdownConverter.Views.AnimationDemoWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:behaviors="clr-namespace:DocxToMarkdownConverter.Behaviors"
        Title="Animation Demo" 
        Height="600" Width="800"
        WindowStartupLocation="CenterScreen"
        Background="{DynamicResource MaterialDesignPaper}"
        FontFamily="{DynamicResource MaterialDesignFont}">

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <materialDesign:Card Grid.Row="0" Padding="20" Margin="0,0,0,20">
            <StackPanel>
                <TextBlock Text="Animation System Demo" 
                           Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,10"/>
                <TextBlock Text="Test all animation effects and transitions" 
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           HorizontalAlignment="Center"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!-- Button Animations -->
                <materialDesign:Card Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Button Hover Animations" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,15"/>
                        
                        <UniformGrid Columns="3" behaviors:AnimationBehaviors.AutoAttachHoverAnimations="True">
                            <Button Content="Primary Button" 
                                    Style="{StaticResource AnimatedPrimaryButtonStyle}"
                                    Margin="5"/>
                            <Button Content="Secondary Button" 
                                    Style="{StaticResource AnimatedSecondaryButtonStyle}"
                                    Margin="5"/>
                            <Button Style="{StaticResource MaterialDesignIconButton}"
                                    Margin="5"
                                    ToolTip="Icon Button">
                                <materialDesign:PackIcon Kind="Heart" Width="24" Height="24"/>
                            </Button>
                        </UniformGrid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Progress Animations -->
                <materialDesign:Card Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Progress Bar Animations" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,15"/>
                        
                        <StackPanel>
                            <TextBlock Text="Animated Progress Bar" Margin="0,0,0,5"/>
                            <ProgressBar x:Name="DemoProgressBar"
                                         Value="0" Maximum="100" Height="8"
                                         Style="{StaticResource MaterialDesignLinearProgressBar}"
                                         behaviors:AnimationBehaviors.EnableProgressAnimation="True"
                                         Margin="0,0,0,10"/>
                            
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <Button Content="25%" Click="SetProgress25_Click" Margin="5"/>
                                <Button Content="50%" Click="SetProgress50_Click" Margin="5"/>
                                <Button Content="75%" Click="SetProgress75_Click" Margin="5"/>
                                <Button Content="100%" Click="SetProgress100_Click" Margin="5"/>
                                <Button Content="Reset" Click="ResetProgress_Click" Margin="5"/>
                            </StackPanel>
                        </StackPanel>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Element Animations -->
                <materialDesign:Card Padding="20" Margin="0,0,0,20">
                    <StackPanel>
                        <TextBlock Text="Element Animations" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,15"/>
                        
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="200"/>
                            </Grid.ColumnDefinitions>
                            
                            <!-- Animation Target -->
                            <Border Grid.Column="0" 
                                    Background="{DynamicResource PrimaryHueLightBrush}"
                                    CornerRadius="10"
                                    Height="100"
                                    Margin="0,0,20,0">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon x:Name="AnimationTarget"
                                                             Kind="Star" 
                                                             Width="48" Height="48"
                                                             Foreground="White"/>
                                    <TextBlock Text="Animation Target" 
                                               Foreground="White"
                                               HorizontalAlignment="Center"
                                               Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>
                            
                            <!-- Animation Controls -->
                            <StackPanel Grid.Column="1">
                                <Button Content="Pulse" Click="PlayPulse_Click" Margin="0,2"/>
                                <Button Content="Shake" Click="PlayShake_Click" Margin="0,2"/>
                                <Button Content="Scale Up" Click="PlayScaleUp_Click" Margin="0,2"/>
                                <Button Content="Scale Down" Click="PlayScaleDown_Click" Margin="0,2"/>
                                <Button Content="Rotate" Click="PlayRotate_Click" Margin="0,2"/>
                                <Button Content="Fade Out" Click="PlayFadeOut_Click" Margin="0,2"/>
                                <Button Content="Fade In" Click="PlayFadeIn_Click" Margin="0,2"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </materialDesign:Card>

                <!-- Page Transition Demo -->
                <materialDesign:Card Padding="20">
                    <StackPanel>
                        <TextBlock Text="Page Transition Demo" 
                                   Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                   Margin="0,0,0,15"/>
                        
                        <Grid Height="150">
                            <Border x:Name="Page1" 
                                    Background="{DynamicResource SecondaryHueLightBrush}"
                                    CornerRadius="5"
                                    Visibility="Visible">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Home" Width="32" Height="32" Foreground="White"/>
                                    <TextBlock Text="Page 1" Foreground="White" FontSize="18" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>
                            
                            <Border x:Name="Page2" 
                                    Background="{DynamicResource PrimaryHueMidBrush}"
                                    CornerRadius="5"
                                    Visibility="Collapsed">
                                <StackPanel HorizontalAlignment="Center" VerticalAlignment="Center">
                                    <materialDesign:PackIcon Kind="Settings" Width="32" Height="32" Foreground="White"/>
                                    <TextBlock Text="Page 2" Foreground="White" FontSize="18" Margin="0,5,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                        
                        <Button Content="Switch Pages" 
                                Click="SwitchPages_Click" 
                                HorizontalAlignment="Center"
                                Margin="0,15,0,0"/>
                    </StackPanel>
                </materialDesign:Card>
            </StackPanel>
        </ScrollViewer>

        <!-- Footer -->
        <materialDesign:Card Grid.Row="2" Padding="15" Margin="0,20,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <CheckBox x:Name="EnableAnimationsCheckBox" 
                          Content="Enable Animations" 
                          IsChecked="True"
                          Checked="EnableAnimations_Changed"
                          Unchecked="EnableAnimations_Changed"
                          Margin="0,0,20,0"/>
                
                <TextBlock Text="Animation Speed:" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <Slider x:Name="AnimationSpeedSlider" 
                        Minimum="0.1" Maximum="3.0" Value="1.0"
                        Width="150"
                        ValueChanged="AnimationSpeed_Changed"/>
                <TextBlock Text="{Binding ElementName=AnimationSpeedSlider, Path=Value, StringFormat='{}{0:F1}x'}" 
                           VerticalAlignment="Center" 
                           Margin="10,0,0,0"/>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</Window>