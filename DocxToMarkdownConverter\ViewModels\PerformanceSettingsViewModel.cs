using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.Logging;
using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;

namespace DocxToMarkdownConverter.ViewModels;

/// <summary>
/// 性能设置视图模型
/// </summary>
public class PerformanceSettingsViewModel : ViewModelBase, IDisposable
{
    private readonly IConfigurationService _configurationService;
    private readonly IAnimationManager _animationManager;
    private readonly ILogger<PerformanceSettingsViewModel> _logger;
    private PerformanceSettings _performanceSettings;
    private bool _disposed = false;
    private bool _isResetting = false;
    private bool _isSaving = false;

    public PerformanceSettingsViewModel(
        IConfigurationService configurationService,
        IAnimationManager animationManager,
        ILogger<PerformanceSettingsViewModel> logger)
    {
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        _animationManager = animationManager ?? throw new ArgumentNullException(nameof(animationManager));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));

        // 加载性能设置
        _performanceSettings = LoadPerformanceSettingsAsync().GetAwaiter().GetResult();
        
        // 订阅设置变更事件
        _performanceSettings.PropertyChanged += OnPerformanceSettingsChanged;

        // 初始化命令
        ResetPerformanceSettingsCommand = new RelayCommand(ExecuteResetPerformanceSettings, CanExecuteResetPerformanceSettings);
        SavePerformanceSettingsCommand = new RelayCommand(ExecuteSavePerformanceSettings, CanExecuteSavePerformanceSettings);

        // 应用当前设置
        ApplyPerformanceSettings();

        _logger.LogInformation("性能设置视图模型已初始化");
    }

    #region 属性

    /// <summary>
    /// 性能设置
    /// </summary>
    public PerformanceSettings PerformanceSettings
    {
        get => _performanceSettings;
        set => SetProperty(ref _performanceSettings, value);
    }

    /// <summary>
    /// 是否正在重置设置
    /// </summary>
    public bool IsResetting
    {
        get => _isResetting;
        set
        {
            if (SetProperty(ref _isResetting, value))
            {
                // 通知命令状态更新
                System.Windows.Input.CommandManager.InvalidateRequerySuggested();
            }
        }
    }

    /// <summary>
    /// 是否正在保存设置
    /// </summary>
    public bool IsSaving
    {
        get => _isSaving;
        set
        {
            if (SetProperty(ref _isSaving, value))
            {
                // 通知命令状态更新
                System.Windows.Input.CommandManager.InvalidateRequerySuggested();
            }
        }
    }

    #endregion

    #region 命令

    /// <summary>
    /// 重置性能设置命令
    /// </summary>
    public ICommand ResetPerformanceSettingsCommand { get; }

    /// <summary>
    /// 保存性能设置命令
    /// </summary>
    public ICommand SavePerformanceSettingsCommand { get; }

    #endregion

    #region 私有方法

    /// <summary>
    /// 加载性能设置
    /// </summary>
    private async Task<PerformanceSettings> LoadPerformanceSettingsAsync()
    {
        try
        {
            return await _configurationService.LoadSettingsAsync<PerformanceSettings>();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "加载性能设置失败，使用默认设置");
            return new PerformanceSettings();
        }
    }

    /// <summary>
    /// 性能设置变更事件处理
    /// </summary>
    private async void OnPerformanceSettingsChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        try
        {
            // 保存设置到配置文件
            await _configurationService.SaveSettingsAsync(_performanceSettings);
            
            // 应用新的性能设置
            ApplyPerformanceSettings();
            
            _logger.LogDebug("性能设置已更新: {PropertyName}", e.PropertyName);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存性能设置时发生错误");
        }
    }

    /// <summary>
    /// 应用性能设置到动画管理器
    /// </summary>
    private void ApplyPerformanceSettings()
    {
        try
        {
            // 应用动画设置
            _animationManager.EnableAnimations(_performanceSettings.EnableAnimations);
            _animationManager.SetAnimationSpeed(_performanceSettings.AnimationSpeed);

            // 应用硬件加速设置
            if (_performanceSettings.UseHardwareAcceleration)
            {
                System.Windows.Media.RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.Default;
            }
            else
            {
                System.Windows.Media.RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.SoftwareOnly;
            }

            _logger.LogInformation("性能设置已应用: 动画={EnableAnimations}, 速度={AnimationSpeed}, 硬件加速={UseHardwareAcceleration}",
                _performanceSettings.EnableAnimations,
                _performanceSettings.AnimationSpeed,
                _performanceSettings.UseHardwareAcceleration);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "应用性能设置时发生错误");
        }
    }

    /// <summary>
    /// 检查是否可以执行重置命令
    /// </summary>
    private bool CanExecuteResetPerformanceSettings()
    {
        return !_isResetting && !_isSaving;
    }

    /// <summary>
    /// 检查是否可以执行保存命令
    /// </summary>
    private bool CanExecuteSavePerformanceSettings()
    {
        return !_isResetting && !_isSaving;
    }

    /// <summary>
    /// 执行重置性能设置命令
    /// </summary>
    private async void ExecuteResetPerformanceSettings()
    {
        if (_isResetting || _isSaving) return;

        try
        {
            // 显示确认对话框
            var result = System.Windows.MessageBox.Show(
                "确定要将所有性能设置重置为默认值吗？\n\n这将覆盖您当前的所有自定义设置。",
                "确认重置",
                System.Windows.MessageBoxButton.YesNo,
                System.Windows.MessageBoxImage.Question);

            if (result != System.Windows.MessageBoxResult.Yes)
            {
                _logger.LogInformation("用户取消了重置操作");
                return;
            }

            IsResetting = true;
            _logger.LogInformation("重置性能设置为默认值");

            // 取消订阅以避免重复保存
            _performanceSettings.PropertyChanged -= OnPerformanceSettingsChanged;

            // 重置设置
            _performanceSettings.ResetToDefaults();

            // 重新订阅
            _performanceSettings.PropertyChanged += OnPerformanceSettingsChanged;

            // 保存并应用设置
            await _configurationService.SaveSettingsAsync(_performanceSettings);
            ApplyPerformanceSettings();

            // 通知UI更新
            OnPropertyChanged(nameof(PerformanceSettings));

            // 显示成功消息
            System.Windows.MessageBox.Show(
                "性能设置已成功重置为默认值。\n\n应用程序将使用推荐的性能配置。",
                "重置成功",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Information);

            _logger.LogInformation("性能设置已重置为默认值");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "重置性能设置时发生错误");

            // 显示错误消息
            System.Windows.MessageBox.Show(
                $"重置性能设置时发生错误：\n\n{ex.Message}",
                "重置失败",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Error);
        }
        finally
        {
            IsResetting = false;
        }
    }

    /// <summary>
    /// 执行保存性能设置命令
    /// </summary>
    private async void ExecuteSavePerformanceSettings()
    {
        if (_isResetting || _isSaving) return;

        try
        {
            IsSaving = true;
            _logger.LogInformation("手动保存性能设置");

            // 保存设置到配置文件
            await _configurationService.SaveSettingsAsync(_performanceSettings);

            // 应用设置
            ApplyPerformanceSettings();

            // 显示成功消息
            System.Windows.MessageBox.Show(
                "性能设置已成功保存。",
                "保存成功",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Information);

            _logger.LogInformation("性能设置已手动保存");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存性能设置时发生错误");

            // 显示错误消息
            System.Windows.MessageBox.Show(
                $"保存性能设置时发生错误：\n\n{ex.Message}",
                "保存失败",
                System.Windows.MessageBoxButton.OK,
                System.Windows.MessageBoxImage.Error);
        }
        finally
        {
            IsSaving = false;
        }
    }

    #endregion

    #region IDisposable

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 取消订阅事件
                if (_performanceSettings != null)
                {
                    _performanceSettings.PropertyChanged -= OnPerformanceSettingsChanged;
                }
            }
            _disposed = true;
        }
    }

    public new void Dispose()
    {
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }

    #endregion
}
