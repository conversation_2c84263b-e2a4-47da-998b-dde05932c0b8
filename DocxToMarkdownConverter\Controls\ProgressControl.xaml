<UserControl x:Class="DocxToMarkdownConverter.Controls.ProgressControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:local="clr-namespace:DocxToMarkdownConverter.Controls"
             xmlns:behaviors="clr-namespace:DocxToMarkdownConverter.Behaviors"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900">
    
    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 样式 -->
        <Style x:Key="StatisticCardStyle" TargetType="materialDesign:Card">
            <Setter Property="Margin" Value="4"/>
            <Setter Property="Padding" Value="16"/>
            <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        </Style>
        
        <Style x:Key="StatisticIconStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Width" Value="24"/>
            <Setter Property="Height" Value="24"/>
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,8"/>
        </Style>
        
        <Style x:Key="StatisticValueStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline4TextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Margin" Value="0,0,0,4"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>
        
        <Style x:Key="StatisticLabelStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignCaptionTextBlock}">
            <Setter Property="HorizontalAlignment" Value="Center"/>
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        </Style>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 主进度显示区域 -->
        <materialDesign:Card Grid.Row="0" Margin="16" Padding="24"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <StackPanel>
                <!-- 标题 -->
                <TextBlock Text="转换进度" 
                          Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                          Margin="0,0,0,20"
                          HorizontalAlignment="Center"/>
                
                <!-- 当前操作信息 -->
                <Grid Margin="0,0,0,16">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <StackPanel Grid.Column="0">
                        <TextBlock Text="{Binding CurrentOperationText}" 
                                  Style="{StaticResource MaterialDesignBody1TextBlock}"
                                  TextTrimming="CharacterEllipsis"
                                  ToolTip="{Binding CurrentOperationText}"/>
                        
                        <TextBlock Text="{Binding EstimatedTimeText}" 
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  Margin="0,4,0,0"/>
                    </StackPanel>
                    
                    <StackPanel Grid.Column="1" HorizontalAlignment="Right">
                        <TextBlock Text="{Binding OverallProgressText, StringFormat={}{0}%}" 
                                  Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                  Foreground="{DynamicResource PrimaryHueMidBrush}"
                                  HorizontalAlignment="Right"/>
                        
                        <TextBlock Text="{Binding LastUpdateTimeText}" 
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource MaterialDesignBodyLight}"
                                  HorizontalAlignment="Right"/>
                    </StackPanel>
                </Grid>
                
                <!-- 进度条 -->
                <ProgressBar Value="{Binding OverallProgress}"
                            Maximum="100"
                            Height="12"
                            Style="{StaticResource MaterialDesignLinearProgressBar}"
                            materialDesign:TransitionAssist.DisableTransitions="True"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 统计信息卡片 -->
        <UniformGrid Grid.Row="1" Columns="4" Margin="16,0,16,16">
            <!-- 总文件数 -->
            <materialDesign:Card Style="{StaticResource StatisticCardStyle}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="FileMultiple" 
                                           Style="{StaticResource StatisticIconStyle}"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                    <TextBlock Text="{Binding TotalFiles}" 
                              Style="{StaticResource StatisticValueStyle}"/>
                    <TextBlock Text="总文件数" 
                              Style="{StaticResource StatisticLabelStyle}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 已完成 -->
            <materialDesign:Card Style="{StaticResource StatisticCardStyle}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="CheckCircle" 
                                           Style="{StaticResource StatisticIconStyle}"
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="{Binding CompletedFiles}" 
                              Style="{StaticResource StatisticValueStyle}"
                              Foreground="{DynamicResource SecondaryHueMidBrush}"/>
                    <TextBlock Text="已完成" 
                              Style="{StaticResource StatisticLabelStyle}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 失败数 -->
            <materialDesign:Card Style="{StaticResource StatisticCardStyle}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Style="{StaticResource StatisticIconStyle}"
                                           Foreground="{DynamicResource ValidationErrorBrush}"/>
                    <TextBlock Text="{Binding FailedFiles}" 
                              Style="{StaticResource StatisticValueStyle}"
                              Foreground="{DynamicResource ValidationErrorBrush}"/>
                    <TextBlock Text="失败数" 
                              Style="{StaticResource StatisticLabelStyle}"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- 成功率 -->
            <materialDesign:Card Style="{StaticResource StatisticCardStyle}">
                <StackPanel>
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Style="{StaticResource StatisticIconStyle}"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="{Binding SuccessRateText}" 
                              Style="{StaticResource StatisticValueStyle}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="成功率" 
                              Style="{StaticResource StatisticLabelStyle}"/>
                </StackPanel>
            </materialDesign:Card>
        </UniformGrid>

        <!-- 日志和详细信息区域 -->
        <materialDesign:Card Grid.Row="2" Margin="16,0,16,16" Padding="0"
                           materialDesign:ElevationAssist.Elevation="Dp2">
            <TabControl Style="{StaticResource MaterialDesignTabControl}">
                <!-- 简单日志视图 -->
                <TabItem Header="转换日志" Style="{StaticResource MaterialDesignTabItem}">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- 日志工具栏 -->
                        <Grid Grid.Row="0" Margin="0,0,0,12">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                      Text="实时转换日志"
                                      Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <CheckBox Content="自动滚动"
                                         IsChecked="{Binding AutoScrollEnabled}"
                                         Style="{StaticResource MaterialDesignCheckBox}"
                                         Margin="0,0,16,0"/>

                                <Button Content="清空"
                                       Command="{Binding ClearLogCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}"
                                       Margin="0,0,8,0">
                                    <Button.ToolTip>
                                        <ToolTip Content="清空所有日志内容"/>
                                    </Button.ToolTip>
                                </Button>

                                <Button Content="保存"
                                       Command="{Binding SaveLogCommand}"
                                       Style="{StaticResource MaterialDesignOutlinedButton}">
                                    <Button.ToolTip>
                                        <ToolTip Content="将日志保存到文件"/>
                                    </Button.ToolTip>
                                </Button>
                            </StackPanel>
                        </Grid>

                        <!-- 日志内容 -->
                        <Border Grid.Row="1"
                               BorderBrush="{DynamicResource MaterialDesignDivider}"
                               BorderThickness="1"
                               CornerRadius="4"
                               Background="{DynamicResource MaterialDesignCardBackground}">
                            <ScrollViewer x:Name="LogScrollViewer"
                                         VerticalScrollBarVisibility="Auto"
                                         HorizontalScrollBarVisibility="Auto"
                                         Padding="8">
                                <TextBox Text="{Binding LogContent, Mode=OneWay}"
                                        IsReadOnly="True"
                                        FontFamily="Consolas, Courier New, monospace"
                                        FontSize="11"
                                        Background="Transparent"
                                        BorderThickness="0"
                                        TextWrapping="NoWrap"
                                        MinHeight="200"
                                        AcceptsReturn="True"
                                        VerticalScrollBarVisibility="Disabled"
                                        HorizontalScrollBarVisibility="Disabled"/>
                            </ScrollViewer>
                        </Border>

                        <!-- 日志统计 -->
                        <Grid Grid.Row="2" Margin="0,8,0,0">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0"
                                      Text="{Binding LogStatisticsText}"
                                      Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                      Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <materialDesign:PackIcon Kind="Update" 
                                                       Width="12" Height="12"
                                                       VerticalAlignment="Center"
                                                       Foreground="{DynamicResource MaterialDesignBodyLight}"
                                                       Margin="0,0,4,0"/>
                                <TextBlock Text="{Binding LastUpdateTimeText}"
                                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </TabItem>

                <!-- 详细日志视图 -->
                <TabItem Header="详细日志" Style="{StaticResource MaterialDesignTabItem}">
                    <local:LogViewerControl x:Name="DetailedLogViewer" Margin="8"/>
                </TabItem>

                <!-- 性能统计 -->
                <TabItem Header="性能统计" Style="{StaticResource MaterialDesignTabItem}">
                    <Grid Margin="16">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0"
                                  Text="转换性能统计"
                                  Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                  Margin="0,0,0,16"/>

                        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto">
                            <StackPanel>
                                <!-- 时间统计 -->
                                <materialDesign:Card Margin="0,0,0,12" Padding="16">
                                    <StackPanel>
                                        <TextBlock Text="时间统计"
                                                  Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                  Margin="0,0,0,8"/>
                                        
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>
                                            
                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="{Binding Statistics.TotalBatchTime, StringFormat=总耗时: {0:hh\\:mm\\:ss}}"
                                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                <TextBlock Text="{Binding Statistics.AverageProcessingTime, StringFormat=平均耗时: {0:ss\\.ff}秒}"
                                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                            
                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="{Binding Statistics.StartTime, StringFormat=开始时间: {0:HH:mm:ss}}"
                                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                                <TextBlock Text="{Binding Statistics.EndTime, StringFormat=结束时间: {0:HH:mm:ss}}"
                                                          Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>
                                        </Grid>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- 文件统计 -->
                                <materialDesign:Card Margin="0,0,0,12" Padding="16">
                                    <StackPanel>
                                        <TextBlock Text="文件统计"
                                                  Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                  Margin="0,0,0,8"/>
                                        
                                        <UniformGrid Columns="2">
                                            <TextBlock Text="{Binding Statistics.TotalFilesProcessed, StringFormat=处理文件: {0}}"
                                                      Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            <TextBlock Text="{Binding Statistics.SuccessfulConversions, StringFormat=成功转换: {0}}"
                                                      Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            <TextBlock Text="{Binding Statistics.FailedConversions, StringFormat=转换失败: {0}}"
                                                      Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            <TextBlock Text="{Binding Statistics.SuccessRate, StringFormat=成功率: {0:F1}%}"
                                                      Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        </UniformGrid>
                                    </StackPanel>
                                </materialDesign:Card>

                                <!-- 处理速率 -->
                                <materialDesign:Card Padding="16">
                                    <StackPanel>
                                        <TextBlock Text="处理速率"
                                                  Style="{StaticResource MaterialDesignSubtitle2TextBlock}"
                                                  Margin="0,0,0,8"/>
                                        
                                        <TextBlock Text="{Binding ProcessingRateText}"
                                                  Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                        <TextBlock Text="{Binding ThroughputText}"
                                                  Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                    </StackPanel>
                                </materialDesign:Card>
                            </StackPanel>
                        </ScrollViewer>
                    </Grid>
                </TabItem>
            </TabControl>
        </materialDesign:Card>

        <!-- 控制按钮区域 -->
        <materialDesign:Card Grid.Row="3" Margin="16" Padding="16"
                           materialDesign:ElevationAssist.Elevation="Dp1">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Command="{Binding StartConversionCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,12,0"
                       IsEnabled="{Binding StartConversionCommand.CanExecute}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Play"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="开始转换" VerticalAlignment="Center"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Content="开始转换选定的文件"/>
                    </Button.ToolTip>
                </Button>

                <Button Command="{Binding PauseConversionCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,12,0"
                       IsEnabled="{Binding PauseConversionCommand.CanExecute}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Pause"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="暂停转换" VerticalAlignment="Center"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Content="暂停当前转换操作"/>
                    </Button.ToolTip>
                </Button>

                <Button Command="{Binding StopConversionCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       IsEnabled="{Binding StopConversionCommand.CanExecute}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Stop"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="停止转换" VerticalAlignment="Center"/>
                    </StackPanel>
                    <Button.ToolTip>
                        <ToolTip Content="停止所有转换操作"/>
                    </Button.ToolTip>
                </Button>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>