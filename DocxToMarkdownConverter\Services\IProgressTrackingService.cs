using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 进度跟踪服务接口
/// </summary>
public interface IProgressTrackingService
{
    /// <summary>
    /// 开始跟踪单个文件转换
    /// </summary>
    void StartFileConversion(string fileName);

    /// <summary>
    /// 更新单个文件转换进度
    /// </summary>
    void UpdateFileProgress(string fileName, string operation, double progress);

    /// <summary>
    /// 完成单个文件转换
    /// </summary>
    void CompleteFileConversion(string fileName, bool success, string? errorMessage = null);

    /// <summary>
    /// 开始批量转换跟踪
    /// </summary>
    void StartBatchConversion(int totalFiles);

    /// <summary>
    /// 更新批量转换进度
    /// </summary>
    void UpdateBatchProgress();

    /// <summary>
    /// 完成批量转换
    /// </summary>
    void CompleteBatchConversion();

    /// <summary>
    /// 重置所有进度
    /// </summary>
    void Reset();

    /// <summary>
    /// 获取当前转换进度
    /// </summary>
    ConversionProgress? GetCurrentProgress();

    /// <summary>
    /// 获取批量转换进度
    /// </summary>
    BatchConversionProgress? GetBatchProgress();

    /// <summary>
    /// 获取转换统计信息
    /// </summary>
    ConversionStatistics GetStatistics();

    /// <summary>
    /// 进度更新事件
    /// </summary>
    event EventHandler<ConversionProgress> ProgressUpdated;

    /// <summary>
    /// 批量进度更新事件
    /// </summary>
    event EventHandler<BatchConversionProgress> BatchProgressUpdated;

    /// <summary>
    /// 统计信息更新事件
    /// </summary>
    event EventHandler<ConversionStatistics> StatisticsUpdated;
}