<Window x:Class="DocxToMarkdownConverter.Views.HelpWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="帮助 - DOCX to Markdown Converter" 
        Height="600" Width="800"
        WindowStartupLocation="CenterOwner"
        Icon="../Resources/AppIcon.ico">
    
    <Window.Resources>
        <Style x:Key="SectionHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="18"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="Margin" Value="0,20,0,10"/>
            <Setter Property="Foreground" Value="{DynamicResource PrimaryBrush}"/>
        </Style>
        
        <Style x:Key="SubHeaderStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="Margin" Value="0,15,0,5"/>
        </Style>
        
        <Style x:Key="ContentStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="0,5,0,5"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
            <Setter Property="LineHeight" Value="18"/>
        </Style>
        
        <Style x:Key="ListItemStyle" TargetType="TextBlock">
            <Setter Property="FontSize" Value="12"/>
            <Setter Property="Margin" Value="20,2,0,2"/>
            <Setter Property="TextWrapping" Value="Wrap"/>
        </Style>
    </Window.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>
        
        <!-- 标题栏 -->
        <Border Grid.Row="0" Background="{DynamicResource PrimaryBrush}" Padding="20,15">
            <StackPanel>
                <TextBlock Text="DOCX to Markdown Converter" 
                          FontSize="24" FontWeight="Bold" 
                          Foreground="White"/>
                <TextBlock Text="用户帮助和快速指南" 
                          FontSize="14" 
                          Foreground="White" Opacity="0.9"/>
            </StackPanel>
        </Border>
        
        <!-- 主要内容 -->
        <ScrollViewer Grid.Row="1" Padding="30" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                
                <!-- 快速开始 -->
                <TextBlock Text="快速开始" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBlock Style="{StaticResource ContentStyle}">
                    欢迎使用 DOCX to Markdown Converter！按照以下步骤快速开始：
                </TextBlock>
                <TextBlock Text="• 点击&quot;添加文件&quot;按钮选择要转换的 DOCX 文件" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• 在设置页面配置转换选项（可选）" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• 点击&quot;开始转换&quot;按钮开始处理" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• 在结果页面查看转换结果和输出文件" Style="{StaticResource ListItemStyle}"/>
                
                <!-- 主要功能 -->
                <TextBlock Text="主要功能" Style="{StaticResource SectionHeaderStyle}"/>
                
                <TextBlock Text="批量转换" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="支持同时转换多个 DOCX 文件，大大提高工作效率。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="图片提取" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="自动提取文档中的图片并保存为独立文件，在 Markdown 中正确引用。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="表格转换" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="将 Word 表格转换为标准的 Markdown 表格格式。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="格式保留" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="保持文本格式（粗体、斜体）、标题层级和列表结构。" Style="{StaticResource ContentStyle}"/>
                
                <!-- 转换选项 -->
                <TextBlock Text="转换选项说明" Style="{StaticResource SectionHeaderStyle}"/>
                
                <TextBlock Text="输出目录" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="指定转换后的 Markdown 文件保存位置。默认与源文件相同目录。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="提取图片" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="启用后会将文档中的图片提取为独立文件，并在 Markdown 中创建正确的图片链接。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="转换表格" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="将 Word 表格转换为 Markdown 表格语法。复杂表格可能需要手动调整。" Style="{StaticResource ContentStyle}"/>
                
                <TextBlock Text="处理公式" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Text="将数学公式转换为 LaTeX 格式，便于在支持数学公式的 Markdown 编辑器中显示。" Style="{StaticResource ContentStyle}"/>
                
                <!-- 键盘快捷键 -->
                <TextBlock Text="键盘快捷键" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBlock Text="• Ctrl+O: 添加文件" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• Ctrl+S: 开始转换" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• Ctrl+D: 清空文件列表" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• F1: 显示帮助" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• F11: 切换全屏模式" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• Ctrl+T: 切换主题" Style="{StaticResource ListItemStyle}"/>
                
                <!-- 故障排除 -->
                <TextBlock Text="常见问题" Style="{StaticResource SectionHeaderStyle}"/>
                
                <TextBlock Text="转换失败怎么办？" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Style="{StaticResource ContentStyle}">
                    • 检查文件是否为有效的 DOCX 格式<LineBreak/>
                    • 确认文件没有被其他程序占用<LineBreak/>
                    • 检查输出目录是否有写入权限<LineBreak/>
                    • 尝试用 Microsoft Word 打开文件验证完整性
                </TextBlock>
                
                <TextBlock Text="程序运行缓慢？" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Style="{StaticResource ContentStyle}">
                    • 关闭其他占用内存的程序<LineBreak/>
                    • 减少同时处理的文件数量<LineBreak/>
                    • 对于大文件，建议单独处理<LineBreak/>
                    • 检查系统可用内存和磁盘空间
                </TextBlock>
                
                <TextBlock Text="图片无法显示？" Style="{StaticResource SubHeaderStyle}"/>
                <TextBlock Style="{StaticResource ContentStyle}">
                    • 确认已启用"提取图片"选项<LineBreak/>
                    • 检查输出目录权限<LineBreak/>
                    • 验证图片文件是否正确生成<LineBreak/>
                    • 检查 Markdown 中的图片链接路径
                </TextBlock>
                
                <!-- 技术支持 -->
                <TextBlock Text="技术支持" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBlock Style="{StaticResource ContentStyle}">
                    如果您遇到问题或有改进建议，请通过以下方式联系我们：
                </TextBlock>
                <TextBlock Text="• 官方网站: https://augmentcode.com/docx-converter" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• 技术支持: <EMAIL>" Style="{StaticResource ListItemStyle}"/>
                <TextBlock Text="• GitHub: https://github.com/augmentcode/docx-converter" Style="{StaticResource ListItemStyle}"/>
                
                <!-- 版本信息 -->
                <TextBlock Text="版本信息" Style="{StaticResource SectionHeaderStyle}"/>
                <TextBlock x:Name="VersionInfoTextBlock" Style="{StaticResource ContentStyle}">
                    版本: 1.0.0<LineBreak/>
                    构建日期: 2024-01-01<LineBreak/>
                    版权所有 © 2024 Augment Code
                </TextBlock>
                
            </StackPanel>
        </ScrollViewer>
        
        <!-- 底部按钮 -->
        <Border Grid.Row="2" Background="{DynamicResource BackgroundBrush}" 
                BorderBrush="{DynamicResource BorderBrush}" BorderThickness="0,1,0,0">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Right"
                       Margin="20,15">
                <Button Content="查看完整文档" 
                       Click="ViewFullDocumentation_Click"
                       Style="{StaticResource SecondaryButtonStyle}"
                       Padding="15,8"/>
                <Button Content="检查更新" 
                       Click="CheckForUpdates_Click"
                       Style="{StaticResource SecondaryButtonStyle}"
                       Padding="15,8"/>
                <Button Content="关闭" 
                       Click="Close_Click"
                       Style="{StaticResource PrimaryButtonStyle}"
                       Padding="20,8"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
