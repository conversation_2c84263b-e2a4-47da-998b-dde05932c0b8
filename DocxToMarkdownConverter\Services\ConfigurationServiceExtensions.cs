using DocxToMarkdownConverter.Models;
using System.IO;
using System.Text.Json;
using System.Xml.Serialization;
using System.Security.Cryptography;
using System.Text;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// Extension methods and additional functionality for ConfigurationService
/// </summary>
public partial class ConfigurationService
{
    #region Multi-format Support

    public async Task<T> LoadSettingsAsync<T>(ConfigurationFormat format) where T : new()
    {
        var fileName = GetSettingsFileName<T>(format);
        var filePath = Path.Combine(_settingsDirectory, fileName);

        if (!File.Exists(filePath))
        {
            _logger?.LogInfo($"Settings file not found for {typeof(T).Name} in {format} format");
            return new T();
        }

        try
        {
            switch (format)
            {
                case ConfigurationFormat.Json:
                    return await LoadJsonSettingsAsync<T>(filePath);
                case ConfigurationFormat.Xml:
                    return await LoadXmlSettingsAsync<T>(filePath);
                default:
                    throw new NotSupportedException($"Configuration format {format} is not supported");
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to load {typeof(T).Name} settings in {format} format: {ex.Message}", ex);
            return new T();
        }
    }

    public async Task SaveSettingsAsync<T>(T settings, ConfigurationFormat format)
    {
        var fileName = GetSettingsFileName<T>(format);
        var filePath = Path.Combine(_settingsDirectory, fileName);

        try
        {
            // Validate settings before saving
            var validationResult = await ValidateSettingsAsync(settings);
            if (validationResult.Result == ValidationResult.Invalid)
            {
                throw new InvalidOperationException($"Cannot save invalid settings: {validationResult.Message}");
            }

            // Create backup before saving
            await BackupSettingsAsync<T>();

            switch (format)
            {
                case ConfigurationFormat.Json:
                    await SaveJsonSettingsAsync(settings, filePath);
                    break;
                case ConfigurationFormat.Xml:
                    await SaveXmlSettingsAsync(settings, filePath);
                    break;
                default:
                    throw new NotSupportedException($"Configuration format {format} is not supported");
            }

            _logger?.LogInfo($"{typeof(T).Name} settings saved successfully in {format} format");
            
            // Notify configuration changed
            ConfigurationChanged?.Invoke(this, new ConfigurationChangedEventArgs(typeof(T)));
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to save {typeof(T).Name} settings in {format} format: {ex.Message}", ex);
            throw;
        }
    }

    public async Task<bool> ConvertSettingsFormatAsync<T>(ConfigurationFormat fromFormat, ConfigurationFormat toFormat) where T : class, new()
    {
        try
        {
            var settings = await LoadSettingsAsync<T>(fromFormat);
            await SaveSettingsAsync(settings, toFormat);
            _logger?.LogInfo($"Successfully converted {typeof(T).Name} settings from {fromFormat} to {toFormat}");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to convert {typeof(T).Name} settings from {fromFormat} to {toFormat}: {ex.Message}", ex);
            return false;
        }
    }

    #endregion

    #region Import/Export Functionality

    public async Task<bool> ExportSettingsAsync<T>(string filePath, ConfigurationFormat format = ConfigurationFormat.Json) where T : class, new()
    {
        try
        {
            var settings = await LoadSettingsAsync<T>();
            
            switch (format)
            {
                case ConfigurationFormat.Json:
                    await SaveJsonSettingsAsync(settings, filePath);
                    break;
                case ConfigurationFormat.Xml:
                    await SaveXmlSettingsAsync(settings, filePath);
                    break;
                default:
                    throw new NotSupportedException($"Export format {format} is not supported");
            }

            _logger?.LogInfo($"Successfully exported {typeof(T).Name} settings to {filePath}");
            return true;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to export {typeof(T).Name} settings to {filePath}: {ex.Message}", ex);
            return false;
        }
    }

    public async Task<T?> ImportSettingsAsync<T>(string filePath, ConfigurationFormat? format = null) where T : new()
    {
        try
        {
            if (!File.Exists(filePath))
            {
                _logger?.LogWarning($"Import file not found: {filePath}");
                return default(T);
            }

            // Auto-detect format if not specified
            var detectedFormat = format ?? DetectConfigurationFormat(filePath);

            T settings;
            switch (detectedFormat)
            {
                case ConfigurationFormat.Json:
                    settings = await LoadJsonSettingsAsync<T>(filePath);
                    break;
                case ConfigurationFormat.Xml:
                    settings = await LoadXmlSettingsAsync<T>(filePath);
                    break;
                default:
                    throw new NotSupportedException($"Import format {detectedFormat} is not supported");
            }

            // Validate imported settings
            var validationResult = await ValidateSettingsAsync(settings);
            if (validationResult.Result == ValidationResult.Invalid)
            {
                _logger?.LogWarning($"Imported settings are invalid: {validationResult.Message}");
                return default(T);
            }

            _logger?.LogInfo($"Successfully imported {typeof(T).Name} settings from {filePath}");
            return settings;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to import {typeof(T).Name} settings from {filePath}: {ex.Message}", ex);
            return default(T);
        }
    }

    public async Task<bool> ExportAllSettingsAsync(string directoryPath, ConfigurationFormat format = ConfigurationFormat.Json)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
            {
                Directory.CreateDirectory(directoryPath);
            }

            var settingsTypes = new[]
            {
                typeof(ApplicationSettings),
                typeof(ConversionOptions),
                typeof(AnimationSettings),
                typeof(ThemeSettings),
                typeof(ConversionStatistics)
            };

            var exportTasks = settingsTypes.Select(async type =>
            {
                var method = typeof(ConfigurationService).GetMethod(nameof(ExportSettingsAsync))?.MakeGenericMethod(type);
                var fileName = GetSettingsFileName(type, format);
                var filePath = Path.Combine(directoryPath, fileName);
                
                if (method != null)
                {
                    var task = (Task<bool>)method.Invoke(this, new object[] { filePath, format })!;
                    return await task;
                }
                return false;
            });

            var results = await Task.WhenAll(exportTasks);
            var success = results.All(r => r);

            if (success)
            {
                _logger?.LogInfo($"Successfully exported all settings to {directoryPath}");
            }
            else
            {
                _logger?.LogWarning($"Some settings failed to export to {directoryPath}");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to export all settings to {directoryPath}: {ex.Message}", ex);
            return false;
        }
    }

    public async Task<bool> ImportAllSettingsAsync(string directoryPath)
    {
        try
        {
            if (!Directory.Exists(directoryPath))
            {
                _logger?.LogWarning($"Import directory not found: {directoryPath}");
                return false;
            }

            var settingsFiles = Directory.GetFiles(directoryPath, "*.*")
                .Where(f => f.EndsWith(".json") || f.EndsWith(".xml"))
                .ToList();

            var importTasks = settingsFiles.Select(async filePath =>
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                var settingsType = GetSettingsTypeFromFileName(fileName);
                
                if (settingsType != null)
                {
                    var method = typeof(ConfigurationService).GetMethod(nameof(ImportSettingsAsync))?.MakeGenericMethod(settingsType);
                    if (method != null)
                    {
                        var invokeResult = method.Invoke(this, new object?[] { filePath, null });
                        if (invokeResult is Task<object?> task)
                        {
                            var result = await task;
                            return result != null;
                        }
                    }
                }
                return false;
            });

            var results = await Task.WhenAll(importTasks);
            var success = results.All(r => r);

            if (success)
            {
                _logger?.LogInfo($"Successfully imported all settings from {directoryPath}");
            }
            else
            {
                _logger?.LogWarning($"Some settings failed to import from {directoryPath}");
            }

            return success;
        }
        catch (Exception ex)
        {
            _logger?.LogError($"Failed to import all settings from {directoryPath}: {ex.Message}", ex);
            return false;
        }
    }

    #endregion

    #region Helper Methods

    private async Task<T> LoadJsonSettingsAsync<T>(string filePath) where T : new()
    {
        var json = await File.ReadAllTextAsync(filePath);
        var options = new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true
        };
        return JsonSerializer.Deserialize<T>(json, options) ?? new T();
    }

    private async Task SaveJsonSettingsAsync<T>(T settings, string filePath)
    {
        var options = new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        };
        var json = JsonSerializer.Serialize(settings, options);
        await File.WriteAllTextAsync(filePath, json);
    }

    private async Task<T> LoadXmlSettingsAsync<T>(string filePath) where T : new()
    {
        var serializer = new XmlSerializer(typeof(T));
        using var stream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
        var result = serializer.Deserialize(stream);
        await Task.CompletedTask; // 消除async警告
        return result is T settings ? settings : new T();
    }

    private async Task SaveXmlSettingsAsync<T>(T settings, string filePath)
    {
        var serializer = new XmlSerializer(typeof(T));
        using var stream = new FileStream(filePath, FileMode.Create, FileAccess.Write);
        serializer.Serialize(stream, settings);
        await Task.CompletedTask; // Make method async for consistency
    }

    private string GetSettingsFileName<T>(ConfigurationFormat format)
    {
        return GetSettingsFileName(typeof(T), format);
    }

    private string GetSettingsFileName(Type type, ConfigurationFormat format)
    {
        var extension = format == ConfigurationFormat.Json ? ".json" : ".xml";
        return type == typeof(ApplicationSettings) ? 
            $"ApplicationSettings{extension}" : 
            $"{type.Name}{extension}";
    }

    private ConfigurationFormat DetectConfigurationFormat(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension switch
        {
            ".json" => ConfigurationFormat.Json,
            ".xml" => ConfigurationFormat.Xml,
            _ => ConfigurationFormat.Json // Default to JSON
        };
    }

    private Type? GetSettingsTypeFromFileName(string fileName)
    {
        return fileName.ToLowerInvariant() switch
        {
            "applicationsettings" => typeof(ApplicationSettings),
            "conversionoptions" => typeof(ConversionOptions),
            "animationsettings" => typeof(AnimationSettings),
            "themesettings" => typeof(ThemeSettings),
            "conversionstatistics" => typeof(ConversionStatistics),
            _ => null
        };
    }

    #endregion
}
