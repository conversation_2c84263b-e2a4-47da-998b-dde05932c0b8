using System.Windows.Controls;
using DocxToMarkdownConverter.ViewModels;

namespace DocxToMarkdownConverter.Controls;

/// <summary>
/// LogViewerControl.xaml 的交互逻辑
/// </summary>
public partial class LogViewerControl : System.Windows.Controls.UserControl
{
    public LogViewerControl()
    {
        InitializeComponent();
        DataContext = new LogViewerViewModel();
        
        // 注册事件处理程序
        Loaded += LogViewerControl_Loaded;
        Unloaded += LogViewerControl_Unloaded;
    }

    /// <summary>
    /// 获取或设置 ViewModel
    /// </summary>
    public LogViewerViewModel ViewModel => (LogViewerViewModel)DataContext;

    /// <summary>
    /// 当日志内容更新时自动滚动到底部
    /// </summary>
    private void OnLogContentChanged()
    {
        if (ViewModel.AutoScrollEnabled)
        {
            Dispatcher.BeginInvoke(() =>
            {
                if (LogListView.Items.Count > 0)
                {
                    LogListView.ScrollIntoView(LogListView.Items[LogListView.Items.Count - 1]);
                }
            });
        }
    }

    /// <summary>
    /// 当控件加载时设置事件处理
    /// </summary>
    private void LogViewerControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
    {
        // 监听 ViewModel 的属性变化
        if (ViewModel != null)
        {
            ViewModel.PropertyChanged += (s, args) =>
            {
                switch (args.PropertyName)
                {
                    case nameof(LogViewerViewModel.FilteredLogEntries):
                        OnLogContentChanged();
                        break;
                    case nameof(LogViewerViewModel.AutoScrollEnabled):
                        if (ViewModel.AutoScrollEnabled && LogListView.Items.Count > 0)
                        {
                            LogListView.ScrollIntoView(LogListView.Items[LogListView.Items.Count - 1]);
                        }
                        break;
                }
            };
        }
    }

    /// <summary>
    /// 当控件卸载时清理资源
    /// </summary>
    private void LogViewerControl_Unloaded(object sender, System.Windows.RoutedEventArgs e)
    {
        ViewModel?.Dispose();
    }
}