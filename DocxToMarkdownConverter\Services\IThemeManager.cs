using DocxToMarkdownConverter.Models;
using System.Windows.Media;
using System.Windows;
using System.Windows.Media.Animation;
using Microsoft.Extensions.Logging;
using Application = System.Windows.Application;
using Color = System.Windows.Media.Color;
using ColorConverter = System.Windows.Media.ColorConverter;

namespace DocxToMarkdownConverter.Services;

public interface IThemeManager
{
    AppTheme CurrentTheme { get; }
    ThemeSettings Settings { get; }
    Task ApplyThemeAsync(AppTheme theme, bool animate = true);
    Task ToggleThemeAsync();
    void SetAccentColor(string colorHex);
    // Simplified color management - removed complex primary/secondary color setting
    bool IsSystemThemeSupported { get; }
    AppTheme GetSystemTheme();
    void StartSystemThemeMonitoring();
    void StopSystemThemeMonitoring();
    event EventHandler<ThemeChangedEventArgs>? ThemeChanged;
    event EventHandler<AccentColorChangedEventArgs>? AccentColorChanged;
}

public class ThemeChangedEventArgs : EventArgs
{
    public AppTheme OldTheme { get; }
    public AppTheme NewTheme { get; }
    public bool WasAnimated { get; }

    public ThemeChangedEventArgs(AppTheme oldTheme, AppTheme newTheme, bool wasAnimated = false)
    {
        OldTheme = oldTheme;
        NewTheme = newTheme;
        WasAnimated = wasAnimated;
    }
}

public class AccentColorChangedEventArgs : EventArgs
{
    public string OldColor { get; }
    public string NewColor { get; }

    public AccentColorChangedEventArgs(string oldColor, string newColor)
    {
        OldColor = oldColor;
        NewColor = newColor;
    }
}

public enum MaterialDesignColor
{
    Red, Pink, Purple, DeepPurple, Indigo, Blue, LightBlue, Cyan, Teal, Green,
    LightGreen, Lime, Yellow, Amber, Orange, DeepOrange, Brown, Grey, BlueGrey
}

