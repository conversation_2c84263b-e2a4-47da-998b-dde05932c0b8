<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Animation-related styles and resources -->

    <!-- 动画控制资源 -->
    <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>

    <!-- 性能优化的动画持续时间 -->
    <Duration x:Key="FastAnimationDuration">0:0:0.1</Duration>
    <Duration x:Key="MediumAnimationDuration">0:0:0.15</Duration>
    <Duration x:Key="SlowAnimationDuration">0:0:0.2</Duration>

    <!-- 无动画样式（用于禁用动画时） -->
    <Style x:Key="NoAnimationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <!-- 无动画效果，只有基本的颜色变化 -->
    </Style>

    <!-- 优化的智能悬停效果 - 性能优化版 -->
    <Style x:Key="SmartGlowButtonStyle" TargetType="Button" BasedOn="{StaticResource RoundedButtonStyle}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 减少动画持续时间和缩放幅度以提升性能 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.02" Duration="0:0:0.1">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.02" Duration="0:0:0.1">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 简化退出动画，使用更快的缓动函数 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 优化的智能卡片悬停效果 - 性能优化版 -->
    <Style x:Key="SmartCardHoverStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource GlassCardStyle}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <!-- 简化变换组，只使用必要的变换 -->
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 减少动画持续时间和缩放幅度 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.01" Duration="0:0:0.12">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.01" Duration="0:0:0.12">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 快速恢复动画 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <!-- Smooth transition style for buttons -->
    <Style x:Key="AnimatedButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.02" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.02" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                           To="1.0" Duration="0:0:0.15">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Fade in animation for content -->
    <Style x:Key="FadeInContentStyle" TargetType="FrameworkElement">
        <Setter Property="Opacity" Value="0"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                       From="0" To="1" Duration="0:0:0.5">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Slide in from left animation -->
    <Style x:Key="SlideInLeftStyle" TargetType="FrameworkElement">
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TranslateTransform X="-50"/>
            </Setter.Value>
        </Setter>
        <Setter Property="Opacity" Value="0"/>
        <Style.Triggers>
            <EventTrigger RoutedEvent="Loaded">
                <BeginStoryboard>
                    <Storyboard>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.X)"
                                       From="-50" To="0" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                       From="0" To="1" Duration="0:0:0.4">
                            <DoubleAnimation.EasingFunction>
                                <CubicEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Progress bar with smooth animation -->
    <Style x:Key="AnimatedProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="materialDesign:TransitionAssist.DisableTransitions" Value="False"/>
        <Style.Triggers>
            <Trigger Property="IsIndeterminate" Value="False">
                <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 页面过渡动画 -->
    <Storyboard x:Key="PageFadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                         From="20" To="0" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="PageFadeOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="1" To="0" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseIn"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TranslateTransform.Y)"
                         From="0" To="-20" Duration="0:0:0.3">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseIn"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <!-- 智能导航按钮样式 -->
    <Style x:Key="SmartNavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource NavigationButtonStyle}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <TransformGroup>
                    <ScaleTransform ScaleX="1" ScaleY="1"/>
                    <TranslateTransform X="0"/>
                </TransformGroup>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 优化：减少缩放幅度和动画持续时间 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                                           To="1.01" Duration="0:0:0.1">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                                           To="1.01" Duration="0:0:0.1">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <!-- 减少平移距离以提升性能 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.X)"
                                           To="4" Duration="0:0:0.1">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <!-- 优化：快速恢复动画 -->
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleX)"
                                           To="1.0" Duration="0:0:0.12">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[0].(ScaleTransform.ScaleY)"
                                           To="1.0" Duration="0:0:0.12">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                            <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(TransformGroup.Children)[1].(TranslateTransform.X)"
                                           To="0" Duration="0:0:0.12">
                                <DoubleAnimation.EasingFunction>
                                    <QuadraticEase EasingMode="EaseOut"/>
                                </DoubleAnimation.EasingFunction>
                            </DoubleAnimation>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>