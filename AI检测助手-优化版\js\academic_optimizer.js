// 学术专业优化器 - 增强版
class AcademicOptimizer {
    constructor() {
        // 学术内容优化功能架构配置
        this.config = {
            // 创新性指数阈值 (Nature子刊基准)
            innovationDensityThreshold: 3, // 创新点/千字
            // 颠覆性指数阈值 (Science级别)
            disruptiveIndexThreshold: 0.35,
            // 学术性评分权重
            academicScoreWeights: {
                structuralCompliance: 0.25,    // 结构规范性
                innovationIndex: 0.30,         // 创新性指数
                evidenceLevel: 0.25,           // 证据等级
                dataIntegrity: 0.20            // 数据呈现完整性
            }
        };

        // 学术同义词库 - 扩展版
        this.academicSynonyms = {
            '研究': ['探索', '调研', '分析', '考察', '探讨', '研判'],
            '方法': ['途径', '手段', '方式', '策略', '技术', '路径'],
            '结果': ['成果', '效果', '产出', '收获', '表现', '成效'],
            '分析': ['剖析', '解析', '研判', '评估', '审视', '考量'],
            '系统': ['体系', '框架', '机制', '平台', '架构', '模式'],
            '问题': ['难题', '挑战', '课题', '议题', '困难', '瓶颈'],
            '重要': ['关键', '核心', '主要', '重点', '关键性', '要害'],
            '提高': ['改善', '增强', '优化', '提升', '强化', '完善'],
            '显示': ['表明', '揭示', '反映', '证实', '展现', '印证'],
            '发现': ['察觉', '观察到', '识别', '注意到', '探明', '洞察'],
            '实验': ['试验', '测试', '验证', '检验', '实证', '测验'],
            '数据': ['资料', '信息', '材料', '统计', '指标', '参数'],
            '算法': ['方法', '策略', '技术', '机制', '模式', '程序'],
            '模型': ['框架', '体系', '结构', '范式', '机制', '样式'],
            '效果': ['成效', '效应', '作用', '影响', '表现', '绩效']
        };

        // 学术表达模板
        this.academicTemplates = {
            '研究表明': [
                '研究结果显示', '分析结果表明', '实证研究证实',
                '调研发现', '数据分析揭示', '实验验证了'
            ],
            '通过分析': [
                '经过深入分析', '基于数据分析', '通过实证研究',
                '借助统计分析', '运用分析方法', '采用分析技术'
            ],
            '实验结果': [
                '测试结果', '验证结果', '实证结果',
                '检验结果', '试验成果', '测试成效'
            ],
            '本研究': [
                '本项研究', '此次研究', '该研究',
                '本次调研', '此项分析', '当前研究'
            ]
        };

        // 学术连接词
        this.academicConnectors = [
            '基于此', '鉴于此', '据此', '由此可见',
            '综合考虑', '统筹分析', '整体而言', '从整体上看',
            '进一步地', '更深层次地', '从另一角度', '换言之'
        ];

        // 专业术语增强
        this.technicalEnhancements = {
            '算法': '智能算法', '系统': '智能系统', '方法': '创新方法',
            '技术': '先进技术', '模式': '优化模式', '策略': '高效策略'
        };

        // 学术写作规范模板 (基于IMRaD结构)
        this.academicStructureTemplates = {
            introduction: [
                '基于现有研究基础', '鉴于当前研究现状', '针对现有理论局限',
                '结合最新研究进展', '立足于前沿理论框架', '基于跨学科研究视角'
            ],
            methodology: [
                '采用多维度分析方法', '运用混合研究设计', '基于实证研究范式',
                '采用定量与定性相结合', '运用创新性研究方法', '基于严谨的实验设计'
            ],
            results: [
                '实证分析结果表明', '数据分析揭示', '统计检验证实',
                '多维度验证显示', '实验结果证明', '量化分析发现'
            ],
            discussion: [
                '综合分析表明', '深入讨论发现', '理论与实践结合显示',
                '多角度分析证实', '系统性评估表明', '批判性分析揭示'
            ]
        };

        // 创新性评价指标
        this.innovationMetrics = {
            // 颠覆性概念密度关键词
            disruptiveKeywords: [
                '突破性', '颠覆性', '革命性', '开创性', '前沿性', '原创性',
                '创新性', '首次', '新颖', '独特', '先进', '领先'
            ],
            // 方法突破强度关键词
            methodBreakthroughKeywords: [
                '新方法', '创新技术', '改进算法', '优化策略', '新模型',
                '新框架', '新范式', '新机制', '新途径', '新手段'
            ],
            // 2023年后新技术关键词
            modernTechKeywords: [
                'ChatGPT', 'GPT-4', 'Transformer', '大语言模型', '深度学习',
                '机器学习', '人工智能', '神经网络', '联邦学习', '边缘计算'
            ]
        };

        // JBI证据分级标准
        this.evidenceLevels = {
            level1: ['系统评价', '荟萃分析', 'Meta分析', '循证医学'],
            level2: ['随机对照试验', 'RCT', '双盲试验', '多中心研究'],
            level3: ['队列研究', '病例对照研究', '横断面研究'],
            level4: ['病例报告', '专家意见', '理论分析']
        };
    }

    /**
     * 动态对比引擎 - 多维对比分析
     * @param {string} textA - 段落A
     * @param {string} textB - 段落B
     * @returns {Object} 对比分析结果
     */
    performDynamicComparison(textA, textB) {
        const comparisonResult = {
            structuralCompliance: this.analyzeStructuralCompliance(textA, textB),
            innovationIndex: this.analyzeInnovationIndex(textA, textB),
            evidenceLevel: this.analyzeEvidenceLevel(textA, textB),
            dataIntegrity: this.analyzeDataIntegrity(textA, textB),
            comparisonMatrix: this.generateComparisonMatrix(textA, textB),
            fusionRecommendations: this.generateFusionRecommendations(textA, textB)
        };

        return comparisonResult;
    }

    /**
     * 分析结构规范性 (IMRaD结构、章节衔接度)
     */
    analyzeStructuralCompliance(textA, textB) {
        const analyzeStructure = (text) => {
            let score = 0;
            let details = [];

            // 检查IMRaD结构元素
            const imradElements = {
                introduction: /引言|介绍|背景|研究背景/g,
                methods: /方法|methodology|实验设计|研究方法/g,
                results: /结果|发现|数据|实验结果/g,
                discussion: /讨论|分析|结论|implications/g
            };

            for (let [element, regex] of Object.entries(imradElements)) {
                if (regex.test(text)) {
                    score += 25;
                    details.push(`包含${element}结构元素`);
                }
            }

            // 检查章节衔接度
            const transitionWords = ['因此', '然而', '此外', '综上所述', '基于此', '进一步地'];
            const transitionCount = transitionWords.filter(word => text.includes(word)).length;
            const transitionScore = Math.min(20, transitionCount * 5);
            score += transitionScore;

            if (transitionScore > 0) {
                details.push(`章节衔接词使用${transitionCount}个`);
            }

            return { score: Math.min(100, score), details };
        };

        const resultA = analyzeStructure(textA);
        const resultB = analyzeStructure(textB);

        return {
            textA: resultA,
            textB: resultB,
            comparison: resultA.score > resultB.score ? 'A更规范' : resultB.score > resultA.score ? 'B更规范' : '相当'
        };
    }

    /**
     * 分析创新性指数 (颠覆性概念密度/方法突破强度)
     */
    analyzeInnovationIndex(textA, textB) {
        const analyzeInnovation = (text) => {
            let score = 0;
            let details = [];

            // 颠覆性概念密度
            const disruptiveCount = this.innovationMetrics.disruptiveKeywords
                .filter(keyword => text.includes(keyword)).length;
            const disruptiveScore = disruptiveCount * 10;
            score += disruptiveScore;

            if (disruptiveCount > 0) {
                details.push(`颠覆性概念${disruptiveCount}个`);
            }

            // 方法突破强度
            const methodCount = this.innovationMetrics.methodBreakthroughKeywords
                .filter(keyword => text.includes(keyword)).length;
            const methodScore = methodCount * 15;
            score += methodScore;

            if (methodCount > 0) {
                details.push(`方法突破${methodCount}个`);
            }

            // 现代技术应用
            const modernTechCount = this.innovationMetrics.modernTechKeywords
                .filter(keyword => text.includes(keyword)).length;
            const modernTechScore = modernTechCount * 20;
            score += modernTechScore;

            if (modernTechCount > 0) {
                details.push(`现代技术${modernTechCount}个`);
            }

            // 计算创新点密度 (每千字)
            const wordCount = this.countWords(text);
            const density = wordCount > 0 ? (disruptiveCount + methodCount + modernTechCount) / wordCount * 1000 : 0;
            details.push(`创新密度: ${density.toFixed(1)}点/千字`);

            return {
                score: Math.min(100, score),
                details,
                density,
                meetsThreshold: density >= this.config.innovationDensityThreshold
            };
        };

        const resultA = analyzeInnovation(textA);
        const resultB = analyzeInnovation(textB);

        return {
            textA: resultA,
            textB: resultB,
            comparison: resultA.density > resultB.density ? 'A更创新' : resultB.density > resultA.density ? 'B更创新' : '相当'
        };
    }

    /**
     * 分析证据等级 (JBI证据分级标准应用程度)
     */
    analyzeEvidenceLevel(textA, textB) {
        const analyzeEvidence = (text) => {
            let score = 0;
            let details = [];
            let highestLevel = 4;

            // 检查各级证据
            for (let level = 1; level <= 4; level++) {
                const keywords = this.evidenceLevels[`level${level}`];
                const found = keywords.filter(keyword => text.includes(keyword));

                if (found.length > 0) {
                    const levelScore = (5 - level) * 25; // 级别越高分数越高
                    score += levelScore;
                    highestLevel = Math.min(highestLevel, level);
                    details.push(`${level}级证据: ${found.join(', ')}`);
                }
            }

            return {
                score: Math.min(100, score),
                details,
                highestLevel,
                evidenceQuality: highestLevel <= 2 ? '高' : highestLevel <= 3 ? '中' : '低'
            };
        };

        const resultA = analyzeEvidence(textA);
        const resultB = analyzeEvidence(textB);

        return {
            textA: resultA,
            textB: resultB,
            comparison: resultA.highestLevel < resultB.highestLevel ? 'A证据更强' :
                       resultB.highestLevel < resultA.highestLevel ? 'B证据更强' : '相当'
        };
    }

    /**
     * 分析数据呈现完整性 (支撑材料的双向链接)
     */
    analyzeDataIntegrity(textA, textB) {
        const analyzeIntegrity = (text) => {
            let score = 0;
            let details = [];

            // 检查数据引用
            const dataReferences = text.match(/图\s*\d+|表\s*\d+|附录\s*\w+|参考文献\s*\[\d+\]/g) || [];
            const referenceScore = Math.min(30, dataReferences.length * 5);
            score += referenceScore;

            if (dataReferences.length > 0) {
                details.push(`数据引用${dataReferences.length}处`);
            }

            // 检查统计数据
            const statisticalData = text.match(/p\s*[<>=]\s*0\.\d+|r\s*=\s*0\.\d+|\d+\.\d+%|\(n\s*=\s*\d+\)/g) || [];
            const statScore = Math.min(25, statisticalData.length * 5);
            score += statScore;

            if (statisticalData.length > 0) {
                details.push(`统计数据${statisticalData.length}处`);
            }

            // 检查方法学描述
            const methodDescriptions = ['样本量', '置信区间', '显著性水平', '效应量', '敏感性分析'];
            const methodCount = methodDescriptions.filter(method => text.includes(method)).length;
            const methodScore = methodCount * 10;
            score += methodScore;

            if (methodCount > 0) {
                details.push(`方法学描述${methodCount}项`);
            }

            // 检查开放数据声明
            const openDataKeywords = ['数据可获得', '开放数据', 'GitHub', 'DOI', '数据仓库'];
            const openDataCount = openDataKeywords.filter(keyword => text.includes(keyword)).length;
            const openDataScore = openDataCount * 15;
            score += openDataScore;

            if (openDataCount > 0) {
                details.push(`开放数据声明${openDataCount}项`);
            }

            return {
                score: Math.min(100, score),
                details,
                hasOpenData: openDataCount > 0,
                integrityLevel: score >= 70 ? '高' : score >= 40 ? '中' : '低'
            };
        };

        const resultA = analyzeIntegrity(textA);
        const resultB = analyzeIntegrity(textB);

        return {
            textA: resultA,
            textB: resultB,
            comparison: resultA.score > resultB.score ? 'A完整性更高' : resultB.score > resultA.score ? 'B完整性更高' : '相当'
        };
    }

    /**
     * 生成对比矩阵
     */
    generateComparisonMatrix(textA, textB) {
        const matrix = {
            headers: ['指标', '段落A', '段落B', '优势方'],
            rows: []
        };

        // 方法创新性对比
        const methodA = this.extractMethodology(textA);
        const methodB = this.extractMethodology(textB);
        matrix.rows.push([
            '方法创新性',
            methodA.description || '传统方法',
            methodB.description || '传统方法',
            methodA.innovationScore > methodB.innovationScore ? 'A' :
            methodB.innovationScore > methodA.innovationScore ? 'B' : '相当'
        ]);

        // 数据透明度对比
        const dataA = this.extractDataTransparency(textA);
        const dataB = this.extractDataTransparency(textB);
        matrix.rows.push([
            '数据透明度',
            dataA.description,
            dataB.description,
            dataA.score > dataB.score ? 'A' : dataB.score > dataA.score ? 'B' : '相当'
        ]);

        // 理论支撑对比
        const theoryA = this.extractTheoreticalSupport(textA);
        const theoryB = this.extractTheoreticalSupport(textB);
        matrix.rows.push([
            '理论支撑',
            theoryA.description,
            theoryB.description,
            theoryA.score > theoryB.score ? 'A' : theoryB.score > theoryA.score ? 'B' : '相当'
        ]);

        return matrix;
    }

    /**
     * 提取方法学信息
     */
    extractMethodology(text) {
        const methodKeywords = {
            traditional: ['传统方法', '经典方法', '常规方法'],
            modern: ['机器学习', '深度学习', '人工智能', '大数据'],
            innovative: ['创新方法', '新方法', '改进方法', '优化方法']
        };

        let innovationScore = 0;
        let description = '未明确说明';

        for (let [category, keywords] of Object.entries(methodKeywords)) {
            const found = keywords.filter(keyword => text.includes(keyword));
            if (found.length > 0) {
                switch (category) {
                    case 'traditional':
                        innovationScore += 10;
                        description = `${found[0]}(${found.length}项)`;
                        break;
                    case 'modern':
                        innovationScore += 30;
                        description = `${found[0]}(${found.length}项)`;
                        break;
                    case 'innovative':
                        innovationScore += 50;
                        description = `${found[0]}(${found.length}项)`;
                        break;
                }
            }
        }

        return { innovationScore, description };
    }

    /**
     * 提取数据透明度信息
     */
    extractDataTransparency(text) {
        let score = 0;
        let features = [];

        if (text.includes('原始数据') || text.includes('开放数据')) {
            score += 30;
            features.push('开放数据');
        }
        if (text.includes('GitHub') || text.includes('代码仓库')) {
            score += 25;
            features.push('代码开源');
        }
        if (text.includes('DOI') || text.includes('数据集')) {
            score += 20;
            features.push('数据集标识');
        }
        if (text.includes('可重复') || text.includes('可复现')) {
            score += 25;
            features.push('可重复性');
        }

        const description = features.length > 0 ? features.join('+') : '数据未公开';
        return { score, description };
    }

    /**
     * 提取理论支撑信息
     */
    extractTheoreticalSupport(text) {
        let score = 0;
        let features = [];

        // 检查理论模型
        const theoryKeywords = ['理论模型', '理论框架', '概念模型', '数学模型'];
        const theoryCount = theoryKeywords.filter(keyword => text.includes(keyword)).length;
        if (theoryCount > 0) {
            score += theoryCount * 20;
            features.push(`理论模型${theoryCount}个`);
        }

        // 检查多尺度分析
        const scaleKeywords = ['多尺度', '多层次', '多维度', '系统性'];
        const scaleCount = scaleKeywords.filter(keyword => text.includes(keyword)).length;
        if (scaleCount > 0) {
            score += scaleCount * 15;
            features.push(`多尺度分析${scaleCount}项`);
        }

        // 检查跨学科融合
        const interdisciplinaryKeywords = ['跨学科', '多学科', '交叉学科', '融合'];
        const interdisciplinaryCount = interdisciplinaryKeywords.filter(keyword => text.includes(keyword)).length;
        if (interdisciplinaryCount > 0) {
            score += interdisciplinaryCount * 25;
            features.push(`跨学科融合${interdisciplinaryCount}项`);
        }

        const description = features.length > 0 ? features.join('+') : '单模型论证';
        return { score, description };
    }

    /**
     * 生成融合推荐
     */
    generateFusionRecommendations(textA, textB) {
        const recommendations = [];

        // 分析各段落优势
        const advantagesA = this.extractAdvantages(textA);
        const advantagesB = this.extractAdvantages(textB);

        // 生成融合策略
        if (advantagesA.methodology.length > 0 && advantagesB.technology.length > 0) {
            recommendations.push({
                type: '方法-技术融合',
                description: `融合段落A的${advantagesA.methodology[0]}与段落B的${advantagesB.technology[0]}，构建创新研究范式`,
                priority: 'high'
            });
        }

        if (advantagesA.data.length > 0 && advantagesB.analysis.length > 0) {
            recommendations.push({
                type: '数据-分析融合',
                description: `结合段落A的${advantagesA.data[0]}与段落B的${advantagesB.analysis[0]}，提升论证强度`,
                priority: 'medium'
            });
        }

        if (advantagesA.theory.length > 0 && advantagesB.practice.length > 0) {
            recommendations.push({
                type: '理论-实践融合',
                description: `整合段落A的${advantagesA.theory[0]}与段落B的${advantagesB.practice[0]}，形成理论-实践双驱动体系`,
                priority: 'high'
            });
        }

        return recommendations;
    }

    /**
     * 提取段落优势
     */
    extractAdvantages(text) {
        return {
            methodology: this.extractKeywords(text, ['方法', '途径', '策略', '技术']),
            technology: this.extractKeywords(text, ['技术', '算法', '模型', '系统']),
            data: this.extractKeywords(text, ['数据', '实验', '测试', '验证']),
            analysis: this.extractKeywords(text, ['分析', '评估', '检验', '统计']),
            theory: this.extractKeywords(text, ['理论', '模型', '框架', '机制']),
            practice: this.extractKeywords(text, ['应用', '实践', '实施', '部署'])
        };
    }

    /**
     * 提取关键词
     */
    extractKeywords(text, keywords) {
        return keywords.filter(keyword => text.includes(keyword));
    }

    // 学术同义词替换
    replaceAcademicSynonyms(text) {
        let result = text;
        for (let [word, synonyms] of Object.entries(this.academicSynonyms)) {
            const regex = new RegExp(word, 'g');
            const matches = result.match(regex);
            if (matches) {
                // 随机替换部分词汇，保持自然性
                result = result.replace(regex, (match, offset) => {
                    if (Math.random() < 0.6) { // 60%概率替换
                        return synonyms[Math.floor(Math.random() * synonyms.length)];
                    }
                    return match;
                });
            }
        }
        return result;
    }

    // 学术表达模板替换
    replaceAcademicTemplates(text) {
        let result = text;
        for (let [template, alternatives] of Object.entries(this.academicTemplates)) {
            const regex = new RegExp(template, 'g');
            result = result.replace(regex, () => {
                return alternatives[Math.floor(Math.random() * alternatives.length)];
            });
        }
        return result;
    }

    // 增强专业术语
    enhanceTechnicalTerms(text) {
        let result = text;
        for (let [term, enhanced] of Object.entries(this.technicalEnhancements)) {
            const regex = new RegExp(`(?<!\\w)${term}(?!\\w)`, 'g');
            result = result.replace(regex, (match) => {
                return Math.random() < 0.3 ? enhanced : match; // 30%概率增强
            });
        }
        return result;
    }

    // 优化句子结构
    optimizeSentenceStructure(text) {
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        
        return sentences.map((sentence, index) => {
            let optimized = sentence.trim();
            
            // 随机添加学术连接词
            if (index > 0 && Math.random() < 0.3) {
                const connector = this.academicConnectors[Math.floor(Math.random() * this.academicConnectors.length)];
                optimized = connector + '，' + optimized;
            }
            
            // 优化句子开头
            if (optimized.startsWith('这') && Math.random() < 0.5) {
                optimized = optimized.replace('这', '该');
            }
            
            return optimized;
        }).join('。') + '。';
    }

    // 增加学术严谨性
    addAcademicRigor(text) {
        let result = text;
        
        // 添加量化表述
        result = result.replace(/很多/g, '大量');
        result = result.replace(/一些/g, '部分');
        result = result.replace(/比较/g, '相对');
        
        // 增强客观性
        result = result.replace(/我们认为/g, '研究表明');
        result = result.replace(/可以看出/g, '数据显示');
        
        return result;
    }

    // 主优化函数 - 增强版
    optimize(text, options = {}) {
        const {
            synonymReplacement = true,
            templateReplacement = true,
            technicalEnhancement = true,
            structureOptimization = true,
            rigorEnhancement = true,
            enableAdvancedAnalysis = false,  // 启用高级分析功能
            enableQualityControl = false,    // 启用质量控制
            targetInnovationDensity = 3      // 目标创新密度
        } = options;

        let optimized = text;
        const improvements = [];
        const statistics = {
            synonymReplacements: 0,
            templateReplacements: 0,
            technicalEnhancements: 0,
            structureOptimizations: 0
        };

        // 高级分析模式
        let advancedAnalysis = null;
        if (enableAdvancedAnalysis) {
            advancedAnalysis = this.extractCoreAdvantages(text);
            improvements.push('✓ 完成核心优势解构分析');
        }

        // 1. 学术同义词替换
        if (synonymReplacement) {
            const before = optimized;
            optimized = this.replaceAcademicSynonyms(optimized);
            if (before !== optimized) {
                improvements.push('✓ 优化了学术词汇表达');
                statistics.synonymReplacements++;
            }
        }

        // 2. 学术表达模板替换
        if (templateReplacement) {
            const before = optimized;
            optimized = this.replaceAcademicTemplates(optimized);
            if (before !== optimized) {
                improvements.push('✓ 改进了学术表达模式');
                statistics.templateReplacements++;
            }
        }

        // 3. 专业术语增强
        if (technicalEnhancement) {
            const before = optimized;
            optimized = this.enhanceTechnicalTerms(optimized);
            if (before !== optimized) {
                improvements.push('✓ 增强了专业术语表达');
                statistics.technicalEnhancements++;
            }
        }

        // 4. 句子结构优化
        if (structureOptimization) {
            const before = optimized;
            optimized = this.optimizeSentenceStructure(optimized);
            if (before !== optimized) {
                improvements.push('✓ 优化了句子结构和逻辑');
                statistics.structureOptimizations++;
            }
        }

        // 5. 学术严谨性增强
        if (rigorEnhancement) {
            const before = optimized;
            optimized = this.addAcademicRigor(optimized);
            if (before !== optimized) {
                improvements.push('✓ 提升了学术表达严谨性');
            }
        }

        // 6. 创新点密度增强 (新功能)
        if (targetInnovationDensity > 0) {
            const before = optimized;
            optimized = this.enhanceInnovationDensity(optimized, targetInnovationDensity);
            if (before !== optimized) {
                improvements.push(`✓ 提升创新点密度至${targetInnovationDensity}点/千字`);
            }
        }

        // 7. 质量控制验证 (新功能)
        let qualityMetrics = null;
        if (enableQualityControl) {
            qualityMetrics = this.performQualityControl(optimized);
            improvements.push('✓ 完成质量控制验证');
        }

        // 计算优化程度
        const originalLength = text.length;
        const optimizedLength = optimized.length;
        const changeRate = Math.abs(optimizedLength - originalLength) / originalLength * 100;

        // 生成增强版优化摘要
        const summary = {
            originalWordCount: this.countWords(text),
            optimizedWordCount: this.countWords(optimized),
            changeRate: changeRate.toFixed(1),
            improvementCount: improvements.length,
            academicScore: this.calculateAcademicScore(optimized),
            innovationDensity: this.calculateInnovationDensity(optimized),
            disruptiveIndex: enableQualityControl ? qualityMetrics?.disruptiveIndex : null
        };

        return {
            optimizedText: optimized,
            improvements: improvements,
            statistics: statistics,
            summary: summary,
            advancedAnalysis: advancedAnalysis,
            qualityMetrics: qualityMetrics,
            confidence: improvements.length >= 5 ? 'high' : improvements.length >= 3 ? 'medium' : 'low'
        };
    }

    // 计算词数
    countWords(text) {
        const words = text.match(/[\u4e00-\u9fa5]+|[a-zA-Z]+/g);
        return words ? words.length : 0;
    }

    // 计算学术性评分
    calculateAcademicScore(text) {
        const academicKeywords = [
            '研究', '分析', '实验', '数据', '方法', '结果', '系统', '算法',
            '模型', '架构', '策略', '技术', '验证', '评估', '优化', '框架'
        ];
        
        let score = 0;
        for (let keyword of academicKeywords) {
            const regex = new RegExp(keyword, 'g');
            const matches = text.match(regex);
            if (matches) {
                score += matches.length;
            }
        }
        
        // 标准化到0-100分
        const wordCount = this.countWords(text);
        const density = score / wordCount * 100;
        return Math.min(100, Math.max(0, density * 10));
    }

    /**
     * 优势解构模块 - 提取核心优势项
     * @param {string} text - 输入文本
     * @returns {Object} 量化报告
     */
    extractCoreAdvantages(text) {
        const report = {
            theoreticalContribution: this.calculateTheoreticalContribution(text),
            technicalNovelty: this.calculateTechnicalNovelty(text),
            argumentRigor: this.calculateArgumentRigor(text),
            interdisciplinaryFusion: this.calculateInterdisciplinaryFusion(text),
            overallScore: 0,
            recommendations: []
        };

        // 计算综合评分
        report.overallScore = (
            report.theoreticalContribution.score * 0.3 +
            report.technicalNovelty.score * 0.25 +
            report.argumentRigor.score * 0.25 +
            report.interdisciplinaryFusion.score * 0.2
        );

        // 生成改进建议
        report.recommendations = this.generateImprovementRecommendations(report);

        return report;
    }

    /**
     * 计算理论贡献值 (引用经典理论与新建构模型的比例)
     */
    calculateTheoreticalContribution(text) {
        let classicTheoryCount = 0;
        let newModelCount = 0;
        let details = [];

        // 经典理论关键词
        const classicTheoryKeywords = [
            '经典理论', '传统理论', '基础理论', '成熟理论',
            '已有理论', '现有理论', '传统模型', '经典模型'
        ];

        // 新建构模型关键词
        const newModelKeywords = [
            '新模型', '创新模型', '改进模型', '优化模型',
            '新理论', '创新理论', '新框架', '创新框架'
        ];

        classicTheoryCount = classicTheoryKeywords.filter(keyword => text.includes(keyword)).length;
        newModelCount = newModelKeywords.filter(keyword => text.includes(keyword)).length;

        const totalTheory = classicTheoryCount + newModelCount;
        const newModelRatio = totalTheory > 0 ? (newModelCount / totalTheory) * 100 : 0;

        let score = 0;
        if (newModelRatio >= 60) score = 90;
        else if (newModelRatio >= 40) score = 70;
        else if (newModelRatio >= 20) score = 50;
        else score = 30;

        details.push(`经典理论引用: ${classicTheoryCount}处`);
        details.push(`新建构模型: ${newModelCount}处`);
        details.push(`创新比例: ${newModelRatio.toFixed(1)}%`);

        return {
            score,
            ratio: newModelRatio,
            classicCount: classicTheoryCount,
            newCount: newModelCount,
            details,
            level: score >= 80 ? '优秀' : score >= 60 ? '良好' : score >= 40 ? '一般' : '需改进'
        };
    }

    /**
     * 计算技术新颖度 (采用2023年后新方法/数据集的比例)
     */
    calculateTechnicalNovelty(text) {
        let modernTechCount = 0;
        let traditionalTechCount = 0;
        let details = [];

        // 2023年后新技术
        const modernTech = [
            'ChatGPT', 'GPT-4', 'Claude', 'Gemini', 'LLaMA',
            'Transformer', '大语言模型', 'BERT', 'T5',
            '联邦学习', '边缘计算', '量子计算', '神经架构搜索'
        ];

        // 传统技术
        const traditionalTech = [
            'SVM', '决策树', '随机森林', '朴素贝叶斯',
            '线性回归', '逻辑回归', 'K-means', 'PCA'
        ];

        modernTechCount = modernTech.filter(tech => text.includes(tech)).length;
        traditionalTechCount = traditionalTech.filter(tech => text.includes(tech)).length;

        const totalTech = modernTechCount + traditionalTechCount;
        const modernRatio = totalTech > 0 ? (modernTechCount / totalTech) * 100 : 0;

        let score = 0;
        if (modernRatio >= 70) score = 95;
        else if (modernRatio >= 50) score = 80;
        else if (modernRatio >= 30) score = 60;
        else if (modernRatio >= 10) score = 40;
        else score = 20;

        details.push(`现代技术: ${modernTechCount}项`);
        details.push(`传统技术: ${traditionalTechCount}项`);
        details.push(`新颖度比例: ${modernRatio.toFixed(1)}%`);

        return {
            score,
            ratio: modernRatio,
            modernCount: modernTechCount,
            traditionalCount: traditionalTechCount,
            details,
            level: score >= 85 ? '前沿' : score >= 65 ? '先进' : score >= 45 ? '一般' : '落后'
        };
    }

    /**
     * 计算论证严谨性 (实验重复次数/敏感性分析覆盖率)
     */
    calculateArgumentRigor(text) {
        let rigorScore = 0;
        let details = [];

        // 实验重复性指标
        const repetitionKeywords = ['重复实验', '多次实验', '重复验证', '重现性'];
        const repetitionCount = repetitionKeywords.filter(keyword => text.includes(keyword)).length;
        rigorScore += repetitionCount * 15;

        if (repetitionCount > 0) {
            details.push(`实验重复性: ${repetitionCount}项`);
        }

        // 敏感性分析
        const sensitivityKeywords = ['敏感性分析', '稳健性检验', '鲁棒性测试', '参数敏感性'];
        const sensitivityCount = sensitivityKeywords.filter(keyword => text.includes(keyword)).length;
        rigorScore += sensitivityCount * 20;

        if (sensitivityCount > 0) {
            details.push(`敏感性分析: ${sensitivityCount}项`);
        }

        // 统计显著性
        const statisticalKeywords = ['显著性检验', 'p值', '置信区间', '效应量'];
        const statisticalCount = statisticalKeywords.filter(keyword => text.includes(keyword)).length;
        rigorScore += statisticalCount * 10;

        if (statisticalCount > 0) {
            details.push(`统计检验: ${statisticalCount}项`);
        }

        // 对照组设置
        const controlKeywords = ['对照组', '控制组', '基线', '对比实验'];
        const controlCount = controlKeywords.filter(keyword => text.includes(keyword)).length;
        rigorScore += controlCount * 15;

        if (controlCount > 0) {
            details.push(`对照设置: ${controlCount}项`);
        }

        const finalScore = Math.min(100, rigorScore);

        return {
            score: finalScore,
            repetitionCount,
            sensitivityCount,
            statisticalCount,
            controlCount,
            details,
            level: finalScore >= 80 ? '严谨' : finalScore >= 60 ? '较严谨' : finalScore >= 40 ? '一般' : '需加强'
        };
    }

    /**
     * 计算跨学科融合度 (非本领域文献引用占比)
     */
    calculateInterdisciplinaryFusion(text) {
        let fusionScore = 0;
        let details = [];

        // 跨学科关键词
        const interdisciplinaryKeywords = [
            '跨学科', '多学科', '交叉学科', '学科融合',
            '综合性', '多领域', '交叉研究', '融合创新'
        ];

        const fusionCount = interdisciplinaryKeywords.filter(keyword => text.includes(keyword)).length;
        fusionScore += fusionCount * 20;

        if (fusionCount > 0) {
            details.push(`跨学科表述: ${fusionCount}处`);
        }

        // 不同领域方法融合
        const methodFusionKeywords = [
            '结合', '融合', '整合', '综合运用',
            '多方法', '混合方法', '协同', '耦合'
        ];

        const methodFusionCount = methodFusionKeywords.filter(keyword => text.includes(keyword)).length;
        fusionScore += methodFusionCount * 10;

        if (methodFusionCount > 0) {
            details.push(`方法融合: ${methodFusionCount}处`);
        }

        // 多元视角
        const perspectiveKeywords = [
            '多角度', '多维度', '多层面', '全方位',
            '系统性', '整体性', '综合性'
        ];

        const perspectiveCount = perspectiveKeywords.filter(keyword => text.includes(keyword)).length;
        fusionScore += perspectiveCount * 15;

        if (perspectiveCount > 0) {
            details.push(`多元视角: ${perspectiveCount}处`);
        }

        const finalScore = Math.min(100, fusionScore);

        return {
            score: finalScore,
            fusionCount,
            methodFusionCount,
            perspectiveCount,
            details,
            level: finalScore >= 80 ? '高度融合' : finalScore >= 60 ? '较好融合' : finalScore >= 40 ? '一般融合' : '缺乏融合'
        };
    }

    /**
     * 生成改进建议
     */
    generateImprovementRecommendations(report) {
        const recommendations = [];

        if (report.theoreticalContribution.score < 60) {
            recommendations.push({
                type: '理论贡献',
                priority: 'high',
                suggestion: '增加新理论模型构建，提升理论创新比例至40%以上'
            });
        }

        if (report.technicalNovelty.score < 70) {
            recommendations.push({
                type: '技术新颖度',
                priority: 'high',
                suggestion: '采用更多2023年后的新技术和方法，如大语言模型、联邦学习等'
            });
        }

        if (report.argumentRigor.score < 60) {
            recommendations.push({
                type: '论证严谨性',
                priority: 'medium',
                suggestion: '增加敏感性分析、重复实验和统计检验，提升论证可信度'
            });
        }

        if (report.interdisciplinaryFusion.score < 50) {
            recommendations.push({
                type: '跨学科融合',
                priority: 'medium',
                suggestion: '引入其他学科的理论和方法，增强研究的综合性和创新性'
            });
        }

        return recommendations;
    }

    /**
     * 融合生成协议 - 生成优化版本
     * @param {string} textA - 段落A
     * @param {string} textB - 段落B
     * @param {Object} options - 优化选项
     * @returns {Object} 融合优化结果
     */
    generateFusedOptimization(textA, textB, options = {}) {
        const {
            targetStructure = 'JACS', // JACS式紧凑结构
            maxWords = 4000,           // 主文本最大词数
            innovationDensity = 3,     // 创新点密度要求
            includeEthicsStatement = true, // 包含伦理声明
            enableVisualization = true     // 启用可视化组件
        } = options;

        // 执行动态对比分析
        const comparison = this.performDynamicComparison(textA, textB);

        // 提取各段落优势
        const advantagesA = this.extractCoreAdvantages(textA);
        const advantagesB = this.extractCoreAdvantages(textB);

        // 生成融合文本
        const fusedText = this.performTextFusion(textA, textB, comparison, advantagesA, advantagesB);

        // 应用JACS结构优化
        const structuredText = this.applyJACSStructure(fusedText, maxWords);

        // 增强创新点密度
        const innovationEnhanced = this.enhanceInnovationDensity(structuredText, innovationDensity);

        // 添加伦理声明
        const ethicsCompliant = includeEthicsStatement ?
            this.addEthicsStatement(innovationEnhanced) : innovationEnhanced;

        // 质量控制验证
        const qualityCheck = this.performQualityControl(ethicsCompliant);

        return {
            originalTextA: textA,
            originalTextB: textB,
            fusedText: ethicsCompliant,
            comparison: comparison,
            advantages: { textA: advantagesA, textB: advantagesB },
            qualityMetrics: qualityCheck,
            optimizationSummary: this.generateOptimizationSummary(comparison, advantagesA, advantagesB, qualityCheck),
            recommendations: this.generatePublicationRecommendations(qualityCheck)
        };
    }

    /**
     * 执行文本融合
     */
    performTextFusion(textA, textB, comparison, advantagesA, advantagesB) {
        let fusedText = '';

        // 选择最佳结构框架
        const bestStructure = comparison.structuralCompliance.textA.score >
                             comparison.structuralCompliance.textB.score ? textA : textB;

        // 融合创新要素
        const innovationElements = this.mergeInnovationElements(textA, textB, comparison);

        // 融合证据支撑
        const evidenceElements = this.mergeEvidenceElements(textA, textB, comparison);

        // 构建融合文本
        fusedText = this.constructFusedNarrative(bestStructure, innovationElements, evidenceElements);

        return fusedText;
    }

    /**
     * 合并创新要素
     */
    mergeInnovationElements(textA, textB, comparison) {
        const elements = [];

        // 从创新性更高的段落提取核心创新点
        const primarySource = comparison.innovationIndex.textA.density >
                             comparison.innovationIndex.textB.density ? textA : textB;
        const secondarySource = primarySource === textA ? textB : textA;

        // 提取主要创新点
        const primaryInnovations = this.extractInnovationPoints(primarySource);
        const secondaryInnovations = this.extractInnovationPoints(secondarySource);

        // 智能融合创新点
        elements.push(...primaryInnovations.slice(0, 2)); // 取前2个主要创新点
        elements.push(...secondaryInnovations.slice(0, 1)); // 取1个次要创新点

        return elements;
    }

    /**
     * 提取创新点
     */
    extractInnovationPoints(text) {
        const innovations = [];
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        sentences.forEach(sentence => {
            const innovationScore = this.calculateSentenceInnovation(sentence);
            if (innovationScore > 0.6) {
                innovations.push({
                    text: sentence.trim(),
                    score: innovationScore,
                    type: this.classifyInnovationType(sentence)
                });
            }
        });

        return innovations.sort((a, b) => b.score - a.score);
    }

    /**
     * 计算句子创新性
     */
    calculateSentenceInnovation(sentence) {
        let score = 0;

        // 检查创新关键词
        const innovationKeywords = this.innovationMetrics.disruptiveKeywords.concat(
            this.innovationMetrics.methodBreakthroughKeywords,
            this.innovationMetrics.modernTechKeywords
        );

        const foundKeywords = innovationKeywords.filter(keyword => sentence.includes(keyword));
        score += foundKeywords.length * 0.2;

        // 检查数值和具体描述
        if (/\d+/.test(sentence)) score += 0.1;
        if (sentence.length > 50) score += 0.1;

        return Math.min(1, score);
    }

    /**
     * 分类创新类型
     */
    classifyInnovationType(sentence) {
        if (this.innovationMetrics.methodBreakthroughKeywords.some(keyword => sentence.includes(keyword))) {
            return 'method';
        }
        if (this.innovationMetrics.modernTechKeywords.some(keyword => sentence.includes(keyword))) {
            return 'technology';
        }
        if (this.innovationMetrics.disruptiveKeywords.some(keyword => sentence.includes(keyword))) {
            return 'concept';
        }
        return 'general';
    }

    /**
     * 合并证据要素
     */
    mergeEvidenceElements(textA, textB, comparison) {
        const elements = [];

        // 选择证据等级更高的段落作为主要来源
        const primarySource = comparison.evidenceLevel.textA.highestLevel <
                             comparison.evidenceLevel.textB.highestLevel ? textA : textB;
        const secondarySource = primarySource === textA ? textB : textA;

        // 提取高质量证据
        elements.push(...this.extractEvidenceElements(primarySource, 'primary'));
        elements.push(...this.extractEvidenceElements(secondarySource, 'secondary'));

        return elements;
    }

    /**
     * 提取证据要素
     */
    extractEvidenceElements(text, type) {
        const elements = [];

        // 提取统计数据
        const statisticalData = text.match(/p\s*[<>=]\s*0\.\d+|r\s*=\s*0\.\d+|\d+\.\d+%|\(n\s*=\s*\d+\)/g) || [];
        elements.push(...statisticalData.map(data => ({ type: 'statistical', content: data, source: type })));

        // 提取引用
        const references = text.match(/\[\d+\]|\(\d{4}\)/g) || [];
        elements.push(...references.map(ref => ({ type: 'reference', content: ref, source: type })));

        return elements;
    }

    /**
     * 构建融合叙述
     */
    constructFusedNarrative(baseStructure, innovations, evidence) {
        let narrative = baseStructure;

        // 智能插入创新要素
        innovations.forEach((innovation, index) => {
            if (index < 3) { // 限制创新点数量
                const insertPosition = this.findOptimalInsertPosition(narrative, innovation.type);
                narrative = this.insertInnovationElement(narrative, innovation, insertPosition);
            }
        });

        // 增强证据支撑
        evidence.forEach(evidenceItem => {
            if (evidenceItem.type === 'statistical') {
                narrative = this.enhanceWithStatisticalEvidence(narrative, evidenceItem);
            }
        });

        return narrative;
    }

    /**
     * 找到最佳插入位置
     */
    findOptimalInsertPosition(text, innovationType) {
        const sentences = text.split(/[。！？]/);

        // 根据创新类型选择插入位置
        switch (innovationType) {
            case 'method':
                return Math.floor(sentences.length * 0.3); // 方法部分
            case 'technology':
                return Math.floor(sentences.length * 0.4); // 技术部分
            case 'concept':
                return Math.floor(sentences.length * 0.2); // 概念部分
            default:
                return Math.floor(sentences.length * 0.5); // 中间位置
        }
    }

    /**
     * 插入创新要素
     */
    insertInnovationElement(text, innovation, position) {
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        if (position < sentences.length) {
            sentences.splice(position, 0, innovation.text);
        } else {
            sentences.push(innovation.text);
        }

        return sentences.join('。') + '。';
    }

    /**
     * 用统计证据增强
     */
    enhanceWithStatisticalEvidence(text, evidence) {
        // 简单的证据增强逻辑
        return text.replace(/结果表明/g, `结果表明(${evidence.content})`);
    }

    /**
     * 应用JACS式紧凑结构
     */
    applyJACSStructure(text, maxWords) {
        const words = this.countWords(text);

        if (words <= maxWords) {
            return text;
        }

        // 压缩策略：移除冗余表述，保留核心内容
        let compressedText = text;

        // 移除重复的连接词
        compressedText = compressedText.replace(/，同时，/g, '，');
        compressedText = compressedText.replace(/，此外，/g, '，');

        // 简化表述
        compressedText = compressedText.replace(/通过深入分析发现/g, '分析发现');
        compressedText = compressedText.replace(/经过仔细研究表明/g, '研究表明');

        // 如果仍然超长，建议移入补充材料
        const finalWords = this.countWords(compressedText);
        if (finalWords > maxWords) {
            compressedText += '\n\n[建议：详细方法描述移入补充材料]';
        }

        return compressedText;
    }

    /**
     * 增强创新点密度
     */
    enhanceInnovationDensity(text, targetDensity) {
        const currentDensity = this.calculateInnovationDensity(text);

        if (currentDensity >= targetDensity) {
            return text;
        }

        let enhancedText = text;
        const wordsNeeded = Math.ceil((targetDensity - currentDensity) * this.countWords(text) / 1000);

        // 添加创新性表述
        const innovationEnhancements = [
            '采用创新性方法',
            '突破传统限制',
            '首次实现',
            '开创性地提出',
            '革命性改进'
        ];

        for (let i = 0; i < wordsNeeded && i < innovationEnhancements.length; i++) {
            const enhancement = innovationEnhancements[i];
            enhancedText = this.insertInnovationPhrase(enhancedText, enhancement);
        }

        return enhancedText;
    }

    /**
     * 计算创新点密度
     */
    calculateInnovationDensity(text) {
        const wordCount = this.countWords(text);
        const innovationCount = this.innovationMetrics.disruptiveKeywords
            .filter(keyword => text.includes(keyword)).length +
            this.innovationMetrics.methodBreakthroughKeywords
            .filter(keyword => text.includes(keyword)).length;

        return wordCount > 0 ? (innovationCount / wordCount) * 1000 : 0;
    }

    /**
     * 插入创新性短语
     */
    insertInnovationPhrase(text, phrase) {
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        const insertPosition = Math.floor(sentences.length / 2);

        if (insertPosition < sentences.length) {
            sentences[insertPosition] = phrase + '，' + sentences[insertPosition];
        }

        return sentences.join('。') + '。';
    }

    /**
     * 添加伦理声明
     */
    addEthicsStatement(text) {
        const ethicsStatements = [
            '\n\n【AI工具使用声明】本研究使用ChatGPT进行语言润色（范围：语法修正+术语统一），核心论证与实验设计均由作者完成。',
            '\n\n【数据伦理声明】本研究遵循数据保护原则，所有数据处理均已通过伦理审查，并采用偏差修正方法确保结果可靠性。'
        ];

        return text + ethicsStatements[0] + ethicsStatements[1];
    }

    /**
     * 质量控制机制 - 创新性验证回路
     */
    performQualityControl(text) {
        const qualityMetrics = {
            disruptiveIndex: this.calculateDisruptiveIndex(text),
            innovationDensity: this.calculateInnovationDensity(text),
            ethicsCompliance: this.checkEthicsCompliance(text),
            structuralIntegrity: this.checkStructuralIntegrity(text),
            overallQuality: 0,
            recommendations: []
        };

        // 计算综合质量分数
        qualityMetrics.overallQuality = (
            (qualityMetrics.disruptiveIndex >= this.config.disruptiveIndexThreshold ? 25 : 0) +
            (qualityMetrics.innovationDensity >= this.config.innovationDensityThreshold ? 25 : 0) +
            (qualityMetrics.ethicsCompliance ? 25 : 0) +
            (qualityMetrics.structuralIntegrity >= 70 ? 25 : 0)
        );

        // 生成质量改进建议
        if (qualityMetrics.disruptiveIndex < this.config.disruptiveIndexThreshold) {
            qualityMetrics.recommendations.push('提升颠覆性指数至Science级别阈值(≥0.35)');
        }
        if (qualityMetrics.innovationDensity < this.config.innovationDensityThreshold) {
            qualityMetrics.recommendations.push('增加创新点密度至Nature子刊基准(≥3点/千字)');
        }
        if (!qualityMetrics.ethicsCompliance) {
            qualityMetrics.recommendations.push('添加AI工具使用和数据伦理声明');
        }

        return qualityMetrics;
    }

    /**
     * 计算颠覆性指数 (CD指数)
     */
    calculateDisruptiveIndex(text) {
        // 简化的颠覆性指数计算
        const disruptiveElements = this.innovationMetrics.disruptiveKeywords
            .filter(keyword => text.includes(keyword)).length;
        const totalWords = this.countWords(text);

        // 基于颠覆性关键词密度的简化计算
        const density = totalWords > 0 ? disruptiveElements / totalWords : 0;
        return Math.min(1, density * 100); // 标准化到0-1范围
    }

    /**
     * 检查伦理合规性
     */
    checkEthicsCompliance(text) {
        const ethicsKeywords = ['AI工具使用声明', '数据伦理声明', '伦理审查', '偏差修正'];
        return ethicsKeywords.some(keyword => text.includes(keyword));
    }

    /**
     * 检查结构完整性
     */
    checkStructuralIntegrity(text) {
        let score = 0;

        // 检查基本结构要素
        const structureElements = ['方法', '结果', '分析', '结论'];
        const foundElements = structureElements.filter(element => text.includes(element));
        score += foundElements.length * 20;

        // 检查逻辑连贯性
        const transitionWords = ['因此', '然而', '此外', '综上所述'];
        const transitionCount = transitionWords.filter(word => text.includes(word)).length;
        score += Math.min(20, transitionCount * 5);

        return score;
    }

    /**
     * 生成优化摘要
     */
    generateOptimizationSummary(comparison, advantagesA, advantagesB, qualityCheck) {
        return {
            comparisonHighlights: {
                structuralWinner: comparison.structuralCompliance.comparison,
                innovationWinner: comparison.innovationIndex.comparison,
                evidenceWinner: comparison.evidenceLevel.comparison,
                dataWinner: comparison.dataIntegrity.comparison
            },
            fusionStrategies: comparison.fusionRecommendations.map(rec => rec.description),
            qualityMetrics: {
                disruptiveIndex: qualityCheck.disruptiveIndex.toFixed(3),
                innovationDensity: qualityCheck.innovationDensity.toFixed(1),
                overallQuality: qualityCheck.overallQuality
            },
            improvementAreas: qualityCheck.recommendations
        };
    }

    /**
     * 生成发表建议
     */
    generatePublicationRecommendations(qualityCheck) {
        const recommendations = [];

        if (qualityCheck.overallQuality >= 90) {
            recommendations.push({
                level: 'top-tier',
                journals: ['Nature', 'Science', 'Cell'],
                confidence: 'high'
            });
        } else if (qualityCheck.overallQuality >= 75) {
            recommendations.push({
                level: 'high-impact',
                journals: ['Nature子刊', 'PNAS', '领域顶级期刊'],
                confidence: 'medium-high'
            });
        } else if (qualityCheck.overallQuality >= 60) {
            recommendations.push({
                level: 'solid',
                journals: ['SCI一区期刊', '专业领域期刊'],
                confidence: 'medium'
            });
        } else {
            recommendations.push({
                level: 'needs-improvement',
                journals: ['建议先完善后投稿'],
                confidence: 'low'
            });
        }

        return recommendations;
    }

    // 批量优化
    batchOptimize(texts, options = {}) {
        return texts.map(text => this.optimize(text, options));
    }

    /**
     * 高级融合生成方法 - 用于自动化学术架构
     * @param {string} textA - 文本A
     * @param {string} textB - 文本B
     * @param {Object} options - 融合选项
     * @returns {Object} 融合结果
     */
    performAdvancedFusion(textA, textB, options = {}) {
        const {
            jacsStructure = true,
            innovationDensity = 3,
            ethicsCompliance = true,
            qualityControl = true
        } = options;

        try {
            // 执行动态对比分析
            const comparison = this.performDynamicComparison(textA, textB);

            // 提取各段落优势
            const advantagesA = this.extractCoreAdvantages(textA);
            const advantagesB = this.extractCoreAdvantages(textB);

            // 生成融合文本
            const fusedText = this.performTextFusion(textA, textB, comparison, advantagesA, advantagesB);

            // 应用JACS结构优化
            let optimizedText = fusedText;
            if (jacsStructure) {
                optimizedText = this.applyJACSStructure(optimizedText, 800);
            }

            // 增强创新点密度
            if (innovationDensity > 1) {
                optimizedText = this.enhanceInnovationDensity(optimizedText, innovationDensity);
            }

            // 添加伦理声明
            if (ethicsCompliance) {
                optimizedText = this.addEthicsStatement(optimizedText);
            }

            // 质量控制验证
            let qualityMetrics = null;
            if (qualityControl) {
                qualityMetrics = this.performQualityControl(optimizedText);
            }

            return {
                optimizedText: optimizedText,
                comparison: comparison,
                advantagesA: advantagesA,
                advantagesB: advantagesB,
                qualityMetrics: qualityMetrics,
                appliedOptions: options,
                fusionStrategies: comparison.fusionRecommendations || [],
                improvements: [
                    '✓ 完成动态对比分析',
                    '✓ 提取核心优势特征',
                    '✓ 执行智能融合生成',
                    ...(jacsStructure ? ['✓ 应用JACS紧凑结构'] : []),
                    ...(innovationDensity > 1 ? [`✓ 提升创新点密度至${innovationDensity}个/千字`] : []),
                    ...(ethicsCompliance ? ['✓ 添加伦理规范声明'] : []),
                    ...(qualityControl ? ['✓ 执行质量控制验证'] : [])
                ]
            };

        } catch (error) {
            console.error('高级融合生成失败:', error);

            // 返回基础融合结果作为备用
            const basicFusion = this.optimize(textA + '\n\n' + textB, {
                synonymReplacement: true,
                templateReplacement: true,
                technicalEnhancement: true,
                structureOptimization: true,
                rigorEnhancement: true
            });

            return {
                optimizedText: basicFusion.optimizedText,
                comparison: { score: 0.5, analysis: '基础对比分析' },
                advantagesA: { overallScore: 50 },
                advantagesB: { overallScore: 50 },
                qualityMetrics: null,
                appliedOptions: options,
                fusionStrategies: ['使用基础优化策略'],
                improvements: basicFusion.improvements,
                fallbackMode: true,
                error: error.message
            };
        }
    }
}

// 创建全局实例
const academicOptimizer = new AcademicOptimizer();

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AcademicOptimizer };
}
