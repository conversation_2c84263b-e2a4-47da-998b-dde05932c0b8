# Technology Stack & Build System

## Primary Technologies

### C# WPF Application (DocxToMarkdownConverter)
- **Framework**: .NET 8.0 Windows
- **UI Framework**: WPF with Material Design Themes 4.9.0
- **Document Processing**: DocumentFormat.OpenXml 3.0.1
- **Architecture**: MVVM pattern with dependency injection
- **Logging**: Serilog with file and console sinks

### Python GUI Application (docx转到md转换器)
- **Python Version**: 3.12+ required
- **GUI Framework**: Tkinter with tkinterdnd2 for drag-drop
- **Document Processing**: python-docx, lxml
- **Image Processing**: Pillow
- **Build Tool**: PyInstaller for executable creation

### Web Application (AI检测助手)
- **Frontend**: Vanilla JavaScript (ES6+), HTML5, CSS3
- **Backend Integration**: Node.js server for local deployment
- **AI Integration**: Ollama API for local LLM models
- **Supported Models**: Qwen3, Qwen2.5, Llama3 series

## Build Commands

### C# WPF Application
```bash
# Build the project
dotnet build

# Run the application
dotnet run

# Publish for deployment
dotnet publish -c Release -r win-x64 --self-contained
```

### Python Application
```bash
# Install dependencies
pip install -r requirements.txt

# Run GUI application
python docx2md_gui.py

# Build executable (Windows)
python build_clean.py
# or use batch file
构建可执行文件.bat

# Quick start menu
quick_start.bat
```

### Web Application
```bash
# Install Node.js dependencies
npm install

# Start development server
npm run dev

# Start production server
npm run prod

# Deploy with custom domain
node server.js --port 3000 --domain ai-detector.local
```

## Development Environment Setup

### Prerequisites
- **.NET 8 SDK** (for C# application)
- **Python 3.12+** (for Python application)
- **Node.js 14+** (for web application)
- **Ollama** (for AI features)

### Ollama Setup
```bash
# Install Ollama (visit ollama.ai for installers)

# Start service with CORS configuration
export OLLAMA_ORIGINS="http://localhost,http://127.0.0.1,file://"
export OLLAMA_HOST=127.0.0.1:11434
ollama serve

# Install recommended models
ollama pull qwen3:30b-a3b      # High performance (20GB+ RAM)
ollama pull qwen2.5:14b-instruct  # Balanced (8GB+ RAM)
ollama pull qwen2.5:7b-instruct   # Lightweight (4GB+ RAM)
```

## Testing Commands

### C# Application
```bash
# Run unit tests
dotnet test

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Python Application
```bash
# Run tests
pytest

# Run with coverage
pytest --cov=src

# Type checking
mypy src/
```

### Web Application
```bash
# Test Ollama connection
# Open test_ollama_connection.html in browser

# Manual API testing
curl http://localhost:11434/api/tags
```

## Package Management

### C# Dependencies
- DocumentFormat.OpenXml (document processing)
- MaterialDesignThemes (UI components)
- Microsoft.Extensions.* (DI, configuration, logging)
- Serilog (structured logging)

### Python Dependencies
- python-docx (DOCX processing)
- Pillow (image processing)
- tkinterdnd2 (enhanced drag-drop)
- lxml (XML processing)
- latex2mathml (formula conversion)

### JavaScript Dependencies
- No external frameworks (vanilla JS approach)
- Node.js for local server deployment only