using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Services;
using UserControl = System.Windows.Controls.UserControl;

namespace DocxToMarkdownConverter.Views;

public partial class SettingsView : UserControl, INavigationAware
{
    private SettingsViewModel? _viewModel;

    public SettingsView()
    {
        InitializeComponent();
    }

    public SettingsView(SettingsViewModel viewModel) : this()
    {
        _viewModel = viewModel;
        // Don't set DataContext immediately to avoid triggering property getters
    }

    public void OnNavigatedTo(object? parameter)
    {
        // Set DataContext only when actually navigated to
        if (_viewModel != null && DataContext == null)
        {
            DataContext = _viewModel;
        }
    }

    public void OnNavigatedFrom()
    {
        // Optional cleanup when navigating away
    }

    public Task<bool> CanNavigateAwayAsync()
    {
        // Always allow navigation away from settings
        return Task.FromResult(true);
    }
}