using DocumentFormat.OpenXml.Packaging;
using DocxToMarkdownConverter.Models;
using System.Drawing;
using System.Drawing.Imaging;
using Microsoft.Extensions.Logging;
using System.IO;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// Handles image extraction and processing from DOCX documents
/// </summary>
public class ImageProcessor : IImageProcessor
{
    private readonly ILogger<ImageProcessor> _logger;
    private static readonly Dictionary<string, System.Drawing.Imaging.ImageFormat> SupportedFormats = new()
    {
        { "image/png", System.Drawing.Imaging.ImageFormat.Png },
        { "image/jpeg", System.Drawing.Imaging.ImageFormat.Jpeg },
        { "image/jpg", System.Drawing.Imaging.ImageFormat.Jpeg },
        { "image/gif", System.Drawing.Imaging.ImageFormat.Gif },
        { "image/bmp", System.Drawing.Imaging.ImageFormat.Bmp },
        { "image/tiff", System.Drawing.Imaging.ImageFormat.Tiff }
    };

    public ImageProcessor(ILogger<ImageProcessor> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<string> ProcessImageAsync(object imagePart, string outputDirectory)
    {
        if (imagePart is not ImagePart imagePartTyped)
        {
            _logger.LogWarning("Invalid image part provided for processing");
            return "<!-- Invalid image part -->";
        }

        try
        {
            // Generate unique filename for the image
            var imageId = GetImageId(imagePartTyped);
            var extension = GetImageExtension(imagePartTyped.ContentType);
            var fileName = $"image_{imageId}{extension}";
            var fullPath = Path.Combine(outputDirectory, fileName);

            // Ensure output directory exists
            Directory.CreateDirectory(outputDirectory);

            // Extract and save the image
            using var imageStream = imagePartTyped.GetStream();
            using var fileStream = new FileStream(fullPath, FileMode.Create, FileAccess.Write);
            await imageStream.CopyToAsync(fileStream);

            _logger.LogDebug("Successfully extracted image: {FileName}", fileName);

            // Get image dimensions for better formatting
            var dimensions = await GetImageDimensionsAsync(fullPath);
            var relativePath = Path.Combine("images", fileName).Replace("\\", "/");

            // Return Markdown image reference with better alt text
            var altText = $"Image";
            if (dimensions.HasValue)
            {
                altText = $"Image ({dimensions.Value.Width}x{dimensions.Value.Height})";
            }

            return $"![{altText}]({relativePath})";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process image part");
            return "<!-- Failed to process image -->";
        }
    }

    public async Task<string> ProcessImageWithCaptionAsync(object imagePart, string outputDirectory, string? caption = null)
    {
        var imageMarkdown = await ProcessImageAsync(imagePart, outputDirectory);

        if (imageMarkdown.Contains("Failed") || imageMarkdown.Contains("Invalid"))
            return imageMarkdown;

        if (!string.IsNullOrEmpty(caption))
        {
            // Add caption below the image
            return $"{imageMarkdown}\n\n*{caption}*";
        }

        return imageMarkdown;
    }

    public async Task<IEnumerable<string>> ExtractImagesAsync(WordprocessingDocument document, string outputDirectory)
    {
        if (document?.MainDocumentPart == null)
        {
            _logger.LogWarning("Document or MainDocumentPart is null");
            return Enumerable.Empty<string>();
        }

        var extractedImages = new List<string>();

        try
        {
            // Get all image parts from the document
            var imageParts = document.MainDocumentPart.ImageParts;
            
            if (!imageParts.Any())
            {
                _logger.LogDebug("No images found in document");
                return extractedImages;
            }

            // Create images subdirectory
            var imagesDirectory = Path.Combine(outputDirectory, "images");
            Directory.CreateDirectory(imagesDirectory);

            _logger.LogInformation("Found {ImageCount} images to extract", imageParts.Count());

            // Process each image part
            foreach (var imagePart in imageParts)
            {
                try
                {
                    var markdownReference = await ProcessImageAsync(imagePart, imagesDirectory);
                    if (!markdownReference.Contains("Failed") && !markdownReference.Contains("Invalid"))
                    {
                        extractedImages.Add(markdownReference);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to extract individual image");
                }
            }

            _logger.LogInformation("Successfully extracted {ExtractedCount} out of {TotalCount} images", 
                extractedImages.Count, imageParts.Count());

            return extractedImages;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to extract images from document");
            return extractedImages;
        }
    }

    /// <summary>
    /// Converts an image to the specified format and saves it
    /// </summary>
    public Task<string> ConvertAndSaveImageAsync(ImagePart imagePart, string outputPath, Models.ImageFormat targetFormat)
    {
        try
        {
            using var imageStream = imagePart.GetStream();
            using var image = Image.FromStream(imageStream);
            
            var format = GetSystemImageFormat(targetFormat);
            var extension = GetExtensionFromFormat(targetFormat);
            
            // Update output path with correct extension
            var pathWithoutExtension = Path.ChangeExtension(outputPath, null);
            var finalPath = $"{pathWithoutExtension}{extension}";
            
            // Save in the target format
            image.Save(finalPath, format);
            
            _logger.LogDebug("Converted and saved image to: {Path}", finalPath);
            return Task.FromResult(Path.GetFileName(finalPath));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to convert and save image");
            throw;
        }
    }

    /// <summary>
    /// Generates Markdown image reference with optional alt text and title
    /// </summary>
    public string GenerateMarkdownReference(string imagePath, string? altText = null, string? title = null)
    {
        var alt = altText ?? "Image";
        var reference = $"![{alt}]({imagePath})";
        
        if (!string.IsNullOrEmpty(title))
        {
            reference = $"![{alt}]({imagePath} \"{title}\")";
        }
        
        return reference;
    }

    private async Task<(int Width, int Height)?> GetImageDimensionsAsync(string imagePath)
    {
        try
        {
            using var image = await Task.Run(() => Image.FromFile(imagePath));
            return (image.Width, image.Height);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get image dimensions for {ImagePath}", imagePath);
            return null;
        }
    }

    private static string GetImageId(ImagePart imagePart)
    {
        // Generate a unique ID based on the image part URI or content
        var uri = imagePart.Uri?.ToString() ?? Guid.NewGuid().ToString();
        return Math.Abs(uri.GetHashCode()).ToString();
    }

    private static string GetImageExtension(string contentType)
    {
        return contentType.ToLowerInvariant() switch
        {
            "image/png" => ".png",
            "image/jpeg" or "image/jpg" => ".jpg",
            "image/gif" => ".gif",
            "image/bmp" => ".bmp",
            "image/tiff" => ".tiff",
            _ => ".png" // Default to PNG for unknown types
        };
    }

    private static System.Drawing.Imaging.ImageFormat GetSystemImageFormat(Models.ImageFormat format)
    {
        return format switch
        {
            Models.ImageFormat.Png => System.Drawing.Imaging.ImageFormat.Png,
            Models.ImageFormat.Jpg => System.Drawing.Imaging.ImageFormat.Jpeg,
            Models.ImageFormat.Gif => System.Drawing.Imaging.ImageFormat.Gif,
            Models.ImageFormat.Bmp => System.Drawing.Imaging.ImageFormat.Bmp,
            _ => System.Drawing.Imaging.ImageFormat.Png
        };
    }

    private static string GetExtensionFromFormat(Models.ImageFormat format)
    {
        return format switch
        {
            Models.ImageFormat.Png => ".png",
            Models.ImageFormat.Jpg => ".jpg",
            Models.ImageFormat.Gif => ".gif",
            Models.ImageFormat.Bmp => ".bmp",
            _ => ".png"
        };
    }
}