using System;
using System.Diagnostics;
using System.IO;
using System.Net.Http;
using System.Reflection;
using System.Security.Cryptography;
using System.Text.Json;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Timer = System.Threading.Timer;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 自动更新服务实现
/// </summary>
public class UpdateService : IUpdateService, IDisposable
{
    private readonly ILogger<UpdateService> _logger;
    private readonly HttpClient _httpClient;
    private readonly Timer? _updateCheckTimer;
    private readonly string _updateCheckUrl;
    private readonly string _tempDirectory;
    
    private bool _autoUpdateEnabled = true;
    private TimeSpan _updateCheckInterval = TimeSpan.FromHours(24);

    public event EventHandler<UpdateAvailableEventArgs>? UpdateAvailable;
    public event EventHandler<UpdateDownloadedEventArgs>? UpdateDownloaded;
    public event EventHandler<UpdateErrorEventArgs>? UpdateError;

    public UpdateService(ILogger<UpdateService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _httpClient = new HttpClient();
        _httpClient.Timeout = TimeSpan.FromMinutes(5);
        
        // 配置更新检查URL（实际部署时需要替换为真实的更新服务器）
        _updateCheckUrl = "https://api.github.com/repos/augmentcode/docx-converter/releases/latest";
        
        _tempDirectory = Path.Combine(Path.GetTempPath(), "DocxConverterUpdates");
        Directory.CreateDirectory(_tempDirectory);

        // 启动定时检查更新
        _updateCheckTimer = new Timer(CheckForUpdatesCallback, null, 
            TimeSpan.FromMinutes(5), _updateCheckInterval);
    }

    public async Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("检查更新...");

            var response = await _httpClient.GetStringAsync(_updateCheckUrl, cancellationToken);
            var releaseInfo = JsonSerializer.Deserialize<GitHubRelease>(response);

            if (releaseInfo == null)
            {
                return new UpdateInfo { IsAvailable = false };
            }

            var currentVersion = GetCurrentVersion();
            var latestVersion = releaseInfo.TagName.TrimStart('v');

            var isNewerVersion = IsNewerVersion(currentVersion.Version, latestVersion);
            
            var updateInfo = new UpdateInfo
            {
                Version = latestVersion,
                DownloadUrl = GetDownloadUrl(releaseInfo),
                ReleaseNotes = releaseInfo.Body ?? "",
                ReleaseDate = releaseInfo.PublishedAt,
                IsAvailable = isNewerVersion,
                IsRequired = false // 可以根据版本差异设置
            };

            if (isNewerVersion)
            {
                _logger.LogInformation("发现新版本: {NewVersion}, 当前版本: {CurrentVersion}", 
                    latestVersion, currentVersion.Version);
                
                UpdateAvailable?.Invoke(this, new UpdateAvailableEventArgs(updateInfo));
            }
            else
            {
                _logger.LogInformation("当前已是最新版本: {Version}", currentVersion.Version);
            }

            return updateInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "检查更新时发生错误");
            UpdateError?.Invoke(this, new UpdateErrorEventArgs(ex, "检查更新失败"));
            return new UpdateInfo { IsAvailable = false };
        }
    }

    public async Task<bool> DownloadUpdateAsync(UpdateInfo updateInfo, IProgress<DownloadProgress>? progress = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogInformation("开始下载更新: {Version}", updateInfo.Version);

            var fileName = $"DocxConverter_v{updateInfo.Version}.exe";
            var filePath = Path.Combine(_tempDirectory, fileName);

            // 如果文件已存在，删除它
            if (File.Exists(filePath))
            {
                File.Delete(filePath);
            }

            var startTime = DateTime.Now;
            using var response = await _httpClient.GetAsync(updateInfo.DownloadUrl, HttpCompletionOption.ResponseHeadersRead, cancellationToken);
            response.EnsureSuccessStatusCode();

            var totalBytes = response.Content.Headers.ContentLength ?? 0;
            updateInfo.FileSize = totalBytes;

            using var contentStream = await response.Content.ReadAsStreamAsync(cancellationToken);
            using var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write, FileShare.None, 8192, true);

            var buffer = new byte[8192];
            var totalBytesRead = 0L;
            int bytesRead;

            while ((bytesRead = await contentStream.ReadAsync(buffer, 0, buffer.Length, cancellationToken)) > 0)
            {
                await fileStream.WriteAsync(buffer, 0, bytesRead, cancellationToken);
                totalBytesRead += bytesRead;

                var elapsed = DateTime.Now - startTime;
                var speed = totalBytesRead / elapsed.TotalSeconds;
                var remaining = totalBytes > 0 && speed > 0 
                    ? TimeSpan.FromSeconds((totalBytes - totalBytesRead) / speed) 
                    : TimeSpan.Zero;

                progress?.Report(new DownloadProgress
                {
                    BytesReceived = totalBytesRead,
                    TotalBytes = totalBytes,
                    ElapsedTime = elapsed,
                    EstimatedTimeRemaining = remaining,
                    DownloadSpeed = speed
                });
            }

            // 验证文件完整性（如果提供了校验和）
            if (!string.IsNullOrEmpty(updateInfo.Checksum))
            {
                var fileChecksum = await CalculateFileChecksumAsync(filePath);
                if (!string.Equals(fileChecksum, updateInfo.Checksum, StringComparison.OrdinalIgnoreCase))
                {
                    File.Delete(filePath);
                    throw new InvalidOperationException("下载文件校验失败");
                }
            }

            _logger.LogInformation("更新下载完成: {FilePath}", filePath);
            UpdateDownloaded?.Invoke(this, new UpdateDownloadedEventArgs(filePath, updateInfo));
            
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "下载更新时发生错误");
            UpdateError?.Invoke(this, new UpdateErrorEventArgs(ex, "下载更新失败"));
            return false;
        }
    }

    public async Task<bool> InstallUpdateAsync(string updateFilePath)
    {
        try
        {
            _logger.LogInformation("开始安装更新: {FilePath}", updateFilePath);

            if (!File.Exists(updateFilePath))
            {
                throw new FileNotFoundException("更新文件不存在", updateFilePath);
            }

            // 创建安装脚本
            var scriptPath = Path.Combine(_tempDirectory, "install_update.bat");
            var currentExePath = Process.GetCurrentProcess().MainModule?.FileName ?? "";
            var backupPath = currentExePath + ".backup";

            var script = $@"
@echo off
echo 正在安装更新...
timeout /t 3 /nobreak > nul

echo 备份当前版本...
if exist ""{currentExePath}"" (
    copy ""{currentExePath}"" ""{backupPath}"" > nul
)

echo 安装新版本...
copy ""{updateFilePath}"" ""{currentExePath}"" > nul

if %errorlevel% equ 0 (
    echo 更新安装成功
    echo 启动新版本...
    start """" ""{currentExePath}""
    del ""{backupPath}"" > nul 2>&1
) else (
    echo 更新安装失败，恢复备份...
    if exist ""{backupPath}"" (
        copy ""{backupPath}"" ""{currentExePath}"" > nul
        del ""{backupPath}"" > nul
    )
    echo 启动原版本...
    start """" ""{currentExePath}""
)

del ""{updateFilePath}"" > nul 2>&1
del ""{scriptPath}"" > nul 2>&1
";

            await File.WriteAllTextAsync(scriptPath, script);

            // 启动安装脚本
            var processInfo = new ProcessStartInfo
            {
                FileName = scriptPath,
                UseShellExecute = false,
                CreateNoWindow = true
            };

            Process.Start(processInfo);

            // 退出当前应用程序
            await Task.Delay(1000); // 给脚本一点时间启动
            Environment.Exit(0);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "安装更新时发生错误");
            UpdateError?.Invoke(this, new UpdateErrorEventArgs(ex, "安装更新失败"));
            return false;
        }
    }

    public VersionInfo GetCurrentVersion()
    {
        var assembly = Assembly.GetExecutingAssembly();
        var version = assembly.GetName().Version?.ToString() ?? "*******";
        var assemblyPath = Path.Combine(AppContext.BaseDirectory, "DocxToMarkdownConverter.exe");
        var buildDate = File.GetLastWriteTime(assemblyPath).ToString("yyyy-MM-dd HH:mm:ss");

        return new VersionInfo
        {
            Version = version,
            BuildDate = buildDate,
            CommitHash = "unknown", // 可以在构建时注入
            Branch = "main"
        };
    }

    public void SetUpdateCheckInterval(TimeSpan interval)
    {
        _updateCheckInterval = interval;
        _updateCheckTimer?.Change(interval, interval);
        _logger.LogInformation("更新检查间隔已设置为: {Interval}", interval);
    }

    public void SetAutoUpdateEnabled(bool enabled)
    {
        _autoUpdateEnabled = enabled;
        _logger.LogInformation("自动更新已{Status}", enabled ? "启用" : "禁用");
    }

    private async void CheckForUpdatesCallback(object? state)
    {
        if (!_autoUpdateEnabled) return;

        try
        {
            await CheckForUpdatesAsync();
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "定时检查更新时发生错误");
        }
    }

    private bool IsNewerVersion(string currentVersion, string latestVersion)
    {
        try
        {
            var current = new Version(currentVersion);
            var latest = new Version(latestVersion);
            return latest > current;
        }
        catch
        {
            return false;
        }
    }

    private string GetDownloadUrl(GitHubRelease release)
    {
        // 查找Windows可执行文件
        foreach (var asset in release.Assets)
        {
            if (asset.Name.EndsWith(".exe", StringComparison.OrdinalIgnoreCase) ||
                asset.Name.Contains("win", StringComparison.OrdinalIgnoreCase))
            {
                return asset.BrowserDownloadUrl;
            }
        }

        // 如果没有找到特定的Windows资产，返回第一个
        return release.Assets.Length > 0 ? release.Assets[0].BrowserDownloadUrl : "";
    }

    private async Task<string> CalculateFileChecksumAsync(string filePath)
    {
        using var sha256 = SHA256.Create();
        using var stream = File.OpenRead(filePath);
        var hash = await Task.Run(() => sha256.ComputeHash(stream));
        return Convert.ToHexString(hash);
    }

    public void Dispose()
    {
        _updateCheckTimer?.Dispose();
        _httpClient?.Dispose();
    }
}

// GitHub API 响应模型
internal class GitHubRelease
{
    public string TagName { get; set; } = string.Empty;
    public string? Body { get; set; }
    public DateTime PublishedAt { get; set; }
    public GitHubAsset[] Assets { get; set; } = Array.Empty<GitHubAsset>();
}

internal class GitHubAsset
{
    public string Name { get; set; } = string.Empty;
    public string BrowserDownloadUrl { get; set; } = string.Empty;
}
