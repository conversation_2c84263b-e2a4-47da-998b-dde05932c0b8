<UserControl x:Class="DocxToMarkdownConverter.Controls.LanguageControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <StackPanel>
        <!-- 语言选择 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="选择语言 / Select Language"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <TextBlock Text="{Binding CurrentLanguageDisplayName, StringFormat='当前语言 / Current Language: {0}'}"
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           Foreground="{DynamicResource AppTextSecondaryBrush}"
                           Margin="0,0,0,8"/>

                <ComboBox ItemsSource="{Binding SupportedLanguages}"
                          SelectedItem="{Binding SelectedLanguage, Mode=TwoWay}"
                          DisplayMemberPath="NativeName"
                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                          materialDesign:HintAssist.Hint="Language / 语言"
                          IsEnabled="{Binding IsLanguageChanging, Converter={StaticResource InverseBooleanConverter}}"
                          MinWidth="200"
                          Margin="0,0,0,12"/>

                <CheckBox Content="{DynamicResource Language.FollowSystem}"
                          IsChecked="{Binding FollowSystemLanguage, Mode=TwoWay}"
                          Style="{StaticResource MaterialDesignCheckBox}"
                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                          Margin="0,0,0,8"/>

                <TextBlock Text="{Binding SystemLanguageDisplayName, StringFormat='系统语言 / System Language: {0}'}"
                           Style="{StaticResource MaterialDesignCaptionTextBlock}"
                           Foreground="{DynamicResource AppTextSecondaryBrush}"
                           Margin="0,0,0,8"/>
            </StackPanel>
        </materialDesign:Card>



        <!-- 语言更改提示 -->
        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
            <StackPanel>
                <TextBlock Text="重要提示 / Important Notice"
                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                           Margin="0,0,0,12"/>

                <!-- 语言更改进度提示 -->
                <Border Background="{DynamicResource MaterialDesignWarning}"
                        CornerRadius="8"
                        Padding="12"
                        Margin="0,0,0,8"
                        Visibility="{Binding IsLanguageChanging, Converter={StaticResource BooleanToVisibilityConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Information"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,8,0"
                                                 Foreground="White"/>
                        <TextBlock Text="正在更改语言... / Changing language..."
                                   Style="{StaticResource MaterialDesignBody2TextBlock}"
                                   Foreground="White"
                                   VerticalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- 重启提示 -->
                <Border Background="{DynamicResource MaterialDesignValidationErrorBrush}"
                        CornerRadius="8"
                        Padding="12"
                        Margin="0,0,0,12"
                        Opacity="0.8">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,4">
                            <materialDesign:PackIcon Kind="AlertCircle"
                                                     VerticalAlignment="Center"
                                                     Margin="0,0,8,0"
                                                     Foreground="White"/>
                            <TextBlock Text="重启提醒 / Restart Required"
                                       Style="{StaticResource MaterialDesignBody1TextBlock}"
                                       Foreground="White"
                                       FontWeight="Medium"
                                       VerticalAlignment="Center"/>
                        </StackPanel>
                        <TextBlock Text="{DynamicResource Language.RestartRequired}"
                                   Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                   Foreground="White"
                                   TextWrapping="Wrap"/>
                    </StackPanel>
                </Border>

                <!-- 操作按钮 -->
                <StackPanel Orientation="Horizontal"
                            HorizontalAlignment="Left">
                    <Button Content="刷新 / Refresh"
                            Command="{Binding RefreshLanguagesCommand}"
                            Style="{StaticResource MaterialDesignOutlinedButton}"
                            Foreground="{DynamicResource AppTextPrimaryBrush}"
                            BorderBrush="{DynamicResource AppBorderBrush}"
                            Padding="16,8"
                            MinWidth="120"
                            IsEnabled="{Binding IsLanguageChanging, Converter={StaticResource InverseBooleanConverter}}"/>
                </StackPanel>
            </StackPanel>
        </materialDesign:Card>
    </StackPanel>
</UserControl>
