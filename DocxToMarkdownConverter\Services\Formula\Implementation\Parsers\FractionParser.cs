using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Parsers;

/// <summary>
/// 分数解析器
/// </summary>
public class FractionParser : FormulaParserBase
{
    public FractionParser(ILogger<FractionParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.Fraction };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Parsing fraction element: {ElementId}", element.Id);

            if (element.SourceElement is not Fraction fraction)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a Fraction");
            }

            var structure = await ParseFractionAsync(fraction);
            var components = await CreateFractionComponentsAsync(fraction);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            _logger.LogDebug("Successfully parsed fraction in {ParseTime}ms", result.ParseTimeMs);
            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse fraction: {ex.Message}", ex);
        }
    }

    private async Task<FractionStructure> ParseFractionAsync(Fraction fraction)
    {
        var structure = new FractionStructure();

        try
        {
            // 解析分数属性
            var fractionProperties = fraction.GetFirstChild<FractionProperties>();
            if (fractionProperties != null)
            {
                structure.FractionType = ParseFractionType(fractionProperties);
            }

            // 解析分子
            var numerator = fraction.GetFirstChild<Numerator>();
            if (numerator != null)
            {
                structure.Numerator = await ParseFractionPartAsync(numerator, "Numerator");
            }
            else
            {
                _logger.LogWarning("Fraction missing numerator");
                structure.Numerator = CreateComponent("Numerator", "");
            }

            // 解析分母
            var denominator = fraction.GetFirstChild<Denominator>();
            if (denominator != null)
            {
                structure.Denominator = await ParseFractionPartAsync(denominator, "Denominator");
            }
            else
            {
                _logger.LogWarning("Fraction missing denominator");
                structure.Denominator = CreateComponent("Denominator", "");
            }

            // 设置元数据
            structure.Metadata.ComplexityScore = CalculateFractionComplexity(structure);

            _logger.LogDebug("Parsed fraction: {Numerator}/{Denominator}", 
                structure.Numerator.Data, structure.Denominator.Data);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing fraction structure");
            throw;
        }

        return structure;
    }

    private async Task<FormulaComponent> ParseFractionPartAsync(DocumentFormat.OpenXml.OpenXmlElement part, string partType)
    {
        try
        {
            var component = CreateComponent(partType);
            
            // 如果部分包含子元素，递归解析
            if (part.HasChildren)
            {
                var children = await ParseChildrenAsync(part);
                foreach (var child in children)
                {
                    component.Children.Add(child);
                }

                // 如果有子元素，使用子元素的组合文本
                var combinedText = string.Join("", children.Select(c => c.Data?.ToString() ?? ""));
                component.Data = CleanText(combinedText);
            }
            else
            {
                // 直接使用文本内容
                component.Data = CleanText(ExtractTextSafely(part));
            }

            _logger.LogDebug("Parsed fraction part {PartType}: {Content}", partType, component.Data);
            return component;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing fraction part: {PartType}", partType);
            return CreateErrorRecoveryComponent(ExtractTextSafely(part), $"Failed to parse {partType}");
        }
    }

    private async Task<IList<FormulaComponent>> CreateFractionComponentsAsync(Fraction fraction)
    {
        var components = new List<FormulaComponent>();

        try
        {
            // 添加分数类型组件
            var fractionProperties = fraction.GetFirstChild<FractionProperties>();
            if (fractionProperties != null)
            {
                var typeComponent = CreateComponent("FractionType", ParseFractionType(fractionProperties).ToString());
                components.Add(typeComponent);
            }

            // 添加分子组件
            var numerator = fraction.GetFirstChild<Numerator>();
            if (numerator != null)
            {
                var numeratorComponent = await ParseFractionPartAsync(numerator, "Numerator");
                components.Add(numeratorComponent);
            }

            // 添加分母组件
            var denominator = fraction.GetFirstChild<Denominator>();
            if (denominator != null)
            {
                var denominatorComponent = await ParseFractionPartAsync(denominator, "Denominator");
                components.Add(denominatorComponent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating fraction components");
        }

        return components;
    }

    private FractionType ParseFractionType(FractionProperties properties)
    {
        try
        {
            var type = properties.GetFirstChild<FractionType>();
            if (type?.Val?.Value != null)
            {
                return type.Val.Value switch
                {
                    FractionTypeValues.Bar => Models.FractionType.Bar,
                    FractionTypeValues.Skewed => Models.FractionType.Skewed,
                    FractionTypeValues.Linear => Models.FractionType.Linear,
                    FractionTypeValues.NoBar => Models.FractionType.NoBar,
                    _ => Models.FractionType.Bar
                };
            }

            return Models.FractionType.Bar; // 默认类型
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing fraction type, using default");
            return Models.FractionType.Bar;
        }
    }

    private int CalculateFractionComplexity(FractionStructure structure)
    {
        try
        {
            var complexity = 3; // 分数的基础复杂度

            // 基于分子复杂度
            var numeratorText = structure.Numerator.Data?.ToString() ?? "";
            complexity += numeratorText.Length / 5;

            // 基于分母复杂度
            var denominatorText = structure.Denominator.Data?.ToString() ?? "";
            complexity += denominatorText.Length / 5;

            // 基于子组件数量
            complexity += structure.Numerator.Children.Count;
            complexity += structure.Denominator.Children.Count;

            // 检查嵌套分数
            if (ContainsNestedFraction(structure.Numerator) || ContainsNestedFraction(structure.Denominator))
            {
                complexity += 5; // 嵌套分数增加额外复杂度
            }

            return Math.Max(3, complexity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating fraction complexity");
            return 3;
        }
    }

    private bool ContainsNestedFraction(FormulaComponent component)
    {
        try
        {
            // 检查当前组件是否是分数
            if (component.Type == "Fraction")
            {
                return true;
            }

            // 递归检查子组件
            return component.Children.Any(ContainsNestedFraction);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking for nested fractions");
            return false;
        }
    }

    /// <summary>
    /// 验证分数解析结果
    /// </summary>
    protected override bool ValidateParseResult(FormulaParseResult result)
    {
        if (!base.ValidateParseResult(result))
        {
            return false;
        }

        if (result.Structure is not FractionStructure fractionStructure)
        {
            _logger.LogWarning("Parse result structure is not a FractionStructure");
            return false;
        }

        // 验证分子和分母
        if (IsEmptyElement(fractionStructure.Numerator.Data) && IsEmptyElement(fractionStructure.Denominator.Data))
        {
            _logger.LogWarning("Both numerator and denominator are empty");
            return false;
        }

        return true;
    }
}
