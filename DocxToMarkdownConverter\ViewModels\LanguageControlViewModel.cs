using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Windows.Input;
using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.ViewModels;

/// <summary>
/// 语言控制视图模型
/// </summary>
public class LanguageControlViewModel : INotifyPropertyChanged
{
    private readonly ILocalizationService _localizationService;
    private readonly ILogger _logger;
    private LanguageInfo? _selectedLanguage;
    private bool _isLanguageChanging;

    public LanguageControlViewModel(ILocalizationService localizationService, ILogger logger)
    {
        _localizationService = localizationService;
        _logger = logger;

        // 初始化命令
        ChangeLanguageCommand = new RelayCommand<LanguageInfo>(async (lang) =>
        {
            if (lang != null)
            {
                await ChangeLanguageAsync(lang);
            }
        }, CanChangeLanguage);
        RefreshLanguagesCommand = new RelayCommand(async () => await RefreshLanguagesAsync());

        // 订阅语言变更事件
        _localizationService.LanguageChanged += OnLanguageChanged;
        _localizationService.PropertyChanged += OnLocalizationServicePropertyChanged;

        // 初始化语言列表（安全同步初始化）
        try
        {
            InitializeLanguages();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to initialize languages in constructor");
        }
    }

    #region Properties

    /// <summary>
    /// 支持的语言列表
    /// </summary>
    public ObservableCollection<LanguageInfo> SupportedLanguages { get; } = new();

    /// <summary>
    /// 当前选中的语言
    /// </summary>
    public LanguageInfo? SelectedLanguage
    {
        get => _selectedLanguage;
        set
        {
            if (SetProperty(ref _selectedLanguage, value) && value != null)
            {
                _ = ChangeLanguageAsync(value);
            }
        }
    }

    /// <summary>
    /// 当前语言代码
    /// </summary>
    public string CurrentLanguageCode => _localizationService.CurrentLanguage;

    /// <summary>
    /// 当前语言显示名称
    /// </summary>
    public string CurrentLanguageDisplayName => 
        SupportedLanguages.FirstOrDefault(l => l.Code == _localizationService.CurrentLanguage)?.NativeName ?? 
        _localizationService.CurrentLanguage;

    /// <summary>
    /// 是否跟随系统语言
    /// </summary>
    public bool FollowSystemLanguage
    {
        get => _localizationService.FollowSystemLanguage;
        set
        {
            if (_localizationService.FollowSystemLanguage != value)
            {
                _localizationService.FollowSystemLanguage = value;
                OnPropertyChanged();
            }
        }
    }

    /// <summary>
    /// 是否正在更改语言
    /// </summary>
    public bool IsLanguageChanging
    {
        get => _isLanguageChanging;
        private set => SetProperty(ref _isLanguageChanging, value);
    }

    /// <summary>
    /// 系统语言代码
    /// </summary>
    public string SystemLanguageCode => _localizationService.GetSystemLanguage();

    /// <summary>
    /// 系统语言显示名称
    /// </summary>
    public string SystemLanguageDisplayName =>
        SupportedLanguages.FirstOrDefault(l => l.Code == SystemLanguageCode)?.NativeName ?? 
        SystemLanguageCode;

    #endregion

    #region Commands

    /// <summary>
    /// 更改语言命令
    /// </summary>
    public ICommand ChangeLanguageCommand { get; }

    /// <summary>
    /// 刷新语言列表命令
    /// </summary>
    public ICommand RefreshLanguagesCommand { get; }

    #endregion

    #region Command Implementations

    private async Task ChangeLanguageAsync(LanguageInfo language)
    {
        if (language == null || IsLanguageChanging) return;

        try
        {
            IsLanguageChanging = true;
            _logger.LogInformation("Changing language to: {LanguageCode}", language.Code);

            var success = await _localizationService.SetLanguageAsync(language.Code);
            if (!success)
            {
                _logger.LogWarning("Failed to change language to: {LanguageCode}", language.Code);
                // 可以在这里显示错误消息
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error changing language to: {LanguageCode}", language.Code);
        }
        finally
        {
            IsLanguageChanging = false;
        }
    }

    private bool CanChangeLanguage(LanguageInfo? language)
    {
        return language != null && !IsLanguageChanging && language.Code != _localizationService.CurrentLanguage;
    }

    private async Task RefreshLanguagesAsync()
    {
        try
        {
            await _localizationService.ReloadResourcesAsync();
            InitializeLanguages();
            _logger.LogInformation("Languages refreshed successfully");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error refreshing languages");
        }
    }

    #endregion

    #region Event Handlers

    private void OnLanguageChanged(object? sender, LanguageChangedEventArgs e)
    {
        // 更新当前选中的语言
        _selectedLanguage = SupportedLanguages.FirstOrDefault(l => l.Code == e.NewLanguage);
        
        // 通知属性变更
        OnPropertyChanged(nameof(SelectedLanguage));
        OnPropertyChanged(nameof(CurrentLanguageCode));
        OnPropertyChanged(nameof(CurrentLanguageDisplayName));
        
        _logger.LogInformation("Language changed event handled: {OldLanguage} -> {NewLanguage}", 
            e.OldLanguage, e.NewLanguage);
    }

    private void OnLocalizationServicePropertyChanged(object? sender, PropertyChangedEventArgs e)
    {
        if (e.PropertyName == nameof(ILocalizationService.FollowSystemLanguage))
        {
            OnPropertyChanged(nameof(FollowSystemLanguage));
        }
    }

    #endregion

    #region Private Methods

    private void InitializeLanguages()
    {
        SupportedLanguages.Clear();
        
        foreach (var language in _localizationService.SupportedLanguages)
        {
            SupportedLanguages.Add(language);
        }

        // 设置当前选中的语言
        _selectedLanguage = SupportedLanguages.FirstOrDefault(l => l.Code == _localizationService.CurrentLanguage);
        OnPropertyChanged(nameof(SelectedLanguage));
    }

    #endregion

    #region INotifyPropertyChanged

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    #endregion
}
