using System;
using System.Collections.Generic;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 流式文档处理器接口
/// </summary>
public interface IStreamingDocumentProcessor
{
    /// <summary>
    /// 流式处理DOCX文档
    /// </summary>
    Task<ConversionResult> ProcessDocumentStreamAsync(
        Stream inputStream, 
        Stream outputStream, 
        ConversionOptions options,
        IProgress<ConversionProgress>? progress = null,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 检查文档大小并确定处理策略
    /// </summary>
    DocumentProcessingStrategy DetermineProcessingStrategy(long documentSizeBytes);

    /// <summary>
    /// 获取推荐的缓冲区大小
    /// </summary>
    int GetRecommendedBufferSize(long documentSizeBytes);

    /// <summary>
    /// 流式读取文档元素
    /// </summary>
    IAsyncEnumerable<DocumentElement> ReadDocumentElementsAsync(
        Stream inputStream, 
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 流式写入Markdown内容
    /// </summary>
    Task WriteMarkdownStreamAsync(
        IAsyncEnumerable<MarkdownElement> markdownElements,
        Stream outputStream,
        CancellationToken cancellationToken = default);

    /// <summary>
    /// 估算内存使用量
    /// </summary>
    long EstimateMemoryUsage(long documentSizeBytes, ConversionOptions options);

    /// <summary>
    /// 设置内存限制
    /// </summary>
    void SetMemoryLimit(long maxMemoryBytes);

    /// <summary>
    /// 获取当前内存使用情况
    /// </summary>
    StreamingProcessorMemoryInfo GetMemoryInfo();
}

/// <summary>
/// 文档处理策略
/// </summary>
public enum DocumentProcessingStrategy
{
    /// <summary>
    /// 内存中处理（小文件）
    /// </summary>
    InMemory,
    
    /// <summary>
    /// 流式处理（中等文件）
    /// </summary>
    Streaming,
    
    /// <summary>
    /// 分块处理（大文件）
    /// </summary>
    Chunked,
    
    /// <summary>
    /// 临时文件处理（超大文件）
    /// </summary>
    TemporaryFile
}

/// <summary>
/// 文档元素基类
/// </summary>
public abstract class DocumentElement
{
    public string ElementType { get; set; } = string.Empty;
    public long Position { get; set; }
    public int Size { get; set; }
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 段落元素
/// </summary>
public class ParagraphElement : DocumentElement
{
    public string Text { get; set; } = string.Empty;
    public List<TextRun> TextRuns { get; set; } = new();
    public ParagraphStyle Style { get; set; } = new();
}

/// <summary>
/// 表格元素
/// </summary>
public class TableElement : DocumentElement
{
    public List<TableRow> Rows { get; set; } = new();
    public TableStyle Style { get; set; } = new();
}

/// <summary>
/// 图片元素
/// </summary>
public class ImageElement : DocumentElement
{
    public string ImageId { get; set; } = string.Empty;
    public byte[]? ImageData { get; set; }
    public string ContentType { get; set; } = string.Empty;
    public int Width { get; set; }
    public int Height { get; set; }
}

/// <summary>
/// Markdown元素基类
/// </summary>
public abstract class MarkdownElement
{
    public string Content { get; set; } = string.Empty;
    public int EstimatedSize { get; set; }
}

/// <summary>
/// Markdown段落
/// </summary>
public class MarkdownParagraph : MarkdownElement
{
    public int HeadingLevel { get; set; }
    public bool IsBold { get; set; }
    public bool IsItalic { get; set; }
}

/// <summary>
/// Markdown表格
/// </summary>
public class MarkdownTable : MarkdownElement
{
    public List<string> Headers { get; set; } = new();
    public List<List<string>> Rows { get; set; } = new();
}

/// <summary>
/// Markdown图片
/// </summary>
public class MarkdownImage : MarkdownElement
{
    public string AltText { get; set; } = string.Empty;
    public string ImagePath { get; set; } = string.Empty;
}

/// <summary>
/// 文本运行
/// </summary>
public class TextRun
{
    public string Text { get; set; } = string.Empty;
    public bool IsBold { get; set; }
    public bool IsItalic { get; set; }
    public bool IsUnderline { get; set; }
    public string FontFamily { get; set; } = string.Empty;
    public double FontSize { get; set; }
}

/// <summary>
/// 段落样式
/// </summary>
public class ParagraphStyle
{
    public string StyleName { get; set; } = string.Empty;
    public int OutlineLevel { get; set; }
    public string Alignment { get; set; } = string.Empty;
    public double SpaceBefore { get; set; }
    public double SpaceAfter { get; set; }
}

/// <summary>
/// 表格样式
/// </summary>
public class TableStyle
{
    public string StyleName { get; set; } = string.Empty;
    public bool HasHeaderRow { get; set; }
    public string BorderStyle { get; set; } = string.Empty;
}

/// <summary>
/// 表格行
/// </summary>
public class TableRow
{
    public List<TableCell> Cells { get; set; } = new();
    public bool IsHeader { get; set; }
}

/// <summary>
/// 表格单元格
/// </summary>
public class TableCell
{
    public string Text { get; set; } = string.Empty;
    public int ColumnSpan { get; set; } = 1;
    public int RowSpan { get; set; } = 1;
    public string Alignment { get; set; } = string.Empty;
}

/// <summary>
/// 流式处理器内存信息
/// </summary>
public class StreamingProcessorMemoryInfo
{
    public long CurrentMemoryUsage { get; set; }
    public long MaxMemoryUsage { get; set; }
    public long MemoryLimit { get; set; }
    public double MemoryUsagePercentage { get; set; }
    public int ActiveBuffers { get; set; }
    public long BufferMemoryUsage { get; set; }
    public DateTime LastUpdated { get; set; }
}
