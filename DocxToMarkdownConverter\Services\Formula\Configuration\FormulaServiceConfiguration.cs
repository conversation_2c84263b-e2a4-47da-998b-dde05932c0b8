using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;
using DocxToMarkdownConverter.Services.Formula.Implementation;

namespace DocxToMarkdownConverter.Services.Formula.Configuration;

/// <summary>
/// 公式服务配置扩展
/// </summary>
public static class FormulaServiceConfiguration
{
    /// <summary>
    /// 添加公式处理服务
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configuration">配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFormulaServices(
        this IServiceCollection services, 
        IConfiguration? configuration = null)
    {
        // 注册配置选项
        if (configuration != null)
        {
            services.Configure<FormulaProcessingOptions>(
                configuration.GetSection("FormulaProcessing"));
            services.Configure<FormulaFormattingOptions>(
                configuration.GetSection("FormulaFormatting"));
        }
        else
        {
            // 使用默认配置
            services.Configure<FormulaProcessingOptions>(options => { });
            services.Configure<FormulaFormattingOptions>(options => { });
        }
        
        // 注册核心服务
        services.AddSingleton<IFormulaCacheService, MemoryFormulaCacheService>();
        services.AddScoped<IFormulaDetector, FormulaDetector>();
        services.AddScoped<IFormulaExtractor, FormulaExtractor>();
        services.AddScoped<IFormulaFormatter, FormulaFormatter>();
        
        // 注册类型检测器
        services.AddScoped<IFormulaTypeDetector, OfficeMathDetector>();
        services.AddScoped<IFormulaTypeDetector, PiecewiseFunctionDetector>();
        services.AddScoped<IFormulaTypeDetector, MatrixDetector>();
        services.AddScoped<IFormulaTypeDetector, FractionDetector>();
        
        // 注册解析器
        services.AddScoped<IFormulaParser, FractionParser>();
        services.AddScoped<IFormulaParser, MatrixParser>();
        services.AddScoped<IFormulaParser, RadicalParser>();
        services.AddScoped<IFormulaParser, ScriptParser>();
        services.AddScoped<IFormulaParser, NaryParser>();
        services.AddScoped<IFormulaParser, DelimiterParser>();
        services.AddScoped<IFormulaParser, FunctionParser>();
        
        // 注册转换器
        services.AddScoped<IFormulaConverter, LaTeXConverter>();
        services.AddScoped<IFormulaConverter, MathMLConverter>();
        
        // 注册后处理器
        services.AddScoped<IFormulaPostProcessor, SymbolNormalizationProcessor>();
        services.AddScoped<IFormulaPostProcessor, SpacingProcessor>();
        services.AddScoped<IFormulaPostProcessor, PiecewiseFunctionProcessor>();
        services.AddScoped<IFormulaPostProcessor, ErrorRecoveryProcessor>();
        
        // 注册主处理器
        services.AddScoped<IFormulaProcessor, FormulaProcessorOrchestrator>();
        
        return services;
    }
    
    /// <summary>
    /// 添加公式处理服务（带自定义配置）
    /// </summary>
    /// <param name="services">服务集合</param>
    /// <param name="configureProcessing">处理选项配置</param>
    /// <param name="configureFormatting">格式化选项配置</param>
    /// <returns>服务集合</returns>
    public static IServiceCollection AddFormulaServices(
        this IServiceCollection services,
        Action<FormulaProcessingOptions>? configureProcessing = null,
        Action<FormulaFormattingOptions>? configureFormatting = null)
    {
        // 配置选项
        if (configureProcessing != null)
        {
            services.Configure(configureProcessing);
        }
        else
        {
            services.Configure<FormulaProcessingOptions>(options => { });
        }
        
        if (configureFormatting != null)
        {
            services.Configure(configureFormatting);
        }
        else
        {
            services.Configure<FormulaFormattingOptions>(options => { });
        }
        
        return services.AddFormulaServices();
    }
    
    /// <summary>
    /// 验证公式服务配置
    /// </summary>
    /// <param name="serviceProvider">服务提供者</param>
    /// <returns>验证结果</returns>
    public static ValidationResult ValidateFormulaServices(IServiceProvider serviceProvider)
    {
        var result = new ValidationResult();
        
        try
        {
            // 检查核心服务
            var processor = serviceProvider.GetService<IFormulaProcessor>();
            if (processor == null)
            {
                result.AddError("IFormulaProcessor service not registered");
            }
            
            var detector = serviceProvider.GetService<IFormulaDetector>();
            if (detector == null)
            {
                result.AddError("IFormulaDetector service not registered");
            }
            
            var extractor = serviceProvider.GetService<IFormulaExtractor>();
            if (extractor == null)
            {
                result.AddError("IFormulaExtractor service not registered");
            }
            
            // 检查解析器
            var parsers = serviceProvider.GetServices<IFormulaParser>();
            if (!parsers.Any())
            {
                result.AddError("No IFormulaParser services registered");
            }
            
            // 检查转换器
            var converters = serviceProvider.GetServices<IFormulaConverter>();
            if (!converters.Any())
            {
                result.AddError("No IFormulaConverter services registered");
            }
            
            // 检查后处理器
            var postProcessors = serviceProvider.GetServices<IFormulaPostProcessor>();
            if (!postProcessors.Any())
            {
                result.AddWarning("No IFormulaPostProcessor services registered");
            }
            
            // 检查缓存服务
            var cacheService = serviceProvider.GetService<IFormulaCacheService>();
            if (cacheService == null)
            {
                result.AddWarning("IFormulaCacheService not registered, caching will be disabled");
            }
        }
        catch (Exception ex)
        {
            result.AddError($"Error validating formula services: {ex.Message}");
        }
        
        return result;
    }
}

/// <summary>
/// 公式处理配置
/// </summary>
public class FormulaProcessingConfiguration
{
    /// <summary>
    /// 类型处理器映射
    /// </summary>
    public Dictionary<FormulaType, string> TypeProcessorMappings { get; set; } = new()
    {
        { FormulaType.Fraction, nameof(FractionParser) },
        { FormulaType.Matrix, nameof(MatrixParser) },
        { FormulaType.Radical, nameof(RadicalParser) },
        { FormulaType.Superscript, nameof(ScriptParser) },
        { FormulaType.Subscript, nameof(ScriptParser) },
        { FormulaType.SubSuperscript, nameof(ScriptParser) },
        { FormulaType.Nary, nameof(NaryParser) },
        { FormulaType.Delimiter, nameof(DelimiterParser) },
        { FormulaType.MathFunction, nameof(FunctionParser) }
    };
    
    /// <summary>
    /// 转换器映射
    /// </summary>
    public Dictionary<FormulaOutputFormat, string> ConverterMappings { get; set; } = new()
    {
        { FormulaOutputFormat.LaTeX, nameof(LaTeXConverter) },
        { FormulaOutputFormat.MathML, nameof(MathMLConverter) }
    };
    
    /// <summary>
    /// 后处理器管道
    /// </summary>
    public List<string> PostProcessorPipeline { get; set; } = new()
    {
        nameof(SymbolNormalizationProcessor),
        nameof(SpacingProcessor),
        nameof(PiecewiseFunctionProcessor),
        nameof(ErrorRecoveryProcessor)
    };
    
    /// <summary>
    /// 默认处理选项
    /// </summary>
    public FormulaProcessingOptions DefaultOptions { get; set; } = new();
    
    /// <summary>
    /// 默认格式化选项
    /// </summary>
    public FormulaFormattingOptions DefaultFormattingOptions { get; set; } = new();
}

/// <summary>
/// 公式服务工厂
/// </summary>
public class FormulaServiceFactory
{
    private readonly IServiceProvider _serviceProvider;
    private readonly ILogger<FormulaServiceFactory> _logger;
    
    public FormulaServiceFactory(IServiceProvider serviceProvider, ILogger<FormulaServiceFactory> logger)
    {
        _serviceProvider = serviceProvider;
        _logger = logger;
    }
    
    /// <summary>
    /// 创建公式处理器
    /// </summary>
    /// <param name="options">处理选项</param>
    /// <returns>公式处理器</returns>
    public IFormulaProcessor CreateProcessor(FormulaProcessingOptions? options = null)
    {
        try
        {
            var processor = _serviceProvider.GetRequiredService<IFormulaProcessor>();
            
            if (options != null)
            {
                // 如果提供了自定义选项，可以在这里进行配置
                _logger.LogDebug("Created formula processor with custom options");
            }
            
            return processor;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create formula processor");
            throw;
        }
    }
    
    /// <summary>
    /// 创建指定类型的解析器
    /// </summary>
    /// <param name="formulaType">公式类型</param>
    /// <returns>解析器</returns>
    public IFormulaParser? CreateParser(FormulaType formulaType)
    {
        try
        {
            var parsers = _serviceProvider.GetServices<IFormulaParser>();
            return parsers.FirstOrDefault(p => p.CanParse(formulaType));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create parser for type {FormulaType}", formulaType);
            return null;
        }
    }
    
    /// <summary>
    /// 创建指定格式的转换器
    /// </summary>
    /// <param name="outputFormat">输出格式</param>
    /// <returns>转换器</returns>
    public IFormulaConverter? CreateConverter(FormulaOutputFormat outputFormat)
    {
        try
        {
            var converters = _serviceProvider.GetServices<IFormulaConverter>();
            return converters.FirstOrDefault(c => c.SupportsFormat(outputFormat));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create converter for format {OutputFormat}", outputFormat);
            return null;
        }
    }
}
