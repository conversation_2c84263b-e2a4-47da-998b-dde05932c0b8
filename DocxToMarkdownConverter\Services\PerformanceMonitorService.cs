using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Microsoft.Extensions.Logging;
using Timer = System.Threading.Timer;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 性能监控服务实现
/// </summary>
public class PerformanceMonitorService : IPerformanceMonitorService, IDisposable
{
    private readonly ILogger<PerformanceMonitorService> _logger;
    private readonly Timer _monitoringTimer;
    private readonly ConcurrentQueue<MemoryUsageSnapshot> _memoryHistory;
    private readonly ConcurrentDictionary<string, Func<double>> _customCounters;
    private readonly List<PerformanceAlert> _alerts;
    private readonly object _lockObject = new();

    private bool _isMonitoring;
    private DateTime _monitoringStartTime;
    private long _memoryWarningThreshold = 500 * 1024 * 1024; // 500MB 默认阈值
    private long _peakMemoryUsage;
    private readonly List<double> _memoryUsageHistory = new();
    private readonly List<double> _cpuUsageHistory = new();
    private Process _currentProcess;

    public event EventHandler<PerformanceEventArgs>? PerformanceAlert;
    public event EventHandler<MemoryUsageEventArgs>? MemoryUsageChanged;

    public PerformanceMonitorService(ILogger<PerformanceMonitorService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _memoryHistory = new ConcurrentQueue<MemoryUsageSnapshot>();
        _customCounters = new ConcurrentDictionary<string, Func<double>>();
        _alerts = new List<PerformanceAlert>();
        _currentProcess = Process.GetCurrentProcess();

        // 创建监控定时器，每5秒检查一次
        _monitoringTimer = new Timer(MonitoringCallback, null, Timeout.Infinite, Timeout.Infinite);
    }

    public void StartMonitoring()
    {
        if (_isMonitoring) return;

        _isMonitoring = true;
        _monitoringStartTime = DateTime.Now;
        _monitoringTimer.Change(TimeSpan.Zero, TimeSpan.FromSeconds(5));
        
        _logger.LogInformation("性能监控已启动");
    }

    public void StopMonitoring()
    {
        if (!_isMonitoring) return;

        _isMonitoring = false;
        _monitoringTimer.Change(Timeout.Infinite, Timeout.Infinite);
        
        _logger.LogInformation("性能监控已停止，监控时长: {Duration}", DateTime.Now - _monitoringStartTime);
    }

    public MemoryUsageInfo GetMemoryUsage()
    {
        try
        {
            _currentProcess.Refresh();
            var managedMemory = GC.GetTotalMemory(false);
            
            var memoryInfo = new MemoryUsageInfo
            {
                WorkingSetBytes = _currentProcess.WorkingSet64,
                PrivateMemoryBytes = _currentProcess.PrivateMemorySize64,
                VirtualMemoryBytes = _currentProcess.VirtualMemorySize64,
                ManagedMemoryBytes = managedMemory,
                Gen0Collections = GC.CollectionCount(0),
                Gen1Collections = GC.CollectionCount(1),
                Gen2Collections = GC.CollectionCount(2),
                Timestamp = DateTime.Now
            };

            // 计算内存使用百分比（基于物理内存）
            var totalPhysicalMemory = GetTotalPhysicalMemory();
            if (totalPhysicalMemory > 0)
            {
                memoryInfo.MemoryUsagePercentage = (double)memoryInfo.WorkingSetBytes / totalPhysicalMemory * 100;
            }

            return memoryInfo;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取内存使用信息时发生错误");
            return new MemoryUsageInfo { Timestamp = DateTime.Now };
        }
    }

    public CpuUsageInfo GetCpuUsage()
    {
        try
        {
            _currentProcess.Refresh();
            
            return new CpuUsageInfo
            {
                ProcessorTime = _currentProcess.TotalProcessorTime.TotalMilliseconds,
                UserTime = _currentProcess.UserProcessorTime.TotalMilliseconds,
                SystemTime = (_currentProcess.TotalProcessorTime - _currentProcess.UserProcessorTime).TotalMilliseconds,
                ThreadCount = _currentProcess.Threads.Count,
                Timestamp = DateTime.Now
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "获取CPU使用信息时发生错误");
            return new CpuUsageInfo { Timestamp = DateTime.Now };
        }
    }

    public PerformanceStatistics GetPerformanceStatistics()
    {
        lock (_lockObject)
        {
            var currentMemory = GetMemoryUsage();
            var currentCpu = GetCpuUsage();

            var statistics = new PerformanceStatistics
            {
                CurrentMemoryUsage = currentMemory,
                CurrentCpuUsage = currentCpu,
                MonitoringDuration = _isMonitoring ? DateTime.Now - _monitoringStartTime : TimeSpan.Zero,
                PeakMemoryUsage = _peakMemoryUsage,
                AverageMemoryUsage = _memoryUsageHistory.Count > 0 ? _memoryUsageHistory.Average() : 0,
                AverageCpuUsage = _cpuUsageHistory.Count > 0 ? _cpuUsageHistory.Average() : 0,
                GarbageCollectionCount = GC.CollectionCount(0) + GC.CollectionCount(1) + GC.CollectionCount(2),
                Alerts = new List<PerformanceAlert>(_alerts)
            };

            // 添加自定义计数器
            foreach (var counter in _customCounters)
            {
                try
                {
                    statistics.CustomCounters[counter.Key] = counter.Value();
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "获取自定义计数器 {CounterName} 值时发生错误", counter.Key);
                }
            }

            return statistics;
        }
    }

    public async Task ForceGarbageCollectionAsync()
    {
        await Task.Run(() =>
        {
            var beforeMemory = GC.GetTotalMemory(false);
            
            GC.Collect();
            GC.WaitForPendingFinalizers();
            GC.Collect();
            
            var afterMemory = GC.GetTotalMemory(false);
            var freedMemory = beforeMemory - afterMemory;
            
            _logger.LogInformation("强制垃圾回收完成，释放内存: {FreedMemory:N0} 字节", freedMemory);
        });
    }

    public MemoryPressureLevel CheckMemoryPressure()
    {
        var memoryUsage = GetMemoryUsage();
        var totalMemory = GetTotalPhysicalMemory();
        
        if (totalMemory <= 0) return MemoryPressureLevel.Low;

        var usagePercentage = (double)memoryUsage.WorkingSetBytes / totalMemory * 100;

        return usagePercentage switch
        {
            >= 90 => MemoryPressureLevel.Critical,
            >= 75 => MemoryPressureLevel.High,
            >= 50 => MemoryPressureLevel.Medium,
            _ => MemoryPressureLevel.Low
        };
    }

    public void AddPerformanceCounter(string name, Func<double> valueProvider)
    {
        _customCounters.AddOrUpdate(name, valueProvider, (key, oldValue) => valueProvider);
        _logger.LogDebug("添加性能计数器: {CounterName}", name);
    }

    public void RemovePerformanceCounter(string name)
    {
        _customCounters.TryRemove(name, out _);
        _logger.LogDebug("移除性能计数器: {CounterName}", name);
    }

    public void SetMemoryWarningThreshold(long thresholdBytes)
    {
        _memoryWarningThreshold = thresholdBytes;
        _logger.LogInformation("内存警告阈值已设置为: {Threshold:N0} 字节", thresholdBytes);
    }

    public IEnumerable<MemoryUsageSnapshot> GetMemoryUsageHistory()
    {
        return _memoryHistory.ToArray();
    }

    private void MonitoringCallback(object? state)
    {
        if (!_isMonitoring) return;

        try
        {
            var memoryUsage = GetMemoryUsage();
            var pressureLevel = CheckMemoryPressure();

            // 更新峰值内存使用
            if (memoryUsage.WorkingSetBytes > _peakMemoryUsage)
            {
                _peakMemoryUsage = memoryUsage.WorkingSetBytes;
            }

            // 记录历史数据
            lock (_lockObject)
            {
                _memoryUsageHistory.Add(memoryUsage.MemoryUsagePercentage);
                if (_memoryUsageHistory.Count > 100) // 保持最近100个数据点
                {
                    _memoryUsageHistory.RemoveAt(0);
                }

                // 添加内存使用快照
                _memoryHistory.Enqueue(new MemoryUsageSnapshot
                {
                    Timestamp = memoryUsage.Timestamp,
                    MemoryUsageBytes = memoryUsage.WorkingSetBytes,
                    MemoryUsagePercentage = memoryUsage.MemoryUsagePercentage,
                    ActiveObjects = GC.GetTotalMemory(false) > 0 ? 1 : 0 // 简化的活动对象计数
                });

                // 保持历史记录在合理范围内
                while (_memoryHistory.Count > 1000)
                {
                    _memoryHistory.TryDequeue(out _);
                }
            }

            // 检查内存警告
            if (memoryUsage.WorkingSetBytes > _memoryWarningThreshold)
            {
                var alert = new PerformanceAlert
                {
                    Type = "MemoryWarning",
                    Message = $"内存使用超过阈值: {memoryUsage.WorkingSetBytes:N0} 字节",
                    Severity = pressureLevel == MemoryPressureLevel.Critical ? AlertSeverity.Critical : AlertSeverity.Warning,
                    Timestamp = DateTime.Now
                };

                alert.Data["MemoryUsage"] = memoryUsage.WorkingSetBytes;
                alert.Data["Threshold"] = _memoryWarningThreshold;
                alert.Data["PressureLevel"] = pressureLevel.ToString();

                RaisePerformanceAlert(alert);
            }

            // 触发内存使用变化事件
            MemoryUsageChanged?.Invoke(this, new MemoryUsageEventArgs(memoryUsage, pressureLevel));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "性能监控回调中发生错误");
        }
    }

    private void RaisePerformanceAlert(PerformanceAlert alert)
    {
        lock (_lockObject)
        {
            _alerts.Add(alert);
            if (_alerts.Count > 100) // 保持最近100个警告
            {
                _alerts.RemoveAt(0);
            }
        }

        _logger.LogWarning("性能警告: {AlertType} - {Message}", alert.Type, alert.Message);
        PerformanceAlert?.Invoke(this, new PerformanceEventArgs(alert));
    }

    private static long GetTotalPhysicalMemory()
    {
        try
        {
            var computerInfo = new Microsoft.VisualBasic.Devices.ComputerInfo();
            return (long)computerInfo.TotalPhysicalMemory;
        }
        catch
        {
            return 0;
        }
    }

    public void Dispose()
    {
        StopMonitoring();
        _monitoringTimer?.Dispose();
        _currentProcess?.Dispose();
    }
}
