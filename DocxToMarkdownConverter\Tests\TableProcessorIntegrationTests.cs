using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.Models;
using System.Text;

namespace DocxToMarkdownConverter.Tests;

/// <summary>
/// 表格处理器集成测试
/// 测试完整的表格转换流程
/// </summary>
public class TableProcessorIntegrationTests
{
    private readonly Mock<ITextProcessor> _mockTextProcessor;
    private readonly Mock<ILogger<TableProcessor>> _mockLogger;
    private readonly TableProcessor _tableProcessor;

    public TableProcessorIntegrationTests()
    {
        _mockTextProcessor = new Mock<ITextProcessor>();
        _mockLogger = new Mock<ILogger<TableProcessor>>();
        _tableProcessor = new TableProcessor(_mockTextProcessor.Object, _mockLogger.Object);

        // 设置文本处理器的默认行为
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns<Paragraph, ConversionOptions>((p, o) => p.InnerText);
    }

    [Fact]
    public void ProcessTable_WithSimpleTable_ReturnsCorrectMarkdown()
    {
        // Arrange
        var table = CreateSimpleTable();
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(table, options);

        // Assert
        Assert.Contains("| 标题1 | 标题2 | 标题3 |", result);
        Assert.Contains("| --- | --- | --- |", result);
        Assert.Contains("| 数据1 | 数据2 | 数据3 |", result);
        Assert.Contains("| 数据4 | 数据5 | 数据6 |", result);
    }

    [Fact]
    public void ProcessTable_WithEmptyTable_ReturnsEmpty()
    {
        // Arrange
        var table = new Table();
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(table, options);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void ProcessTable_WithSingleRowTable_ReturnsHeaderOnly()
    {
        // Arrange
        var table = CreateSingleRowTable();
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(table, options);

        // Assert
        Assert.Contains("| 单行标题1 | 单行标题2 |", result);
        Assert.Contains("| --- | --- |", result);
        // 不应该有数据行
        Assert.DoesNotContain("数据", result);
    }

    [Fact]
    public void ProcessTable_WithMergedCells_HandlesSpanCorrectly()
    {
        // Arrange
        var table = CreateTableWithMergedCells();
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(table, options);

        // Assert
        Assert.Contains("[跨2列]", result);
        Assert.Contains("| 标题1 | 标题2 | 标题3 |", result);
        Assert.Contains("| --- | --- | --- |", result);
    }

    [Fact]
    public void ProcessTable_WithSpecialCharacters_EscapesCorrectly()
    {
        // Arrange
        var table = CreateTableWithSpecialCharacters();
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(table, options);

        // Assert
        Assert.Contains("\\|", result); // 管道字符应该被转义
        Assert.DoesNotContain("\n", result); // 换行符应该被替换
        Assert.DoesNotContain("\t", result); // 制表符应该被替换
    }

    /// <summary>
    /// 创建简单的测试表格
    /// </summary>
    private Table CreateSimpleTable()
    {
        var table = new Table();
        
        // 添加表头行
        var headerRow = new TableRow();
        headerRow.Append(CreateTableCell("标题1"));
        headerRow.Append(CreateTableCell("标题2"));
        headerRow.Append(CreateTableCell("标题3"));
        table.Append(headerRow);
        
        // 添加数据行1
        var dataRow1 = new TableRow();
        dataRow1.Append(CreateTableCell("数据1"));
        dataRow1.Append(CreateTableCell("数据2"));
        dataRow1.Append(CreateTableCell("数据3"));
        table.Append(dataRow1);
        
        // 添加数据行2
        var dataRow2 = new TableRow();
        dataRow2.Append(CreateTableCell("数据4"));
        dataRow2.Append(CreateTableCell("数据5"));
        dataRow2.Append(CreateTableCell("数据6"));
        table.Append(dataRow2);
        
        return table;
    }

    /// <summary>
    /// 创建单行表格
    /// </summary>
    private Table CreateSingleRowTable()
    {
        var table = new Table();
        
        var headerRow = new TableRow();
        headerRow.Append(CreateTableCell("单行标题1"));
        headerRow.Append(CreateTableCell("单行标题2"));
        table.Append(headerRow);
        
        return table;
    }

    /// <summary>
    /// 创建包含合并单元格的表格
    /// </summary>
    private Table CreateTableWithMergedCells()
    {
        var table = new Table();
        
        // 表头行
        var headerRow = new TableRow();
        headerRow.Append(CreateTableCell("标题1"));
        headerRow.Append(CreateTableCell("标题2"));
        headerRow.Append(CreateTableCell("标题3"));
        table.Append(headerRow);
        
        // 包含合并单元格的数据行
        var dataRow = new TableRow();
        dataRow.Append(CreateTableCellWithSpan("合并单元格", 2));
        dataRow.Append(CreateTableCell("普通单元格"));
        table.Append(dataRow);
        
        return table;
    }

    /// <summary>
    /// 创建包含特殊字符的表格
    /// </summary>
    private Table CreateTableWithSpecialCharacters()
    {
        var table = new Table();
        
        var headerRow = new TableRow();
        headerRow.Append(CreateTableCell("标题|管道"));
        headerRow.Append(CreateTableCell("标题\n换行"));
        headerRow.Append(CreateTableCell("标题\t制表"));
        table.Append(headerRow);
        
        var dataRow = new TableRow();
        dataRow.Append(CreateTableCell("数据|管道"));
        dataRow.Append(CreateTableCell("数据\n换行"));
        dataRow.Append(CreateTableCell("数据\t制表"));
        table.Append(dataRow);
        
        return table;
    }

    /// <summary>
    /// 创建表格单元格
    /// </summary>
    private TableCell CreateTableCell(string text)
    {
        var cell = new TableCell();
        var paragraph = new Paragraph();
        var run = new Run();
        var textElement = new Text(text);
        
        run.Append(textElement);
        paragraph.Append(run);
        cell.Append(paragraph);
        
        return cell;
    }

    /// <summary>
    /// 创建带跨列属性的表格单元格
    /// </summary>
    private TableCell CreateTableCellWithSpan(string text, int span)
    {
        var cell = CreateTableCell(text);
        var cellProperties = new TableCellProperties();
        var gridSpan = new GridSpan() { Val = span };
        cellProperties.Append(gridSpan);
        cell.Append(cellProperties);
        
        return cell;
    }
}
