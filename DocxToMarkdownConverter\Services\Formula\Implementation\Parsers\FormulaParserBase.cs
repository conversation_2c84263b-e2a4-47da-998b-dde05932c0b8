using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Parsers;

/// <summary>
/// 公式解析器基类
/// </summary>
public abstract class FormulaParserBase : IFormulaParser
{
    protected readonly ILogger _logger;

    protected FormulaParserBase(ILogger logger)
    {
        _logger = logger;
    }

    /// <summary>
    /// 支持的公式类型
    /// </summary>
    public abstract FormulaType[] SupportedTypes { get; }

    /// <summary>
    /// 检查是否可以解析指定类型的公式
    /// </summary>
    public virtual bool CanParse(FormulaType type)
    {
        return SupportedTypes.Contains(type);
    }

    /// <summary>
    /// 解析公式元素
    /// </summary>
    public abstract Task<FormulaParseResult> ParseAsync(FormulaElement element);

    /// <summary>
    /// 创建公式组件
    /// </summary>
    protected virtual FormulaComponent CreateComponent(string type, object? data = null)
    {
        return new FormulaComponent
        {
            Type = type,
            Data = data
        };
    }

    /// <summary>
    /// 创建成功的解析结果
    /// </summary>
    protected FormulaParseResult CreateSuccessResult(FormulaStructure structure, IList<FormulaComponent>? components = null)
    {
        return new FormulaParseResult
        {
            Success = true,
            Structure = structure,
            Components = components ?? new List<FormulaComponent>()
        };
    }

    /// <summary>
    /// 创建失败的解析结果
    /// </summary>
    protected FormulaParseResult CreateFailureResult(string errorCode, string errorMessage, Exception? exception = null)
    {
        var result = new FormulaParseResult
        {
            Success = false
        };

        result.Errors.Add(new FormulaParseError
        {
            Code = errorCode,
            Message = errorMessage
        });

        _logger.LogError(exception, "Parse failed: {ErrorCode} - {ErrorMessage}", errorCode, errorMessage);

        return result;
    }

    /// <summary>
    /// 安全地提取元素文本
    /// </summary>
    protected string ExtractTextSafely(object? element)
    {
        try
        {
            return element switch
            {
                null => string.Empty,
                string str => str,
                DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.InnerText ?? string.Empty,
                _ => element.ToString() ?? string.Empty
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting text from element: {ElementType}", element?.GetType().Name);
            return string.Empty;
        }
    }

    /// <summary>
    /// 递归解析子元素
    /// </summary>
    protected async Task<IList<FormulaComponent>> ParseChildrenAsync(DocumentFormat.OpenXml.OpenXmlElement parent)
    {
        var children = new List<FormulaComponent>();

        try
        {
            foreach (var child in parent.Elements())
            {
                var childComponent = await ParseChildElementAsync(child);
                if (childComponent != null)
                {
                    children.Add(childComponent);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing children of element: {ElementType}", parent?.GetType().Name);
        }

        return children;
    }

    /// <summary>
    /// 解析单个子元素
    /// </summary>
    protected virtual async Task<FormulaComponent?> ParseChildElementAsync(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        try
        {
            var component = CreateComponent(element.LocalName, ExtractTextSafely(element));
            
            // 递归解析子元素
            if (element.HasChildren)
            {
                var grandChildren = await ParseChildrenAsync(element);
                foreach (var grandChild in grandChildren)
                {
                    component.Children.Add(grandChild);
                }
            }

            return component;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing child element: {ElementType}", element?.GetType().Name);
            return null;
        }
    }

    /// <summary>
    /// 验证解析结果
    /// </summary>
    protected virtual bool ValidateParseResult(FormulaParseResult result)
    {
        if (!result.Success)
        {
            return false;
        }

        if (result.Structure == null)
        {
            _logger.LogWarning("Parse result has no structure");
            return false;
        }

        return true;
    }

    /// <summary>
    /// 计算解析复杂度
    /// </summary>
    protected virtual int CalculateParseComplexity(FormulaElement element)
    {
        try
        {
            var complexity = 1; // 基础复杂度

            if (element.SourceElement is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
            {
                // 基于元素深度
                complexity += CalculateElementDepth(xmlElement);

                // 基于子元素数量
                complexity += xmlElement.Elements().Count();

                // 基于文本长度
                var textLength = xmlElement.InnerText?.Length ?? 0;
                complexity += textLength / 10;
            }

            return Math.Max(1, complexity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating parse complexity");
            return 1;
        }
    }

    /// <summary>
    /// 计算元素深度
    /// </summary>
    protected int CalculateElementDepth(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        var maxDepth = 0;

        foreach (var child in element.Elements())
        {
            var childDepth = 1 + CalculateElementDepth(child);
            maxDepth = Math.Max(maxDepth, childDepth);
        }

        return maxDepth;
    }

    /// <summary>
    /// 清理和标准化文本
    /// </summary>
    protected string CleanText(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return string.Empty;
        }

        try
        {
            // 移除多余的空白字符
            text = System.Text.RegularExpressions.Regex.Replace(text, @"\s+", " ");
            
            // 移除首尾空白
            text = text.Trim();

            return text;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning text: {Text}", text);
            return text;
        }
    }

    /// <summary>
    /// 检查元素是否为空
    /// </summary>
    protected bool IsEmptyElement(object? element)
    {
        if (element == null)
        {
            return true;
        }

        if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
        {
            return !xmlElement.HasChildren && string.IsNullOrWhiteSpace(xmlElement.InnerText);
        }

        if (element is string str)
        {
            return string.IsNullOrWhiteSpace(str);
        }

        return false;
    }

    /// <summary>
    /// 创建错误恢复组件
    /// </summary>
    protected FormulaComponent CreateErrorRecoveryComponent(string originalText, string errorReason)
    {
        var component = CreateComponent("ErrorRecovery", originalText);
        component.Properties["ErrorReason"] = errorReason;
        component.Properties["RecoveryMethod"] = "TextFallback";
        
        _logger.LogWarning("Created error recovery component: {ErrorReason}", errorReason);
        
        return component;
    }
}
