using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts boolean to visibility with inverse logic (false = Visible, true = Collapsed)
/// </summary>
public class InverseBooleanToVisibilityConverter : IValueConverter
{
    public static readonly InverseBooleanToVisibilityConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is bool boolValue)
        {
            return boolValue ? Visibility.Collapsed : Visibility.Visible;
        }
        
        return Visibility.Visible;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is Visibility visibility)
        {
            return visibility == Visibility.Collapsed;
        }
        
        return false;
    }
}