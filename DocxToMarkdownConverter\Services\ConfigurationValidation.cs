using DocxToMarkdownConverter.Models;
using System.IO;
using System.Text.Json;
using System.Security.Cryptography;
using System.Text;
using System.Reflection;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// Configuration validation methods for ConfigurationService
/// </summary>
public partial class ConfigurationService
{
    #region Validation Methods

    private async Task<ConfigurationValidationResult> ValidateApplicationSettingsAsync(ApplicationSettings settings)
    {
        await Task.CompletedTask; // Make method async for consistency

        var result = new ConfigurationValidationResult { Result = ValidationResult.Valid };

        try
        {
            // Validate last output directory
            if (!string.IsNullOrEmpty(settings.LastOutputDirectory) && !Directory.Exists(settings.LastOutputDirectory))
            {
                result.Result = ValidationResult.Warning;
                result.Message = "Last output directory no longer exists";
                result.PropertyName = nameof(ApplicationSettings.LastOutputDirectory);
            }

            // Validate nested settings
            if (settings.ConversionOptions != null)
            {
                var conversionResult = await ValidateConversionOptionsAsync(settings.ConversionOptions);
                if (conversionResult.Result == ValidationResult.Invalid)
                {
                    return conversionResult;
                }
            }

            if (settings.AnimationSettings != null)
            {
                var animationResult = await ValidateAnimationSettingsAsync(settings.AnimationSettings);
                if (animationResult.Result == ValidationResult.Invalid)
                {
                    return animationResult;
                }
            }

            if (settings.ThemeSettings != null)
            {
                var themeResult = await ValidateThemeSettingsAsync(settings.ThemeSettings);
                if (themeResult.Result == ValidationResult.Invalid)
                {
                    return themeResult;
                }
            }

            return result;
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Application settings validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    private async Task<ConfigurationValidationResult> ValidateConversionOptionsAsync(ConversionOptions options)
    {
        await Task.CompletedTask; // Make method async for consistency

        try
        {
            // Validate output directory
            if (!string.IsNullOrEmpty(options.OutputDirectory))
            {
                try
                {
                    var fullPath = Path.GetFullPath(options.OutputDirectory);
                    var directory = Path.GetDirectoryName(fullPath);
                    if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                    {
                        return new ConfigurationValidationResult
                        {
                            Result = ValidationResult.Warning,
                            Message = "Output directory path does not exist",
                            PropertyName = nameof(ConversionOptions.OutputDirectory)
                        };
                    }
                }
                catch (Exception)
                {
                    return new ConfigurationValidationResult
                    {
                        Result = ValidationResult.Invalid,
                        Message = "Invalid output directory path",
                        PropertyName = nameof(ConversionOptions.OutputDirectory)
                    };
                }
            }

            // Validate image directory
            if (string.IsNullOrWhiteSpace(options.ImageDirectory))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Image directory cannot be empty",
                    PropertyName = nameof(ConversionOptions.ImageDirectory)
                };
            }

            // Validate image format
            if (!Enum.IsDefined(typeof(ImageFormat), options.ImageFormat))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Invalid image format specified",
                    PropertyName = nameof(ConversionOptions.ImageFormat)
                };
            }

            // Validate output encoding
            if (options.OutputEncoding == null)
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Output encoding cannot be null",
                    PropertyName = nameof(ConversionOptions.OutputEncoding)
                };
            }

            return new ConfigurationValidationResult { Result = ValidationResult.Valid };
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Conversion options validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    private async Task<ConfigurationValidationResult> ValidateAnimationSettingsAsync(AnimationSettings settings)
    {
        await Task.CompletedTask; // Make method async for consistency

        try
        {
            // Validate speed
            if (settings.Speed < 0.1 || settings.Speed > 3.0)
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Animation speed must be between 0.1 and 3.0",
                    PropertyName = nameof(AnimationSettings.Speed)
                };
            }

            // Validate default duration
            if (settings.DefaultDuration.TotalMilliseconds < 50 || settings.DefaultDuration.TotalMilliseconds > 5000)
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Default duration must be between 50ms and 5000ms",
                    PropertyName = nameof(AnimationSettings.DefaultDuration)
                };
            }

            // Validate easing mode
            if (!Enum.IsDefined(typeof(System.Windows.Media.Animation.EasingMode), settings.DefaultEasing))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Invalid easing mode specified",
                    PropertyName = nameof(AnimationSettings.DefaultEasing)
                };
            }

            return new ConfigurationValidationResult { Result = ValidationResult.Valid };
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Animation settings validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    private async Task<ConfigurationValidationResult> ValidateThemeSettingsAsync(ThemeSettings settings)
    {
        await Task.CompletedTask; // Make method async for consistency

        try
        {
            // Validate current theme
            if (!Enum.IsDefined(typeof(AppTheme), settings.CurrentTheme))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Invalid theme specified",
                    PropertyName = nameof(ThemeSettings.CurrentTheme)
                };
            }

            // Validate accent color
            if (string.IsNullOrWhiteSpace(settings.AccentColor))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Accent color cannot be empty",
                    PropertyName = nameof(ThemeSettings.AccentColor)
                };
            }

            // Validate accent color format (should be hex color)
            if (!IsValidHexColor(settings.AccentColor))
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Accent color must be a valid hex color (e.g., #FF0000)",
                    PropertyName = nameof(ThemeSettings.AccentColor)
                };
            }

            // Validate background opacity
            if (settings.BackgroundOpacity < 0.1 || settings.BackgroundOpacity > 1.0)
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Background opacity must be between 0.1 and 1.0",
                    PropertyName = nameof(ThemeSettings.BackgroundOpacity)
                };
            }

            return new ConfigurationValidationResult { Result = ValidationResult.Valid };
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Theme settings validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    private async Task<ConfigurationValidationResult> ValidateGenericSettingsAsync<T>(T settings)
    {
        await Task.CompletedTask; // Make method async for consistency

        try
        {
            if (settings == null)
            {
                return new ConfigurationValidationResult
                {
                    Result = ValidationResult.Invalid,
                    Message = "Settings object cannot be null"
                };
            }

            // Use reflection to check for required properties
            var type = typeof(T);
            var properties = type.GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (var property in properties)
            {
                var value = property.GetValue(settings);
                
                // Check for null reference types (excluding nullable value types)
                if (value == null && !IsNullableType(property.PropertyType))
                {
                    return new ConfigurationValidationResult
                    {
                        Result = ValidationResult.Invalid,
                        Message = $"Property '{property.Name}' cannot be null",
                        PropertyName = property.Name
                    };
                }

                // Check for empty strings in string properties
                if (property.PropertyType == typeof(string) && value is string stringValue && string.IsNullOrWhiteSpace(stringValue))
                {
                    // Only flag as invalid if the property name suggests it's required
                    if (property.Name.Contains("Directory") || property.Name.Contains("Path") || property.Name.Contains("Name"))
                    {
                        return new ConfigurationValidationResult
                        {
                            Result = ValidationResult.Warning,
                            Message = $"Property '{property.Name}' is empty",
                            PropertyName = property.Name
                        };
                    }
                }
            }

            return new ConfigurationValidationResult { Result = ValidationResult.Valid };
        }
        catch (Exception ex)
        {
            return new ConfigurationValidationResult
            {
                Result = ValidationResult.Invalid,
                Message = $"Generic validation failed: {ex.Message}",
                Exception = ex
            };
        }
    }

    #endregion

    #region Helper Methods

    private bool IsValidHexColor(string color)
    {
        if (string.IsNullOrWhiteSpace(color))
            return false;

        if (!color.StartsWith("#"))
            return false;

        if (color.Length != 7 && color.Length != 9) // #RRGGBB or #AARRGGBB
            return false;

        return color.Skip(1).All(c => char.IsDigit(c) || (c >= 'A' && c <= 'F') || (c >= 'a' && c <= 'f'));
    }

    private bool IsNullableType(Type type)
    {
        return type.IsGenericType && type.GetGenericTypeDefinition() == typeof(Nullable<>);
    }

    #endregion
}
