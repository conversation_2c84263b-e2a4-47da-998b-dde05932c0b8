using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.Text.Json;
using System.Text;
using System.IO;
using DocxToMarkdownConverter.Exceptions;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 高级日志记录服务实现
/// </summary>
public class AdvancedLoggingService : IAdvancedLoggingService
{
    private readonly ILogger<AdvancedLoggingService> _logger;
    private readonly ConcurrentQueue<EnhancedLogEntry> _logs = new();
    private readonly ConcurrentDictionary<string, int> _categoryCounts = new();
    private readonly ConcurrentDictionary<string, int> _exceptionCounts = new();
    private readonly string _sessionId = Guid.NewGuid().ToString("N")[..8];
    private LogLevel _currentLogLevel = LogLevel.Info;
    private readonly object _lockObject = new();

    public AdvancedLoggingService(ILogger<AdvancedLoggingService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public event EventHandler<LogEventArgs>? LogEvent;
    public event EventHandler<LogEventArgs>? LogReceived;

    public void LogDebug(string message)
    {
        if (_currentLogLevel <= LogLevel.Debug)
        {
            _logger.LogDebug(message);
            AddEnhancedLogEntry(LogLevel.Debug, "Debug", message);
        }
    }

    public void LogDebug(string message, params object[] args)
    {
        if (_currentLogLevel <= LogLevel.Debug)
        {
            var formattedMessage = string.Format(message, args);
            _logger.LogDebug(formattedMessage);
            AddEnhancedLogEntry(LogLevel.Debug, "Debug", formattedMessage);
        }
    }

    public void LogInfo(string message)
    {
        if (_currentLogLevel <= LogLevel.Info)
        {
            _logger.LogInformation(message);
            AddEnhancedLogEntry(LogLevel.Info, "Info", message);
        }
    }

    public void LogInfo(string message, params object[] args)
    {
        if (_currentLogLevel <= LogLevel.Info)
        {
            var formattedMessage = string.Format(message, args);
            _logger.LogInformation(formattedMessage);
            AddEnhancedLogEntry(LogLevel.Info, "Info", formattedMessage);
        }
    }

    public void LogWarning(string message)
    {
        if (_currentLogLevel <= LogLevel.Warning)
        {
            _logger.LogWarning(message);
            AddEnhancedLogEntry(LogLevel.Warning, "Warning", message);
        }
    }

    public void LogWarning(string message, params object[] args)
    {
        if (_currentLogLevel <= LogLevel.Warning)
        {
            var formattedMessage = string.Format(message, args);
            _logger.LogWarning(formattedMessage);
            AddEnhancedLogEntry(LogLevel.Warning, "Warning", formattedMessage);
        }
    }

    public void LogError(string message)
    {
        _logger.LogError(message);
        AddEnhancedLogEntry(LogLevel.Error, "Error", message);
    }

    public void LogError(string message, params object[] args)
    {
        var formattedMessage = string.Format(message, args);
        _logger.LogError(formattedMessage);
        AddEnhancedLogEntry(LogLevel.Error, "Error", formattedMessage);
    }

    public void LogError(Exception exception, string message)
    {
        _logger.LogError(exception, message);
        AddEnhancedLogEntry(LogLevel.Error, "Error", message, exception);
    }

    public void LogException(Exception exception, string? context = null, Dictionary<string, object>? additionalData = null)
    {
        var exceptionType = exception.GetType().Name;
        _exceptionCounts.AddOrUpdate(exceptionType, 1, (key, value) => value + 1);

        var message = $"异常: {exception.Message}";
        if (!string.IsNullOrEmpty(context))
        {
            message = $"[{context}] {message}";
        }

        var logEntry = new EnhancedLogEntry
        {
            Timestamp = DateTime.Now,
            Level = LogLevel.Error,
            Message = message,
            Exception = exception,
            Context = context,
            SessionId = _sessionId,
            CorrelationId = Guid.NewGuid().ToString("N")[..8],
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            ThreadId = Environment.CurrentManagedThreadId,
            Component = "ExceptionHandler"
        };

        if (additionalData != null)
        {
            foreach (var kvp in additionalData)
            {
                logEntry.AdditionalData[kvp.Key] = kvp.Value;
            }
        }

        // 添加异常特定信息
        if (exception is DocxToMarkdownConverter.Exceptions.ApplicationException appEx)
        {
            logEntry.AdditionalData["ErrorCode"] = appEx.ErrorCode;
            logEntry.AdditionalData["Severity"] = appEx.Severity.ToString();
        }

        _logs.Enqueue(logEntry);
        TrimLogsIfNeeded();

        var eventArgs = new LogEventArgs(logEntry.Timestamp, logEntry.Level, "Exception", message, exception);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs);

        _logger.LogError(exception, message);
    }

    public void LogPerformance(string operation, TimeSpan duration, Dictionary<string, object>? metrics = null)
    {
        var message = $"性能: {operation} 耗时 {duration.TotalMilliseconds:F2}ms";
        
        var logEntry = new EnhancedLogEntry
        {
            Timestamp = DateTime.Now,
            Level = LogLevel.Info,
            Message = message,
            Duration = duration,
            SessionId = _sessionId,
            OperationId = Guid.NewGuid().ToString("N")[..8],
            Component = "Performance",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            ThreadId = Environment.CurrentManagedThreadId
        };

        logEntry.AdditionalData["Operation"] = operation;
        logEntry.AdditionalData["DurationMs"] = duration.TotalMilliseconds;

        if (metrics != null)
        {
            foreach (var kvp in metrics)
            {
                logEntry.AdditionalData[kvp.Key] = kvp.Value;
            }
        }

        _logs.Enqueue(logEntry);
        TrimLogsIfNeeded();

        var eventArgs = new LogEventArgs(logEntry.Timestamp, logEntry.Level, "Performance", message);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs);

        _logger.LogInformation(message);
    }

    public void LogUserAction(string action, string? details = null, Dictionary<string, object>? parameters = null)
    {
        var message = $"用户操作: {action}";
        if (!string.IsNullOrEmpty(details))
        {
            message += $" - {details}";
        }

        var logEntry = new EnhancedLogEntry
        {
            Timestamp = DateTime.Now,
            Level = LogLevel.Info,
            Message = message,
            SessionId = _sessionId,
            Component = "UserAction",
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            ThreadId = Environment.CurrentManagedThreadId
        };

        logEntry.AdditionalData["Action"] = action;
        if (!string.IsNullOrEmpty(details))
        {
            logEntry.AdditionalData["Details"] = details;
        }

        if (parameters != null)
        {
            foreach (var kvp in parameters)
            {
                logEntry.AdditionalData[kvp.Key] = kvp.Value;
            }
        }

        _logs.Enqueue(logEntry);
        TrimLogsIfNeeded();

        var eventArgs = new LogEventArgs(logEntry.Timestamp, logEntry.Level, "UserAction", message);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs);

        _logger.LogInformation(message);
    }

    public void LogSystemStatus(string component, string status, Dictionary<string, object>? statusData = null)
    {
        var message = $"系统状态: {component} - {status}";
        
        var logEntry = new EnhancedLogEntry
        {
            Timestamp = DateTime.Now,
            Level = LogLevel.Info,
            Message = message,
            SessionId = _sessionId,
            Component = component,
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            ThreadId = Environment.CurrentManagedThreadId
        };

        logEntry.AdditionalData["Status"] = status;

        if (statusData != null)
        {
            foreach (var kvp in statusData)
            {
                logEntry.AdditionalData[kvp.Key] = kvp.Value;
            }
        }

        _logs.Enqueue(logEntry);
        TrimLogsIfNeeded();

        var eventArgs = new LogEventArgs(logEntry.Timestamp, logEntry.Level, "SystemStatus", message);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs);

        _logger.LogInformation(message);
    }

    public IDisposable BeginPerformanceTracking(string operation, Dictionary<string, object>? initialData = null)
    {
        return new PerformanceTracker(this, operation, initialData);
    }

    public LogStatistics GetLogStatistics()
    {
        var logs = _logs.ToArray();
        
        return new LogStatistics
        {
            TotalLogs = logs.Length,
            ErrorCount = logs.Count(l => l.Level == LogLevel.Error),
            WarningCount = logs.Count(l => l.Level == LogLevel.Warning),
            InfoCount = logs.Count(l => l.Level == LogLevel.Info),
            DebugCount = logs.Count(l => l.Level == LogLevel.Debug),
            OldestLogTime = logs.Length > 0 ? logs.Min(l => l.Timestamp) : DateTime.MinValue,
            NewestLogTime = logs.Length > 0 ? logs.Max(l => l.Timestamp) : DateTime.MinValue,
            TotalLogSizeBytes = logs.Sum(l => Encoding.UTF8.GetByteCount(l.Message ?? "")),
            CategoryCounts = new Dictionary<string, int>(_categoryCounts),
            ExceptionCounts = new Dictionary<string, int>(_exceptionCounts)
        };
    }

    public async Task ExportLogsAsync(string filePath, LogExportOptions? options = null)
    {
        options ??= new LogExportOptions();
        var logs = _logs.ToArray()
            .Where(l => l.Level >= options.MinLevel)
            .Where(l => options.StartTime == null || l.Timestamp >= options.StartTime)
            .Where(l => options.EndTime == null || l.Timestamp <= options.EndTime)
            .Where(l => string.IsNullOrEmpty(options.CategoryFilter) ||
                       (l.Component?.Contains(options.CategoryFilter, StringComparison.OrdinalIgnoreCase) ?? false))
            .Take(options.MaxEntries)
            .OrderBy(l => l.Timestamp)
            .ToArray();

        var content = options.Format switch
        {
            LogExportFormat.Json => await ExportToJsonAsync(logs, options),
            LogExportFormat.Csv => await ExportToCsvAsync(logs, options),
            LogExportFormat.Xml => await ExportToXmlAsync(logs, options),
            _ => await ExportToTextAsync(logs, options)
        };

        await File.WriteAllTextAsync(filePath, content, Encoding.UTF8);
    }

    public async Task CleanupOldLogsAsync(TimeSpan maxAge)
    {
        var cutoffTime = DateTime.Now - maxAge;
        var logsToKeep = new List<EnhancedLogEntry>();

        while (_logs.TryDequeue(out var log))
        {
            if (log.Timestamp >= cutoffTime)
            {
                logsToKeep.Add(log);
            }
        }

        foreach (var log in logsToKeep)
        {
            _logs.Enqueue(log);
        }

        await Task.CompletedTask;
    }

    public void SetLogLevel(LogLevel level)
    {
        _currentLogLevel = level;
        LogInfo($"日志级别已设置为: {level}");
    }

    public LogLevel GetLogLevel()
    {
        return _currentLogLevel;
    }

    // ILoggingService 接口的其他方法
    public void LogConversionStart(string fileName, string operation)
    {
        LogUserAction("ConversionStart", $"{operation}: {fileName}");
    }

    public void LogConversionProgress(string fileName, string operation, double progress)
    {
        if (_currentLogLevel <= LogLevel.Debug)
        {
            LogDebug($"转换进度: {fileName} - {operation} ({progress:F1}%)");
        }
    }

    public void LogConversionComplete(string fileName, bool success, TimeSpan duration, string? errorMessage = null)
    {
        var status = success ? "成功" : "失败";
        var message = $"转换完成: {fileName} - {status}";

        var metrics = new Dictionary<string, object>
        {
            ["FileName"] = fileName,
            ["Success"] = success,
            ["Duration"] = duration.TotalMilliseconds
        };

        if (!success && !string.IsNullOrEmpty(errorMessage))
        {
            metrics["ErrorMessage"] = errorMessage;
        }

        LogPerformance($"Conversion_{status}", duration, metrics);
    }

    public void LogBatchStatistics(int totalFiles, int completedFiles, int failedFiles, TimeSpan totalDuration)
    {
        var metrics = new Dictionary<string, object>
        {
            ["TotalFiles"] = totalFiles,
            ["CompletedFiles"] = completedFiles,
            ["FailedFiles"] = failedFiles,
            ["TotalDuration"] = totalDuration.TotalMilliseconds,
            ["SuccessRate"] = totalFiles > 0 ? (double)completedFiles / totalFiles * 100 : 0
        };

        LogPerformance("BatchConversion", totalDuration, metrics);
    }

    public void ClearLogs()
    {
        while (_logs.TryDequeue(out _)) { }
        _categoryCounts.Clear();
        _exceptionCounts.Clear();
    }

    public IEnumerable<LogEntry> GetLogs()
    {
        return _logs.ToArray().Cast<LogEntry>();
    }

    private void AddEnhancedLogEntry(LogLevel level, string category, string message, Exception? exception = null)
    {
        _categoryCounts.AddOrUpdate(category, 1, (key, value) => value + 1);

        var logEntry = new EnhancedLogEntry
        {
            Timestamp = DateTime.Now,
            Level = level,
            Message = message,
            Exception = exception,
            SessionId = _sessionId,
            Component = category,
            MachineName = Environment.MachineName,
            ProcessId = Environment.ProcessId,
            ThreadId = Environment.CurrentManagedThreadId
        };

        _logs.Enqueue(logEntry);
        TrimLogsIfNeeded();

        var eventArgs = new LogEventArgs(logEntry.Timestamp, level, category, message, exception);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs);
    }

    private void TrimLogsIfNeeded()
    {
        const int maxLogs = 10000;
        while (_logs.Count > maxLogs)
        {
            _logs.TryDequeue(out _);
        }
    }

    private async Task<string> ExportToTextAsync(EnhancedLogEntry[] logs, LogExportOptions options)
    {
        var sb = new StringBuilder();
        sb.AppendLine($"日志导出 - {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"总计 {logs.Length} 条日志");
        sb.AppendLine(new string('=', 80));
        sb.AppendLine();

        foreach (var log in logs)
        {
            sb.AppendLine($"[{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff}] [{log.Level}] [{log.Component}]");
            sb.AppendLine($"消息: {log.Message}");

            if (!string.IsNullOrEmpty(log.Context))
                sb.AppendLine($"上下文: {log.Context}");

            if (log.Duration.HasValue)
                sb.AppendLine($"耗时: {log.Duration.Value.TotalMilliseconds:F2}ms");

            if (log.AdditionalData.Any())
            {
                sb.AppendLine("附加数据:");
                foreach (var kvp in log.AdditionalData)
                {
                    sb.AppendLine($"  {kvp.Key}: {kvp.Value}");
                }
            }

            if (options.IncludeExceptions && log.Exception != null)
            {
                sb.AppendLine($"异常: {log.Exception.Message}");
                if (options.IncludeStackTrace)
                {
                    sb.AppendLine($"堆栈跟踪:\n{log.Exception.StackTrace}");
                }
            }

            sb.AppendLine(new string('-', 40));
        }

        await Task.CompletedTask; // 消除async警告
        return sb.ToString();
    }

    private async Task<string> ExportToJsonAsync(EnhancedLogEntry[] logs, LogExportOptions options)
    {
        var exportData = logs.Select(log => new
        {
            timestamp = log.Timestamp,
            level = log.Level.ToString(),
            component = log.Component,
            message = log.Message,
            context = log.Context,
            sessionId = log.SessionId,
            correlationId = log.CorrelationId,
            operationId = log.OperationId,
            duration = log.Duration?.TotalMilliseconds,
            additionalData = log.AdditionalData,
            exception = options.IncludeExceptions && log.Exception != null ? new
            {
                type = log.Exception.GetType().Name,
                message = log.Exception.Message,
                stackTrace = options.IncludeStackTrace ? log.Exception.StackTrace : null
            } : null
        });

        await Task.CompletedTask; // 消除async警告
        return JsonSerializer.Serialize(exportData, new JsonSerializerOptions
        {
            WriteIndented = true,
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase
        });
    }

    private async Task<string> ExportToCsvAsync(EnhancedLogEntry[] logs, LogExportOptions options)
    {
        var sb = new StringBuilder();
        sb.AppendLine("Timestamp,Level,Component,Message,Context,Duration,Exception");

        foreach (var log in logs)
        {
            var duration = log.Duration?.TotalMilliseconds.ToString("F2") ?? "";
            var exception = options.IncludeExceptions && log.Exception != null ?
                log.Exception.Message.Replace("\"", "\"\"") : "";

            sb.AppendLine($"\"{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff}\"," +
                         $"\"{log.Level}\"," +
                         $"\"{log.Component}\"," +
                         $"\"{log.Message?.Replace("\"", "\"\"")}\"," +
                         $"\"{log.Context?.Replace("\"", "\"\"")}\"," +
                         $"\"{duration}\"," +
                         $"\"{exception}\"");
        }

        await Task.CompletedTask; // 消除async警告
        return sb.ToString();
    }

    private async Task<string> ExportToXmlAsync(EnhancedLogEntry[] logs, LogExportOptions options)
    {
        var sb = new StringBuilder();
        sb.AppendLine("<?xml version=\"1.0\" encoding=\"UTF-8\"?>");
        sb.AppendLine("<logs>");

        foreach (var log in logs)
        {
            sb.AppendLine("  <log>");
            sb.AppendLine($"    <timestamp>{log.Timestamp:yyyy-MM-dd HH:mm:ss.fff}</timestamp>");
            sb.AppendLine($"    <level>{log.Level}</level>");
            sb.AppendLine($"    <component>{log.Component}</component>");
            sb.AppendLine($"    <message><![CDATA[{log.Message}]]></message>");

            if (!string.IsNullOrEmpty(log.Context))
                sb.AppendLine($"    <context><![CDATA[{log.Context}]]></context>");

            if (log.Duration.HasValue)
                sb.AppendLine($"    <duration>{log.Duration.Value.TotalMilliseconds:F2}</duration>");

            if (options.IncludeExceptions && log.Exception != null)
            {
                sb.AppendLine("    <exception>");
                sb.AppendLine($"      <type>{log.Exception.GetType().Name}</type>");
                sb.AppendLine($"      <message><![CDATA[{log.Exception.Message}]]></message>");
                if (options.IncludeStackTrace)
                    sb.AppendLine($"      <stackTrace><![CDATA[{log.Exception.StackTrace}]]></stackTrace>");
                sb.AppendLine("    </exception>");
            }

            sb.AppendLine("  </log>");
        }

        sb.AppendLine("</logs>");
        await Task.CompletedTask; // 消除async警告
        return sb.ToString();
    }
}

/// <summary>
/// 性能跟踪器实现
/// </summary>
public class PerformanceTracker : IPerformanceTracker
{
    private readonly AdvancedLoggingService _loggingService;
    private readonly string _operation;
    private readonly Stopwatch _stopwatch;
    private readonly Dictionary<string, object> _metrics;
    private readonly Dictionary<string, string> _tags;
    private string _result = "Success";

    public PerformanceTracker(AdvancedLoggingService loggingService, string operation, Dictionary<string, object>? initialData = null)
    {
        _loggingService = loggingService;
        _operation = operation;
        _stopwatch = Stopwatch.StartNew();
        _metrics = initialData != null ? new Dictionary<string, object>(initialData) : new Dictionary<string, object>();
        _tags = new Dictionary<string, string>();
    }

    public void AddMetric(string name, object value)
    {
        _metrics[name] = value;
    }

    public void AddTag(string key, string value)
    {
        _tags[key] = value;
    }

    public void SetResult(string result)
    {
        _result = result;
    }

    public void Dispose()
    {
        _stopwatch.Stop();

        // 添加标签到指标中
        foreach (var tag in _tags)
        {
            _metrics[$"Tag_{tag.Key}"] = tag.Value;
        }

        _metrics["Result"] = _result;

        _loggingService.LogPerformance(_operation, _stopwatch.Elapsed, _metrics);
    }
}
