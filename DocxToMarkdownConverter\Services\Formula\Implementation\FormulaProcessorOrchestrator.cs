using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;
using System.Diagnostics;

namespace DocxToMarkdownConverter.Services.Formula.Implementation;

/// <summary>
/// 公式处理器编排器 - 协调所有公式处理组件
/// </summary>
public class FormulaProcessorOrchestrator : IFormulaProcessor
{
    private readonly ILogger<FormulaProcessorOrchestrator> _logger;
    private readonly IFormulaDetector _detector;
    private readonly IFormulaExtractor _extractor;
    private readonly IEnumerable<IFormulaParser> _parsers;
    private readonly IEnumerable<IFormulaConverter> _converters;
    private readonly IFormulaFormatter _formatter;
    private readonly IFormulaCacheService? _cacheService;
    private readonly FormulaProcessingOptions _options;

    public FormulaProcessorOrchestrator(
        ILogger<FormulaProcessorOrchestrator> logger,
        IFormulaDetector detector,
        IFormulaExtractor extractor,
        IEnumerable<IFormulaParser> parsers,
        IEnumerable<IFormulaConverter> converters,
        IFormulaFormatter formatter,
        IOptions<FormulaProcessingOptions> options,
        IFormulaCacheService? cacheService = null)
    {
        _logger = logger;
        _detector = detector;
        _extractor = extractor;
        _parsers = parsers;
        _converters = converters;
        _formatter = formatter;
        _cacheService = cacheService;
        _options = options.Value;
    }

    /// <summary>
    /// 异步处理数学公式
    /// </summary>
    public async Task<FormulaProcessingResult> ProcessAsync(FormulaProcessingRequest request)
    {
        var stopwatch = Stopwatch.StartNew();
        var result = new FormulaProcessingResult
        {
            RequestId = request.RequestId,
            Format = request.Options.OutputFormat
        };

        try
        {
            _logger.LogDebug("Starting formula processing for request: {RequestId}", request.RequestId);

            // 验证请求
            var validationResult = ValidateRequest(request);
            if (!validationResult.IsValid)
            {
                result.Success = false;
                result.Errors.AddRange(validationResult.Errors.Select(e => new FormulaProcessingError
                {
                    Code = "VALIDATION_ERROR",
                    Message = e,
                    Severity = FormulaErrorSeverity.Error
                }));
                return result;
            }

            // 检查缓存
            if (_options.EnableCaching && _cacheService != null)
            {
                var cacheKey = GenerateCacheKey(request);
                var cachedResult = await _cacheService.GetOrCreateAsync(cacheKey, 
                    () => ProcessFormulaInternalAsync(request), _options.CacheExpiration);
                
                if (cachedResult != null)
                {
                    _logger.LogDebug("Retrieved result from cache for request: {RequestId}", request.RequestId);
                    return cachedResult;
                }
            }

            // 处理公式
            result = await ProcessFormulaInternalAsync(request);
        }
        catch (OperationCanceledException)
        {
            _logger.LogWarning("Formula processing was cancelled for request: {RequestId}", request.RequestId);
            result.Success = false;
            result.Errors.Add(new FormulaProcessingError
            {
                Code = "OPERATION_CANCELLED",
                Message = "Processing was cancelled",
                Severity = FormulaErrorSeverity.Warning
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Unexpected error processing formula for request: {RequestId}", request.RequestId);
            result.Success = false;
            result.Errors.Add(new FormulaProcessingError
            {
                Code = "UNEXPECTED_ERROR",
                Message = ex.Message,
                Severity = FormulaErrorSeverity.Critical,
                InnerException = ex
            });
        }
        finally
        {
            stopwatch.Stop();
            result.ProcessingTimeMs = stopwatch.ElapsedMilliseconds;
            _logger.LogDebug("Formula processing completed in {ProcessingTime}ms for request: {RequestId}", 
                result.ProcessingTimeMs, request.RequestId);
        }

        return result;
    }

    /// <summary>
    /// 检查是否可以处理指定元素
    /// </summary>
    public async Task<bool> CanProcessAsync(object element)
    {
        try
        {
            return await _detector.ContainsFormulasAsync(element);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if element can be processed");
            return false;
        }
    }

    /// <summary>
    /// 从容器中提取所有公式
    /// </summary>
    public async Task<IEnumerable<FormulaElement>> ExtractFormulasAsync(object container)
    {
        try
        {
            return await _extractor.ExtractAsync(container);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting formulas from container");
            return Enumerable.Empty<FormulaElement>();
        }
    }

    #region Private Methods

    private async Task<FormulaProcessingResult> ProcessFormulaInternalAsync(FormulaProcessingRequest request)
    {
        var result = new FormulaProcessingResult
        {
            RequestId = request.RequestId,
            Format = request.Options.OutputFormat
        };

        try
        {
            // 1. 检测公式
            var canProcess = await _detector.ContainsFormulasAsync(request.SourceElement);
            if (!canProcess)
            {
                result.Success = false;
                result.Errors.Add(new FormulaProcessingError
                {
                    Code = "NO_FORMULAS_DETECTED",
                    Message = "No formulas detected in the source element",
                    Severity = FormulaErrorSeverity.Warning
                });
                return result;
            }

            // 2. 提取公式元素
            var formulaElement = await _extractor.ExtractSingleAsync(request.SourceElement);
            if (formulaElement == null)
            {
                result.Success = false;
                result.Errors.Add(new FormulaProcessingError
                {
                    Code = "EXTRACTION_FAILED",
                    Message = "Failed to extract formula element",
                    Severity = FormulaErrorSeverity.Error
                });
                return result;
            }

            // 3. 解析公式结构
            var parseResult = await ParseFormulaAsync(formulaElement, request.CancellationToken);
            if (!parseResult.Success || parseResult.Structure == null)
            {
                result.Success = false;
                result.Errors.AddRange(parseResult.Errors.Select(e => new FormulaProcessingError
                {
                    Code = "PARSE_ERROR",
                    Message = e.Message,
                    Severity = FormulaErrorSeverity.Error
                }));
                return result;
            }

            // 4. 转换为目标格式
            var convertedOutput = await ConvertFormulaAsync(parseResult.Structure, request.Options.OutputFormat, request.CancellationToken);
            if (string.IsNullOrEmpty(convertedOutput))
            {
                result.Success = false;
                result.Errors.Add(new FormulaProcessingError
                {
                    Code = "CONVERSION_FAILED",
                    Message = "Failed to convert formula to target format",
                    Severity = FormulaErrorSeverity.Error
                });
                return result;
            }

            // 5. 格式化输出
            if (request.Options.EnablePostProcessing)
            {
                var formattingOptions = CreateFormattingOptions(request.Options);
                convertedOutput = await _formatter.FormatAsync(convertedOutput, formattingOptions);
            }

            // 6. 设置结果
            result.Success = true;
            result.Output = convertedOutput;
            result.Metadata = formulaElement.Metadata;

            _logger.LogDebug("Successfully processed formula: {Output}", convertedOutput);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in internal formula processing");
            result.Success = false;
            result.Errors.Add(new FormulaProcessingError
            {
                Code = "INTERNAL_ERROR",
                Message = ex.Message,
                Severity = FormulaErrorSeverity.Critical,
                InnerException = ex
            });
        }

        return result;
    }

    private async Task<FormulaParseResult> ParseFormulaAsync(FormulaElement element, CancellationToken cancellationToken)
    {
        try
        {
            // 查找合适的解析器
            var parser = _parsers.FirstOrDefault(p => p.CanParse(element.Type));
            if (parser == null)
            {
                _logger.LogWarning("No parser found for formula type: {FormulaType}", element.Type);
                return new FormulaParseResult
                {
                    Success = false,
                    Errors = { new FormulaParseError
                    {
                        Code = "NO_PARSER",
                        Message = $"No parser available for formula type: {element.Type}"
                    }}
                };
            }

            // 执行解析
            _logger.LogDebug("Parsing formula with parser: {ParserType}", parser.GetType().Name);
            var result = await parser.ParseAsync(element);

            if (result.Success)
            {
                _logger.LogDebug("Formula parsed successfully");
            }
            else
            {
                _logger.LogWarning("Formula parsing failed with {ErrorCount} errors", result.Errors.Count);
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing formula");
            return new FormulaParseResult
            {
                Success = false,
                Errors = { new FormulaParseError
                {
                    Code = "PARSE_EXCEPTION",
                    Message = ex.Message
                }}
            };
        }
    }

    private async Task<string> ConvertFormulaAsync(FormulaStructure structure, FormulaOutputFormat format, CancellationToken cancellationToken)
    {
        try
        {
            // 查找合适的转换器
            var converter = _converters.FirstOrDefault(c => c.SupportsFormat(format));
            if (converter == null)
            {
                _logger.LogWarning("No converter found for format: {Format}", format);
                return $"<!-- No converter available for format: {format} -->";
            }

            // 执行转换
            _logger.LogDebug("Converting formula with converter: {ConverterType}", converter.GetType().Name);
            var result = await converter.ConvertAsync(structure, format);

            _logger.LogDebug("Formula converted successfully to format: {Format}", format);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting formula to format: {Format}", format);
            return $"<!-- Error converting formula: {ex.Message} -->";
        }
    }

    private FormulaFormattingOptions CreateFormattingOptions(FormulaProcessingOptions processingOptions)
    {
        return new FormulaFormattingOptions
        {
            NormalizeSymbols = true,
            FixSpacing = true,
            OptimizeOutput = true,
            AddSpacingAroundOperators = true,
            BeautifyOutput = true,
            MaxInlineFormulaLength = 50,
            CustomSymbolMap = processingOptions.CustomSymbolMap
        };
    }

    private ValidationResult ValidateRequest(FormulaProcessingRequest request)
    {
        var result = new ValidationResult();

        if (request == null)
        {
            result.AddError("Request cannot be null");
            return result;
        }

        if (request.SourceElement == null)
        {
            result.AddError("Source element cannot be null");
        }

        if (request.Options == null)
        {
            result.AddError("Options cannot be null");
        }

        // 验证处理选项
        if (request.Options != null)
        {
            var optionsValidation = FormulaOptionsValidator.ValidateProcessingOptions(request.Options);
            result.Errors.AddRange(optionsValidation.Errors);
            result.Warnings.AddRange(optionsValidation.Warnings);
        }

        return result;
    }

    private string GenerateCacheKey(FormulaProcessingRequest request)
    {
        try
        {
            var keyComponents = new[]
            {
                request.SourceElement?.GetType().Name ?? "null",
                request.SourceElement?.GetHashCode().ToString() ?? "0",
                request.Options.OutputFormat.ToString(),
                request.Options.ProcessingMode.ToString(),
                request.Options.EnablePostProcessing.ToString()
            };

            return string.Join(":", keyComponents);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error generating cache key");
            return Guid.NewGuid().ToString();
        }
    }

    #endregion
}

/// <summary>
/// 新的FormulaProcessor实现，提供向后兼容性
/// </summary>
public class NewFormulaProcessor : IFormulaProcessor
{
    private readonly FormulaProcessorOrchestrator _orchestrator;
    private readonly ILogger<NewFormulaProcessor> _logger;

    public NewFormulaProcessor(
        FormulaProcessorOrchestrator orchestrator,
        ILogger<NewFormulaProcessor> logger)
    {
        _orchestrator = orchestrator;
        _logger = logger;
    }

    public async Task<FormulaProcessingResult> ProcessAsync(FormulaProcessingRequest request)
    {
        return await _orchestrator.ProcessAsync(request);
    }

    public async Task<bool> CanProcessAsync(object element)
    {
        return await _orchestrator.CanProcessAsync(element);
    }

    public async Task<IEnumerable<FormulaElement>> ExtractFormulasAsync(object container)
    {
        return await _orchestrator.ExtractFormulasAsync(container);
    }

    /// <summary>
    /// 向后兼容的同步方法
    /// </summary>
    public string ProcessFormula(object formula, ConversionOptions options)
    {
        try
        {
            var request = new FormulaProcessingRequest
            {
                SourceElement = formula,
                Options = ConvertOptions(options)
            };

            var result = ProcessAsync(request).GetAwaiter().GetResult();
            return result.Success ? result.Output : "<!-- Error processing formula -->";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in legacy ProcessFormula method");
            return "<!-- Error processing formula -->";
        }
    }

    /// <summary>
    /// 向后兼容的异步LaTeX转换
    /// </summary>
    public async Task<string> ConvertToLatexAsync(object formula)
    {
        try
        {
            var request = new FormulaProcessingRequest
            {
                SourceElement = formula,
                Options = new FormulaProcessingOptions
                {
                    OutputFormat = FormulaOutputFormat.LaTeX
                }
            };

            var result = await ProcessAsync(request);
            return result.Success ? result.Output : "<!-- Error converting to LaTeX -->";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting to LaTeX");
            return "<!-- Error converting to LaTeX -->";
        }
    }

    /// <summary>
    /// 向后兼容的异步MathML转换
    /// </summary>
    public async Task<string> ConvertToMathMLAsync(object formula)
    {
        try
        {
            var request = new FormulaProcessingRequest
            {
                SourceElement = formula,
                Options = new FormulaProcessingOptions
                {
                    OutputFormat = FormulaOutputFormat.MathML
                }
            };

            var result = await ProcessAsync(request);
            return result.Success ? result.Output : "<!-- Error converting to MathML -->";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting to MathML");
            return "<!-- Error converting to MathML -->";
        }
    }

    /// <summary>
    /// 向后兼容的公式检测
    /// </summary>
    public bool ContainsFormulas(object element)
    {
        try
        {
            return CanProcessAsync(element).GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if element contains formulas");
            return false;
        }
    }

    /// <summary>
    /// 向后兼容的公式提取
    /// </summary>
    public IList<object> ExtractFormulas(DocumentFormat.OpenXml.Wordprocessing.Paragraph paragraph)
    {
        try
        {
            var formulas = ExtractFormulasAsync(paragraph).GetAwaiter().GetResult();
            return formulas.Select(f => f.SourceElement).ToList();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting formulas from paragraph");
            return new List<object>();
        }
    }

    private FormulaProcessingOptions ConvertOptions(ConversionOptions options)
    {
        return new FormulaProcessingOptions
        {
            OutputFormat = FormulaOutputFormat.LaTeX,
            ProcessInlineFormulas = true,
            ProcessDisplayFormulas = true,
            EnablePostProcessing = options.ProcessFormulas,
            EnableErrorRecovery = true
        };
    }
}
