using DocxToMarkdownConverter.Models;
using DocumentFormat.OpenXml.Packaging;

namespace DocxToMarkdownConverter.Services;

public interface IDocxConverter : IDisposable
{
    Task<ConversionResult> ConvertAsync(string inputPath, string outputPath, 
        ConversionOptions options, IProgress<Models.ConversionProgress>? progress, 
        CancellationToken cancellationToken);
    
    Task<IEnumerable<ConversionResult>> ConvertBatchAsync(
        IEnumerable<ConversionTask> tasks, 
        IProgress<Models.BatchConversionProgress>? progress,
        CancellationToken cancellationToken);
    
    void PauseBatchConversion();
    void ResumeBatchConversion();
    bool IsBatchConversionPaused { get; }
}

