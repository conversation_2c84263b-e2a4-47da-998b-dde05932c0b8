// 混合AI检测器 - 结合规则算法和LLM模型
class HybridDetector {
    constructor() {
        this.ruleBasedWeight = 0.4;  // 规则算法权重
        this.llmWeight = 0.6;        // LLM模型权重
        this.confidenceThreshold = 0.8;
        
        // 动态权重调整参数
        this.adaptiveWeighting = true;
        this.textLengthThreshold = 500;
        this.academicKeywords = [
            '研究', '分析', '实验', '数据', '方法', '结果', '系统', '算法',
            '模型', '架构', '策略', '技术', '验证', '评估', '优化', '框架',
            '本文', '本研究', '论文', '期刊', '学术', '科研'
        ];
    }

    // 主检测方法
    async detect(text) {
        const startTime = Date.now();
        
        try {
            // 并行执行规则检测和LLM检测
            const [ruleResult, llmResult] = await Promise.allSettled([
                this.getRuleBasedResult(text),
                this.getLLMResult(text)
            ]);

            // 处理检测结果
            const processedResults = this.processResults(ruleResult, llmResult);
            
            // 计算最终分数
            const finalResult = this.calculateFinalScore(processedResults, text);
            
            // 添加性能信息
            finalResult.processingTime = Date.now() - startTime;
            finalResult.timestamp = new Date().toISOString();
            
            return finalResult;

        } catch (error) {
            console.error('混合检测失败:', error);
            
            // 降级到规则检测
            try {
                const ruleResult = await this.getRuleBasedResult(text);
                return {
                    ...ruleResult,
                    confidence: 'medium',
                    details: [...ruleResult.details, '⚠️ LLM检测不可用，仅使用规则算法'],
                    processingTime: Date.now() - startTime,
                    fallbackMode: true
                };
            } catch (fallbackError) {
                throw new Error('所有检测方法都失败了');
            }
        }
    }

    // 获取规则算法结果
    async getRuleBasedResult(text) {
        return new Promise((resolve) => {
            try {
                const result = aiDetector.detectAI(text);
                resolve({
                    score: result.score,
                    confidence: result.confidence,
                    details: result.details,
                    source: 'rule'
                });
            } catch (error) {
                console.error('规则检测失败:', error);
                resolve({
                    score: 50,
                    confidence: 'low',
                    details: ['规则检测失败'],
                    source: 'rule',
                    error: true
                });
            }
        });
    }

    // 获取LLM模型结果
    async getLLMResult(text) {
        try {
            if (!llmService.isAvailable) {
                throw new Error('LLM服务不可用');
            }
            
            const result = await llmService.detectWithLLM(text);
            return {
                score: result.score,
                confidence: result.confidence,
                details: [result.reason],
                source: 'llm'
            };
        } catch (error) {
            console.warn('LLM检测失败:', error.message);
            throw error;
        }
    }

    // 处理检测结果
    processResults(ruleResult, llmResult) {
        const processed = {
            rule: null,
            llm: null,
            ruleAvailable: false,
            llmAvailable: false
        };

        // 处理规则检测结果
        if (ruleResult.status === 'fulfilled') {
            processed.rule = ruleResult.value;
            processed.ruleAvailable = !ruleResult.value.error;
        }

        // 处理LLM检测结果
        if (llmResult.status === 'fulfilled') {
            processed.llm = llmResult.value;
            processed.llmAvailable = true;
        }

        return processed;
    }

    // 计算最终分数
    calculateFinalScore(results, text) {
        const { rule, llm, ruleAvailable, llmAvailable } = results;
        
        // 动态调整权重
        const weights = this.calculateDynamicWeights(text, ruleAvailable, llmAvailable);
        
        let finalScore = 0;
        let confidence = 'low';
        let details = [];
        let sources = [];

        // 计算加权平均分数
        if (ruleAvailable && llmAvailable) {
            finalScore = (rule.score * weights.rule) + (llm.score * weights.llm);
            confidence = this.calculateConfidence(rule, llm, weights);
            details = this.mergeDetails(rule.details, llm.details);
            sources = ['rule', 'llm'];
            
        } else if (ruleAvailable) {
            finalScore = rule.score;
            confidence = rule.confidence;
            details = [...rule.details, '⚠️ LLM检测不可用'];
            sources = ['rule'];
            
        } else if (llmAvailable) {
            finalScore = llm.score;
            confidence = llm.confidence;
            details = [...llm.details, '⚠️ 规则检测不可用'];
            sources = ['llm'];
            
        } else {
            throw new Error('所有检测方法都不可用');
        }

        // 应用学术文本调整
        const academicAdjustment = this.applyAcademicAdjustment(text, finalScore);
        finalScore = academicAdjustment.score;
        if (academicAdjustment.applied) {
            details.push(academicAdjustment.reason);
        }

        // 确保分数在有效范围内
        finalScore = Math.min(100, Math.max(0, Math.round(finalScore)));

        return {
            score: finalScore,
            confidence: confidence,
            details: details,
            sources: sources,
            weights: weights,
            breakdown: {
                rule: ruleAvailable ? rule.score : null,
                llm: llmAvailable ? llm.score : null
            },
            academicAdjustment: academicAdjustment
        };
    }

    // 动态权重计算
    calculateDynamicWeights(text, ruleAvailable, llmAvailable) {
        if (!this.adaptiveWeighting) {
            return { rule: this.ruleBasedWeight, llm: this.llmWeight };
        }

        let ruleWeight = this.ruleBasedWeight;
        let llmWeight = this.llmWeight;

        // 根据文本长度调整权重
        if (text.length < this.textLengthThreshold) {
            // 短文本更依赖规则算法
            ruleWeight += 0.2;
            llmWeight -= 0.2;
        } else {
            // 长文本更依赖LLM
            ruleWeight -= 0.1;
            llmWeight += 0.1;
        }

        // 根据学术特征调整权重
        const academicScore = this.calculateAcademicScore(text);
        if (academicScore > 0.3) {
            // 学术文本更依赖规则算法（已针对学术文本优化）
            ruleWeight += 0.15;
            llmWeight -= 0.15;
        }

        // 确保权重和为1
        const total = ruleWeight + llmWeight;
        ruleWeight = ruleWeight / total;
        llmWeight = llmWeight / total;

        // 处理不可用的检测方法
        if (!ruleAvailable) {
            return { rule: 0, llm: 1 };
        }
        if (!llmAvailable) {
            return { rule: 1, llm: 0 };
        }

        return { rule: ruleWeight, llm: llmWeight };
    }

    // 计算学术特征分数
    calculateAcademicScore(text) {
        const words = text.length;
        let academicCount = 0;

        for (const keyword of this.academicKeywords) {
            const regex = new RegExp(keyword, 'gi');
            const matches = text.match(regex);
            if (matches) {
                academicCount += matches.length;
            }
        }

        return academicCount / words * 100;
    }

    // 计算置信度
    calculateConfidence(rule, llm, weights) {
        const scoreDiff = Math.abs(rule.score - llm.score);
        
        if (scoreDiff <= 10) {
            return 'high';  // 两种方法结果接近
        } else if (scoreDiff <= 25) {
            return 'medium';
        } else {
            return 'low';   // 两种方法结果差异较大
        }
    }

    // 合并详细信息
    mergeDetails(ruleDetails, llmDetails) {
        const merged = [];
        
        // 添加规则检测详情
        merged.push('📊 规则算法分析:');
        ruleDetails.forEach(detail => merged.push(`  • ${detail}`));
        
        // 添加LLM检测详情
        merged.push('🤖 LLM模型分析:');
        llmDetails.forEach(detail => merged.push(`  • ${detail}`));
        
        return merged;
    }

    // 应用学术文本调整
    applyAcademicAdjustment(text, score) {
        const academicScore = this.calculateAcademicScore(text);
        
        // 检测论文结构特征
        const structurePatterns = [
            /第[一二三四五六七八九十\d]+[节章部分]/g,
            /本文|本研究|本论文/g,
            /如下|总结|结论/g
        ];
        
        let structureCount = 0;
        structurePatterns.forEach(pattern => {
            const matches = text.match(pattern);
            if (matches) structureCount += matches.length;
        });

        // 如果是明显的学术文本，进行调整
        if (academicScore > 0.5 && structureCount >= 2) {
            const adjustedScore = score * 0.7; // 降低30%
            return {
                score: adjustedScore,
                applied: true,
                reason: '✅ 检测到学术论文结构，已应用学术文本调整'
            };
        }

        return {
            score: score,
            applied: false,
            reason: null
        };
    }

    // 设置权重
    setWeights(ruleWeight, llmWeight) {
        const total = ruleWeight + llmWeight;
        this.ruleBasedWeight = ruleWeight / total;
        this.llmWeight = llmWeight / total;
    }

    // 获取状态信息
    getStatus() {
        return {
            ruleBasedWeight: this.ruleBasedWeight,
            llmWeight: this.llmWeight,
            adaptiveWeighting: this.adaptiveWeighting,
            llmAvailable: llmService.isAvailable,
            ruleAvailable: typeof aiDetector !== 'undefined'
        };
    }

    // 批量检测
    async batchDetect(texts) {
        const results = [];
        
        for (let i = 0; i < texts.length; i++) {
            try {
                const result = await this.detect(texts[i]);
                results.push({
                    index: i,
                    text: texts[i].substring(0, 100) + '...',
                    result: result
                });
            } catch (error) {
                results.push({
                    index: i,
                    text: texts[i].substring(0, 100) + '...',
                    error: error.message
                });
            }
        }
        
        return results;
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { HybridDetector };
}

// 创建全局实例 - 延迟初始化
window.addEventListener('DOMContentLoaded', function() {
    try {
        if (typeof window.hybridDetector === 'undefined') {
            window.hybridDetector = new HybridDetector();
            console.log('✅ HybridDetector 实例已创建');
        }
    } catch (error) {
        console.error('创建混合检测器实例时出错:', error);
    }
});
