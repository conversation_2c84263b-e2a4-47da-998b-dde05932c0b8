using System.ComponentModel;
using System.Globalization;
using System.Runtime.CompilerServices;
using System.Windows;
using DocxToMarkdownConverter.Models;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 本地化服务实现
/// </summary>
public class LocalizationService : ILocalizationService
{
    private readonly ILogger<LocalizationService> _logger;
    private readonly IConfigurationService _configurationService;
    private CultureInfo _currentCulture;
    private string _currentLanguage;
    private bool _followSystemLanguage;
    private ResourceDictionary? _currentResourceDictionary;

    private static readonly Dictionary<string, LanguageInfo> SupportedLanguageMap = new()
    {
        {
            "zh-CN", new LanguageInfo
            {
                Code = "zh-CN",
                Name = "Chinese (Simplified)",
                NativeName = "中文（简体）",
                Culture = new CultureInfo("zh-CN")
            }
        },
        {
            "en-US", new LanguageInfo
            {
                Code = "en-US", 
                Name = "English (United States)",
                NativeName = "English",
                Culture = new CultureInfo("en-US")
            }
        }
    };

    public LocalizationService(ILogger<LocalizationService> logger, IConfigurationService configurationService)
    {
        _logger = logger;
        _configurationService = configurationService;
        
        // 初始化默认语言
        _currentLanguage = "zh-CN";
        _currentCulture = new CultureInfo(_currentLanguage);
        _followSystemLanguage = false;

        // 加载保存的语言设置
        _ = LoadLanguageSettingsAsync();
    }

    public CultureInfo CurrentCulture => _currentCulture;
    public string CurrentLanguage => _currentLanguage;
    public IReadOnlyList<LanguageInfo> SupportedLanguages => SupportedLanguageMap.Values.ToList();

    public bool FollowSystemLanguage
    {
        get => _followSystemLanguage;
        set
        {
            if (_followSystemLanguage != value)
            {
                _followSystemLanguage = value;
                OnPropertyChanged();
                
                if (value)
                {
                    var systemLanguage = GetSystemLanguage();
                    _ = SetLanguageAsync(systemLanguage);
                }
                
                _ = SaveLanguageSettingsAsync();
            }
        }
    }

    public event EventHandler<LanguageChangedEventArgs>? LanguageChanged;
    public event PropertyChangedEventHandler? PropertyChanged;

    public string GetString(string key, string? defaultValue = null)
    {
        try
        {
            if (_currentResourceDictionary?.Contains(key) == true)
            {
                return _currentResourceDictionary[key]?.ToString() ?? defaultValue ?? key;
            }
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get localized string for key: {Key}", key);
        }

        return defaultValue ?? key;
    }

    public string GetFormattedString(string key, params object[] args)
    {
        var format = GetString(key);
        try
        {
            return string.Format(_currentCulture, format, args);
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to format string for key: {Key}", key);
            return format;
        }
    }

    public async Task<bool> SetLanguageAsync(string languageCode)
    {
        if (!SupportedLanguageMap.ContainsKey(languageCode))
        {
            _logger.LogWarning("Unsupported language code: {LanguageCode}", languageCode);
            return false;
        }

        var oldLanguage = _currentLanguage;
        var oldCulture = _currentCulture;

        try
        {
            _currentLanguage = languageCode;
            _currentCulture = new CultureInfo(languageCode);

            // 设置线程文化
            Thread.CurrentThread.CurrentCulture = _currentCulture;
            Thread.CurrentThread.CurrentUICulture = _currentCulture;
            CultureInfo.DefaultThreadCurrentCulture = _currentCulture;
            CultureInfo.DefaultThreadCurrentUICulture = _currentCulture;

            // 加载资源
            await LoadResourceDictionaryAsync(languageCode);

            // 保存设置
            await SaveLanguageSettingsAsync();

            // 通知属性变更
            OnPropertyChanged(nameof(CurrentLanguage));
            OnPropertyChanged(nameof(CurrentCulture));

            // 触发语言变更事件
            LanguageChanged?.Invoke(this, new LanguageChangedEventArgs(oldLanguage, _currentLanguage, oldCulture, _currentCulture));

            _logger.LogInformation("Language changed from {OldLanguage} to {NewLanguage}", oldLanguage, _currentLanguage);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to set language to {LanguageCode}", languageCode);
            
            // 恢复原来的设置
            _currentLanguage = oldLanguage;
            _currentCulture = oldCulture;
            return false;
        }
    }

    public async Task ReloadResourcesAsync()
    {
        await LoadResourceDictionaryAsync(_currentLanguage);
    }

    public string GetSystemLanguage()
    {
        var systemCulture = CultureInfo.CurrentUICulture;
        var languageCode = systemCulture.Name;

        // 检查是否支持系统语言
        if (SupportedLanguageMap.ContainsKey(languageCode))
        {
            return languageCode;
        }

        // 尝试匹配语言部分
        var languagePart = systemCulture.TwoLetterISOLanguageName;
        var matchingLanguage = SupportedLanguageMap.Keys.FirstOrDefault(k => k.StartsWith(languagePart));
        
        return matchingLanguage ?? "en-US"; // 默认返回英文
    }

    private async Task LoadResourceDictionaryAsync(string languageCode)
    {
        try
        {
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                var resourceUri = new Uri($"Resources/Strings.{languageCode}.xaml", UriKind.Relative);
                _currentResourceDictionary = new ResourceDictionary { Source = resourceUri };

                // 更新应用程序资源
                var app = System.Windows.Application.Current;
                var existingResource = app.Resources.MergedDictionaries
                    .FirstOrDefault(d => d.Source?.ToString().Contains("Strings.") == true);

                if (existingResource != null)
                {
                    app.Resources.MergedDictionaries.Remove(existingResource);
                }

                app.Resources.MergedDictionaries.Insert(0, _currentResourceDictionary);
            });

            _logger.LogDebug("Loaded resource dictionary for language: {LanguageCode}", languageCode);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load resource dictionary for language: {LanguageCode}", languageCode);
            throw;
        }
    }

    private async Task LoadLanguageSettingsAsync()
    {
        try
        {
            var languageSettings = await _configurationService.LoadSettingsAsync<LanguageSettings>();
            if (languageSettings != null)
            {
                _followSystemLanguage = languageSettings.FollowSystemLanguage;

                var targetLanguage = _followSystemLanguage ? GetSystemLanguage() : languageSettings.CurrentLanguage;
                if (SupportedLanguageMap.ContainsKey(targetLanguage))
                {
                    await SetLanguageAsync(targetLanguage);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load language settings");
        }
    }

    private async Task SaveLanguageSettingsAsync()
    {
        try
        {
            var languageSettings = new LanguageSettings
            {
                CurrentLanguage = _currentLanguage,
                FollowSystemLanguage = _followSystemLanguage
            };

            await _configurationService.SaveSettingsAsync(languageSettings);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to save language settings");
        }
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}
