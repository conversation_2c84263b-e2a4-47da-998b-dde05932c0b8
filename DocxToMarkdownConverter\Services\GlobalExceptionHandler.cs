using Microsoft.Extensions.Logging;
using System.Windows;
using System.IO;
using System.Text;
using DocxToMarkdownConverter.Exceptions;
using Application = System.Windows.Application;
using MessageBox = System.Windows.MessageBox;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 增强的全局异常处理器
/// </summary>
public class GlobalExceptionHandler
{
    private static ILogger? _logger;
    private static IErrorRecoveryService? _errorRecoveryService;
    private static readonly object _lock = new();
    private static readonly Dictionary<string, DateTime> _recentErrors = new();
    private static readonly TimeSpan _errorThrottleTime = TimeSpan.FromSeconds(5);

    public static void Initialize(ILogger logger, IErrorRecoveryService? errorRecoveryService = null)
    {
        _logger = logger;
        _errorRecoveryService = errorRecoveryService;

        // 注册全局异常处理
        AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
        Application.Current.DispatcherUnhandledException += OnDispatcherUnhandledException;
        TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
    }

    public static async Task<bool> HandleExceptionAsync(Exception exception, string? context = null, bool showUI = true)
    {
        try
        {
            // 防止重复错误消息
            if (IsRecentError(exception))
            {
                return false;
            }

            // 记录错误
            var errorId = LogException(exception, context);

            // 尝试自动恢复
            var recovered = await TryAutoRecoveryAsync(exception);

            if (!recovered && showUI)
            {
                // 显示用户友好的错误消息
                await ShowUserFriendlyErrorAsync(exception, errorId);
            }

            return recovered;
        }
        catch (Exception handlerException)
        {
            // 异常处理器本身出错，记录并继续
            _logger?.LogCritical(handlerException, "异常处理器发生错误");
            return false;
        }
    }

    private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        if (e.ExceptionObject is Exception exception)
        {
            _ = Task.Run(async () => await HandleExceptionAsync(exception, "UnhandledException"));
        }
    }

    private static void OnDispatcherUnhandledException(object sender, System.Windows.Threading.DispatcherUnhandledExceptionEventArgs e)
    {
        _ = Task.Run(async () =>
        {
            var handled = await HandleExceptionAsync(e.Exception, "DispatcherUnhandledException");
            e.Handled = handled;
        });
    }

    private static void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
    {
        _ = Task.Run(async () =>
        {
            await HandleExceptionAsync(e.Exception, "UnobservedTaskException");
            e.SetObserved();
        });
    }

    private static string LogException(Exception exception, string? context)
    {
        var errorId = Guid.NewGuid().ToString("N")[..8];
        var contextInfo = context ?? "Unknown";

        _logger?.LogError(exception, "异常 [{ErrorId}] 在 {Context}: {Message}",
            errorId, contextInfo, exception.Message);

        // 记录详细的异常信息
        if (exception is DocxToMarkdownConverter.Exceptions.ApplicationException appEx)
        {
            _logger?.LogError("错误代码: {ErrorCode}, 严重程度: {Severity}, 时间戳: {Timestamp}",
                appEx.ErrorCode, appEx.Severity, appEx.Timestamp);
        }

        return errorId;
    }

    private static async Task<bool> TryAutoRecoveryAsync(Exception exception)
    {
        if (_errorRecoveryService == null || !_errorRecoveryService.CanAutoRecover(exception))
        {
            return false;
        }

        try
        {
            _logger?.LogInformation("尝试自动恢复异常: {ExceptionType}", exception.GetType().Name);

            switch (exception)
            {
                case ConversionException ce:
                    return await _errorRecoveryService.TryRecoverConversionAsync(ce.FilePath, ce.ErrorType);
                case FileOperationException foe:
                    return await _errorRecoveryService.TryRecoverFileOperationAsync(foe.FilePath, foe.OperationType);
                case OutOfMemoryException:
                    // 强制垃圾回收
                    GC.Collect();
                    GC.WaitForPendingFinalizers();
                    GC.Collect();
                    await Task.Delay(1000);
                    return true;
                default:
                    return false;
            }
        }
        catch (Exception recoveryException)
        {
            _logger?.LogWarning(recoveryException, "自动恢复失败");
            return false;
        }
    }

    private static async Task ShowUserFriendlyErrorAsync(Exception exception, string errorId)
    {
        await Application.Current.Dispatcher.InvokeAsync(() =>
        {
            var errorInfo = BuildErrorMessage(exception, errorId);
            var suggestions = _errorRecoveryService?.GetRecoverySuggestions(exception) ?? Enumerable.Empty<string>();

            var message = new StringBuilder();
            message.AppendLine(errorInfo.Message);

            if (suggestions.Any())
            {
                message.AppendLine();
                message.AppendLine("建议解决方案：");
                foreach (var suggestion in suggestions.Take(3))
                {
                    message.AppendLine($"• {suggestion}");
                }
            }

            message.AppendLine();
            message.AppendLine($"错误ID: {errorId}");

            MessageBox.Show(message.ToString(), errorInfo.Title, MessageBoxButton.OK, errorInfo.Icon);
        });
    }

    private static (string Title, string Message, MessageBoxImage Icon) BuildErrorMessage(Exception exception, string errorId)
    {
        return exception switch
        {
            ConversionException ce => (
                "转换错误",
                GetConversionErrorMessage(ce),
                MessageBoxImage.Warning
            ),
            FileOperationException foe => (
                "文件操作错误",
                GetFileOperationErrorMessage(foe),
                MessageBoxImage.Error
            ),
            ConfigurationException configEx => (
                "配置错误",
                $"配置项 '{configEx.ConfigurationKey}' 出现问题：{configEx.Message}",
                MessageBoxImage.Warning
            ),
            UIException uiEx => (
                "界面错误",
                $"界面组件 '{uiEx.ComponentName}' 出现问题：{uiEx.Message}",
                MessageBoxImage.Information
            ),
            ValidationException valEx => (
                "验证错误",
                $"属性 '{valEx.PropertyName}' 的值 '{valEx.InvalidValue}' 无效：{valEx.Message}",
                MessageBoxImage.Warning
            ),
            UnauthorizedAccessException => (
                "权限错误",
                "没有足够的权限执行此操作。请检查文件权限或以管理员身份运行程序。",
                MessageBoxImage.Error
            ),
            FileNotFoundException => (
                "文件未找到",
                "找不到指定的文件。请确认文件路径是否正确。",
                MessageBoxImage.Warning
            ),
            DirectoryNotFoundException => (
                "目录未找到",
                "找不到指定的目录。请确认目录路径是否正确。",
                MessageBoxImage.Warning
            ),
            IOException => (
                "文件操作失败",
                "文件操作失败。请检查文件是否被其他程序占用或磁盘空间是否充足。",
                MessageBoxImage.Error
            ),
            OutOfMemoryException => (
                "内存不足",
                "系统内存不足。请尝试处理较小的文件或关闭其他程序。",
                MessageBoxImage.Error
            ),
            _ => (
                "未知错误",
                $"发生了未知错误：{exception.Message}\n\n请重试或联系技术支持。",
                MessageBoxImage.Error
            )
        };
    }

    private static string GetConversionErrorMessage(ConversionException exception)
    {
        return exception.ErrorType switch
        {
            ConversionErrorType.FileNotFound => $"找不到文件：{exception.FilePath}",
            ConversionErrorType.InvalidFormat => $"文件格式无效：{exception.FilePath}\n请确认这是一个有效的DOCX文件。",
            ConversionErrorType.PermissionDenied => $"没有权限访问文件：{exception.FilePath}",
            ConversionErrorType.CorruptedFile => $"文件已损坏：{exception.FilePath}\n请尝试用Microsoft Word修复文件。",
            ConversionErrorType.ProcessingError => $"处理文件时出错：{exception.Message}",
            ConversionErrorType.OutputError => $"保存输出文件时出错：{exception.Message}",
            ConversionErrorType.UnsupportedFeature => $"文件包含不支持的功能：{exception.Message}",
            ConversionErrorType.MemoryError => $"内存不足，无法处理文件：{exception.FilePath}",
            ConversionErrorType.TimeoutError => $"处理文件超时：{exception.FilePath}",
            _ => $"转换文件时出错：{exception.Message}"
        };
    }

    private static string GetFileOperationErrorMessage(FileOperationException exception)
    {
        var operation = exception.OperationType switch
        {
            FileOperationType.Read => "读取",
            FileOperationType.Write => "写入",
            FileOperationType.Delete => "删除",
            FileOperationType.Copy => "复制",
            FileOperationType.Move => "移动",
            FileOperationType.Create => "创建",
            FileOperationType.Access => "访问",
            _ => "操作"
        };

        return $"{operation}文件失败：{exception.FilePath}\n{exception.Message}";
    }

    private static bool IsRecentError(Exception exception)
    {
        lock (_lock)
        {
            var errorKey = $"{exception.GetType().Name}:{exception.Message}";
            var now = DateTime.UtcNow;

            if (_recentErrors.TryGetValue(errorKey, out var lastTime))
            {
                if (now - lastTime < _errorThrottleTime)
                {
                    return true;
                }
            }

            _recentErrors[errorKey] = now;

            // 清理过期的错误记录
            var expiredKeys = _recentErrors
                .Where(kvp => now - kvp.Value > _errorThrottleTime)
                .Select(kvp => kvp.Key)
                .ToList();

            foreach (var key in expiredKeys)
            {
                _recentErrors.Remove(key);
            }

            return false;
        }
    }

    /// <summary>
    /// 清理资源
    /// </summary>
    public static void Cleanup()
    {
        AppDomain.CurrentDomain.UnhandledException -= OnUnhandledException;
        if (Application.Current != null)
        {
            Application.Current.DispatcherUnhandledException -= OnDispatcherUnhandledException;
        }
        TaskScheduler.UnobservedTaskException -= OnUnobservedTaskException;

        lock (_lock)
        {
            _recentErrors.Clear();
        }
    }
}