using DocxToMarkdownConverter.Exceptions;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 高级日志记录服务接口
/// </summary>
public interface IAdvancedLoggingService : ILoggingService
{
    /// <summary>
    /// 记录异常详细信息
    /// </summary>
    void LogException(Exception exception, string? context = null, Dictionary<string, object>? additionalData = null);

    /// <summary>
    /// 记录性能指标
    /// </summary>
    void LogPerformance(string operation, TimeSpan duration, Dictionary<string, object>? metrics = null);

    /// <summary>
    /// 记录用户操作
    /// </summary>
    void LogUserAction(string action, string? details = null, Dictionary<string, object>? parameters = null);

    /// <summary>
    /// 记录系统状态
    /// </summary>
    void LogSystemStatus(string component, string status, Dictionary<string, object>? statusData = null);

    /// <summary>
    /// 开始性能跟踪
    /// </summary>
    IDisposable BeginPerformanceTracking(string operation, Dictionary<string, object>? initialData = null);

    /// <summary>
    /// 获取日志统计信息
    /// </summary>
    LogStatistics GetLogStatistics();

    /// <summary>
    /// 导出日志到文件
    /// </summary>
    Task ExportLogsAsync(string filePath, LogExportOptions? options = null);

    /// <summary>
    /// 清理旧日志
    /// </summary>
    Task CleanupOldLogsAsync(TimeSpan maxAge);

    /// <summary>
    /// 设置日志级别
    /// </summary>
    void SetLogLevel(LogLevel level);

    /// <summary>
    /// 获取当前日志级别
    /// </summary>
    LogLevel GetLogLevel();
}

/// <summary>
/// 日志统计信息
/// </summary>
public class LogStatistics
{
    public int TotalLogs { get; set; }
    public int ErrorCount { get; set; }
    public int WarningCount { get; set; }
    public int InfoCount { get; set; }
    public int DebugCount { get; set; }
    public DateTime OldestLogTime { get; set; }
    public DateTime NewestLogTime { get; set; }
    public long TotalLogSizeBytes { get; set; }
    public Dictionary<string, int> CategoryCounts { get; set; } = new();
    public Dictionary<string, int> ExceptionCounts { get; set; } = new();
}

/// <summary>
/// 日志导出选项
/// </summary>
public class LogExportOptions
{
    public LogLevel MinLevel { get; set; } = LogLevel.Debug;
    public DateTime? StartTime { get; set; }
    public DateTime? EndTime { get; set; }
    public string? CategoryFilter { get; set; }
    public LogExportFormat Format { get; set; } = LogExportFormat.Text;
    public bool IncludeExceptions { get; set; } = true;
    public bool IncludeStackTrace { get; set; } = false;
    public int MaxEntries { get; set; } = 10000;
}

/// <summary>
/// 日志导出格式
/// </summary>
public enum LogExportFormat
{
    Text,
    Json,
    Csv,
    Xml
}

/// <summary>
/// 性能跟踪器
/// </summary>
public interface IPerformanceTracker : IDisposable
{
    void AddMetric(string name, object value);
    void AddTag(string key, string value);
    void SetResult(string result);
}

/// <summary>
/// 增强的日志条目
/// </summary>
public class EnhancedLogEntry : LogEntry
{
    public string? Context { get; set; }
    public string? UserId { get; set; }
    public string? SessionId { get; set; }
    public Dictionary<string, object> AdditionalData { get; set; } = new();
    public string? CorrelationId { get; set; }
    public string? OperationId { get; set; }
    public TimeSpan? Duration { get; set; }
    public string? Component { get; set; }
    public string? Version { get; set; }
    public string? MachineName { get; set; }
    public int ProcessId { get; set; }
    public int ThreadId { get; set; }
}
