using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DocxToMarkdownConverter.Models;

/// <summary>
/// 性能设置模型
/// </summary>
public class PerformanceSettings : INotifyPropertyChanged
{
    private bool _enableAnimations = true;
    private double _animationSpeed = 1.0;
    private bool _enablePageTransitions = true;
    private bool _enableButtonAnimations = true;
    private bool _enableProgressAnimations = true;
    private bool _enableGlassEffects = true;
    private bool _enableShadowEffects = true;
    private int _maxAnimationFps = 60;
    private bool _useHardwareAcceleration = true;
    private PerformanceLevel _performanceLevel = PerformanceLevel.Auto;

    /// <summary>
    /// 是否启用动画
    /// </summary>
    public bool EnableAnimations
    {
        get => _enableAnimations;
        set => SetProperty(ref _enableAnimations, value);
    }

    /// <summary>
    /// 动画速度倍数 (0.1 - 3.0)
    /// </summary>
    public double AnimationSpeed
    {
        get => _animationSpeed;
        set => SetProperty(ref _animationSpeed, Math.Clamp(value, 0.1, 3.0));
    }

    /// <summary>
    /// 是否启用页面切换动画
    /// </summary>
    public bool EnablePageTransitions
    {
        get => _enablePageTransitions;
        set => SetProperty(ref _enablePageTransitions, value);
    }

    /// <summary>
    /// 是否启用按钮动画
    /// </summary>
    public bool EnableButtonAnimations
    {
        get => _enableButtonAnimations;
        set => SetProperty(ref _enableButtonAnimations, value);
    }

    /// <summary>
    /// 是否启用进度条动画
    /// </summary>
    public bool EnableProgressAnimations
    {
        get => _enableProgressAnimations;
        set => SetProperty(ref _enableProgressAnimations, value);
    }

    /// <summary>
    /// 是否启用玻璃效果
    /// </summary>
    public bool EnableGlassEffects
    {
        get => _enableGlassEffects;
        set => SetProperty(ref _enableGlassEffects, value);
    }

    /// <summary>
    /// 是否启用阴影效果
    /// </summary>
    public bool EnableShadowEffects
    {
        get => _enableShadowEffects;
        set => SetProperty(ref _enableShadowEffects, value);
    }

    /// <summary>
    /// 最大动画帧率
    /// </summary>
    public int MaxAnimationFps
    {
        get => _maxAnimationFps;
        set => SetProperty(ref _maxAnimationFps, Math.Clamp(value, 10, 120));
    }

    /// <summary>
    /// 是否使用硬件加速
    /// </summary>
    public bool UseHardwareAcceleration
    {
        get => _useHardwareAcceleration;
        set => SetProperty(ref _useHardwareAcceleration, value);
    }

    /// <summary>
    /// 性能级别
    /// </summary>
    public PerformanceLevel PerformanceLevel
    {
        get => _performanceLevel;
        set
        {
            if (SetProperty(ref _performanceLevel, value))
            {
                ApplyPerformanceLevel(value);
            }
        }
    }

    /// <summary>
    /// 根据性能级别应用预设配置
    /// </summary>
    private void ApplyPerformanceLevel(PerformanceLevel level)
    {
        switch (level)
        {
            case PerformanceLevel.Low:
                EnableAnimations = false;
                EnablePageTransitions = false;
                EnableButtonAnimations = false;
                EnableProgressAnimations = false;
                EnableGlassEffects = false;
                EnableShadowEffects = false;
                MaxAnimationFps = 30;
                break;

            case PerformanceLevel.Medium:
                EnableAnimations = true;
                AnimationSpeed = 1.5;
                EnablePageTransitions = false;
                EnableButtonAnimations = true;
                EnableProgressAnimations = true;
                EnableGlassEffects = false;
                EnableShadowEffects = false;
                MaxAnimationFps = 30;
                break;

            case PerformanceLevel.High:
                EnableAnimations = true;
                AnimationSpeed = 1.0;
                EnablePageTransitions = true;
                EnableButtonAnimations = true;
                EnableProgressAnimations = true;
                EnableGlassEffects = true;
                EnableShadowEffects = true;
                MaxAnimationFps = 60;
                break;

            case PerformanceLevel.Auto:
                // 自动检测系统性能并应用合适的设置
                DetectAndApplyOptimalSettings();
                break;
        }
    }

    /// <summary>
    /// 检测系统性能并应用最优设置
    /// </summary>
    private void DetectAndApplyOptimalSettings()
    {
        try
        {
            // 简单的性能检测逻辑
            var totalMemory = GC.GetTotalMemory(false);
            var processorCount = Environment.ProcessorCount;

            if (totalMemory > 8L * 1024 * 1024 * 1024 && processorCount >= 8) // 8GB+ RAM, 8+ cores
            {
                ApplyPerformanceLevel(PerformanceLevel.High);
            }
            else if (totalMemory > 4L * 1024 * 1024 * 1024 && processorCount >= 4) // 4GB+ RAM, 4+ cores
            {
                ApplyPerformanceLevel(PerformanceLevel.Medium);
            }
            else
            {
                ApplyPerformanceLevel(PerformanceLevel.Low);
            }
        }
        catch
        {
            // 如果检测失败，使用中等性能设置
            ApplyPerformanceLevel(PerformanceLevel.Medium);
        }
    }

    /// <summary>
    /// 重置为默认设置
    /// </summary>
    public void ResetToDefaults()
    {
        // 重置所有性能设置为默认值
        PerformanceLevel = PerformanceLevel.Auto;
        EnableAnimations = true;
        AnimationSpeed = 1.0;
        EnablePageTransitions = true;
        EnableButtonAnimations = true;
        EnableProgressAnimations = true;
        EnableGlassEffects = true;
        EnableShadowEffects = true;
        MaxAnimationFps = 60;
        UseHardwareAcceleration = true;

        // 应用自动检测的最佳设置
        DetectAndApplyOptimalSettings();
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value))
            return false;

        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }
}

/// <summary>
/// 性能级别枚举
/// </summary>
public enum PerformanceLevel
{
    /// <summary>
    /// 低性能模式 - 禁用大部分动画和效果
    /// </summary>
    Low,

    /// <summary>
    /// 中等性能模式 - 启用基本动画，禁用复杂效果
    /// </summary>
    Medium,

    /// <summary>
    /// 高性能模式 - 启用所有动画和效果
    /// </summary>
    High,

    /// <summary>
    /// 自动模式 - 根据系统性能自动选择
    /// </summary>
    Auto
}
