using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts count to visibility based on comparison parameter
/// </summary>
public class CountToVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is int count && parameter is string paramStr && int.TryParse(paramStr, out int threshold))
        {
            return threshold switch
            {
                0 => count == 0 ? Visibility.Visible : Visibility.Collapsed, // Show when empty
                1 => count > 0 ? Visibility.Visible : Visibility.Collapsed,  // Show when has items
                _ => count >= threshold ? Visibility.Visible : Visibility.Collapsed
            };
        }

        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}