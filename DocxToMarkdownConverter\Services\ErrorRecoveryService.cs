using DocxToMarkdownConverter.Exceptions;
using Microsoft.Extensions.Logging;
using System.IO;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 错误恢复服务实现
/// </summary>
public class ErrorRecoveryService : IErrorRecoveryService
{
    private readonly ILogger<ErrorRecoveryService> _logger;
    private readonly Random _random = new();

    public ErrorRecoveryService(ILogger<ErrorRecoveryService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, RetryPolicy? policy = null)
    {
        policy ??= RetryPolicy.Default;
        var attempt = 0;
        var delay = policy.InitialDelay;

        while (true)
        {
            try
            {
                attempt++;
                _logger.LogDebug("执行操作，尝试次数: {Attempt}", attempt);
                return await operation();
            }
            catch (Exception ex)
            {
                if (attempt >= policy.MaxRetries || (policy.ShouldRetry != null && !policy.ShouldRetry(ex)))
                {
                    _logger.LogError(ex, "操作失败，已达到最大重试次数: {MaxRetries}", policy.MaxRetries);
                    throw;
                }

                _logger.LogWarning(ex, "操作失败，将在 {Delay}ms 后重试 (尝试 {Attempt}/{MaxRetries})", 
                    delay.TotalMilliseconds, attempt, policy.MaxRetries);

                await Task.Delay(delay);

                // 计算下次延迟时间
                delay = CalculateNextDelay(delay, policy);
            }
        }
    }

    public async Task ExecuteWithRetryAsync(Func<Task> operation, RetryPolicy? policy = null)
    {
        await ExecuteWithRetryAsync(async () =>
        {
            await operation();
            return true;
        }, policy);
    }

    public async Task<bool> TryRecoverFileOperationAsync(string filePath, FileOperationType operationType)
    {
        try
        {
            _logger.LogInformation("尝试恢复文件操作: {FilePath}, 操作类型: {OperationType}", filePath, operationType);

            switch (operationType)
            {
                case FileOperationType.Read:
                    return await TryRecoverFileReadAsync(filePath);
                case FileOperationType.Write:
                    return await TryRecoverFileWriteAsync(filePath);
                case FileOperationType.Access:
                    return await TryRecoverFileAccessAsync(filePath);
                default:
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "文件操作恢复失败: {FilePath}", filePath);
            return false;
        }
    }

    public async Task<bool> TryRecoverConversionAsync(string filePath, ConversionErrorType errorType)
    {
        try
        {
            _logger.LogInformation("尝试恢复转换操作: {FilePath}, 错误类型: {ErrorType}", filePath, errorType);

            switch (errorType)
            {
                case ConversionErrorType.MemoryError:
                    return await TryRecoverMemoryErrorAsync();
                case ConversionErrorType.CorruptedFile:
                    return await TryRecoverCorruptedFileAsync(filePath);
                case ConversionErrorType.OutputError:
                    return await TryRecoverOutputErrorAsync(filePath);
                default:
                    return false;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "转换操作恢复失败: {FilePath}", filePath);
            return false;
        }
    }

    public IEnumerable<string> GetRecoverySuggestions(Exception exception)
    {
        var suggestions = new List<string>();

        switch (exception)
        {
            case ConversionException ce:
                suggestions.AddRange(GetConversionSuggestions(ce));
                break;
            case FileOperationException foe:
                suggestions.AddRange(GetFileOperationSuggestions(foe));
                break;
            case UnauthorizedAccessException:
                suggestions.Add("请检查文件权限或以管理员身份运行程序");
                suggestions.Add("确保文件没有被其他程序占用");
                break;
            case FileNotFoundException:
                suggestions.Add("请确认文件路径是否正确");
                suggestions.Add("检查文件是否已被移动或删除");
                break;
            case DirectoryNotFoundException:
                suggestions.Add("请确认目录路径是否正确");
                suggestions.Add("尝试创建缺失的目录");
                break;
            case IOException:
                suggestions.Add("请检查磁盘空间是否充足");
                suggestions.Add("确保文件没有被其他程序占用");
                suggestions.Add("尝试重启应用程序");
                break;
            case OutOfMemoryException:
                suggestions.Add("尝试处理较小的文件");
                suggestions.Add("关闭其他占用内存的程序");
                suggestions.Add("重启应用程序以释放内存");
                break;
            default:
                suggestions.Add("请重试操作");
                suggestions.Add("如果问题持续存在，请联系技术支持");
                break;
        }

        return suggestions;
    }

    public bool CanAutoRecover(Exception exception)
    {
        return exception switch
        {
            ConversionException ce => ce.ErrorType == ConversionErrorType.MemoryError ||
                                     ce.ErrorType == ConversionErrorType.OutputError,
            FileOperationException => true,
            IOException => true,
            UnauthorizedAccessException => false, // 需要用户干预
            OutOfMemoryException => true,
            _ => false
        };
    }

    private TimeSpan CalculateNextDelay(TimeSpan currentDelay, RetryPolicy policy)
    {
        var nextDelay = TimeSpan.FromMilliseconds(currentDelay.TotalMilliseconds * policy.BackoffMultiplier);
        
        if (nextDelay > policy.MaxDelay)
            nextDelay = policy.MaxDelay;

        if (policy.UseJitter)
        {
            // 添加随机抖动以避免雷群效应
            var jitter = _random.NextDouble() * 0.1; // ±10% 抖动
            var jitterMultiplier = 1.0 + (jitter - 0.05);
            nextDelay = TimeSpan.FromMilliseconds(nextDelay.TotalMilliseconds * jitterMultiplier);
        }

        return nextDelay;
    }

    private async Task<bool> TryRecoverFileReadAsync(string filePath)
    {
        // 等待文件解锁
        await Task.Delay(1000);
        
        // 检查文件是否存在且可读
        return File.Exists(filePath) && await CanReadFileAsync(filePath);
    }

    private async Task<bool> TryRecoverFileWriteAsync(string filePath)
    {
        try
        {
            // 确保目录存在
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
            }

            // 等待文件解锁
            await Task.Delay(1000);
            
            // 测试写入权限
            return await CanWriteFileAsync(filePath);
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> TryRecoverFileAccessAsync(string filePath)
    {
        await Task.Delay(500);
        return File.Exists(filePath);
    }

    private async Task<bool> TryRecoverMemoryErrorAsync()
    {
        // 强制垃圾回收
        GC.Collect();
        GC.WaitForPendingFinalizers();
        GC.Collect();
        
        await Task.Delay(2000);
        return true;
    }

    private async Task<bool> TryRecoverCorruptedFileAsync(string filePath)
    {
        // 对于损坏的文件，我们无法自动恢复，但可以建议用户操作
        await Task.Delay(100);
        return false;
    }

    private async Task<bool> TryRecoverOutputErrorAsync(string filePath)
    {
        try
        {
            var directory = Path.GetDirectoryName(filePath);
            if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
            {
                Directory.CreateDirectory(directory);
                await Task.Delay(500);
                return true;
            }
        }
        catch
        {
            // 忽略错误
        }
        
        return false;
    }

    private async Task<bool> CanReadFileAsync(string filePath)
    {
        try
        {
            using var stream = File.OpenRead(filePath);
            await stream.ReadAsync(new byte[1], 0, 1);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private async Task<bool> CanWriteFileAsync(string filePath)
    {
        try
        {
            using var stream = File.OpenWrite(filePath);
            await Task.Delay(10);
            return true;
        }
        catch
        {
            return false;
        }
    }

    private IEnumerable<string> GetConversionSuggestions(ConversionException exception)
    {
        return exception.ErrorType switch
        {
            ConversionErrorType.FileNotFound => new[] { "请确认文件路径是否正确", "检查文件是否已被移动或删除" },
            ConversionErrorType.InvalidFormat => new[] { "请确认文件是有效的DOCX格式", "尝试用Microsoft Word打开文件检查是否损坏" },
            ConversionErrorType.PermissionDenied => new[] { "请检查文件权限", "尝试以管理员身份运行程序" },
            ConversionErrorType.CorruptedFile => new[] { "文件可能已损坏", "尝试用Microsoft Word修复文件", "使用备份文件" },
            ConversionErrorType.ProcessingError => new[] { "请重试转换操作", "检查文件内容是否包含不支持的元素" },
            ConversionErrorType.OutputError => new[] { "检查输出目录权限", "确保磁盘空间充足" },
            ConversionErrorType.MemoryError => new[] { "尝试处理较小的文件", "关闭其他程序释放内存", "重启应用程序" },
            ConversionErrorType.TimeoutError => new[] { "文件可能过大", "尝试增加超时时间", "分批处理文件" },
            _ => new[] { "请重试操作", "如果问题持续存在，请联系技术支持" }
        };
    }

    private IEnumerable<string> GetFileOperationSuggestions(FileOperationException exception)
    {
        return exception.OperationType switch
        {
            FileOperationType.Read => new[] { "检查文件是否存在", "确认文件没有被其他程序占用" },
            FileOperationType.Write => new[] { "检查目录权限", "确保磁盘空间充足" },
            FileOperationType.Delete => new[] { "确认文件没有被其他程序占用", "检查文件权限" },
            FileOperationType.Access => new[] { "检查文件权限", "确认文件路径正确" },
            _ => new[] { "请重试操作" }
        };
    }
}
