using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DocxToMarkdownConverter.Models;

public class ThemeSettings : INotifyPropertyChanged
{
    private AppTheme _currentTheme = AppTheme.Dark;
    private bool _followSystemTheme = false;
    private string _accentColor = "#0078D4";
    private double _backgroundOpacity = 1.0;
    private bool _enableThemeTransitions = true;
    private bool _enableAnimations = true;
    private double _animationSpeed = 1.0;

    public AppTheme CurrentTheme
    {
        get => _currentTheme;
        set => SetProperty(ref _currentTheme, value);
    }

    public bool FollowSystemTheme
    {
        get => _followSystemTheme;
        set => SetProperty(ref _followSystemTheme, value);
    }

    public string AccentColor
    {
        get => _accentColor;
        set => SetProperty(ref _accentColor, value);
    }

    public double BackgroundOpacity
    {
        get => _backgroundOpacity;
        set => SetProperty(ref _backgroundOpacity, Math.Max(0.1, Math.Min(1.0, value)));
    }

    public bool EnableThemeTransitions
    {
        get => _enableThemeTransitions;
        set => SetProperty(ref _enableThemeTransitions, value);
    }

    public bool EnableAnimations
    {
        get => _enableAnimations;
        set => SetProperty(ref _enableAnimations, value);
    }

    public double AnimationSpeed
    {
        get => _animationSpeed;
        set => SetProperty(ref _animationSpeed, Math.Max(0.1, Math.Min(3.0, value)));
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

public enum AppTheme
{
    Light,
    Dark,
    Auto
}