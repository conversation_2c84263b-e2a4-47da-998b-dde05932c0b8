# Project Structure & Organization

## Repository Layout

This is a multi-project repository containing three main applications and supporting documentation:

```
├── .kiro/                          # Kiro IDE configuration and specs
├── DocxToMarkdownConverter/        # C# WPF application
├── docx转到md转换器/               # Python GUI application  
├── AI检测助手-优化版/              # Web-based AI detection tool
├── markdown文档/                   # Generated markdown documents
├── 基于多策略融合协同式自适应进化算法系统的高校排课优化研究/  # Academic papers
└── *.md, *.docx                   # Root-level documents
```

## C# WPF Application Structure (DocxToMarkdownConverter/)

Follows **MVVM architecture** with dependency injection:

```
DocxToMarkdownConverter/
├── Models/                 # Data models and entities
│   ├── ConversionOptions.cs
│   ├── ConversionProgress.cs
│   ├── ConversionStatistics.cs
│   ├── ApplicationSettings.cs
│   ├── ThemeSettings.cs
│   └── AnimationSettings.cs
├── ViewModels/            # MVVM ViewModels with INotifyPropertyChanged
│   ├── MainWindowViewModel.cs
│   ├── SettingsViewModel.cs
│   ├── ProgressViewModel.cs
│   └── ResultsViewModel.cs
├── Views/                 # WPF XAML views
│   ├── MainWindow.xaml
│   └── SettingsView.xaml
├── Services/              # Business logic and interfaces
│   ├── IDocxConverter.cs
│   ├── IDocumentProcessor.cs
│   ├── IThemeManager.cs
│   ├── IAnimationManager.cs
│   ├── INavigationService.cs
│   └── GlobalExceptionHandler.cs
├── Controls/              # Custom WPF controls
├── Commands/              # RelayCommand implementations
├── Converters/            # Value converters for data binding
├── Styles/                # XAML styles and resources
├── App.xaml              # Application definition and DI setup
└── appsettings.json      # Configuration settings
```

### Key Patterns
- **MVVM**: Strict separation of concerns
- **Dependency Injection**: Microsoft.Extensions.DependencyInjection
- **Interface-based**: All services implement interfaces
- **Material Design**: Consistent UI theming

## Python Application Structure (docx转到md转换器/)

Follows **modular architecture** with clear separation:

```
docx转到md转换器/
├── src/docx2markdown/     # Main source code
│   ├── core/             # Core conversion logic
│   ├── gui/              # GUI components
│   │   ├── enhanced_animations.py
│   │   ├── animation_config.py
│   │   ├── modern_main_window_v2.py
│   │   └── widgets/      # Custom GUI widgets
│   └── processors/       # Document processors
├── docs/                 # Project documentation
├── build/                # Build configuration
├── assets/               # Static resources
├── tests/                # Unit tests
├── examples/             # Usage examples
├── output/               # Default output directory
├── dist/                 # Built executables
├── docx2md_gui.py       # Main entry point
├── requirements.txt      # Python dependencies
├── pyproject.toml       # Project configuration
└── *.py                 # Utility scripts
```

### Key Patterns
- **Package-based**: Clear module organization under src/
- **Entry Point**: Single launcher script (docx2md_gui.py)
- **Build System**: PyInstaller with automated build scripts
- **Animation System**: 60FPS animation engine with performance monitoring

## Web Application Structure (AI检测助手-优化版/)

Follows **modular JavaScript** architecture:

```
AI检测助手-优化版/
├── js/                   # JavaScript modules
│   ├── main.js          # Main application controller
│   ├── ai_detector.js   # AI detection algorithms
│   ├── academic_optimizer.js  # Academic text optimization
│   ├── hybrid_detector.js     # Hybrid detection engine
│   ├── multi_round_optimizer.js  # Multi-round optimization
│   └── ollama_manager.js      # Ollama integration manager
├── docs/                # Integrated documentation
├── index.html          # Main application interface
├── server.js           # Node.js local server
├── package.json        # Node.js configuration
├── deploy.bat/.sh      # Deployment scripts
└── test_*.html         # Testing utilities
```

### Key Patterns
- **Vanilla JavaScript**: No framework dependencies
- **Module Pattern**: Each feature in separate JS file
- **Event-Driven**: Ollama manager uses event system
- **Progressive Enhancement**: Works with or without local AI models

## Naming Conventions

### C# Application
- **PascalCase**: Classes, methods, properties, public members
- **camelCase**: Private fields, local variables
- **Interface Prefix**: All interfaces start with 'I' (IDocxConverter)
- **Async Suffix**: Async methods end with 'Async'

### Python Application
- **snake_case**: Functions, variables, module names
- **PascalCase**: Class names
- **UPPER_CASE**: Constants
- **Private Prefix**: Private members start with underscore

### JavaScript Application
- **camelCase**: Functions, variables
- **PascalCase**: Constructor functions, classes
- **kebab-case**: HTML attributes, CSS classes
- **UPPER_CASE**: Constants

## File Organization Principles

1. **Separation of Concerns**: Each file has a single responsibility
2. **Interface Segregation**: Small, focused interfaces
3. **Dependency Direction**: Dependencies point inward (toward business logic)
4. **Configuration Externalization**: Settings in separate config files
5. **Resource Isolation**: Static resources in dedicated folders

## Documentation Structure

- **README.md**: Each project has comprehensive documentation
- **Inline Comments**: JSDoc for JavaScript, XML docs for C#, docstrings for Python
- **Architecture Docs**: Separate files for complex systems (animation, AI integration)
- **API Documentation**: Interface definitions and usage examples

## Testing Organization

- **Unit Tests**: Alongside source code or in dedicated test folders
- **Integration Tests**: Separate test projects/modules
- **Manual Tests**: HTML files for web application testing
- **Build Verification**: Automated testing in build scripts