#!/bin/bash

# AI检测助手本地部署脚本 v2.1.0

echo "========================================"
echo "   AI检测助手本地部署脚本 v2.1.0"
echo "========================================"
echo

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ 错误: 未检测到Node.js"
    echo "💡 请先安装Node.js:"
    echo "   - macOS: brew install node"
    echo "   - Ubuntu: sudo apt install nodejs npm"
    echo "   - 或访问: https://nodejs.org/"
    exit 1
fi

echo "✅ Node.js已安装"
node --version

# 检查端口是否被占用
if lsof -Pi :3000 -sTCP:LISTEN -t >/dev/null; then
    echo "⚠️  警告: 端口3000已被占用"
    echo "💡 将使用端口3001启动服务"
    PORT=3001
else
    PORT=3000
fi

# 配置hosts文件提示
echo
echo "📝 配置hosts文件:"
echo "   sudo echo '127.0.0.1 ai-detector.local' >> /etc/hosts"
echo

read -p "是否需要自动配置hosts文件? (y/n): " HOSTS_CONFIG
if [[ $HOSTS_CONFIG =~ ^[Yy]$ ]]; then
    echo "🔧 正在配置hosts文件..."
    if grep -q "ai-detector.local" /etc/hosts; then
        echo "✅ hosts文件已配置"
    else
        echo "127.0.0.1 ai-detector.local" | sudo tee -a /etc/hosts
        echo "✅ hosts文件配置完成"
    fi
else
    echo "💡 您可以稍后手动配置hosts文件"
fi

echo

# 启动服务器
echo "🚀 正在启动AI检测助手服务器..."
echo

if [ "$PORT" = "3001" ]; then
    node server.js --port 3001 --domain ai-detector.local
else
    node server.js --port 3000 --domain ai-detector.local
fi
