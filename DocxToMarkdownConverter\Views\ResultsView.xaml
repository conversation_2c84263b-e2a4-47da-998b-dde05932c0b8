<UserControl x:Class="DocxToMarkdownConverter.Views.ResultsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:DocxToMarkdownConverter.Models"
             xmlns:converters="clr-namespace:DocxToMarkdownConverter.Converters"
             Background="{DynamicResource MaterialDesignPaper}">

    <UserControl.Resources>
        <!-- Status to Color Converter -->
        <Style x:Key="StatusIndicatorStyle" TargetType="Border">
            <Setter Property="Width" Value="12"/>
            <Setter Property="Height" Value="12"/>
            <Setter Property="CornerRadius" Value="6"/>
            <Setter Property="Margin" Value="0,0,8,0"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding IsSuccess}" Value="True">
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                </DataTrigger>
                <DataTrigger Binding="{Binding IsSuccess}" Value="False">
                    <Setter Property="Background" Value="{DynamicResource MaterialDesignValidationErrorBrush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>

        <!-- File Size Converter -->
        <Style x:Key="FileSizeTextStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignCaptionTextBlock}">
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
        </Style>

        <!-- Context Menu for Results -->
        <ContextMenu x:Key="ResultContextMenu">
            <MenuItem Header="{DynamicResource Results.OpenOutputFile}"
                      Command="{Binding DataContext.OpenOutputFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="FileDocument"/>
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Header="{DynamicResource Results.OpenOutputDirectory}"
                      Command="{Binding DataContext.OpenOutputDirectoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="FolderOpen"/>
                </MenuItem.Icon>
            </MenuItem>
            <Separator/>
            <MenuItem Header="{DynamicResource Results.OpenInputFile}"
                      Command="{Binding DataContext.OpenInputFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="FileDocumentOutline"/>
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Header="{DynamicResource Results.OpenInputDirectory}"
                      Command="{Binding DataContext.OpenInputDirectoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="FolderOutline"/>
                </MenuItem.Icon>
            </MenuItem>
            <Separator/>
            <MenuItem Header="{DynamicResource Results.CopyOutputPath}"
                      Command="{Binding DataContext.CopyOutputPathCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="ContentCopy"/>
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Header="{DynamicResource Results.RetryConversion}"
                      Command="{Binding DataContext.RetryConversionCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="Refresh"/>
                </MenuItem.Icon>
            </MenuItem>
            <Separator/>
            <MenuItem Header="{DynamicResource Results.RemoveResult}"
                      Command="{Binding DataContext.RemoveResultCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                      CommandParameter="{Binding}">
                <MenuItem.Icon>
                    <materialDesign:PackIcon Kind="Delete"/>
                </MenuItem.Icon>
            </MenuItem>
        </ContextMenu>
    </UserControl.Resources>

    <Grid Margin="24">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header Section -->
        <Grid Grid.Row="0" Margin="0,0,0,24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <StackPanel Grid.Column="0">
                <TextBlock Text="{DynamicResource Results.Title}"
                           Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                           Margin="0,0,0,8"/>
                <TextBlock Text="{DynamicResource Results.Description}"
                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </StackPanel>

            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Style="{StaticResource MaterialDesignIconButton}"
                        Command="{Binding RefreshResultsCommand}"
                        ToolTip="Refresh Results"
                        Margin="0,0,8,0">
                    <materialDesign:PackIcon Kind="Refresh"/>
                </Button>
                <Button Style="{StaticResource MaterialDesignIconButton}"
                        Command="{Binding ExportResultsCommand}"
                        ToolTip="{DynamicResource Results.ExportResults}"
                        Margin="0,0,8,0">
                    <materialDesign:PackIcon Kind="Export"/>
                </Button>
                <Button Style="{StaticResource MaterialDesignIconButton}"
                        Command="{Binding ClearAllResultsCommand}"
                        ToolTip="{DynamicResource Results.ClearAllResults}">
                    <materialDesign:PackIcon Kind="DeleteSweep"/>
                </Button>
            </StackPanel>
        </Grid>

        <!-- Statistics Cards -->
        <Grid Grid.Row="1" Margin="0,0,0,24">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Total Results Card -->
            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="FileMultiple" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                 DockPanel.Dock="Left"
                                                 VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalResults}" 
                                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"/>
                    </DockPanel>
                    <TextBlock Text="{DynamicResource Results.TotalResults}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Success Rate Card -->
            <materialDesign:Card Grid.Column="1" Margin="8,0" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="CheckCircle" 
                                                 Width="24" Height="24"
                                                 Foreground="Green"
                                                 DockPanel.Dock="Left"
                                                 VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding SuccessRate, StringFormat={}{0:F1}%}" 
                                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"/>
                    </DockPanel>
                    <TextBlock Text="{DynamicResource Results.SuccessRate}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Processing Time Card -->
            <materialDesign:Card Grid.Column="2" Margin="8,0" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="Clock" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource SecondaryHueMidBrush}"
                                                 DockPanel.Dock="Left"
                                                 VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding AverageProcessingTime, StringFormat={}{0:mm\\:ss}}" 
                                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"/>
                    </DockPanel>
                    <TextBlock Text="{DynamicResource Results.AverageTime}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </materialDesign:Card>

            <!-- Content Processed Card -->
            <materialDesign:Card Grid.Column="3" Margin="8,0,0,0" Padding="16">
                <StackPanel>
                    <DockPanel>
                        <materialDesign:PackIcon Kind="ImageMultiple" 
                                                 Width="24" Height="24"
                                                 Foreground="{DynamicResource PrimaryHueDarkBrush}"
                                                 DockPanel.Dock="Left"
                                                 VerticalAlignment="Center"/>
                        <TextBlock Text="{Binding TotalImagesProcessed}" 
                                   Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                                   HorizontalAlignment="Right"
                                   VerticalAlignment="Center"/>
                    </DockPanel>
                    <TextBlock Text="{DynamicResource Results.ImagesProcessed}"
                               Style="{StaticResource MaterialDesignCaptionTextBlock}"
                               Foreground="{DynamicResource MaterialDesignBodyLight}"
                               Margin="0,4,0,0"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- Filter and Search Section -->
        <materialDesign:Card Grid.Row="2" Margin="0,0,0,16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Filter Bar -->
                <Border Grid.Row="0" 
                        Background="{DynamicResource MaterialDesignToolBarBackground}"
                        Padding="16,12">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <!-- Search Box -->
                        <TextBox Grid.Column="0"
                                 Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                 materialDesign:HintAssist.Hint="{DynamicResource Results.SearchHint}"
                                 materialDesign:TextFieldAssist.HasLeadingIcon="True"
                                 materialDesign:TextFieldAssist.LeadingIcon="Magnify"
                                 Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                 MaxWidth="300"
                                 HorizontalAlignment="Left"/>

                        <!-- Status Filter -->
                        <ComboBox Grid.Column="1"
                                  SelectedValue="{Binding StatusFilter}"
                                  materialDesign:HintAssist.Hint="{DynamicResource Results.FilterByStatus}"
                                  Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                  MinWidth="120"
                                  Margin="16,0,0,0">
                            <ComboBoxItem Content="{DynamicResource Results.FilterAll}"/>
                            <ComboBoxItem Content="{DynamicResource Results.FilterSuccess}"/>
                            <ComboBoxItem Content="{DynamicResource Results.FilterFailed}"/>
                        </ComboBox>
                    </Grid>
                </Border>

                <!-- Results List -->
                <ListView Grid.Row="1"
                          ItemsSource="{Binding ResultsView}"
                          Margin="16"
                          ScrollViewer.VerticalScrollBarVisibility="Auto"
                          ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                          ScrollViewer.CanContentScroll="True"
                          VirtualizingPanel.IsVirtualizing="True"
                          VirtualizingPanel.VirtualizationMode="Recycling"
                          VirtualizingPanel.IsContainerVirtualizable="True"
                          VirtualizingPanel.ScrollUnit="Pixel"
                          Background="Transparent"
                          BorderThickness="0"
                          SelectionMode="Single">
                    <ListView.ItemContainerStyle>
                        <Style TargetType="ListViewItem">
                            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                            <Setter Property="Padding" Value="0"/>
                            <Setter Property="Margin" Value="0"/>
                            <Setter Property="Background" Value="Transparent"/>
                            <Setter Property="BorderThickness" Value="0"/>
                            <Setter Property="Template">
                                <Setter.Value>
                                    <ControlTemplate TargetType="ListViewItem">
                                        <ContentPresenter/>
                                    </ControlTemplate>
                                </Setter.Value>
                            </Setter>
                        </Style>
                    </ListView.ItemContainerStyle>
                    <ListView.ItemTemplate>
                            <DataTemplate DataType="{x:Type models:ConversionResult}">
                                <materialDesign:Card Margin="0,0,0,8" 
                                                     Padding="16"
                                                     ContextMenu="{StaticResource ResultContextMenu}">
                                    <Grid>
                                        <Grid.RowDefinitions>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                            <RowDefinition Height="Auto"/>
                                        </Grid.RowDefinitions>

                                        <!-- Header Row -->
                                        <Grid Grid.Row="0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0" Orientation="Horizontal">
                                                <Border>
                                                    <Border.Style>
                                                        <Style TargetType="Border" BasedOn="{StaticResource StatusIndicatorStyle}">
                                                            <Style.Triggers>
                                                                <DataTrigger Binding="{Binding IsSuccess}" Value="True">
                                                                    <Setter Property="Background" Value="Green"/>
                                                                </DataTrigger>
                                                                <DataTrigger Binding="{Binding IsSuccess}" Value="False">
                                                                    <Setter Property="Background" Value="Red"/>
                                                                </DataTrigger>
                                                            </Style.Triggers>
                                                        </Style>
                                                    </Border.Style>
                                                </Border>
                                                <TextBlock Text="{Binding InputPath, Converter={x:Static converters:PathToFileNameConverter.Instance}}" 
                                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                                           VerticalAlignment="Center"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.OpenOutputFileCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="Open Output File"
                                                        Margin="0,0,4,0">
                                                    <materialDesign:PackIcon Kind="FileDocument" Width="16" Height="16"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.OpenOutputDirectoryCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="Open Output Directory"
                                                        Margin="0,0,4,0">
                                                    <materialDesign:PackIcon Kind="FolderOpen" Width="16" Height="16"/>
                                                </Button>
                                                <Button Style="{StaticResource MaterialDesignIconButton}"
                                                        Command="{Binding DataContext.RemoveResultCommand, RelativeSource={RelativeSource AncestorType=UserControl}}"
                                                        CommandParameter="{Binding}"
                                                        ToolTip="Remove Result">
                                                    <materialDesign:PackIcon Kind="Delete" Width="16" Height="16"/>
                                                </Button>
                                            </StackPanel>
                                        </Grid>

                                        <!-- Details Row -->
                                        <Grid Grid.Row="1" Margin="20,8,0,0">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="*"/>
                                            </Grid.ColumnDefinitions>

                                            <StackPanel Grid.Column="0">
                                                <TextBlock Text="Duration" Style="{StaticResource FileSizeTextStyle}"/>
                                                <TextBlock Text="{Binding Duration, StringFormat={}{0:mm\\:ss\\.fff}}" 
                                                           Style="{StaticResource MaterialDesignBody2TextBlock}"/>
                                            </StackPanel>

                                            <StackPanel Grid.Column="1">
                                                <TextBlock Text="File Size" Style="{StaticResource FileSizeTextStyle}"/>
                                                <TextBlock Style="{StaticResource MaterialDesignBody2TextBlock}">
                                                    <TextBlock.Text>
                                                        <MultiBinding StringFormat="{}{0} → {1}">
                                                            <Binding Path="InputSize" Converter="{x:Static converters:FileSizeConverter.Instance}"/>
                                                            <Binding Path="OutputSize" Converter="{x:Static converters:FileSizeConverter.Instance}"/>
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </StackPanel>

                                            <StackPanel Grid.Column="2">
                                                <TextBlock Text="Content" Style="{StaticResource FileSizeTextStyle}"/>
                                                <TextBlock Style="{StaticResource MaterialDesignBody2TextBlock}">
                                                    <TextBlock.Text>
                                                        <MultiBinding StringFormat="Images: {0}, Tables: {1}, Formulas: {2}">
                                                            <Binding Path="ImageCount"/>
                                                            <Binding Path="TableCount"/>
                                                            <Binding Path="FormulaCount"/>
                                                        </MultiBinding>
                                                    </TextBlock.Text>
                                                </TextBlock>
                                            </StackPanel>
                                        </Grid>

                                        <!-- Error Message Row (if failed) -->
                                        <Border Grid.Row="2" 
                                                Background="{DynamicResource MaterialDesignValidationErrorBrush}"
                                                CornerRadius="4"
                                                Padding="12,8"
                                                Margin="0,8,0,0"
                                                Visibility="{Binding IsSuccess, Converter={x:Static converters:InverseBooleanToVisibilityConverter.Instance}}">
                                            <StackPanel>
                                                <TextBlock Text="Error Details:" 
                                                           Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                                           Foreground="White"
                                                           FontWeight="Medium"/>
                                                <TextBlock Text="{Binding ErrorMessage}" 
                                                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                                                           Foreground="White"
                                                           TextWrapping="Wrap"
                                                           Margin="0,4,0,0"/>
                                            </StackPanel>
                                        </Border>
                                    </Grid>
                                </materialDesign:Card>
                            </DataTemplate>
                        </ListView.ItemTemplate>
                    </ListView>
            </Grid>
        </materialDesign:Card>

        <!-- Empty State -->
        <Grid Grid.Row="2" 
              Visibility="{Binding TotalResults, Converter={x:Static converters:EqualityToVisibilityConverter.Instance}, ConverterParameter=0}">
            <StackPanel HorizontalAlignment="Center" 
                        VerticalAlignment="Center"
                        MaxWidth="400">
                <materialDesign:PackIcon Kind="FileDocumentMultiple" 
                                         Width="64" Height="64"
                                         Foreground="{DynamicResource MaterialDesignBodyLight}"
                                         HorizontalAlignment="Center"
                                         Margin="0,0,0,16"/>
                <TextBlock Text="{DynamicResource Results.NoResultsTitle}"
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                           HorizontalAlignment="Center"
                           Margin="0,0,0,8"/>
                <TextBlock Text="{DynamicResource Results.NoResultsDescription}"
                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                           HorizontalAlignment="Center"
                           TextAlignment="Center"
                           TextWrapping="Wrap"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>