<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- 玻璃磨砂效果资源 -->
    
    <!-- 模糊效果 -->
    <BlurEffect x:Key="LightBlurEffect" Radius="8"/>
    <BlurEffect x:Key="MediumBlurEffect" Radius="12"/>
    <BlurEffect x:Key="HeavyBlurEffect" Radius="16"/>
    
    <!-- 半透明背景画刷 -->
    <SolidColorBrush x:Key="GlassBackgroundLight" Color="#E6FFFFFF" Opacity="0.7"/>
    <SolidColorBrush x:Key="GlassBackgroundDark" Color="#********" Opacity="0.6"/>
    <SolidColorBrush x:Key="GlassBackgroundAccent" Color="#E60078D4" Opacity="0.8"/>
    
    <!-- 渐变玻璃背景 -->
    <LinearGradientBrush x:Key="GlassGradientLight" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#80FFFFFF" Offset="0"/>
        <GradientStop Color="#40FFFFFF" Offset="0.5"/>
        <GradientStop Color="#20FFFFFF" Offset="1"/>
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="GlassGradientDark" StartPoint="0,0" EndPoint="1,1">
        <GradientStop Color="#60000000" Offset="0"/>
        <GradientStop Color="#40000000" Offset="0.5"/>
        <GradientStop Color="#20000000" Offset="1"/>
    </LinearGradientBrush>
    
    <!-- 玻璃磨砂窗口背景样式 -->
    <Style x:Key="GlassWindowBackgroundStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#CC161B22" Offset="0"/>
                    <GradientStop Color="#E6161B22" Offset="0.3"/>
                    <GradientStop Color="#F0161B22" Offset="0.7"/>
                    <GradientStop Color="#CC0D1117" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="#30363D"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource MediumCornerRadius}"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.4" ShadowDepth="8" BlurRadius="16"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 玻璃磨砂侧边栏样式 -->
    <Style x:Key="GlassSidebarStyle" TargetType="materialDesign:Card">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <GradientStop Color="#E621262D" Offset="0"/>
                    <GradientStop Color="#F021262D" Offset="0.5"/>
                    <GradientStop Color="#E6161B22" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="#30363D"/>
        <Setter Property="BorderThickness" Value="0,0,1,0"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp0"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="materialDesign:Card">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{StaticResource MediumCornerRadius}"
                            Padding="{TemplateBinding Padding}">
                        <!-- 玻璃效果叠加层 -->
                        <Border Background="{StaticResource GlassGradientDark}"
                                CornerRadius="{StaticResource MediumCornerRadius}">
                            <ContentPresenter/>
                        </Border>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 玻璃磨砂对话框样式 -->
    <Style x:Key="GlassDialogStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <RadialGradientBrush Center="0.5,0.3" RadiusX="0.8" RadiusY="0.8">
                    <GradientStop Color="#F021262D" Offset="0"/>
                    <GradientStop Color="#E621262D" Offset="0.6"/>
                    <GradientStop Color="#CC161B22" Offset="1"/>
                </RadialGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="#40363D"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource LargeCornerRadius}"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.6" ShadowDepth="12" BlurRadius="24"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 玻璃磨砂悬浮提示样式 -->
    <Style x:Key="GlassTooltipStyle" TargetType="Border">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#F021262D"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="#50363D"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Padding" Value="12,8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.4" ShadowDepth="4" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>
    
    <!-- 玻璃磨砂按钮悬停效果 -->
    <Style x:Key="GlassButtonHoverStyle" TargetType="Border">
        <Setter Property="Background" Value="Transparent"/>
        <Setter Property="CornerRadius" Value="{StaticResource SmallCornerRadius}"/>
        <Setter Property="Opacity" Value="0"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                             To="1" Duration="0:0:0.2"/>
                            <ColorAnimation Storyboard.TargetProperty="(Border.Background).(SolidColorBrush.Color)"
                                            To="#40FFFFFF" Duration="0:0:0.2"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard>
                        <Storyboard>
                            <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                             To="0" Duration="0:0:0.3"/>
                        </Storyboard>
                    </BeginStoryboard>
                </Trigger.ExitActions>
            </Trigger>
        </Style.Triggers>
    </Style>
    
    <!-- 玻璃磨砂卡片样式 -->
    <Style x:Key="GlassCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background">
            <Setter.Value>
                <SolidColorBrush Color="#E621262D"/>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderBrush" Value="#30363D"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
    </Style>

    <!-- 高性能玻璃卡片样式（用于进度页面） -->
    <Style x:Key="PerformanceGlassCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource MaterialDesignDivider}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        <!-- 移除复杂的模板和效果以提高性能 -->
    </Style>

</ResourceDictionary>
