using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Parsers;

/// <summary>
/// 矩阵解析器
/// </summary>
public class MatrixParser : FormulaParserBase
{
    public MatrixParser(ILogger<MatrixParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.Matrix, FormulaType.PiecewiseFunction };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Parsing matrix element: {ElementId}", element.Id);

            if (element.SourceElement is not DocumentFormat.OpenXml.Math.Matrix matrix)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a Matrix");
            }

            var structure = await ParseMatrixAsync(matrix);
            var components = await CreateMatrixComponentsAsync(matrix);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            _logger.LogDebug("Successfully parsed matrix in {ParseTime}ms", result.ParseTimeMs);
            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse matrix: {ex.Message}", ex);
        }
    }

    private async Task<MatrixStructure> ParseMatrixAsync(DocumentFormat.OpenXml.Math.Matrix matrix)
    {
        var structure = new MatrixStructure();

        try
        {
            // 解析矩阵属性
            var matrixProperties = matrix.GetFirstChild<MatrixProperties>();
            if (matrixProperties != null)
            {
                structure.Alignment = ParseMatrixAlignment(matrixProperties);
            }

            // 解析矩阵行
            var matrixRows = matrix.Elements<MatrixRow>().ToList();
            foreach (var matrixRow in matrixRows)
            {
                var row = await ParseMatrixRowAsync(matrixRow);
                structure.Rows.Add(row);
            }

            // 检查是否为分段函数
            structure.IsPiecewise = await IsPiecewiseFunctionAsync(structure);
            if (structure.IsPiecewise)
            {
                structure.Type = FormulaType.PiecewiseFunction;
                _logger.LogDebug("Matrix identified as piecewise function");
            }

            // 设置元数据
            structure.Metadata.ComplexityScore = CalculateMatrixComplexity(structure);

            _logger.LogDebug("Parsed matrix: {Rows}x{Columns}, IsPiecewise: {IsPiecewise}", 
                structure.RowCount, structure.ColumnCount, structure.IsPiecewise);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing matrix structure");
            throw;
        }

        return structure;
    }

    private async Task<MatrixRow> ParseMatrixRowAsync(MatrixRow matrixRow)
    {
        var row = new MatrixRow();

        try
        {
            // 解析行中的每个元素
            var elements = matrixRow.Elements<Base>().ToList();
            foreach (var element in elements)
            {
                var cellComponent = await ParseMatrixCellAsync(element);
                row.Cells.Add(cellComponent);
            }

            _logger.LogDebug("Parsed matrix row with {CellCount} cells", row.Cells.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing matrix row");
            
            // 错误恢复：创建一个包含原始文本的单元格
            var errorCell = CreateErrorRecoveryComponent(ExtractTextSafely(matrixRow), "Failed to parse matrix row");
            row.Cells.Add(errorCell);
        }

        return row;
    }

    private async Task<FormulaComponent> ParseMatrixCellAsync(Base cellElement)
    {
        try
        {
            var component = CreateComponent("MatrixCell");

            // 如果单元格包含子元素，递归解析
            if (cellElement.HasChildren)
            {
                var children = await ParseChildrenAsync(cellElement);
                foreach (var child in children)
                {
                    component.Children.Add(child);
                }

                // 组合子元素的文本
                var combinedText = string.Join("", children.Select(c => c.Data?.ToString() ?? ""));
                component.Data = CleanText(combinedText);
            }
            else
            {
                // 直接使用文本内容
                component.Data = CleanText(ExtractTextSafely(cellElement));
            }

            _logger.LogDebug("Parsed matrix cell: {Content}", component.Data);
            return component;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing matrix cell");
            return CreateErrorRecoveryComponent(ExtractTextSafely(cellElement), "Failed to parse matrix cell");
        }
    }

    private async Task<IList<FormulaComponent>> CreateMatrixComponentsAsync(DocumentFormat.OpenXml.Math.Matrix matrix)
    {
        var components = new List<FormulaComponent>();

        try
        {
            // 添加矩阵属性组件
            var matrixProperties = matrix.GetFirstChild<MatrixProperties>();
            if (matrixProperties != null)
            {
                var propertiesComponent = CreateComponent("MatrixProperties");
                propertiesComponent.Properties["Alignment"] = ParseMatrixAlignment(matrixProperties).ToString();
                components.Add(propertiesComponent);
            }

            // 添加行组件
            var matrixRows = matrix.Elements<MatrixRow>().ToList();
            for (int i = 0; i < matrixRows.Count; i++)
            {
                var rowComponent = CreateComponent($"Row{i}");
                var row = await ParseMatrixRowAsync(matrixRows[i]);
                
                foreach (var cell in row.Cells)
                {
                    rowComponent.Children.Add(cell);
                }

                components.Add(rowComponent);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating matrix components");
        }

        return components;
    }

    private MatrixAlignment ParseMatrixAlignment(MatrixProperties properties)
    {
        try
        {
            // 这里可以解析具体的对齐属性
            // OpenXML中的矩阵对齐信息可能在不同的子元素中
            return MatrixAlignment.Center; // 默认居中对齐
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error parsing matrix alignment, using default");
            return MatrixAlignment.Center;
        }
    }

    private async Task<bool> IsPiecewiseFunctionAsync(MatrixStructure structure)
    {
        try
        {
            if (structure.RowCount < 2)
            {
                return false; // 分段函数至少需要2行
            }

            // 检查是否包含条件表达式
            var hasConditions = false;
            var hasComparisons = false;

            foreach (var row in structure.Rows)
            {
                foreach (var cell in row.Cells)
                {
                    var cellText = cell.Data?.ToString()?.ToLower() ?? "";
                    
                    // 检查条件关键词
                    if (cellText.Contains("if") || cellText.Contains("when") || 
                        cellText.Contains("otherwise") || cellText.Contains("else") ||
                        cellText.Contains("条件") || cellText.Contains("如果") || 
                        cellText.Contains("否则"))
                    {
                        hasConditions = true;
                    }

                    // 检查比较运算符
                    if (cellText.Contains("≤") || cellText.Contains("≥") ||
                        cellText.Contains("<=") || cellText.Contains(">=") ||
                        cellText.Contains("<") || cellText.Contains(">") ||
                        cellText.Contains("=") || cellText.Contains("≠"))
                    {
                        hasComparisons = true;
                    }
                }
            }

            // 分段函数通常有条件表达式或比较运算符
            var isPiecewise = hasConditions || hasComparisons;

            _logger.LogDebug("Piecewise function detection: Conditions={HasConditions}, Comparisons={HasComparisons}, Result={IsPiecewise}", 
                hasConditions, hasComparisons, isPiecewise);

            return isPiecewise;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting piecewise function");
            return false;
        }
    }

    private int CalculateMatrixComplexity(MatrixStructure structure)
    {
        try
        {
            var complexity = 5; // 矩阵的基础复杂度

            // 基于矩阵大小
            complexity += structure.RowCount * structure.ColumnCount;

            // 基于单元格内容复杂度
            foreach (var row in structure.Rows)
            {
                foreach (var cell in row.Cells)
                {
                    var cellText = cell.Data?.ToString() ?? "";
                    complexity += cellText.Length / 10;
                    complexity += cell.Children.Count;
                }
            }

            // 分段函数增加额外复杂度
            if (structure.IsPiecewise)
            {
                complexity += 10;
            }

            return Math.Max(5, complexity);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating matrix complexity");
            return 5;
        }
    }

    /// <summary>
    /// 验证矩阵解析结果
    /// </summary>
    protected override bool ValidateParseResult(FormulaParseResult result)
    {
        if (!base.ValidateParseResult(result))
        {
            return false;
        }

        if (result.Structure is not MatrixStructure matrixStructure)
        {
            _logger.LogWarning("Parse result structure is not a MatrixStructure");
            return false;
        }

        // 验证矩阵不为空
        if (matrixStructure.RowCount == 0)
        {
            _logger.LogWarning("Matrix has no rows");
            return false;
        }

        // 验证所有行的列数一致
        var expectedColumnCount = matrixStructure.ColumnCount;
        foreach (var row in matrixStructure.Rows)
        {
            if (row.Cells.Count != expectedColumnCount)
            {
                _logger.LogWarning("Matrix row has inconsistent column count: expected {Expected}, actual {Actual}", 
                    expectedColumnCount, row.Cells.Count);
                return false;
            }
        }

        return true;
    }
}
