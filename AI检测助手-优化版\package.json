{"name": "ai-detector-assistant", "version": "2.1.0", "description": "AI检测助手 - 智能文本检测与优化工具", "main": "server.js", "scripts": {"start": "node server.js", "dev": "node server.js --port 3000 --domain ai-detector.local", "prod": "node server.js --port 80 --domain ai-detector.local", "test": "node server.js --port 3001 --domain localhost"}, "keywords": ["ai-detection", "text-optimization", "ollama", "local-llm", "academic-writing"], "author": "AI检测助手开发团队", "license": "MIT", "engines": {"node": ">=14.0.0"}, "repository": {"type": "git", "url": "https://github.com/your-repo/ai-detector-assistant.git"}, "bugs": {"url": "https://github.com/your-repo/ai-detector-assistant/issues"}, "homepage": "https://github.com/your-repo/ai-detector-assistant#readme"}