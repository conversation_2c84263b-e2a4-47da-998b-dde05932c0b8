using System.Windows.Controls;
using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Services;

namespace DocxToMarkdownConverter.Controls;

/// <summary>
/// ProgressControl.xaml 的交互逻辑
/// 提供实时进度显示、日志查看和转换控制功能
/// </summary>
public partial class ProgressControl : System.Windows.Controls.UserControl
{
    private ProgressViewModel? _viewModel;

    public ProgressControl()
    {
        InitializeComponent();

        // 注册事件处理程序
        Loaded += ProgressControl_Loaded;
        Unloaded += ProgressControl_Unloaded;
    }

    // 构造函数注入依赖
    public ProgressControl(ProgressViewModel viewModel) : this()
    {
        ViewModel = viewModel;
    }

    /// <summary>
    /// 获取或设置 ViewModel
    /// </summary>
    public ProgressViewModel? ViewModel
    {
        get => _viewModel;
        set
        {
            if (_viewModel != value)
            {
                if (_viewModel != null)
                {
                    _viewModel.PropertyChanged -= OnViewModelPropertyChanged;
                }

                _viewModel = value;
                DataContext = _viewModel;

                if (_viewModel != null)
                {
                    _viewModel.PropertyChanged += OnViewModelPropertyChanged;
                }
            }
        }
    }

    /// <summary>
    /// 设置服务依赖
    /// </summary>
    public void SetServices(IProgressTrackingService progressTrackingService, ILoggingService loggingService)
    {
        ViewModel = new ProgressViewModel(progressTrackingService, loggingService);
    }

    /// <summary>
    /// 当日志内容更新时自动滚动到底部
    /// </summary>
    private void OnLogContentChanged()
    {
        if (ViewModel?.AutoScrollEnabled == true)
        {
            Dispatcher.BeginInvoke(() =>
            {
                try
                {
                    LogScrollViewer.ScrollToEnd();
                }
                catch
                {
                    // 忽略滚动错误
                }
            });
        }
    }

    /// <summary>
    /// ViewModel 属性变化处理
    /// </summary>
    private void OnViewModelPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        switch (e.PropertyName)
        {
            case nameof(ProgressViewModel.LogContent):
                OnLogContentChanged();
                break;
            case nameof(ProgressViewModel.AutoScrollEnabled):
                if (ViewModel?.AutoScrollEnabled == true)
                {
                    Dispatcher.BeginInvoke(() => LogScrollViewer.ScrollToEnd());
                }
                break;
        }
    }

    /// <summary>
    /// 当控件加载时初始化
    /// </summary>
    private void ProgressControl_Loaded(object sender, System.Windows.RoutedEventArgs e)
    {
        // 如果没有设置 ViewModel，创建默认的
        if (ViewModel == null)
        {
            var loggingService = new SimpleLoggingService(Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleLoggingService>.Instance);
            var progressTrackingService = new ProgressTrackingService(loggingService);
            ViewModel = new ProgressViewModel(progressTrackingService, loggingService);
        }

        // 初始化详细日志查看器
        if (DetailedLogViewer != null && ViewModel != null)
        {
            // 为详细日志查看器设置相同的日志服务
            DetailedLogViewer.ViewModel.Dispose(); // 清理默认的
            DetailedLogViewer.DataContext = new LogViewerViewModel(ViewModel.LoggingService);
        }

        // 初始化日志显示
        if (ViewModel?.AutoScrollEnabled == true)
        {
            LogScrollViewer.ScrollToEnd();
        }
    }

    /// <summary>
    /// 当控件卸载时清理资源
    /// </summary>
    private void ProgressControl_Unloaded(object sender, System.Windows.RoutedEventArgs e)
    {
        ViewModel?.Dispose();
    }

    /// <summary>
    /// 手动触发进度更新（用于测试）
    /// </summary>
    public void UpdateProgress(string fileName, string operation, double progress)
    {
        // 这个方法可以被外部调用来更新进度
        // 实际使用中，进度更新应该通过 IProgressTrackingService 来处理
    }

    /// <summary>
    /// 添加日志消息（用于测试）
    /// </summary>
    public void AddLogMessage(string message, Services.LogLevel level = Services.LogLevel.Info)
    {
        // 这个方法可以被外部调用来添加日志
        // 实际使用中，日志应该通过 ILoggingService 来处理
    }

    /// <summary>
    /// 清空所有显示内容
    /// </summary>
    public void Clear()
    {
        ViewModel?.ClearLogCommand.Execute(null);
    }

    /// <summary>
    /// 重置进度显示
    /// </summary>
    public void Reset()
    {
        // 重置所有进度相关的显示
        if (ViewModel != null)
        {
            // 这里可以添加重置逻辑
        }
    }
}

/// <summary>
/// 日志级别枚举（与服务层保持一致）
/// </summary>
public enum LogLevel
{
    Debug,
    Info,
    Warning,
    Error
}