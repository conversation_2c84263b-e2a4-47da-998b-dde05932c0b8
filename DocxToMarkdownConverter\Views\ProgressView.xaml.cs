using System.Windows.Controls;
using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Services;

namespace DocxToMarkdownConverter.Views;

/// <summary>
/// ProgressView.xaml 的交互逻辑
/// </summary>
public partial class ProgressView : System.Windows.Controls.UserControl
{
    public ProgressView()
    {
        InitializeComponent();
    }

    public ProgressView(ProgressViewModel viewModel) : this()
    {
        DataContext = viewModel;
        
        // 将ViewModel传递给ProgressControl
        if (ProgressControlInstance != null)
        {
            ProgressControlInstance.ViewModel = viewModel;
        }
    }

    /// <summary>
    /// 获取内部的ProgressControl实例
    /// </summary>
    public Controls.ProgressControl ProgressControl => ProgressControlInstance;
}
