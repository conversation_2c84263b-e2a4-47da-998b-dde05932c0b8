# DOCX转换器 (DOCX to Markdown Converter)

一个功能强大、界面美观的专业DOCX文档转Markdown工具，支持批量转换、图片提取、表格转换、数学公式处理等高级功能。

![应用程序版本](https://img.shields.io/badge/版本-3.2.0-blue.svg)
![.NET版本](https://img.shields.io/badge/.NET-8.0-purple.svg)
![平台支持](https://img.shields.io/badge/平台-Windows-lightgrey.svg)
![许可证](https://img.shields.io/badge/许可证-MIT-green.svg)

## ✨ 主要特性

### 🚀 核心功能
- **批量转换** - 支持同时转换多个DOCX文件，提高工作效率
- **智能解析** - 精确解析DOCX文档结构，保持原有格式层次
- **图片提取** - 自动提取文档中的图片并保存到指定目录
- **表格转换** - 完美转换复杂表格为Markdown表格格式
- **数学公式** - 支持数学公式的识别和转换
- **格式保留** - 保持文本格式（粗体、斜体、下划线等）

### 🎨 用户界面
- **现代化设计** - 基于Material Design的美观界面
- **深色/浅色主题** - 支持主题切换，适应不同使用环境
- **响应式布局** - 自适应窗口大小，优化用户体验
- **动画效果** - 流畅的界面动画和过渡效果
- **拖拽支持** - 支持拖拽文件到应用程序进行转换

### ⚡ 性能优化
- **多线程处理** - 利用多核CPU提升转换速度
- **内存优化** - 智能内存管理，支持大文件处理
- **进度跟踪** - 实时显示转换进度和状态
- **错误恢复** - 完善的错误处理和恢复机制

### 🛠️ 高级功能
- **自定义输出** - 灵活配置输出目录和文件命名
- **多语言支持** - 支持中文和英文界面
- **配置管理** - 保存和恢复用户偏好设置
- **日志记录** - 详细的操作日志和错误记录
- **性能监控** - 实时监控系统资源使用情况

## 🖥️ 系统要求

### 最低要求
- **操作系统**: Windows 10 (版本 1809) 或更高版本
- **处理器**: x64 架构处理器
- **.NET运行时**: .NET 8.0 Runtime (应用程序自包含，无需单独安装)
- **内存**: 512 MB RAM
- **存储空间**: 100 MB 可用磁盘空间

### 推荐配置
- **操作系统**: Windows 11
- **处理器**: 多核 x64 处理器
- **内存**: 2 GB RAM 或更多
- **存储空间**: 500 MB 可用磁盘空间

## 📦 安装说明

### 方式一：直接运行可执行文件
1. 下载最新版本的可执行文件
2. 双击 `DocxToMarkdownConverter.exe` 即可运行
3. 首次运行时可能需要Windows Defender确认

### 方式二：从源代码构建
1. 确保已安装 .NET 8.0 SDK
2. 克隆或下载项目源代码
3. 在项目根目录执行以下命令：
```bash
dotnet build --configuration Release
dotnet run --project DocxToMarkdownConverter
```

### 可执行文件位置
编译后的可执行文件位于：
```
DocxToMarkdownConverter\bin\Debug\net8.0-windows\win-x64\DocxToMarkdownConverter.exe
```

## 🚀 使用方法

### 基本使用流程
1. **启动应用程序** - 双击运行 DocxToMarkdownConverter.exe
2. **添加文件** - 将DOCX文件拖拽到应用程序窗口，或点击"文件"页面添加
3. **配置选项** - 在"设置"页面配置转换选项
4. **开始转换** - 点击"开始转换"按钮
5. **查看结果** - 在"结果"页面查看转换结果和输出文件

### 详细操作指南

#### 文件管理
- **添加文件**: 支持拖拽DOCX文件到应用程序窗口
- **批量添加**: 可同时选择多个DOCX文件进行批量转换
- **文件预览**: 显示文件大小、修改时间等信息
- **移除文件**: 可单独移除不需要转换的文件

#### 转换设置
- **输出目录**: 设置转换后的Markdown文件保存位置
- **图片目录**: 设置提取的图片文件保存目录
- **图片格式**: 选择图片输出格式（PNG、JPEG等）
- **转换选项**: 
  - ✅ 提取图片 - 自动提取并保存文档中的图片
  - ✅ 转换表格 - 将表格转换为Markdown表格格式
  - ✅ 处理公式 - 识别和转换数学公式
  - ✅ 保留格式 - 保持文本格式（粗体、斜体等）

#### 主题和界面
- **主题切换**: 支持浅色、深色和跟随系统主题
- **语言切换**: 支持中文和英文界面
- **动画效果**: 可开启/关闭界面动画以适应不同性能需求
- **性能设置**: 根据设备性能调整处理参数

## 🏗️ 技术架构

### 核心技术栈
- **框架**: .NET 8.0 + WPF (Windows Presentation Foundation)
- **UI库**: Material Design In XAML Toolkit
- **文档处理**: DocumentFormat.OpenXml
- **依赖注入**: Microsoft.Extensions.DependencyInjection
- **配置管理**: Microsoft.Extensions.Configuration
- **日志记录**: Serilog
- **数据绑定**: MVVM (Model-View-ViewModel) 架构模式

### 项目结构
```
DocxToMarkdownConverter/
├── Behaviors/          # 行为和交互逻辑
├── Commands/           # 命令模式实现
├── Controls/           # 自定义用户控件
├── Converters/         # 数据转换器
├── Exceptions/         # 自定义异常类
├── Models/             # 数据模型
├── Resources/          # 资源文件（图标、字符串等）
├── Services/           # 业务逻辑服务
├── Styles/             # XAML样式和主题
├── ViewModels/         # 视图模型
├── Views/              # 用户界面视图
└── Scripts/            # 构建和部署脚本
```

### 核心服务模块
- **DocxConverter**: 核心转换引擎
- **DocumentProcessor**: 文档解析处理器
- **ImageProcessor**: 图片提取和处理
- **FormulaProcessor**: 数学公式处理器
- **TableProcessor**: 表格转换处理器
- **ThemeManager**: 主题管理服务
- **ConfigurationService**: 配置管理服务
- **LoggingService**: 日志记录服务

## 🤝 贡献指南

我们欢迎社区贡献！如果您想为项目做出贡献，请遵循以下步骤：

1. Fork 本项目
2. 创建您的功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交您的更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 开启一个 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 📞 支持与反馈

如果您在使用过程中遇到问题或有改进建议，请通过以下方式联系我们：

- **问题报告**: 在 GitHub Issues 中提交问题
- **功能建议**: 在 GitHub Discussions 中讨论新功能
- **邮件联系**: <EMAIL>

## 🔄 更新日志

### 版本 3.2.0 (2025-07-24)
- ✨ **新功能**: 重新添加了"添加文件"按钮，提供更直观的文件选择方式
- 🔄 **功能重构**: 完全重写了拖拽功能，使用更简单、更稳定的实现方式
- 🛡️ **稳定性提升**: 新的拖拽实现避免了所有可能导致程序冻结的问题
- ⚡ **性能优化**: 简化了拖拽处理逻辑，提升了响应速度和稳定性
- 🎨 **用户体验**: 改进了文件添加的交互方式，支持按钮点击和拖拽两种方式
- 🔧 **代码优化**: 移除了复杂的拖拽动画和反馈系统，专注于核心功能
- 📱 **界面改进**: 优化了文件管理界面的布局和按钮设计

### 版本 3.1.1 (2025-07-24)
- 🐛 **关键修复**: 彻底解决拖拽文件后程序"未响应"和冻结的问题
- 🔧 **跨线程修复**: 修复了在后台线程中访问UI集合导致的死锁问题
- ⚡ **事件处理优化**: 重构拖拽事件处理，避免async void导致的潜在问题
- 🛡️ **线程安全**: 确保所有UI操作都在正确的线程中执行
- 📊 **性能改进**: 优化了文件处理流程，提升响应速度
- 🔍 **错误处理**: 增强了异常处理和错误恢复机制
- 🧪 **稳定性**: 通过全面测试确保拖拽功能的稳定性

### 版本 3.1.0 (2025-07-24)
- 🐛 **重要修复**: 修复拖拽文件功能导致程序卡死和闪退的严重问题
- ⚡ **性能优化**: 将文件验证和信息获取操作移至后台线程，避免UI线程阻塞
- 🔧 **异步处理**: 重构文件添加逻辑，使用异步方式处理大量文件或大文件
- 🛡️ **错误处理**: 增强错误处理机制，提供更好的用户反馈
- 📊 **用户体验**: 改进拖拽操作的即时反馈，提供更流畅的交互体验
- 🔍 **诊断改进**: 优化文件验证逻辑，减少不必要的文件系统操作
- 📝 **日志增强**: 添加详细的错误日志记录，便于问题诊断

### 版本 3.0.0 (2025-07-24)
- 🚀 重大版本升级
- 📦 优化单文件发布配置
- 🔧 改进性能和稳定性
- 🛡️ 安全更新和漏洞修复
- 📝 完善文档和用户指南
- 🎯 增强用户体验
- ⚡ 启动速度优化
- 🔄 版本管理系统完善

### 版本 1.0.0 (2024-07-24)
- ✨ 首次发布
- 🚀 支持DOCX到Markdown的基本转换功能
- 🎨 现代化Material Design界面
- 📱 支持深色/浅色主题切换
- 🌐 支持中英文双语界面
- 📊 批量文件处理功能
- 🖼️ 图片提取和转换功能
- 📋 表格转换支持
- 🔢 数学公式处理功能
- ⚡ 性能优化和多线程支持

---

**开发团队**: Augment Code  
**版权所有**: © 2024 Augment Code. All rights reserved.
