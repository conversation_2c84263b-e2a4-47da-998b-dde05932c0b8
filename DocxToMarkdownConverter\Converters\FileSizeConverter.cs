using System;
using System.Globalization;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts file size in bytes to human-readable format
/// </summary>
public class FileSizeConverter : IValueConverter
{
    public static readonly FileSizeConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        try
        {
            long bytes = 0;

            if (value is long longValue)
            {
                bytes = longValue;
            }
            else if (value is int intValue)
            {
                bytes = intValue;
            }
            else if (value is double doubleValue)
            {
                bytes = (long)doubleValue;
            }
            else if (value is string stringValue && long.TryParse(stringValue, out var parsedValue))
            {
                bytes = parsedValue;
            }
            else if (value != null)
            {
                // 尝试转换为long
                if (long.TryParse(value.ToString(), out var convertedValue))
                {
                    bytes = convertedValue;
                }
            }

            return FormatFileSize(bytes);
        }
        catch (Exception)
        {
            return "0 B";
        }
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        
        return $"{len:0.##} {sizes[order]}";
    }
}