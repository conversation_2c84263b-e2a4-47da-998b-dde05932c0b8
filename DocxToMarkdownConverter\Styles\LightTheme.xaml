<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <ResourceDictionary.MergedDictionaries>
        <!-- Material Design Light Theme -->
        <materialDesign:BundledTheme BaseTheme="Light" PrimaryColor="DeepPurple" SecondaryColor="Lime" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Light Theme Custom Colors -->
    <SolidColorBrush x:Key="AppBackgroundBrush" Color="#FAFAFA"/>
    <SolidColorBrush x:Key="AppSurfaceBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="AppCardBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="AppTextPrimaryBrush" Color="#212121"/>
    <SolidColorBrush x:Key="AppTextSecondaryBrush" Color="#757575"/>
    <SolidColorBrush x:Key="AppDividerBrush" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="AppHoverBrush" Color="#F5F5F5"/>
    <SolidColorBrush x:Key="AppSelectedBrush" Color="#E8EAF6"/>
    <SolidColorBrush x:Key="AppBorderBrush" Color="#E0E0E0"/>

    <!-- Shadow and elevation colors for light theme -->
    <Color x:Key="MaterialDesignShadow">#000000</Color>
    <SolidColorBrush x:Key="MaterialDesignShadowBrush" Color="{StaticResource MaterialDesignShadow}"/>
    
    <!-- Enhanced Navigation Colors for Better Contrast -->
    <SolidColorBrush x:Key="NavigationBackgroundBrush" Color="#F8F9FA"/>
    <SolidColorBrush x:Key="NavigationHeaderBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="NavigationItemDefaultBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="NavigationItemHoverBrush" Color="#E3F2FD"/>
    <SolidColorBrush x:Key="NavigationItemSelectedBrush" Color="#1976D2"/>
    <SolidColorBrush x:Key="NavigationItemActiveBrush" Color="#1565C0"/>
    <SolidColorBrush x:Key="NavigationTextDefaultBrush" Color="#616161"/>
    <SolidColorBrush x:Key="NavigationTextHoverBrush" Color="#212121"/>
    <SolidColorBrush x:Key="NavigationTextSelectedBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="NavigationIconDefaultBrush" Color="#616161"/>
    <SolidColorBrush x:Key="NavigationIconHoverBrush" Color="#212121"/>
    <SolidColorBrush x:Key="NavigationIconSelectedBrush" Color="#FFFFFF"/>
    
    <!-- Progress Colors -->
    <SolidColorBrush x:Key="ProgressBackgroundBrush" Color="#E0E0E0"/>
    <SolidColorBrush x:Key="ProgressForegroundBrush" Color="#673AB7"/>
    
    <!-- Status Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#4CAF50"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#FF9800"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F44336"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#2196F3"/>
    
    <!-- Shadow Effects -->
    <DropShadowEffect x:Key="CardShadowEffect" Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
    <DropShadowEffect x:Key="ButtonShadowEffect" Color="Black" Opacity="0.2" ShadowDepth="2" BlurRadius="4"/>
    <DropShadowEffect x:Key="ElevatedShadowEffect" Color="Black" Opacity="0.15" ShadowDepth="4" BlurRadius="12"/>

</ResourceDictionary>