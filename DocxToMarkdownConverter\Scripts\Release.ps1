# DOCX to Markdown Converter 发布脚本
# 自动化版本管理和发布流程

param(
    [Parameter(Mandatory=$true)]
    [string]$Version,
    
    [string]$Configuration = "Release",
    [string]$Platform = "x64",
    [switch]$CreateGitTag = $true,
    [switch]$PushToGitHub = $false,
    [switch]$CreateRelease = $false,
    [string]$ReleaseNotes = ""
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始发布流程..." -ForegroundColor Green
Write-Host "版本: $Version" -ForegroundColor Yellow
Write-Host "配置: $Configuration" -ForegroundColor Yellow

# 验证版本格式
if ($Version -notmatch '^\d+\.\d+\.\d+(\.\d+)?$') {
    throw "版本格式无效。请使用格式: x.y.z 或 x.y.z.w"
}

# 获取项目路径
$ProjectRoot = Split-Path -Parent $PSScriptRoot
$ProjectFile = Join-Path $ProjectRoot "DocxToMarkdownConverter.csproj"
$OutputPath = Join-Path $ProjectRoot "Release\v$Version"

# 创建输出目录
if (!(Test-Path $OutputPath)) {
    New-Item -ItemType Directory -Path $OutputPath -Force | Out-Null
}

try {
    # 1. 更新项目文件中的版本信息
    Write-Host "更新版本信息..." -ForegroundColor Cyan
    
    $projectContent = Get-Content $ProjectFile -Raw
    $projectContent = $projectContent -replace '<AssemblyVersion>.*?</AssemblyVersion>', "<AssemblyVersion>$Version.0</AssemblyVersion>"
    $projectContent = $projectContent -replace '<FileVersion>.*?</FileVersion>', "<FileVersion>$Version.0</FileVersion>"
    $projectContent = $projectContent -replace '<InformationalVersion>.*?</InformationalVersion>', "<InformationalVersion>$Version</InformationalVersion>"
    
    Set-Content -Path $ProjectFile -Value $projectContent -Encoding UTF8

    # 2. 构建和测试
    Write-Host "构建项目..." -ForegroundColor Cyan
    dotnet build $ProjectFile --configuration $Configuration
    if ($LASTEXITCODE -ne 0) {
        throw "构建失败"
    }

    # 运行测试
    Write-Host "运行测试..." -ForegroundColor Cyan
    $TestProject = Join-Path $ProjectRoot "Tests\DocxToMarkdownConverter.Tests.csproj"
    if (Test-Path $TestProject) {
        dotnet test $TestProject --configuration $Configuration --verbosity minimal
        if ($LASTEXITCODE -ne 0) {
            throw "测试失败"
        }
    }

    # 3. 发布应用程序
    Write-Host "发布应用程序..." -ForegroundColor Cyan
    $PublishPath = Join-Path $OutputPath "Application"
    
    dotnet publish $ProjectFile `
        --configuration $Configuration `
        --runtime "win-$Platform" `
        --self-contained true `
        --single-file true `
        --output $PublishPath `
        /p:PublishReadyToRun=true `
        /p:PublishTrimmed=false `
        /p:IncludeNativeLibrariesForSelfExtract=true

    if ($LASTEXITCODE -ne 0) {
        throw "发布失败"
    }

    # 4. 创建发布包
    Write-Host "创建发布包..." -ForegroundColor Cyan
    
    # 便携版
    $PortableZip = Join-Path $OutputPath "DocxConverter_Portable_v$Version.zip"
    Compress-Archive -Path "$PublishPath\*" -DestinationPath $PortableZip -Force
    
    # 复制文档
    $DocsPath = Join-Path $ProjectRoot "Documentation"
    if (Test-Path $DocsPath) {
        Copy-Item -Path $DocsPath -Destination (Join-Path $OutputPath "Documentation") -Recurse -Force
    }

    # 5. 生成变更日志
    Write-Host "生成变更日志..." -ForegroundColor Cyan
    $ChangelogPath = Join-Path $OutputPath "CHANGELOG.md"
    $ChangelogContent = @"
# 版本 $Version 发布说明

发布日期: $(Get-Date -Format 'yyyy-MM-dd')

## 新功能和改进

$ReleaseNotes

## 技术信息

- 构建配置: $Configuration
- 目标平台: Windows $Platform
- .NET 版本: 8.0
- 发布类型: 单文件自包含

## 下载

- [便携版 (ZIP)](DocxConverter_Portable_v$Version.zip)
- [安装包 (MSI)](DocxConverter_Setup_v$Version.msi) - 如果可用

## 系统要求

- Windows 10 或更高版本
- 至少 4GB RAM
- 100MB 可用磁盘空间

---

完整的用户指南请参考 Documentation 文件夹中的文档。
"@
    
    Set-Content -Path $ChangelogPath -Value $ChangelogContent -Encoding UTF8

    # 6. 创建版本信息文件
    $VersionInfoPath = Join-Path $OutputPath "version.json"
    $VersionInfo = @{
        version = $Version
        buildDate = (Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
        configuration = $Configuration
        platform = "win-$Platform"
        commitHash = ""
        branch = ""
    } | ConvertTo-Json -Depth 2

    # 尝试获取 Git 信息
    try {
        $GitCommit = git rev-parse HEAD 2>$null
        $GitBranch = git rev-parse --abbrev-ref HEAD 2>$null
        if ($GitCommit -and $GitBranch) {
            $VersionInfoObj = $VersionInfo | ConvertFrom-Json
            $VersionInfoObj.commitHash = $GitCommit.Substring(0, 8)
            $VersionInfoObj.branch = $GitBranch
            $VersionInfo = $VersionInfoObj | ConvertTo-Json -Depth 2
        }
    } catch {
        Write-Host "无法获取 Git 信息" -ForegroundColor Yellow
    }

    Set-Content -Path $VersionInfoPath -Value $VersionInfo -Encoding UTF8

    # 7. Git 操作
    if ($CreateGitTag) {
        Write-Host "创建 Git 标签..." -ForegroundColor Cyan
        try {
            git add .
            git commit -m "Release version $Version"
            git tag -a "v$Version" -m "Release version $Version"
            
            if ($PushToGitHub) {
                Write-Host "推送到 GitHub..." -ForegroundColor Cyan
                git push origin main
                git push origin "v$Version"
            }
        } catch {
            Write-Host "Git 操作失败: $($_.Exception.Message)" -ForegroundColor Yellow
        }
    }

    # 8. 创建 GitHub Release（如果启用）
    if ($CreateRelease -and $PushToGitHub) {
        Write-Host "创建 GitHub Release..." -ForegroundColor Cyan
        try {
            # 这里需要 GitHub CLI (gh) 工具
            $ReleaseTitle = "DOCX to Markdown Converter v$Version"
            $ReleaseBody = $ReleaseNotes
            
            gh release create "v$Version" `
                --title $ReleaseTitle `
                --notes $ReleaseBody `
                $PortableZip
                
            Write-Host "GitHub Release 已创建" -ForegroundColor Green
        } catch {
            Write-Host "创建 GitHub Release 失败: $($_.Exception.Message)" -ForegroundColor Yellow
            Write-Host "请手动创建 Release 或安装 GitHub CLI" -ForegroundColor Yellow
        }
    }

    # 9. 生成发布报告
    Write-Host "生成发布报告..." -ForegroundColor Cyan
    $ReportPath = Join-Path $OutputPath "ReleaseReport.txt"
    $ReportContent = @"
DOCX to Markdown Converter 发布报告
=====================================

版本: $Version
发布时间: $(Get-Date)
配置: $Configuration
平台: Windows $Platform

发布文件:
"@

    $ReleaseFiles = Get-ChildItem -Path $OutputPath -File
    foreach ($File in $ReleaseFiles) {
        $Size = [math]::Round($File.Length / 1MB, 2)
        $ReportContent += "`n- $($File.Name) ($Size MB)"
    }

    $ReportContent += @"

`n
发布检查清单:
- [x] 版本号已更新
- [x] 项目构建成功
- [x] 测试通过
- [x] 应用程序发布完成
- [x] 便携版创建完成
- [x] 文档已包含
- [x] 变更日志已生成
"@

    if ($CreateGitTag) {
        $ReportContent += "`n- [x] Git 标签已创建"
    }

    if ($PushToGitHub) {
        $ReportContent += "`n- [x] 代码已推送到 GitHub"
    }

    if ($CreateRelease) {
        $ReportContent += "`n- [x] GitHub Release 已创建"
    }

    Set-Content -Path $ReportPath -Value $ReportContent -Encoding UTF8

    Write-Host "`n发布完成！" -ForegroundColor Green
    Write-Host "发布文件位置: $OutputPath" -ForegroundColor Green
    Write-Host "便携版: $PortableZip" -ForegroundColor Green
    
    # 打开输出目录
    Start-Process -FilePath $OutputPath

} catch {
    Write-Host "发布失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "`n发布流程完成！" -ForegroundColor Green
