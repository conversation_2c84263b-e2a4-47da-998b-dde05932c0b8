using System.Collections.Concurrent;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 时间估算服务，用于预测转换剩余时间
/// </summary>
public class TimeEstimationService
{
    private readonly ConcurrentQueue<TimeEstimationSample> _samples;
    private readonly int _maxSamples;
    private readonly object _lockObject = new object();

    public TimeEstimationService(int maxSamples = 50)
    {
        _maxSamples = maxSamples;
        _samples = new ConcurrentQueue<TimeEstimationSample>();
    }

    /// <summary>
    /// 添加时间样本
    /// </summary>
    public void AddSample(long fileSize, TimeSpan processingTime)
    {
        var sample = new TimeEstimationSample
        {
            FileSize = fileSize,
            ProcessingTime = processingTime,
            Timestamp = DateTime.Now
        };

        _samples.Enqueue(sample);

        // 保持样本数量在限制范围内
        while (_samples.Count > _maxSamples)
        {
            _samples.TryDequeue(out _);
        }
    }

    /// <summary>
    /// 估算处理指定大小文件所需的时间
    /// </summary>
    public TimeSpan? EstimateProcessingTime(long fileSize)
    {
        lock (_lockObject)
        {
            var samples = _samples.ToArray();
            if (samples.Length < 3) // 需要至少3个样本才能进行估算
                return null;

            // 使用最近的样本，并按时间加权
            var now = DateTime.Now;
            var weightedSamples = samples
                .Where(s => s.ProcessingTime.TotalMilliseconds > 0) // 排除无效样本
                .Select(s => new
                {
                    Sample = s,
                    Weight = Math.Exp(-(now - s.Timestamp).TotalMinutes / 10.0), // 10分钟半衰期
                    Rate = s.ProcessingTime.TotalMilliseconds / Math.Max(s.FileSize, 1) // 每字节处理时间
                })
                .Where(w => w.Weight > 0.1) // 排除过旧的样本
                .ToArray();

            if (weightedSamples.Length == 0)
                return null;

            // 计算加权平均处理速率
            var totalWeight = weightedSamples.Sum(w => w.Weight);
            var averageRate = weightedSamples.Sum(w => w.Rate * w.Weight) / totalWeight;

            // 估算时间
            var estimatedMs = averageRate * fileSize;
            return TimeSpan.FromMilliseconds(Math.Max(estimatedMs, 100)); // 最少100ms
        }
    }

    /// <summary>
    /// 估算批量处理剩余时间
    /// </summary>
    public TimeSpan? EstimateBatchRemainingTime(IEnumerable<long> remainingFileSizes)
    {
        var fileSizes = remainingFileSizes.ToArray();
        if (fileSizes.Length == 0)
            return TimeSpan.Zero;

        var totalEstimatedMs = 0.0;
        var estimatedCount = 0;

        foreach (var fileSize in fileSizes)
        {
            var estimated = EstimateProcessingTime(fileSize);
            if (estimated.HasValue)
            {
                totalEstimatedMs += estimated.Value.TotalMilliseconds;
                estimatedCount++;
            }
        }

        if (estimatedCount == 0)
            return null;

        // 如果只有部分文件有估算，按比例调整
        if (estimatedCount < fileSizes.Length)
        {
            var scaleFactor = (double)fileSizes.Length / estimatedCount;
            totalEstimatedMs *= scaleFactor;
        }

        return TimeSpan.FromMilliseconds(totalEstimatedMs);
    }

    /// <summary>
    /// 获取当前处理速率统计
    /// </summary>
    public ProcessingRateStatistics GetRateStatistics()
    {
        lock (_lockObject)
        {
            var samples = _samples.ToArray();
            if (samples.Length == 0)
                return new ProcessingRateStatistics();

            var rates = samples
                .Where(s => s.ProcessingTime.TotalMilliseconds > 0 && s.FileSize > 0)
                .Select(s => s.FileSize / s.ProcessingTime.TotalSeconds) // 字节/秒
                .ToArray();

            if (rates.Length == 0)
                return new ProcessingRateStatistics();

            return new ProcessingRateStatistics
            {
                AverageRate = rates.Average(),
                MinRate = rates.Min(),
                MaxRate = rates.Max(),
                SampleCount = rates.Length,
                LastSampleTime = samples.Max(s => s.Timestamp)
            };
        }
    }

    /// <summary>
    /// 清除所有样本
    /// </summary>
    public void ClearSamples()
    {
        lock (_lockObject)
        {
            while (_samples.TryDequeue(out _)) { }
        }
    }

    /// <summary>
    /// 时间估算样本
    /// </summary>
    private class TimeEstimationSample
    {
        public long FileSize { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public DateTime Timestamp { get; set; }
    }
}

/// <summary>
/// 处理速率统计信息
/// </summary>
public class ProcessingRateStatistics
{
    public double AverageRate { get; set; } // 字节/秒
    public double MinRate { get; set; }
    public double MaxRate { get; set; }
    public int SampleCount { get; set; }
    public DateTime LastSampleTime { get; set; }

    public string GetFormattedAverageRate()
    {
        if (AverageRate < 1024)
            return $"{AverageRate:F0} B/s";
        else if (AverageRate < 1024 * 1024)
            return $"{AverageRate / 1024:F1} KB/s";
        else
            return $"{AverageRate / (1024 * 1024):F1} MB/s";
    }
}