﻿#pragma checksum "..\..\..\..\..\Views\AnimationDemoWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "59535AA829D79F370C589DAA7152BF7C2AB0E9D2"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using DocxToMarkdownConverter.Behaviors;
using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocxToMarkdownConverter.Views {
    
    
    /// <summary>
    /// AnimationDemoWindow
    /// </summary>
    public partial class AnimationDemoWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 68 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.ProgressBar DemoProgressBar;
        
        #line default
        #line hidden
        
        
        #line 105 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal MaterialDesignThemes.Wpf.PackIcon AnimationTarget;
        
        #line default
        #line hidden
        
        
        #line 138 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border Page1;
        
        #line default
        #line hidden
        
        
        #line 148 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Border Page2;
        
        #line default
        #line hidden
        
        
        #line 171 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.CheckBox EnableAnimationsCheckBox;
        
        #line default
        #line hidden
        
        
        #line 179 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Slider AnimationSpeedSlider;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocxToMarkdownConverter-v3.2.0;V3.2.0.0;component/views/animationdemowindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal System.Delegate _CreateDelegate(System.Type delegateType, string handler) {
            return System.Delegate.CreateDelegate(delegateType, this, handler);
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DemoProgressBar = ((System.Windows.Controls.ProgressBar)(target));
            return;
            case 2:
            
            #line 75 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetProgress25_Click);
            
            #line default
            #line hidden
            return;
            case 3:
            
            #line 76 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetProgress50_Click);
            
            #line default
            #line hidden
            return;
            case 4:
            
            #line 77 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetProgress75_Click);
            
            #line default
            #line hidden
            return;
            case 5:
            
            #line 78 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SetProgress100_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            
            #line 79 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.ResetProgress_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.AnimationTarget = ((MaterialDesignThemes.Wpf.PackIcon)(target));
            return;
            case 8:
            
            #line 118 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayPulse_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            
            #line 119 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayShake_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            
            #line 120 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayScaleUp_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            
            #line 121 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayScaleDown_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            
            #line 122 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayRotate_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            
            #line 123 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayFadeOut_Click);
            
            #line default
            #line hidden
            return;
            case 14:
            
            #line 124 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.PlayFadeIn_Click);
            
            #line default
            #line hidden
            return;
            case 15:
            this.Page1 = ((System.Windows.Controls.Border)(target));
            return;
            case 16:
            this.Page2 = ((System.Windows.Controls.Border)(target));
            return;
            case 17:
            
            #line 160 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            ((System.Windows.Controls.Button)(target)).Click += new System.Windows.RoutedEventHandler(this.SwitchPages_Click);
            
            #line default
            #line hidden
            return;
            case 18:
            this.EnableAnimationsCheckBox = ((System.Windows.Controls.CheckBox)(target));
            
            #line 174 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            this.EnableAnimationsCheckBox.Checked += new System.Windows.RoutedEventHandler(this.EnableAnimations_Changed);
            
            #line default
            #line hidden
            
            #line 175 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            this.EnableAnimationsCheckBox.Unchecked += new System.Windows.RoutedEventHandler(this.EnableAnimations_Changed);
            
            #line default
            #line hidden
            return;
            case 19:
            this.AnimationSpeedSlider = ((System.Windows.Controls.Slider)(target));
            
            #line 182 "..\..\..\..\..\Views\AnimationDemoWindow.xaml"
            this.AnimationSpeedSlider.ValueChanged += new System.Windows.RoutedPropertyChangedEventHandler<double>(this.AnimationSpeed_Changed);
            
            #line default
            #line hidden
            return;
            }
            this._contentLoaded = true;
        }
    }
}

