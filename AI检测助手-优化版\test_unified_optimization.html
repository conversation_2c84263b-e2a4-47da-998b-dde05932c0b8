<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>统一学术智能优化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        textarea {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            line-height: 1.5;
        }
        .mode-selector {
            display: flex;
            gap: 15px;
            margin: 15px 0;
        }
        .mode-option {
            padding: 10px 15px;
            border: 2px solid #ddd;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
        }
        .mode-option.selected {
            border-color: #007bff;
            background-color: #e7f3ff;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #0056b3;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .metrics {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 15px 0;
        }
        .metric-card {
            background: white;
            border: 1px solid #ddd;
            border-radius: 4px;
            padding: 10px;
            text-align: center;
        }
        .metric-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }
        .metric-label {
            font-size: 0.9em;
            color: #666;
        }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <div class="container">
        <h1>统一学术智能优化测试</h1>
        <p>测试整合后的学术智能优化功能：学术规范化 → 表达优化 → AI特征消除</p>
        
        <div class="form-group">
            <label for="testText">测试文本：</label>
            <textarea id="testText" placeholder="请输入要测试的学术文本...">人工智能技术的发展为各行各业带来了革命性的变化。首先，在医疗领域，AI技术能够协助医生进行诊断。其次，在教育领域，智能化的教学系统可以根据学生的学习情况提供个性化的学习方案。最后，在交通领域，自动驾驶技术的应用将大大提高道路安全性。总之，人工智能技术的广泛应用将为人类社会带来更多便利和发展机遇。</textarea>
        </div>

        <div class="form-group">
            <label>优化模式：</label>
            <div class="mode-selector">
                <div class="mode-option selected" data-mode="conservative">
                    <strong>保守模式</strong><br>
                    <small>保持原文风格</small>
                </div>
                <div class="mode-option" data-mode="balanced">
                    <strong>平衡模式</strong><br>
                    <small>推荐选择</small>
                </div>
                <div class="mode-option" data-mode="aggressive">
                    <strong>激进模式</strong><br>
                    <small>最大化优化</small>
                </div>
            </div>
        </div>

        <div>
            <button onclick="testUnifiedOptimization()">开始统一优化测试</button>
            <button onclick="testStageByStage()">分阶段测试</button>
            <button onclick="clearResults()">清空结果</button>
        </div>

        <div id="results"></div>
        <div id="log" class="log"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <script src="js/ai_detector.js"></script>
    <script src="js/academic_optimizer.js"></script>
    <script src="js/unified_academic_optimizer.js"></script>
    <script src="js/zhuque_optimizer.js"></script>
    <script src="js/prompt_templates.js"></script>
    <script src="js/ollama_manager_v2.js"></script>

    <script>
        let selectedMode = 'conservative';

        function log(message, type = 'info') {
            const logDiv = document.getElementById('log');
            const timestamp = new Date().toLocaleTimeString();
            logDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            logDiv.scrollTop = logDiv.scrollHeight;
            console.log(`[${timestamp}] ${message}`);
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '';
            document.getElementById('log').innerHTML = '';
        }

        // 模式选择
        document.querySelectorAll('.mode-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.mode-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
                selectedMode = this.dataset.mode;
                log(`选择优化模式: ${selectedMode}`, 'info');
            });
        });

        async function testUnifiedOptimization() {
            const text = document.getElementById('testText').value.trim();
            if (!text) {
                alert('请输入测试文本！');
                return;
            }

            log('开始统一学术智能优化测试...', 'info');
            
            try {
                const options = {
                    enableLLM: false, // 测试时不使用LLM
                    optimizationLevel: selectedMode,
                    preserveSemantics: true,
                    maintainAcademicRigor: true,
                    targetAIScore: selectedMode === 'aggressive' ? 20 : selectedMode === 'balanced' ? 30 : 40
                };

                log(`优化配置: ${JSON.stringify(options)}`, 'info');
                
                const result = await unifiedAcademicOptimizer.optimize(text, options);
                
                if (result.success) {
                    log('✅ 优化成功完成', 'success');
                    displayResults(result, text);
                } else {
                    log('❌ 优化失败: ' + result.error, 'error');
                }
                
            } catch (error) {
                log('❌ 测试出错: ' + error.message, 'error');
                console.error('详细错误:', error);
            }
        }

        function displayResults(result, originalText) {
            const resultsDiv = document.getElementById('results');
            
            const metrics = result.qualityMetrics || {};
            const improvements = result.overallImprovements || [];
            
            resultsDiv.innerHTML = `
                <div class="container">
                    <h3>优化结果</h3>
                    
                    <div class="metrics">
                        <div class="metric-card">
                            <div class="metric-value">${(metrics.academicQuality * 100).toFixed(0)}%</div>
                            <div class="metric-label">学术质量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${(metrics.expressionQuality * 100).toFixed(0)}%</div>
                            <div class="metric-label">表达质量</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${(metrics.aiReductionQuality * 100).toFixed(0)}%</div>
                            <div class="metric-label">AI消除度</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-value">${metrics.lengthChange}%</div>
                            <div class="metric-label">长度变化</div>
                        </div>
                    </div>
                    
                    <h4>原始文本：</h4>
                    <div class="result">${originalText}</div>
                    
                    <h4>优化后文本：</h4>
                    <div class="result">${result.optimizedText}</div>
                    
                    <h4>应用的优化技术：</h4>
                    <ul>
                        ${improvements.map(imp => `<li>${imp}</li>`).join('')}
                    </ul>
                    
                    <h4>处理时间：</h4>
                    <p>${result.processingTime || 0}ms</p>
                </div>
            `;
        }

        async function testStageByStage() {
            const text = document.getElementById('testText').value.trim();
            if (!text) {
                alert('请输入测试文本！');
                return;
            }

            log('开始分阶段测试...', 'info');
            
            try {
                // 测试第一阶段：学术规范化
                log('第一阶段：学术规范化', 'info');
                const stage1 = await unifiedAcademicOptimizer.academicNormalization(text);
                log(`学术规范化完成，质量评分: ${(stage1.qualityScore * 100).toFixed(1)}%`, 'success');
                
                // 测试第二阶段：表达优化
                log('第二阶段：表达优化', 'info');
                const stage2 = await unifiedAcademicOptimizer.expressionOptimization(stage1.optimizedText, {
                    enableLLM: false,
                    optimizationLevel: selectedMode
                });
                log(`表达优化完成，质量评分: ${(stage2.qualityScore * 100).toFixed(1)}%`, 'success');
                
                // 测试第三阶段：AI特征消除
                log('第三阶段：AI特征消除', 'info');
                const stage3 = await unifiedAcademicOptimizer.aiFeatureReduction(stage2.optimizedText, {
                    targetScore: 30,
                    optimizationLevel: selectedMode
                });
                log(`AI特征消除完成，质量评分: ${(stage3.qualityScore * 100).toFixed(1)}%`, 'success');
                
                log('✅ 分阶段测试完成', 'success');
                
            } catch (error) {
                log('❌ 分阶段测试出错: ' + error.message, 'error');
                console.error('详细错误:', error);
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('统一学术智能优化测试页面加载完成', 'info');
            
            // 检查统一优化器是否加载
            if (typeof unifiedAcademicOptimizer !== 'undefined') {
                log('✅ 统一学术优化器已加载', 'success');
            } else {
                log('❌ 统一学术优化器未加载', 'error');
            }
        });
    </script>
</body>
</html>
