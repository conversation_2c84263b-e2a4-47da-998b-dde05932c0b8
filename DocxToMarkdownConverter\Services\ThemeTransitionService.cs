using System.Windows;
using System.Windows.Media.Animation;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.Services;

public interface IThemeTransitionService
{
    Task PlayThemeTransitionAsync(FrameworkElement element, TimeSpan duration);
    Task PlayGlobalThemeTransitionAsync();
    void SetTransitionEnabled(bool enabled);
}

public class ThemeTransitionService : IThemeTransitionService
{
    private readonly ILogger<ThemeTransitionService> _logger;
    private bool _transitionsEnabled = true;

    public ThemeTransitionService(ILogger<ThemeTransitionService> logger)
    {
        _logger = logger;
    }

    public void SetTransitionEnabled(bool enabled)
    {
        _transitionsEnabled = enabled;
        _logger.LogInformation("Theme transitions {Status}", enabled ? "enabled" : "disabled");
    }

    public async Task PlayThemeTransitionAsync(FrameworkElement element, TimeSpan duration)
    {
        if (!_transitionsEnabled || element == null) return;

        var tcs = new TaskCompletionSource<bool>();

        try
        {
            // Create a subtle fade animation for theme transitions
            var fadeAnimation = new DoubleAnimation
            {
                From = element.Opacity,
                To = 0.7,
                Duration = new Duration(TimeSpan.FromMilliseconds(duration.TotalMilliseconds * 0.4)),
                AutoReverse = true,
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
            };

            fadeAnimation.Completed += (s, e) => tcs.SetResult(true);

            element.BeginAnimation(UIElement.OpacityProperty, fadeAnimation);
            await tcs.Task;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to play theme transition animation");
            tcs.SetResult(false);
        }
    }

    public async Task PlayGlobalThemeTransitionAsync()
    {
        if (!_transitionsEnabled) return;

        var mainWindow = System.Windows.Application.Current.MainWindow;
        if (mainWindow == null) return;

        try
        {
            // Create a smooth global transition effect
            var storyboard = new Storyboard();

            // Main window fade
            var windowFade = new DoubleAnimation
            {
                From = 1.0,
                To = 0.85,
                Duration = TimeSpan.FromMilliseconds(200),
                AutoReverse = true,
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(windowFade, mainWindow);
            Storyboard.SetTargetProperty(windowFade, new PropertyPath(UIElement.OpacityProperty));
            storyboard.Children.Add(windowFade);

            // Scale animation for subtle zoom effect
            var scaleTransform = new System.Windows.Media.ScaleTransform(1.0, 1.0);
            mainWindow.RenderTransform = scaleTransform;
            mainWindow.RenderTransformOrigin = new System.Windows.Point(0.5, 0.5);

            var scaleAnimation = new DoubleAnimation
            {
                From = 1.0,
                To = 0.98,
                Duration = TimeSpan.FromMilliseconds(200),
                AutoReverse = true,
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(scaleAnimation, scaleTransform);
            Storyboard.SetTargetProperty(scaleAnimation, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleXProperty));
            storyboard.Children.Add(scaleAnimation);

            var scaleAnimationY = new DoubleAnimation
            {
                From = 1.0,
                To = 0.98,
                Duration = TimeSpan.FromMilliseconds(200),
                AutoReverse = true,
                EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
            };

            Storyboard.SetTarget(scaleAnimationY, scaleTransform);
            Storyboard.SetTargetProperty(scaleAnimationY, new PropertyPath(System.Windows.Media.ScaleTransform.ScaleYProperty));
            storyboard.Children.Add(scaleAnimationY);

            var tcs = new TaskCompletionSource<bool>();
            storyboard.Completed += (s, e) => tcs.SetResult(true);

            storyboard.Begin();
            await tcs.Task;

            _logger.LogDebug("Global theme transition completed");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to play global theme transition");
        }
    }
}