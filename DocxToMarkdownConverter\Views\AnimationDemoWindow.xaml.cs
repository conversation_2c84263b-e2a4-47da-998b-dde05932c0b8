using System.Windows;
using System.Windows.Media;
using DocxToMarkdownConverter.Services;

namespace DocxToMarkdownConverter.Views;

/// <summary>
/// Animation demo window for testing animation system
/// </summary>
public partial class AnimationDemoWindow : Window
{
    private readonly IAnimationManager _animationManager;
    private bool _isPage1Visible = true;

    public AnimationDemoWindow()
    {
        InitializeComponent();
        _animationManager = new AnimationManager();
    }

    public AnimationDemoWindow(IAnimationManager animationManager)
    {
        InitializeComponent();
        _animationManager = animationManager;
    }

    #region Progress Bar Events

    private async void SetProgress25_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayProgressAnimationAsync(DemoProgressBar, 25);
    }

    private async void SetProgress50_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayProgressAnimationAsync(DemoProgressBar, 50);
    }

    private async void SetProgress75_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayProgressAnimationAsync(DemoProgressBar, 75);
    }

    private async void SetProgress100_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayProgressAnimationAsync(DemoProgressBar, 100);
    }

    private async void ResetProgress_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayProgressAnimationAsync(DemoProgressBar, 0);
    }

    #endregion

    #region Element Animation Events

    private async void PlayPulse_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayPulseAnimationAsync(AnimationTarget);
    }

    private async void PlayShake_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayShakeAnimationAsync(AnimationTarget);
    }

    private async void PlayScaleUp_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayScaleAnimationAsync(AnimationTarget, 1.0, 1.5);
    }

    private async void PlayScaleDown_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayScaleAnimationAsync(AnimationTarget, 1.5, 1.0);
    }

    private async void PlayRotate_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayRotateAnimationAsync(AnimationTarget, 0, 360);
    }

    private async void PlayFadeOut_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayFadeAnimationAsync(AnimationTarget, 1.0, 0.3);
    }

    private async void PlayFadeIn_Click(object sender, RoutedEventArgs e)
    {
        await _animationManager.PlayFadeAnimationAsync(AnimationTarget, 0.3, 1.0);
    }

    #endregion

    #region Page Transition Events

    private async void SwitchPages_Click(object sender, RoutedEventArgs e)
    {
        if (_isPage1Visible)
        {
            // Switch from Page1 to Page2
            await _animationManager.PlayPageTransitionAsync(Page1, Page2);
            Page1.Visibility = Visibility.Collapsed;
            Page2.Visibility = Visibility.Visible;
        }
        else
        {
            // Switch from Page2 to Page1
            await _animationManager.PlayPageTransitionAsync(Page2, Page1);
            Page2.Visibility = Visibility.Collapsed;
            Page1.Visibility = Visibility.Visible;
        }
        
        _isPage1Visible = !_isPage1Visible;
    }

    #endregion

    #region Animation Settings Events

    private void EnableAnimations_Changed(object sender, RoutedEventArgs e)
    {
        if (EnableAnimationsCheckBox != null)
        {
            _animationManager.EnableAnimations(EnableAnimationsCheckBox.IsChecked == true);
        }
    }

    private void AnimationSpeed_Changed(object sender, RoutedPropertyChangedEventArgs<double> e)
    {
        if (AnimationSpeedSlider != null)
        {
            _animationManager.SetAnimationSpeed(AnimationSpeedSlider.Value);
        }
    }

    #endregion

    #region Window Events

    private async void Window_Loaded(object sender, RoutedEventArgs e)
    {
        // Play entrance animation for the entire window
        await _animationManager.PlayEntranceAnimationAsync(this);
    }

    #endregion
}