using DocxToMarkdownConverter.Models;
using DocumentFormat.OpenXml.Wordprocessing;
using System.Text;

namespace DocxToMarkdownConverter.Services;

public class TextProcessor : ITextProcessor
{
    public string ProcessParagraph(object paragraph, ConversionOptions options)
    {
        if (paragraph is not Paragraph para)
            return string.Empty;

        var textBuilder = new StringBuilder();

        // Check if this is a heading
        var headingLevel = GetHeadingLevel(para);
        if (headingLevel > 0)
        {
            return ProcessHeading(para, headingLevel, options);
        }

        // Check if this is a list item
        var listInfo = GetListInfo(para);
        if (listInfo.IsListItem)
        {
            return ProcessListItem(para, listInfo, options);
        }

        // Check for special paragraph styles
        var paragraphStyle = GetParagraphStyle(para);

        // Process all elements in the paragraph (runs, hyperlinks, etc.)
        foreach (var element in para.Elements())
        {
            if (element is Run run)
            {
                var runText = ProcessRun(run, options);
                textBuilder.Append(runText);
            }
            else if (element is Hyperlink hyperlink)
            {
                var hyperlinkText = ProcessHyperlink(hyperlink, options);
                textBuilder.Append(hyperlinkText);
            }
            else if (element is BookmarkStart bookmarkStart)
            {
                var bookmarkText = ProcessBookmark(bookmarkStart);
                textBuilder.Append(bookmarkText);
            }
        }

        var result = textBuilder.ToString().Trim();

        // Return empty string for empty paragraphs
        if (string.IsNullOrEmpty(result))
            return string.Empty;

        // Apply paragraph-level formatting
        result = ApplyParagraphFormatting(result, paragraphStyle, options);

        return result;
    }

    public string ProcessRun(object run, ConversionOptions options)
    {
        if (run is not Run runElement)
            return string.Empty;

        var textBuilder = new StringBuilder();
        var formatting = GetRunFormatting(runElement);

        // Process text elements in the run
        foreach (var element in runElement.Elements())
        {
            if (element is Text textElement)
            {
                var text = textElement.Text;

                if (options.PreserveFormatting)
                {
                    text = ApplyMarkdownFormatting(text, formatting);
                }

                textBuilder.Append(text);
            }
            else if (element is Break)
            {
                textBuilder.AppendLine();
            }
            else if (element is TabChar)
            {
                textBuilder.Append("\t");
            }
            else if (element is FootnoteReference footnoteRef)
            {
                // Process footnote reference
                var footnoteText = ProcessFootnoteReference(footnoteRef);
                textBuilder.Append(footnoteText);
            }
            else if (element is EndnoteReference endnoteRef)
            {
                // Process endnote reference
                var endnoteText = ProcessEndnoteReference(endnoteRef);
                textBuilder.Append(endnoteText);
            }
        }

        return textBuilder.ToString();
    }

    public string ProcessHeading(object paragraph, int level, ConversionOptions options)
    {
        if (paragraph is not Paragraph para)
            return string.Empty;

        var textBuilder = new StringBuilder();

        // Process all runs to get the heading text with formatting
        foreach (var run in para.Elements<Run>())
        {
            var runText = ProcessRun(run, options);
            textBuilder.Append(runText);
        }

        var headingText = textBuilder.ToString().Trim();
        if (string.IsNullOrEmpty(headingText))
            return string.Empty;

        // Generate markdown heading with proper spacing
        var headingPrefix = new string('#', Math.Min(level, 6));
        return $"{headingPrefix} {headingText}\n";
    }

    private int GetHeadingLevel(Paragraph paragraph)
    {
        // Check paragraph style for heading
        var paragraphProperties = paragraph.ParagraphProperties;
        if (paragraphProperties?.ParagraphStyleId?.Val?.Value != null)
        {
            var styleId = paragraphProperties.ParagraphStyleId.Val.Value;
            
            // Common heading style patterns
            if (styleId.StartsWith("Heading", StringComparison.OrdinalIgnoreCase))
            {
                // Extract number from "Heading1", "Heading2", etc.
                var numberPart = styleId.Substring(7);
                if (int.TryParse(numberPart, out var level) && level >= 1 && level <= 6)
                {
                    return level;
                }
            }
            else if (styleId.StartsWith("Title", StringComparison.OrdinalIgnoreCase))
            {
                return 1;
            }
            else if (styleId.StartsWith("Subtitle", StringComparison.OrdinalIgnoreCase))
            {
                return 2;
            }
        }

        return 0; // Not a heading
    }

    private RunFormatting GetRunFormatting(Run run)
    {
        var formatting = new RunFormatting();
        
        if (run.RunProperties != null)
        {
            formatting.IsBold = run.RunProperties.Bold != null;
            formatting.IsItalic = run.RunProperties.Italic != null;
            formatting.IsUnderline = run.RunProperties.Underline != null;
            formatting.IsStrikethrough = run.RunProperties.Strike != null;
        }
        
        return formatting;
    }

    private ParagraphStyle GetParagraphStyle(Paragraph paragraph)
    {
        var style = new ParagraphStyle();

        var paragraphProperties = paragraph.ParagraphProperties;
        if (paragraphProperties != null)
        {
            // Check for center alignment
            if (paragraphProperties.Justification?.Val?.Value == JustificationValues.Center)
                style.IsCenter = true;

            // Check for right alignment
            if (paragraphProperties.Justification?.Val?.Value == JustificationValues.Right)
                style.IsRight = true;

            // Check for quote style
            var styleId = paragraphProperties.ParagraphStyleId?.Val?.Value;
            if (!string.IsNullOrEmpty(styleId))
            {
                if (styleId.Contains("Quote", StringComparison.OrdinalIgnoreCase) ||
                    styleId.Contains("BlockQuote", StringComparison.OrdinalIgnoreCase))
                {
                    style.IsQuote = true;
                }

                if (styleId.Contains("Code", StringComparison.OrdinalIgnoreCase))
                {
                    style.IsCode = true;
                }
            }

            // Check for indentation (potential quote)
            if (paragraphProperties.Indentation?.Left?.Value != null &&
                int.TryParse(paragraphProperties.Indentation.Left.Value, out var leftIndent) &&
                leftIndent > 720) // 720 twips = 0.5 inch
            {
                style.IsQuote = true;
            }
        }

        return style;
    }

    private string ApplyParagraphFormatting(string text, ParagraphStyle style, ConversionOptions options)
    {
        if (!options.PreserveFormatting)
            return text;

        if (style.IsCode)
        {
            return $"```\n{text}\n```";
        }

        if (style.IsQuote)
        {
            var lines = text.Split('\n');
            var quotedLines = lines.Select(line => $"> {line}");
            return string.Join("\n", quotedLines);
        }

        if (style.IsCenter)
        {
            // Use HTML for center alignment as Markdown doesn't have native support
            return $"<div align=\"center\">{text}</div>";
        }

        if (style.IsRight)
        {
            // Use HTML for right alignment as Markdown doesn't have native support
            return $"<div align=\"right\">{text}</div>";
        }

        return text;
    }

    private string ApplyMarkdownFormatting(string text, RunFormatting formatting)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        // Normalize Chinese text first
        text = NormalizeChineseText(text);

        // Apply strikethrough first (outermost)
        if (formatting.IsStrikethrough)
            text = $"~~{text}~~";

        // Apply underline (HTML fallback since Markdown doesn't have native underline)
        if (formatting.IsUnderline)
            text = $"<u>{text}</u>";

        // Apply bold and italic with Chinese-aware spacing
        if (formatting.IsBold && formatting.IsItalic)
            text = ApplyFormattingWithChineseSpacing(text, "***", "***");
        else if (formatting.IsBold)
            text = ApplyFormattingWithChineseSpacing(text, "**", "**");
        else if (formatting.IsItalic)
            text = ApplyFormattingWithChineseSpacing(text, "*", "*");

        return text;
    }

    private string NormalizeChineseText(string text)
    {
        if (string.IsNullOrEmpty(text))
            return text;

        // For now, just return the text as-is to avoid character encoding issues
        // This can be enhanced later with proper Chinese text normalization
        return text;
    }

    private string ApplyFormattingWithChineseSpacing(string text, string startMarker, string endMarker)
    {
        // For Chinese text, we need to be careful about spacing around formatting markers
        // Check if the text contains Chinese characters
        bool containsChinese = ContainsChinese(text);

        if (containsChinese)
        {
            // For Chinese text, apply formatting without extra spaces
            return $"{startMarker}{text}{endMarker}";
        }
        else
        {
            // For English text, normal formatting
            return $"{startMarker}{text}{endMarker}";
        }
    }

    private bool ContainsChinese(string text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        foreach (char c in text)
        {
            // Check if character is in CJK (Chinese, Japanese, Korean) range
            if ((c >= 0x4E00 && c <= 0x9FFF) ||  // CJK Unified Ideographs
                (c >= 0x3400 && c <= 0x4DBF) ||  // CJK Extension A
                (c >= 0x20000 && c <= 0x2A6DF) || // CJK Extension B
                (c >= 0x2A700 && c <= 0x2B73F) || // CJK Extension C
                (c >= 0x2B740 && c <= 0x2B81F) || // CJK Extension D
                (c >= 0x2B820 && c <= 0x2CEAF) || // CJK Extension E
                (c >= 0x3000 && c <= 0x303F) ||   // CJK Symbols and Punctuation
                (c >= 0xFF00 && c <= 0xFFEF))     // Halfwidth and Fullwidth Forms
            {
                return true;
            }
        }
        return false;
    }

    private ListInfo GetListInfo(Paragraph paragraph)
    {
        var listInfo = new ListInfo();
        
        var paragraphProperties = paragraph.ParagraphProperties;
        if (paragraphProperties?.NumberingProperties != null)
        {
            listInfo.IsListItem = true;
            
            // Get numbering level (0-based)
            var levelValue = paragraphProperties.NumberingProperties.NumberingLevelReference?.Val?.Value;
            listInfo.Level = levelValue ?? 0;
            
            // Get numbering ID to determine list type
            var numberingId = paragraphProperties.NumberingProperties.NumberingId?.Val?.Value;
            listInfo.NumberingId = numberingId ?? 0;
            
            // For now, assume ordered list if numbering ID is present
            // In a full implementation, we would need to check the numbering definition
            listInfo.IsOrdered = numberingId > 0;
        }
        else
        {
            // Check for bullet-style paragraphs by style
            var styleId = paragraphProperties?.ParagraphStyleId?.Val?.Value;
            if (!string.IsNullOrEmpty(styleId))
            {
                if (styleId.Contains("List", StringComparison.OrdinalIgnoreCase) ||
                    styleId.Contains("Bullet", StringComparison.OrdinalIgnoreCase))
                {
                    listInfo.IsListItem = true;
                    listInfo.IsOrdered = styleId.Contains("Number", StringComparison.OrdinalIgnoreCase) ||
                                        styleId.Contains("Ordered", StringComparison.OrdinalIgnoreCase);
                }
            }
        }
        
        return listInfo;
    }

    private string ProcessListItem(Paragraph paragraph, ListInfo listInfo, ConversionOptions options)
    {
        var textBuilder = new StringBuilder();

        // Process all runs to get the list item text with formatting
        foreach (var run in paragraph.Elements<Run>())
        {
            var runText = ProcessRun(run, options);
            textBuilder.Append(runText);
        }

        var itemText = textBuilder.ToString().Trim();
        if (string.IsNullOrEmpty(itemText))
            return string.Empty;

        // Generate appropriate indentation for nested lists
        var indent = new string(' ', listInfo.Level * 2); // Use 2 spaces for standard Markdown

        // Generate list marker based on type and level
        string marker;
        if (listInfo.IsOrdered)
        {
            marker = "1."; // Markdown will auto-number
        }
        else
        {
            // Use different markers for different levels for better visual distinction
            marker = listInfo.Level switch
            {
                0 => "-",
                1 => "*",
                2 => "+",
                _ => "-"
            };
        }

        // Ensure proper line spacing for list items
        var result = $"{indent}{marker} {itemText}";

        // Add extra line break after list items for better readability
        return result;
    }

    private class RunFormatting
    {
        public bool IsBold { get; set; }
        public bool IsItalic { get; set; }
        public bool IsUnderline { get; set; }
        public bool IsStrikethrough { get; set; }
    }

    private class ListInfo
    {
        public bool IsListItem { get; set; }
        public bool IsOrdered { get; set; }
        public int Level { get; set; }
        public int NumberingId { get; set; }
    }

    private class ParagraphStyle
    {
        public bool IsCenter { get; set; }
        public bool IsRight { get; set; }
        public bool IsQuote { get; set; }
        public bool IsCode { get; set; }
    }

    private string ProcessHyperlink(Hyperlink hyperlink, ConversionOptions options)
    {
        try
        {
            var textBuilder = new StringBuilder();

            // Extract the hyperlink text
            foreach (var run in hyperlink.Elements<Run>())
            {
                var runText = ProcessRun(run, options);
                textBuilder.Append(runText);
            }

            var linkText = textBuilder.ToString().Trim();
            if (string.IsNullOrEmpty(linkText))
                return string.Empty;

            // Get the hyperlink target
            var anchor = hyperlink.Anchor?.Value;
            var docLocation = hyperlink.DocLocation?.Value;
            var id = hyperlink.Id?.Value;

            if (!string.IsNullOrEmpty(anchor))
            {
                // Internal link to bookmark
                return $"[{linkText}](#{anchor})";
            }
            else if (!string.IsNullOrEmpty(docLocation))
            {
                // Internal document location
                return $"[{linkText}](#{docLocation})";
            }
            else if (!string.IsNullOrEmpty(id))
            {
                // External link - would need to resolve the relationship
                // For now, just return the text with a placeholder
                return $"[{linkText}](#external-link-{id})";
            }
            else
            {
                // No valid link target, just return the text
                return linkText;
            }
        }
        catch (Exception)
        {
            // If hyperlink processing fails, just return the text content
            return hyperlink.InnerText;
        }
    }

    private string ProcessBookmark(BookmarkStart bookmarkStart)
    {
        var name = bookmarkStart.Name?.Value;
        if (!string.IsNullOrEmpty(name))
        {
            return $"<a id=\"{name}\"></a>";
        }
        return string.Empty;
    }

    private string ProcessFootnoteReference(FootnoteReference footnoteRef)
    {
        var id = footnoteRef.Id?.Value;
        if (id.HasValue)
        {
            return $"[^{id}]";
        }
        return "[^footnote]";
    }

    private string ProcessEndnoteReference(EndnoteReference endnoteRef)
    {
        var id = endnoteRef.Id?.Value;
        if (id.HasValue)
        {
            return $"[^end{id}]";
        }
        return "[^endnote]";
    }
}