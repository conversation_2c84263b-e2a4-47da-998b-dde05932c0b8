using DocxToMarkdownConverter.Models;
using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using W = DocumentFormat.OpenXml.Wordprocessing;
using M = DocumentFormat.OpenXml.Math;
using System.Text;
using System.Xml;
using System.Linq;
using System.IO;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// Processes mathematical formulas from DOCX documents and converts them to LaTeX or MathML format
/// </summary>
public class FormulaProcessor : IFormulaProcessor
{
    private readonly ILogger<FormulaProcessor>? _logger;

    public FormulaProcessor(ILogger<FormulaProcessor>? logger = null)
    {
        _logger = logger;
    }

    /// <summary>
    /// Processes a mathematical formula and converts it to appropriate markdown format (async version)
    /// </summary>
    public async Task<string> ProcessFormulaAsync(object formula, ConversionOptions options)
    {
        try
        {
            _logger?.LogDebug("Processing mathematical formula");

            return formula switch
            {
                OfficeMath officeMath => await ProcessOfficeMathAsync(officeMath, options),
                _ => await ProcessGenericFormulaAsync(formula, options)
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to process mathematical formula");
            return "<!-- Error processing formula -->";
        }
    }

    /// <summary>
    /// Processes a mathematical formula and converts it to appropriate markdown format (synchronous version for backward compatibility)
    /// </summary>
    public string ProcessFormula(object formula, ConversionOptions options)
    {
        try
        {
            return ProcessFormulaAsync(formula, options).GetAwaiter().GetResult();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to process mathematical formula");
            return "<!-- Error processing formula -->";
        }
    }

    /// <summary>
    /// Converts a formula to LaTeX format
    /// </summary>
    public async Task<string> ConvertToLatexAsync(object formula)
    {
        try
        {
            _logger?.LogDebug("Converting formula to LaTeX format");

            return formula switch
            {
                OfficeMath officeMath => await ConvertOfficeMathToLatexAsync(officeMath),
                _ => await ConvertGenericFormulaToLatexAsync(formula)
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to convert formula to LaTeX");
            return "<!-- LaTeX conversion failed -->";
        }
    }

    /// <summary>
    /// Converts a formula to MathML format
    /// </summary>
    public async Task<string> ConvertToMathMLAsync(object formula)
    {
        try
        {
            _logger?.LogDebug("Converting formula to MathML format");

            return formula switch
            {
                OfficeMath officeMath => await ConvertOfficeMathToMathMLAsync(officeMath),
                _ => await ConvertGenericFormulaToMathMLAsync(formula)
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to convert formula to MathML");
            return "<!-- MathML conversion failed -->";
        }
    }

    /// <summary>
    /// Detects if an element contains mathematical formulas
    /// </summary>
    public bool ContainsFormulas(object element)
    {
        try
        {
            return element switch
            {
                W.Paragraph paragraph => paragraph.Descendants<OfficeMath>().Any(),
                OfficeMath => true,
                _ => false
            };
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to detect formulas in element");
            return false;
        }
    }

    /// <summary>
    /// Extracts all formulas from a paragraph
    /// </summary>
    public IEnumerable<object> ExtractFormulas(W.Paragraph paragraph)
    {
        try
        {
            var formulas = new List<object>();
            
            // Extract OfficeMath elements
            formulas.AddRange(paragraph.Descendants<OfficeMath>().Cast<object>());
            
            // Extract MathParagraph elements (if they exist in the document structure)
            // Note: MathParagraph is not a standard OpenXml type, using OfficeMath instead
            // formulas.AddRange(paragraph.Descendants<MathParagraph>().Cast<object>());
            
            _logger?.LogDebug("Extracted {FormulaCount} formulas from paragraph", formulas.Count);
            return formulas;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Failed to extract formulas from paragraph");
            return Enumerable.Empty<object>();
        }
    }

    #region Private Methods

    private async Task<string> ProcessOfficeMathAsync(OfficeMath officeMath, ConversionOptions options)
    {
        var latex = await ConvertOfficeMathToLatexAsync(officeMath);

        if (!options.ProcessFormulas)
            return $"<!-- Formula: {latex} -->";

        // Determine if this is an inline or display formula
        var isDisplayFormula = IsDisplayFormula(officeMath);

        if (isDisplayFormula)
        {
            // Display formula (block)
            return $"\n$$\n{latex}\n$$\n";
        }
        else
        {
            // Inline formula
            return $"${latex}$";
        }
    }

    private bool IsDisplayFormula(OfficeMath officeMath)
    {
        // Check if the formula is in its own paragraph or has display properties
        var parent = officeMath.Parent;

        // If the parent paragraph contains only this formula (and maybe whitespace), treat as display
        if (parent is W.Paragraph paragraph)
        {
            try
            {
                // Use safe text extraction instead of InnerText
                var textContent = ExtractTextFromXmlElement(paragraph).Trim();
                var formulaContent = ExtractTextFromXmlElement(officeMath).Trim();

                // If the paragraph text is mostly the formula, treat as display
                if (string.IsNullOrEmpty(textContent) || textContent == formulaContent)
                    return true;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Error in IsDisplayFormula text extraction");
                // Fallback: assume inline formula
                return false;
            }
        }

        // Check for display properties in the math element
        // Note: Display property might not be available in all OpenXml versions
        // For now, use a simpler heuristic
        try
        {
            var mathProperties = officeMath.GetFirstChild<M.MathProperties>();
            // If we can't find display properties, fall back to content-based detection
        }
        catch
        {
            // Ignore if Display type is not available
        }

        return false;
    }

    private async Task<string> ProcessMathParagraphAsync(object mathParagraph, ConversionOptions options)
    {
        if (mathParagraph is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
        {
            var formulas = xmlElement.Descendants<OfficeMath>();
            var latexBuilder = new StringBuilder();

            foreach (var formula in formulas)
            {
                var latex = await ConvertOfficeMathToLatexAsync(formula);
                latexBuilder.AppendLine($"$${latex}$$");
            }

            return options.ProcessFormulas ? latexBuilder.ToString().Trim() :
                   $"<!-- Math Paragraph: {latexBuilder.ToString().Trim()} -->";
        }

        return string.Empty;
    }

    private async Task<string> ProcessGenericFormulaAsync(object formula, ConversionOptions options)
    {
        // Fallback for unknown formula types
        var text = ExtractTextFromElement(formula);

        // Apply post-processing to handle fractions even in generic formulas
        var processedText = PostProcessLatexFormatting(text, false);

        return options.ProcessFormulas ? $"$${processedText}$$" : $"<!-- Formula: {processedText} -->";
    }

    private string ProcessGenericFormula(object formula, ConversionOptions options)
    {
        // Fallback for unknown formula types
        var text = ExtractTextFromElement(formula);

        // Apply post-processing to handle fractions even in generic formulas
        var processedText = PostProcessLatexFormatting(text, false);

        return options.ProcessFormulas ? $"$${processedText}$$" : $"<!-- Formula: {processedText} -->";
    }

    private async Task<string> ConvertOfficeMathToLatexAsync(OfficeMath officeMath)
    {
        return await Task.Run(() =>
        {
            try
            {
                var latexBuilder = new StringBuilder();
                ProcessMathElement(officeMath, latexBuilder);

                // Get the raw LaTeX result
                var rawLatex = latexBuilder.ToString();

                // Check if this might be a piecewise function
                bool isPiecewise = rawLatex.Contains("\\begin{cases}") ||
                                 rawLatex.Contains("\\text{if}") ||
                                 rawLatex.Contains("\\text{otherwise}") ||
                                 rawLatex.Contains("if ") ||
                                 rawLatex.Contains("otherwise");

                // Apply post-processing to handle fractions and other formatting
                var processedLatex = PostProcessLatexFormatting(rawLatex, isPiecewise);

                _logger?.LogDebug($"ConvertOfficeMathToLatexAsync: '{rawLatex}' -> '{processedLatex}'");

                return processedLatex;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to convert OfficeMath to LaTeX");

                // Try to extract text safely without using InnerText directly
                try
                {
                    var fallbackText = ExtractTextFromElement(officeMath);

                    // Apply post-processing even to fallback text to handle fractions
                    var processedFallback = PostProcessLatexFormatting(fallbackText, false);

                    _logger?.LogDebug($"Fallback processing: '{fallbackText}' -> '{processedFallback}'");

                    return processedFallback;
                }
                catch (Exception fallbackEx)
                {
                    _logger?.LogError(fallbackEx, "Failed to process fallback text");
                    // Last resort: return a safe placeholder
                    return "<!-- Math formula processing failed -->";
                }
            }
        });
    }

    private async Task<string> ConvertMathParagraphToLatexAsync(M.Paragraph mathParagraph)
    {
        return await Task.Run(() =>
        {
            try
            {
                var latexBuilder = new StringBuilder();
                var formulas = mathParagraph.Descendants<OfficeMath>();

                foreach (var formula in formulas)
                {
                    ProcessMathElement(formula, latexBuilder);
                    latexBuilder.AppendLine();
                }

                // Get the raw LaTeX result
                var rawLatex = latexBuilder.ToString().Trim();

                // Check if this might be a piecewise function
                bool isPiecewise = rawLatex.Contains("\\begin{cases}") ||
                                 rawLatex.Contains("\\text{if}") ||
                                 rawLatex.Contains("\\text{otherwise}") ||
                                 rawLatex.Contains("if ") ||
                                 rawLatex.Contains("otherwise");

                // Apply post-processing to handle fractions and other formatting
                var processedLatex = PostProcessLatexFormatting(rawLatex, isPiecewise);

                _logger?.LogDebug($"ConvertMathParagraphToLatexAsync: '{rawLatex}' -> '{processedLatex}'");

                return processedLatex;
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to convert MathParagraph to LaTeX");

                // Try to extract text safely without using InnerText directly
                try
                {
                    var fallbackText = ExtractTextFromElement(mathParagraph);

                    // Apply post-processing even to fallback text to handle fractions
                    var processedFallback = PostProcessLatexFormatting(fallbackText, true);

                    _logger?.LogDebug($"MathParagraph fallback processing: '{fallbackText}' -> '{processedFallback}'");

                    return processedFallback;
                }
                catch (Exception fallbackEx)
                {
                    _logger?.LogError(fallbackEx, "Failed to process MathParagraph fallback text");
                    // Last resort: return a safe placeholder
                    return "<!-- Math paragraph processing failed -->";
                }
            }
        });
    }

    private async Task<string> ConvertGenericFormulaToLatexAsync(object formula)
    {
        return await Task.FromResult(ExtractTextFromElement(formula));
    }

    private async Task<string> ConvertOfficeMathToMathMLAsync(OfficeMath officeMath)
    {
        return await Task.Run(() =>
        {
            try
            {
                // Convert to MathML format
                var mathMLBuilder = new StringBuilder();
                mathMLBuilder.AppendLine("<math xmlns=\"http://www.w3.org/1998/Math/MathML\">");
                ProcessMathElementToMathML(officeMath, mathMLBuilder);
                mathMLBuilder.AppendLine("</math>");
                return mathMLBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to convert OfficeMath to MathML");
                return $"<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>{ExtractTextFromElement(officeMath)}</mtext></math>";
            }
        });
    }

    private async Task<string> ConvertMathParagraphToMathMLAsync(M.Paragraph mathParagraph)
    {
        return await Task.Run(() =>
        {
            try
            {
                var mathMLBuilder = new StringBuilder();
                mathMLBuilder.AppendLine("<math xmlns=\"http://www.w3.org/1998/Math/MathML\">");
                
                var formulas = mathParagraph.Descendants<OfficeMath>();
                foreach (var formula in formulas)
                {
                    ProcessMathElementToMathML(formula, mathMLBuilder);
                }
                
                mathMLBuilder.AppendLine("</math>");
                return mathMLBuilder.ToString();
            }
            catch (Exception ex)
            {
                _logger?.LogError(ex, "Failed to convert MathParagraph to MathML");
                return $"<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>{ExtractTextFromElement(mathParagraph)}</mtext></math>";
            }
        });
    }

    private async Task<string> ConvertGenericFormulaToMathMLAsync(object formula)
    {
        return await Task.FromResult($"<math xmlns=\"http://www.w3.org/1998/Math/MathML\"><mtext>{ExtractTextFromElement(formula)}</mtext></math>");
    }

    private void ProcessMathElement(object element, StringBuilder latexBuilder)
    {
        // Add debug logging to track element types
        var elementType = element?.GetType().Name ?? "null";
        _logger?.LogDebug($"Processing math element: {elementType}");

        switch (element)
        {
            case MathFunction mathFunction:
                _logger?.LogDebug("Processing MathFunction");
                ProcessMathFunction(mathFunction, latexBuilder);
                break;
            case Fraction fraction:
                _logger?.LogDebug("Processing Fraction");
                ProcessFraction(fraction, latexBuilder);
                break;
            case Radical radical:
                _logger?.LogDebug("Processing Radical");
                ProcessRadical(radical, latexBuilder);
                break;
            case Superscript superScript:
                _logger?.LogDebug("Processing Superscript");
                ProcessSuperScript(superScript, latexBuilder);
                break;
            case Subscript subScript:
                _logger?.LogDebug("Processing Subscript");
                ProcessSubScript(subScript, latexBuilder);
                break;
            case M.Text mathText:
                _logger?.LogDebug($"Processing M.Text: {mathText.InnerText}");
                latexBuilder.Append(mathText.InnerText);
                break;
            case DocumentFormat.OpenXml.Wordprocessing.Text text:
                _logger?.LogDebug($"Processing Text: {text.Text}");
                // Handle regular text elements that might appear in math contexts
                latexBuilder.Append(text.Text);
                break;
            case Nary nary:
                _logger?.LogDebug("Processing Nary");
                ProcessNary(nary, latexBuilder);
                break;
            case Delimiter delimiter:
                _logger?.LogDebug("Processing Delimiter");
                ProcessDelimiter(delimiter, latexBuilder);
                break;
            case M.Matrix matrix:
                _logger?.LogDebug("Processing M.Matrix - FOUND MATRIX!");
                ProcessMatrix(matrix, latexBuilder);
                break;
            case Run run:
                _logger?.LogDebug("Processing Run");
                // Process run content
                foreach (var child in run.Elements())
                {
                    ProcessMathElement(child, latexBuilder);
                }
                break;
            default:
                // Handle other math elements that might not be explicitly covered
                if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
                {
                    // Check for specific math element types by name
                    var elementName = xmlElement.LocalName;
                    _logger?.LogDebug($"Processing unknown element by LocalName: {elementName}");

                    switch (elementName)
                    {
                        case "num":
                        case "den":
                        case "e":
                        case "base":
                        case "deg":
                            _logger?.LogDebug($"Processing structural element: {elementName}");
                            // These are structural elements, process their children
                            foreach (var child in xmlElement.Elements())
                            {
                                ProcessMathElement(child, latexBuilder);
                            }
                            break;
                        case "t":
                            _logger?.LogDebug($"Processing text element: {xmlElement.InnerText}");
                            // Text element in math context
                            latexBuilder.Append(xmlElement.InnerText);
                            break;
                        case "d":
                            _logger?.LogDebug("Processing delimiter element");
                            // Delimiter element - might be used for piecewise functions
                            ProcessDelimiterElement(xmlElement, latexBuilder);
                            break;
                        case "m":
                            _logger?.LogDebug("Processing matrix element by LocalName - FOUND MATRIX!");
                            // Matrix element - handle specially
                            ProcessMatrixElementByXml(xmlElement, latexBuilder);
                            break;
                        case "mr":
                            _logger?.LogDebug("Processing matrix row element");
                            // Matrix row
                            ProcessMatrixRowElement(xmlElement, latexBuilder);
                            break;
                        case "mc":
                            _logger?.LogDebug("Processing matrix column element");
                            // Matrix column
                            ProcessMatrixColumnElement(xmlElement, latexBuilder);
                            break;
                        default:
                            _logger?.LogDebug($"Processing unknown element recursively: {elementName}");
                            // For unknown elements, try to process children
                            foreach (var child in xmlElement.Elements())
                            {
                                ProcessMathElement(child, latexBuilder);
                            }
                            break;
                    }
                }
                else
                {
                    _logger?.LogWarning($"Element is not OpenXmlElement: {element?.GetType().Name}");
                }
                break;
        }
    }

    private void ProcessMathElementToMathML(object element, StringBuilder mathMLBuilder)
    {
        switch (element)
        {
            case MathFunction mathFunction:
                ProcessMathFunctionToMathML(mathFunction, mathMLBuilder);
                break;
            case Fraction fraction:
                ProcessFractionToMathML(fraction, mathMLBuilder);
                break;
            case Radical radical:
                ProcessRadicalToMathML(radical, mathMLBuilder);
                break;
            case Superscript superScript:
                ProcessSuperScriptToMathML(superScript, mathMLBuilder);
                break;
            case Subscript subScript:
                ProcessSubScriptToMathML(subScript, mathMLBuilder);
                break;
            case M.Text mathText:
                mathMLBuilder.Append($"<mtext>{XmlEscape(mathText.InnerText)}</mtext>");
                break;
            case Nary nary:
                ProcessNaryToMathML(nary, mathMLBuilder);
                break;
            case Delimiter delimiter:
                ProcessDelimiterToMathML(delimiter, mathMLBuilder);
                break;
            case Matrix matrix:
                ProcessMatrixToMathML(matrix, mathMLBuilder);
                break;
            case Run run:
                // Process run content
                foreach (var child in run.Elements())
                {
                    ProcessMathElementToMathML(child, mathMLBuilder);
                }
                break;
            default:
                // Process child elements recursively
                if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
                {
                    foreach (var child in xmlElement.Elements())
                    {
                        ProcessMathElementToMathML(child, mathMLBuilder);
                    }
                }
                break;
        }
    }

    private void ProcessMathFunction(MathFunction mathFunction, StringBuilder latexBuilder)
    {
        var functionName = mathFunction.GetFirstChild<FunctionName>()?.InnerText ?? "";
        var element = mathFunction.GetFirstChild<Base>();
        var argument = ExtractTextFromElement(element);
        latexBuilder.Append($"\\{functionName}({argument})");
    }

    private void ProcessFraction(Fraction fraction, StringBuilder latexBuilder)
    {
        var numerator = ExtractTextFromElement(fraction.GetFirstChild<Numerator>());
        var denominator = ExtractTextFromElement(fraction.GetFirstChild<Denominator>());
        latexBuilder.Append($"\\frac{{{numerator}}}{{{denominator}}}");
    }

    private void ProcessRadical(Radical radical, StringBuilder latexBuilder)
    {
        var degreeElement = radical.GetFirstChild<Degree>();
        var degree = degreeElement != null ? ExtractTextFromElement(degreeElement) : "";
        var element = ExtractTextFromElement(radical.GetFirstChild<Base>());

        if (string.IsNullOrEmpty(degree))
        {
            latexBuilder.Append($"\\sqrt{{{element}}}");
        }
        else
        {
            latexBuilder.Append($"\\sqrt[{degree}]{{{element}}}");
        }
    }

    private void ProcessSuperScript(Superscript superScript, StringBuilder latexBuilder)
    {
        // For superscript, we need to process the base and superscript parts
        var baseText = string.Empty;
        var superText = string.Empty;
        
        // Extract text from all child elements
        foreach (var child in superScript.Elements())
        {
            var childText = ExtractTextFromElement(child);
            if (!string.IsNullOrEmpty(childText))
            {
                if (string.IsNullOrEmpty(baseText))
                    baseText = childText;
                else
                    superText = childText;
            }
        }
        
        if (!string.IsNullOrEmpty(superText))
            latexBuilder.Append($"{baseText}^{{{superText}}}");
        else
            latexBuilder.Append(baseText);
    }

    private void ProcessSubScript(Subscript subScript, StringBuilder latexBuilder)
    {
        // For subscript, we need to process the base and subscript parts
        var baseText = string.Empty;
        var subText = string.Empty;
        
        // Extract text from all child elements
        foreach (var child in subScript.Elements())
        {
            var childText = ExtractTextFromElement(child);
            if (!string.IsNullOrEmpty(childText))
            {
                if (string.IsNullOrEmpty(baseText))
                    baseText = childText;
                else
                    subText = childText;
            }
        }
        
        if (!string.IsNullOrEmpty(subText))
            latexBuilder.Append($"{baseText}_{{{subText}}}");
        else
            latexBuilder.Append(baseText);
    }

    private void ProcessMathFunctionToMathML(MathFunction mathFunction, StringBuilder mathMLBuilder)
    {
        var functionName = mathFunction.GetFirstChild<FunctionName>()?.InnerText ?? "";
        var element = mathFunction.GetFirstChild<Base>();
        var argument = ExtractTextFromElement(element);
        mathMLBuilder.Append($"<mi>{XmlEscape(functionName)}</mi><mo>(</mo><mtext>{XmlEscape(argument)}</mtext><mo>)</mo>");
    }

    private void ProcessFractionToMathML(Fraction fraction, StringBuilder mathMLBuilder)
    {
        var numerator = ExtractTextFromElement(fraction.GetFirstChild<Numerator>());
        var denominator = ExtractTextFromElement(fraction.GetFirstChild<Denominator>());
        mathMLBuilder.Append($"<mfrac><mtext>{XmlEscape(numerator)}</mtext><mtext>{XmlEscape(denominator)}</mtext></mfrac>");
    }

    private void ProcessRadicalToMathML(Radical radical, StringBuilder mathMLBuilder)
    {
        var degreeElement = radical.GetFirstChild<Degree>();
        var degree = degreeElement != null ? ExtractTextFromElement(degreeElement) : "";
        var element = ExtractTextFromElement(radical.GetFirstChild<Base>());

        if (string.IsNullOrEmpty(degree))
        {
            mathMLBuilder.Append($"<msqrt><mtext>{XmlEscape(element)}</mtext></msqrt>");
        }
        else
        {
            mathMLBuilder.Append($"<mroot><mtext>{XmlEscape(element)}</mtext><mtext>{XmlEscape(degree)}</mtext></mroot>");
        }
    }

    private void ProcessSuperScriptToMathML(Superscript superScript, StringBuilder mathMLBuilder)
    {
        // For superscript, we need to process the base and superscript parts
        var baseText = string.Empty;
        var superText = string.Empty;
        
        // Extract text from all child elements
        foreach (var child in superScript.Elements())
        {
            var childText = ExtractTextFromElement(child);
            if (!string.IsNullOrEmpty(childText))
            {
                if (string.IsNullOrEmpty(baseText))
                    baseText = childText;
                else
                    superText = childText;
            }
        }
        
        if (!string.IsNullOrEmpty(superText))
            mathMLBuilder.Append($"<msup><mtext>{XmlEscape(baseText)}</mtext><mtext>{XmlEscape(superText)}</mtext></msup>");
        else
            mathMLBuilder.Append($"<mtext>{XmlEscape(baseText)}</mtext>");
    }

    private void ProcessSubScriptToMathML(Subscript subScript, StringBuilder mathMLBuilder)
    {
        // For subscript, we need to process the base and subscript parts
        var baseText = string.Empty;
        var subText = string.Empty;
        
        // Extract text from all child elements
        foreach (var child in subScript.Elements())
        {
            var childText = ExtractTextFromElement(child);
            if (!string.IsNullOrEmpty(childText))
            {
                if (string.IsNullOrEmpty(baseText))
                    baseText = childText;
                else
                    subText = childText;
            }
        }
        
        if (!string.IsNullOrEmpty(subText))
            mathMLBuilder.Append($"<msub><mtext>{XmlEscape(baseText)}</mtext><mtext>{XmlEscape(subText)}</mtext></msub>");
        else
            mathMLBuilder.Append($"<mtext>{XmlEscape(baseText)}</mtext>");
    }

    private string ExtractTextFromElement(object? element)
    {
        if (element == null) return string.Empty;

        return element switch
        {
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => ExtractTextFromXmlElement(xmlElement),
            string text => text,
            _ => element.ToString() ?? string.Empty
        };
    }

    private string ExtractTextFromXmlElement(DocumentFormat.OpenXml.OpenXmlElement xmlElement)
    {
        try
        {
            // For math elements, we need to preserve structure and apply post-processing
            if (IsMathElement(xmlElement))
            {
                var latexBuilder = new StringBuilder();
                ProcessMathElement(xmlElement, latexBuilder);
                var rawLatex = latexBuilder.ToString();

                // Check if this might be a piecewise function or contain fractions
                bool isPiecewise = rawLatex.Contains("\\begin{cases}") ||
                                 rawLatex.Contains("\\text{if}") ||
                                 rawLatex.Contains("\\text{otherwise}") ||
                                 rawLatex.Contains("if ") ||
                                 rawLatex.Contains("otherwise");

                // Apply post-processing to handle fractions and other formatting
                var processedLatex = PostProcessLatexFormatting(rawLatex, isPiecewise);

                _logger?.LogDebug($"ExtractTextFromXmlElement (math): '{rawLatex}' -> '{processedLatex}'");

                return processedLatex;
            }

            // For non-math elements, use InnerText
            return xmlElement.InnerText;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error extracting text from XML element");
            return xmlElement.InnerText; // Fallback to original behavior
        }
    }

    private bool IsMathElement(DocumentFormat.OpenXml.OpenXmlElement xmlElement)
    {
        // Check if this is a math-related element
        var elementName = xmlElement.LocalName?.ToLower();
        var namespaceName = xmlElement.NamespaceUri;

        // Check for OpenXML Math namespace
        if (namespaceName == "http://schemas.openxmlformats.org/officeDocument/2006/math")
            return true;

        // Check for common math element names
        if (elementName != null && (
            elementName.Contains("math") ||
            elementName.Contains("frac") ||
            elementName.Contains("num") ||
            elementName.Contains("den") ||
            elementName == "e" ||
            elementName == "f" ||
            elementName == "omath"))
            return true;

        // Check if element contains math descendants
        if (xmlElement.Descendants<OfficeMath>().Any() ||
            xmlElement.Descendants<Fraction>().Any() ||
            xmlElement.Descendants<M.Matrix>().Any())
            return true;

        return false;
    }

    private string XmlEscape(string text)
    {
        return text.Replace("&", "&amp;")
                  .Replace("<", "&lt;")
                  .Replace(">", "&gt;")
                  .Replace("\"", "&quot;")
                  .Replace("'", "&apos;");
    }

    #endregion
    
    private void ProcessNary(Nary nary, StringBuilder latexBuilder)
    {
        // Handle summation, product, integral, etc.
        var naryProps = nary.GetFirstChild<NaryProperties>();
        var naryChar = naryProps?.GetFirstChild<M.Text>();
        var naryOperator = naryChar?.InnerText ?? "\\sum";

        switch (naryOperator)
        {
            case "∑":
                naryOperator = "\\sum";
                break;
            case "∏":
                naryOperator = "\\prod";
                break;
            case "∫":
                naryOperator = "\\int";
                break;
            case "∮":
                naryOperator = "\\oint";
                break;
            case "⋂":
                naryOperator = "\\bigcap";
                break;
            case "⋃":
                naryOperator = "\\bigcup";
                break;
        }

        // Process subscript (lower limit)
        var subScript = nary.GetFirstChild<Subscript>();
        var subScriptText = subScript != null ? ExtractTextFromElement(subScript) : "";

        // Process superscript (upper limit)
        var superScript = nary.GetFirstChild<Superscript>();
        var superScriptText = superScript != null ? ExtractTextFromElement(superScript) : "";

        // Process base (the expression being summed/integrated)
        var baseElement = nary.GetFirstChild<Base>();
        var baseText = baseElement != null ? ExtractTextFromElement(baseElement) : "";

        // Format as LaTeX
        if (!string.IsNullOrEmpty(subScriptText) && !string.IsNullOrEmpty(superScriptText))
        {
            latexBuilder.Append($"{naryOperator}_{{{subScriptText}}}^{{{superScriptText}}} {baseText}");
        }
        else if (!string.IsNullOrEmpty(subScriptText))
        {
            latexBuilder.Append($"{naryOperator}_{{{subScriptText}}} {baseText}");
        }
        else if (!string.IsNullOrEmpty(superScriptText))
        {
            latexBuilder.Append($"{naryOperator}^{{{superScriptText}}} {baseText}");
        }
        else
        {
            latexBuilder.Append($"{naryOperator} {baseText}");
        }
    }
    
    private void ProcessDelimiter(Delimiter delimiter, StringBuilder latexBuilder)
    {
        // Get opening and closing delimiters
        var delimiterProps = delimiter.GetFirstChild<DelimiterProperties>();
        var beginChar = delimiterProps?.GetFirstChild<BeginChar>();
        var endChar = delimiterProps?.GetFirstChild<EndChar>();

        var openingDelimiter = beginChar?.InnerText?.Trim();
        var closingDelimiter = endChar?.InnerText?.Trim();

        // Handle empty or null delimiters
        if (string.IsNullOrEmpty(openingDelimiter) && string.IsNullOrEmpty(closingDelimiter))
        {
            // No delimiters, just process content
            foreach (var child in delimiter.Elements())
            {
                ProcessMathElement(child, latexBuilder);
            }
            return;
        }

        // Use default delimiters if one is missing
        openingDelimiter ??= "(";
        closingDelimiter ??= ")";

        // Map common delimiters to LaTeX commands
        var openingLatex = openingDelimiter switch
        {
            "(" => "\\left(",
            "[" => "\\left[",
            "{" => "\\left\\{",
            "|" => "\\left|",
            "‖" => "\\left\\|",
            "⟨" => "\\left\\langle",
            "⌊" => "\\left\\lfloor",
            "⌈" => "\\left\\lceil",
            "" => "\\left.",  // Invisible delimiter
            _ => $"\\left{openingDelimiter}"
        };

        var closingLatex = closingDelimiter switch
        {
            ")" => "\\right)",
            "]" => "\\right]",
            "}" => "\\right\\}",
            "|" => "\\right|",
            "‖" => "\\right\\|",
            "⟩" => "\\right\\rangle",
            "⌋" => "\\right\\rfloor",
            "⌉" => "\\right\\rceil",
            "" => "\\right.",  // Invisible delimiter
            _ => $"\\right{closingDelimiter}"
        };

        // Process the content inside the delimiters
        latexBuilder.Append(openingLatex);

        foreach (var child in delimiter.Elements())
        {
            ProcessMathElement(child, latexBuilder);
        }

        latexBuilder.Append(closingLatex);
    }
    
    private void ProcessMatrix(M.Matrix matrix, StringBuilder latexBuilder)
    {
        _logger?.LogDebug("ProcessMatrix called - extracting matrix content");

        // First, let's dump the complete XML structure for debugging
        DumpMatrixXmlStructure(matrix);

        var rows = matrix.Elements<M.MatrixRow>().ToList();
        _logger?.LogDebug($"Matrix has {rows.Count} rows");

        // Check if this might be a piecewise function (cases)
        bool isPiecewise = IsPiecewiseFunction(matrix);

        if (isPiecewise)
        {
            _logger?.LogDebug("Detected piecewise function, using cases environment");
            latexBuilder.Append("\\begin{cases}");
        }
        else
        {
            _logger?.LogDebug("Regular matrix, using pmatrix environment");
            latexBuilder.Append("\\begin{pmatrix}");
        }

        // Process matrix content with enhanced extraction
        if (!rows.Any())
        {
            _logger?.LogDebug("No rows found, trying direct content extraction");
            var directContent = ExtractAllContentFromMatrix(matrix);
            if (!string.IsNullOrEmpty(directContent))
            {
                latexBuilder.Append(directContent);
            }
            else
            {
                latexBuilder.Append(" ");
            }
        }
        else
        {
            // Process each row with comprehensive extraction
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                var cells = row.Elements<M.MatrixColumn>().ToList();
                _logger?.LogDebug($"Row {i} has {cells.Count} cells");

                if (!cells.Any())
                {
                    // Try to extract content directly from row
                    var rowContent = ExtractAllContentFromElement(row);
                    if (!string.IsNullOrEmpty(rowContent))
                    {
                        latexBuilder.Append(rowContent);
                    }
                    else
                    {
                        latexBuilder.Append(" ");
                    }
                }
                else
                {
                    // Process each cell with multiple extraction strategies
                    for (int j = 0; j < cells.Count; j++)
                    {
                        var cell = cells[j];
                        var cellStartLength = latexBuilder.Length;

                        // Use comprehensive content extraction
                        ExtractCellContentComprehensive(cell, latexBuilder);

                        // If still no content, this is a serious problem
                        if (latexBuilder.Length == cellStartLength)
                        {
                            _logger?.LogError($"CRITICAL: Failed to extract any content from cell {j} in row {i}");
                            _logger?.LogError($"Cell XML: {cell.OuterXml}");

                            // Last resort: add a placeholder to maintain structure
                            latexBuilder.Append(" ");
                        }

                        // Add column separator except for the last cell
                        if (j < cells.Count - 1)
                        {
                            latexBuilder.Append(" & ");
                        }
                        else if (isPiecewise && cells.Count == 1)
                        {
                            // For piecewise functions with single column, add & for condition
                            // This handles cases where condition is in the same cell
                            var cellContent = latexBuilder.ToString();
                            var lastCommaIndex = cellContent.LastIndexOf(',');
                            if (lastCommaIndex > 0 && lastCommaIndex < cellContent.Length - 1)
                            {
                                // Insert & before the condition part
                                var beforeComma = cellContent.Substring(0, lastCommaIndex + 1);
                                var afterComma = cellContent.Substring(lastCommaIndex + 1);
                                latexBuilder.Clear();
                                latexBuilder.Append(beforeComma);
                                latexBuilder.Append(" & ");
                                latexBuilder.Append(afterComma.Trim());
                            }
                        }
                    }
                }

                // Add row separator except for the last row
                if (i < rows.Count - 1)
                {
                    latexBuilder.Append(" \\\\ ");
                }
            }
        }

        // End matrix environment
        if (isPiecewise)
        {
            latexBuilder.Append("\\end{cases}");
        }
        else
        {
            latexBuilder.Append("\\end{pmatrix}");
        }

        // Post-process the LaTeX to improve formatting
        var result = latexBuilder.ToString();
        result = PostProcessLatexFormatting(result, isPiecewise);

        // Replace the content in the builder
        latexBuilder.Clear();
        latexBuilder.Append(result);

        _logger?.LogDebug($"Matrix processing complete, result length: {latexBuilder.Length}");
    }

    private bool IsPiecewiseFunction(M.Matrix matrix)
    {
        // Check if this matrix represents a piecewise function
        // Look for text patterns that suggest conditions
        var matrixText = matrix.InnerText?.ToLower() ?? "";

        _logger?.LogDebug($"Checking if matrix is piecewise function. Text: '{matrixText}'");

        // Check for condition indicators
        bool hasConditions = matrixText.Contains("if") ||
                           matrixText.Contains("when") ||
                           matrixText.Contains("otherwise") ||
                           matrixText.Contains("else") ||
                           matrixText.Contains("条件") ||
                           matrixText.Contains("如果") ||
                           matrixText.Contains("否则");

        // Check for comparison operators that suggest conditions
        bool hasComparisons = matrixText.Contains("≤") ||
                            matrixText.Contains("≥") ||
                            matrixText.Contains("<=") ||
                            matrixText.Contains(">=") ||
                            matrixText.Contains("<") ||
                            matrixText.Contains(">") ||
                            matrixText.Contains("=");

        // Check for multiple expressions separated by conditions
        var rows = matrix.Elements<M.MatrixRow>().ToList();
        bool hasMultipleRows = rows.Count > 1;

        // If we have multiple rows with conditions or comparisons, it's likely a piecewise function
        bool isPiecewise = hasConditions || (hasComparisons && hasMultipleRows);

        _logger?.LogDebug($"Piecewise detection: hasConditions={hasConditions}, hasComparisons={hasComparisons}, hasMultipleRows={hasMultipleRows}, result={isPiecewise}");

        return isPiecewise;
    }

    private void ExtractCellContent(M.MatrixColumn cell, StringBuilder latexBuilder)
    {
        var startLength = latexBuilder.Length;
        _logger?.LogDebug($"ExtractCellContent: Starting extraction, cell InnerText: '{cell.InnerText}'");

        // Method 1: Process all child elements recursively
        foreach (var element in cell.Elements())
        {
            _logger?.LogDebug($"Processing cell child element: {element.GetType().Name}, LocalName: {element.LocalName}");

            // Try direct processing first
            ProcessMathElement(element, latexBuilder);

            // If still no content, try alternative processing
            if (latexBuilder.Length == startLength)
            {
                ProcessElementAlternatively(element, latexBuilder);
            }
        }

        // Method 2: If no content extracted, try direct text extraction
        if (latexBuilder.Length == startLength)
        {
            var cellText = cell.InnerText?.Trim();
            _logger?.LogDebug($"No content from children, trying direct text: '{cellText}'");
            if (!string.IsNullOrEmpty(cellText))
            {
                latexBuilder.Append(cellText);
            }
        }

        // Method 3: Try to extract from all descendants
        if (latexBuilder.Length == startLength)
        {
            _logger?.LogDebug("Still no content, trying all descendants");

            // Try all text elements
            var textElements = cell.Descendants<M.Text>();
            foreach (var textElement in textElements)
            {
                var text = textElement.InnerText?.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    _logger?.LogDebug($"Found text in M.Text: '{text}'");
                    latexBuilder.Append(text);
                }
            }

            // Try regular text elements
            if (latexBuilder.Length == startLength)
            {
                var regularTexts = cell.Descendants<DocumentFormat.OpenXml.Wordprocessing.Text>();
                foreach (var textElement in regularTexts)
                {
                    var text = textElement.Text?.Trim();
                    if (!string.IsNullOrEmpty(text))
                    {
                        _logger?.LogDebug($"Found text in regular Text: '{text}'");
                        latexBuilder.Append(text);
                    }
                }
            }

            // Try runs
            if (latexBuilder.Length == startLength)
            {
                var runs = cell.Descendants<M.Run>();
                foreach (var run in runs)
                {
                    var text = run.InnerText?.Trim();
                    if (!string.IsNullOrEmpty(text))
                    {
                        _logger?.LogDebug($"Found text in M.Run: '{text}'");
                        latexBuilder.Append(text);
                    }
                }
            }

            // Try any OpenXml element with text
            if (latexBuilder.Length == startLength)
            {
                var allElements = cell.Descendants();
                foreach (var element in allElements)
                {
                    if (element.HasChildren) continue; // Skip container elements

                    var text = element.InnerText?.Trim();
                    if (!string.IsNullOrEmpty(text))
                    {
                        _logger?.LogDebug($"Found text in {element.GetType().Name}: '{text}'");
                        latexBuilder.Append(text);
                        break; // Take the first non-empty text
                    }
                }
            }
        }

        var extractedLength = latexBuilder.Length - startLength;
        _logger?.LogDebug($"ExtractCellContent: Extracted {extractedLength} characters");
    }

    private void DumpMatrixXmlStructure(M.Matrix matrix)
    {
        try
        {
            _logger?.LogDebug("=== MATRIX XML STRUCTURE DUMP ===");
            _logger?.LogDebug($"Matrix OuterXml: {matrix.OuterXml}");
            _logger?.LogDebug($"Matrix InnerText: '{matrix.InnerText}'");

            var rows = matrix.Elements<M.MatrixRow>().ToList();
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                _logger?.LogDebug($"Row {i} XML: {row.OuterXml}");
                _logger?.LogDebug($"Row {i} InnerText: '{row.InnerText}'");

                var cells = row.Elements<M.MatrixColumn>().ToList();
                for (int j = 0; j < cells.Count; j++)
                {
                    var cell = cells[j];
                    _logger?.LogDebug($"  Cell {i},{j} XML: {cell.OuterXml}");
                    _logger?.LogDebug($"  Cell {i},{j} InnerText: '{cell.InnerText}'");

                    // Dump all child elements
                    foreach (var child in cell.Elements())
                    {
                        _logger?.LogDebug($"    Child: {child.GetType().Name} ({child.LocalName}) - '{child.InnerText}'");
                    }
                }
            }
            _logger?.LogDebug("=== END MATRIX XML DUMP ===");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error dumping matrix XML structure");
        }
    }

    private string ExtractAllContentFromMatrix(M.Matrix matrix)
    {
        var content = new StringBuilder();

        // Try multiple extraction strategies

        // Strategy 1: Direct InnerText
        var directText = matrix.InnerText?.Trim();
        if (!string.IsNullOrEmpty(directText))
        {
            content.Append(directText);
            return content.ToString();
        }

        // Strategy 2: Recursive element extraction
        ExtractAllContentFromElement(matrix, content);

        return content.ToString().Trim();
    }

    private string ExtractAllContentFromElement(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        var content = new StringBuilder();
        ExtractAllContentFromElement(element, content);
        return content.ToString().Trim();
    }

    private void ExtractAllContentFromElement(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder content)
    {
        // If this is a text-containing element, extract its text
        if (!element.HasChildren)
        {
            var text = element.InnerText?.Trim();
            if (!string.IsNullOrEmpty(text))
            {
                content.Append(text);
                return;
            }
        }

        // Process children recursively
        foreach (var child in element.Elements())
        {
            ExtractAllContentFromElement(child, content);
        }
    }

    private void ExtractCellContentComprehensive(M.MatrixColumn cell, StringBuilder latexBuilder)
    {
        var startLength = latexBuilder.Length;
        _logger?.LogDebug($"ExtractCellContentComprehensive: Starting, cell InnerText: '{cell.InnerText}'");

        // Strategy 1: Use existing ExtractCellContent
        ExtractCellContent(cell, latexBuilder);

        if (latexBuilder.Length > startLength)
        {
            _logger?.LogDebug("Strategy 1 (ExtractCellContent) succeeded");
            return;
        }

        // Strategy 2: Direct InnerText extraction
        var cellText = cell.InnerText?.Trim();
        if (!string.IsNullOrEmpty(cellText))
        {
            _logger?.LogDebug($"Strategy 2 (Direct InnerText) succeeded: '{cellText}'");
            latexBuilder.Append(cellText);
            return;
        }

        // Strategy 3: Comprehensive recursive extraction
        var allContent = ExtractAllContentFromElement(cell);
        if (!string.IsNullOrEmpty(allContent))
        {
            _logger?.LogDebug($"Strategy 3 (Recursive extraction) succeeded: '{allContent}'");
            latexBuilder.Append(allContent);
            return;
        }

        // Strategy 4: XML text extraction using regex
        var xmlContent = ExtractTextFromXml(cell.OuterXml);
        if (!string.IsNullOrEmpty(xmlContent))
        {
            _logger?.LogDebug($"Strategy 4 (XML regex) succeeded: '{xmlContent}'");
            latexBuilder.Append(xmlContent);
            return;
        }

        // Strategy 5: Brute force - extract any text from any descendant
        var bruteForceContent = BruteForceTextExtraction(cell);
        if (!string.IsNullOrEmpty(bruteForceContent))
        {
            _logger?.LogDebug($"Strategy 5 (Brute force) succeeded: '{bruteForceContent}'");
            latexBuilder.Append(bruteForceContent);
            return;
        }

        _logger?.LogError("ALL EXTRACTION STRATEGIES FAILED for cell");
    }

    private string ExtractTextFromXml(string xml)
    {
        try
        {
            var content = new StringBuilder();

            // Use regex to extract text between XML tags
            var textMatches = System.Text.RegularExpressions.Regex.Matches(xml, @">([^<]+)<");
            foreach (System.Text.RegularExpressions.Match match in textMatches)
            {
                var text = match.Groups[1].Value.Trim();
                if (!string.IsNullOrEmpty(text) &&
                    !text.StartsWith("m:") &&
                    !text.StartsWith("w:") &&
                    !text.All(char.IsWhiteSpace))
                {
                    content.Append(text);
                }
            }

            return content.ToString().Trim();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in ExtractTextFromXml");
            return string.Empty;
        }
    }

    private string BruteForceTextExtraction(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        try
        {
            var content = new StringBuilder();

            // Get all descendants and try to extract text from each
            var allDescendants = element.Descendants().ToList();
            _logger?.LogDebug($"BruteForceTextExtraction: Found {allDescendants.Count} descendants");

            foreach (var descendant in allDescendants)
            {
                // Try InnerText
                var text = descendant.InnerText?.Trim();
                if (!string.IsNullOrEmpty(text) && text.Length < 100) // Avoid huge text blocks
                {
                    content.Append(text);
                    _logger?.LogDebug($"BruteForce found text in {descendant.GetType().Name}: '{text}'");
                    break; // Take the first meaningful text we find
                }

                // Try specific properties for text elements
                if (descendant is M.Text mathText)
                {
                    var mathTextContent = mathText.Text?.Trim();
                    if (!string.IsNullOrEmpty(mathTextContent))
                    {
                        content.Append(mathTextContent);
                        _logger?.LogDebug($"BruteForce found M.Text: '{mathTextContent}'");
                        break;
                    }
                }

                if (descendant is DocumentFormat.OpenXml.Wordprocessing.Text wordText)
                {
                    var wordTextContent = wordText.Text?.Trim();
                    if (!string.IsNullOrEmpty(wordTextContent))
                    {
                        content.Append(wordTextContent);
                        _logger?.LogDebug($"BruteForce found Word.Text: '{wordTextContent}'");
                        break;
                    }
                }
            }

            return content.ToString().Trim();
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in BruteForceTextExtraction");
            return string.Empty;
        }
    }

    private string ExtractFromXmlStructure(M.MatrixColumn cell)
    {
        var content = new StringBuilder();

        try
        {
            // Method 1: Try to get all text content recursively
            ExtractAllTextRecursively(cell, content);

            if (content.Length == 0)
            {
                // Method 2: Try specific OpenXML math elements
                ExtractMathElementsRecursively(cell, content);
            }

            if (content.Length == 0)
            {
                // Method 3: Raw XML parsing as last resort
                var xmlString = cell.OuterXml;
                _logger?.LogDebug($"Raw XML for empty cell: {xmlString}");

                // Extract text between XML tags
                var textMatches = System.Text.RegularExpressions.Regex.Matches(xmlString, @">([^<]+)<");
                foreach (System.Text.RegularExpressions.Match match in textMatches)
                {
                    var text = match.Groups[1].Value.Trim();
                    if (!string.IsNullOrEmpty(text) && !text.StartsWith("m:"))
                    {
                        content.Append(text);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in ExtractFromXmlStructure");
        }

        return content.ToString().Trim();
    }

    private void ExtractAllTextRecursively(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder content)
    {
        // Extract text from this element if it's a leaf node
        if (!element.HasChildren)
        {
            var text = element.InnerText?.Trim();
            if (!string.IsNullOrEmpty(text))
            {
                content.Append(text);
                return;
            }
        }

        // Recursively process children
        foreach (var child in element.Elements())
        {
            ExtractAllTextRecursively(child, content);
        }
    }

    private void ExtractMathElementsRecursively(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder content)
    {
        var elementName = element.LocalName;

        switch (elementName)
        {
            case "t": // Text element
                var text = element.InnerText?.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    content.Append(text);
                }
                break;

            case "r": // Run element
                foreach (var child in element.Elements())
                {
                    ExtractMathElementsRecursively(child, content);
                }
                break;

            case "f": // Fraction
                var num = element.Elements().FirstOrDefault(e => e.LocalName == "num");
                var den = element.Elements().FirstOrDefault(e => e.LocalName == "den");

                if (num != null && den != null)
                {
                    content.Append("\\frac{");
                    ExtractMathElementsRecursively(num, content);
                    content.Append("}{");
                    ExtractMathElementsRecursively(den, content);
                    content.Append("}");
                }
                break;

            case "sSup": // Superscript
                var baseElem = element.Elements().FirstOrDefault(e => e.LocalName == "e");
                var supElem = element.Elements().FirstOrDefault(e => e.LocalName == "sup");

                if (baseElem != null)
                {
                    ExtractMathElementsRecursively(baseElem, content);
                }
                if (supElem != null)
                {
                    content.Append("^{");
                    ExtractMathElementsRecursively(supElem, content);
                    content.Append("}");
                }
                break;

            case "sSub": // Subscript
                var baseSubElem = element.Elements().FirstOrDefault(e => e.LocalName == "e");
                var subElem = element.Elements().FirstOrDefault(e => e.LocalName == "sub");

                if (baseSubElem != null)
                {
                    ExtractMathElementsRecursively(baseSubElem, content);
                }
                if (subElem != null)
                {
                    content.Append("_{");
                    ExtractMathElementsRecursively(subElem, content);
                    content.Append("}");
                }
                break;

            default:
                // For unknown elements, process children
                foreach (var child in element.Elements())
                {
                    ExtractMathElementsRecursively(child, content);
                }
                break;
        }
    }

    private void ProcessElementAlternatively(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder latexBuilder)
    {
        var startLength = latexBuilder.Length;

        // Try to extract content using element's LocalName
        var elementName = element.LocalName;
        _logger?.LogDebug($"ProcessElementAlternatively: {elementName}");

        switch (elementName)
        {
            case "r": // Math run
                ProcessMathRun(element, latexBuilder);
                break;

            case "t": // Text
                var text = element.InnerText?.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    latexBuilder.Append(text);
                }
                break;

            case "f": // Fraction
                ProcessFractionElement(element, latexBuilder);
                break;

            case "sSup": // Superscript
                ProcessSuperscriptElement(element, latexBuilder);
                break;

            case "sSub": // Subscript
                ProcessSubscriptElement(element, latexBuilder);
                break;

            default:
                // For unknown elements, try to extract any text content
                var allText = element.InnerText?.Trim();
                if (!string.IsNullOrEmpty(allText))
                {
                    latexBuilder.Append(allText);
                }
                else
                {
                    // Recursively process children
                    foreach (var child in element.Elements())
                    {
                        ProcessElementAlternatively(child, latexBuilder);
                    }
                }
                break;
        }

        var extractedLength = latexBuilder.Length - startLength;
        _logger?.LogDebug($"ProcessElementAlternatively extracted {extractedLength} characters");
    }

    private void ProcessMathRun(DocumentFormat.OpenXml.OpenXmlElement runElement, StringBuilder latexBuilder)
    {
        foreach (var child in runElement.Elements())
        {
            if (child.LocalName == "t")
            {
                var text = child.InnerText?.Trim();
                if (!string.IsNullOrEmpty(text))
                {
                    latexBuilder.Append(text);
                }
            }
            else
            {
                ProcessElementAlternatively(child, latexBuilder);
            }
        }
    }

    private void ProcessFractionElement(DocumentFormat.OpenXml.OpenXmlElement fracElement, StringBuilder latexBuilder)
    {
        var num = fracElement.Elements().FirstOrDefault(e => e.LocalName == "num");
        var den = fracElement.Elements().FirstOrDefault(e => e.LocalName == "den");

        if (num != null && den != null)
        {
            latexBuilder.Append("\\frac{");
            ProcessElementAlternatively(num, latexBuilder);
            latexBuilder.Append("}{");
            ProcessElementAlternatively(den, latexBuilder);
            latexBuilder.Append("}");
        }
    }

    private void ProcessSuperscriptElement(DocumentFormat.OpenXml.OpenXmlElement supElement, StringBuilder latexBuilder)
    {
        var baseElem = supElement.Elements().FirstOrDefault(e => e.LocalName == "e");
        var supElem = supElement.Elements().FirstOrDefault(e => e.LocalName == "sup");

        if (baseElem != null)
        {
            ProcessElementAlternatively(baseElem, latexBuilder);
        }
        if (supElem != null)
        {
            latexBuilder.Append("^{");
            ProcessElementAlternatively(supElem, latexBuilder);
            latexBuilder.Append("}");
        }
    }

    private void ProcessSubscriptElement(DocumentFormat.OpenXml.OpenXmlElement subElement, StringBuilder latexBuilder)
    {
        var baseElem = subElement.Elements().FirstOrDefault(e => e.LocalName == "e");
        var subElem = subElement.Elements().FirstOrDefault(e => e.LocalName == "sub");

        if (baseElem != null)
        {
            ProcessElementAlternatively(baseElem, latexBuilder);
        }
        if (subElem != null)
        {
            latexBuilder.Append("_{");
            ProcessElementAlternatively(subElem, latexBuilder);
            latexBuilder.Append("}");
        }
    }

    private void ProcessDelimiterElement(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder latexBuilder)
    {
        // Simplified delimiter processing - just process children
        // This handles most cases including potential piecewise functions
        foreach (var child in element.Elements())
        {
            ProcessMathElement(child, latexBuilder);
        }
    }

    private void ProcessPiecewiseContent(IEnumerable<DocumentFormat.OpenXml.OpenXmlElement> content, StringBuilder latexBuilder)
    {
        var rows = new List<string>();
        var currentRow = new StringBuilder();

        foreach (var element in content)
        {
            if (element.LocalName == "mr") // Matrix row
            {
                if (currentRow.Length > 0)
                {
                    rows.Add(currentRow.ToString());
                    currentRow.Clear();
                }

                var columns = element.Elements().Where(e => e.LocalName == "mc").ToList();
                for (int i = 0; i < columns.Count; i++)
                {
                    var column = columns[i];
                    foreach (var child in column.Elements())
                    {
                        ProcessMathElement(child, currentRow);
                    }

                    if (i < columns.Count - 1)
                    {
                        currentRow.Append(" & ");
                    }
                }
            }
            else
            {
                ProcessMathElement(element, currentRow);
            }
        }

        if (currentRow.Length > 0)
        {
            rows.Add(currentRow.ToString());
        }

        for (int i = 0; i < rows.Count; i++)
        {
            latexBuilder.Append(rows[i]);
            if (i < rows.Count - 1)
            {
                latexBuilder.Append(" \\\\ ");
            }
        }
    }

    private void ProcessMatrixElementByXml(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder latexBuilder)
    {
        _logger?.LogDebug("ProcessMatrixElementByXml called");

        // Check if this might be a piecewise function
        var elementText = element.InnerText?.ToLower() ?? "";
        bool isPiecewise = elementText.Contains("if") ||
                          elementText.Contains("otherwise") ||
                          elementText.Contains("when") ||
                          elementText.Contains("条件") ||
                          elementText.Contains("如果") ||
                          elementText.Contains("否则");

        if (isPiecewise)
        {
            _logger?.LogDebug("Detected piecewise function in XML matrix");
            latexBuilder.Append("\\begin{cases}");
        }
        else
        {
            latexBuilder.Append("\\begin{pmatrix}");
        }

        var rows = element.Elements().Where(e => e.LocalName == "mr").ToList();
        _logger?.LogDebug($"Found {rows.Count} matrix rows in XML");

        if (!rows.Any())
        {
            // No rows found, try to extract any content
            var allText = element.InnerText?.Trim();
            if (!string.IsNullOrEmpty(allText))
            {
                latexBuilder.Append(allText);
            }
            else
            {
                latexBuilder.Append(" ");
            }
        }
        else
        {
            for (int i = 0; i < rows.Count; i++)
            {
                var row = rows[i];
                var columns = row.Elements().Where(e => e.LocalName == "mc").ToList();

                if (!columns.Any())
                {
                    // No columns, try to get row content directly
                    var rowText = row.InnerText?.Trim();
                    if (!string.IsNullOrEmpty(rowText))
                    {
                        latexBuilder.Append(rowText);
                    }
                    else
                    {
                        latexBuilder.Append(" ");
                    }
                }
                else
                {
                    for (int j = 0; j < columns.Count; j++)
                    {
                        var column = columns[j];
                        var cellStartLength = latexBuilder.Length;

                        // Try multiple extraction methods
                        foreach (var child in column.Elements())
                        {
                            ProcessMathElement(child, latexBuilder);
                        }

                        // If no content extracted, try direct text
                        if (latexBuilder.Length == cellStartLength)
                        {
                            var columnText = column.InnerText?.Trim();
                            if (!string.IsNullOrEmpty(columnText))
                            {
                                latexBuilder.Append(columnText);
                            }
                            else
                            {
                                latexBuilder.Append(" ");
                            }
                        }

                        if (j < columns.Count - 1)
                        {
                            latexBuilder.Append(" & ");
                        }
                    }
                }

                if (i < rows.Count - 1)
                {
                    latexBuilder.Append(" \\\\ ");
                }
            }
        }

        if (isPiecewise)
        {
            latexBuilder.Append("\\end{cases}");
        }
        else
        {
            latexBuilder.Append("\\end{pmatrix}");
        }

        _logger?.LogDebug($"XML matrix processing complete");
    }

    private void ProcessMatrixRowElement(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder latexBuilder)
    {
        // Process matrix row content
        var columns = element.Elements().Where(e => e.LocalName == "mc").ToList();

        for (int i = 0; i < columns.Count; i++)
        {
            var column = columns[i];
            foreach (var child in column.Elements())
            {
                ProcessMathElement(child, latexBuilder);
            }

            if (i < columns.Count - 1)
            {
                latexBuilder.Append(" & ");
            }
        }
    }

    private void ProcessMatrixColumnElement(DocumentFormat.OpenXml.OpenXmlElement element, StringBuilder latexBuilder)
    {
        // Process matrix column content
        foreach (var child in element.Elements())
        {
            ProcessMathElement(child, latexBuilder);
        }
    }
    
    private void ProcessNaryToMathML(Nary nary, StringBuilder mathMLBuilder)
    {
        // Handle summation, product, integral, etc.
        var naryProps = nary.GetFirstChild<NaryProperties>();
        var naryChar = naryProps?.GetFirstChild<M.Text>();
        var naryOperator = naryChar?.InnerText ?? "∑";

        // Start the nary element
        mathMLBuilder.Append("<mrow>");

        // Add the operator
        mathMLBuilder.Append($"<mo>{XmlEscape(naryOperator)}</mo>");

        // Process subscript (lower limit)
        var subScript = nary.GetFirstChild<Subscript>();
        if (subScript != null)
        {
            mathMLBuilder.Append("<munder>");
            mathMLBuilder.Append($"<mo>{XmlEscape(naryOperator)}</mo>");
            mathMLBuilder.Append("<mrow>");
            foreach (var element in subScript.Elements())
            {
                ProcessMathElementToMathML(element, mathMLBuilder);
            }
            mathMLBuilder.Append("</mrow>");
            mathMLBuilder.Append("</munder>");
        }

        // Process superscript (upper limit)
        var superScript = nary.GetFirstChild<Superscript>();
        if (superScript != null)
        {
            mathMLBuilder.Append("<mover>");
            mathMLBuilder.Append($"<mo>{XmlEscape(naryOperator)}</mo>");
            mathMLBuilder.Append("<mrow>");
            foreach (var element in superScript.Elements())
            {
                ProcessMathElementToMathML(element, mathMLBuilder);
            }
            mathMLBuilder.Append("</mrow>");
            mathMLBuilder.Append("</mover>");
        }

        // Process base (the expression being summed/integrated)
        var baseElement = nary.GetFirstChild<Base>();
        if (baseElement != null)
        {
            mathMLBuilder.Append("<mrow>");
            foreach (var element in baseElement.Elements())
            {
                ProcessMathElementToMathML(element, mathMLBuilder);
            }
            mathMLBuilder.Append("</mrow>");
        }

        mathMLBuilder.Append("</mrow>");
    }
    
    private void ProcessDelimiterToMathML(Delimiter delimiter, StringBuilder mathMLBuilder)
    {
        // Get opening and closing delimiters
        var delimiterProps = delimiter.GetFirstChild<DelimiterProperties>();
        var beginChar = delimiterProps?.GetFirstChild<BeginChar>();
        var endChar = delimiterProps?.GetFirstChild<EndChar>();

        var openingDelimiter = beginChar?.InnerText ?? "(";
        var closingDelimiter = endChar?.InnerText ?? ")";

        // Start the delimiter element
        mathMLBuilder.Append("<mfenced>");
        mathMLBuilder.Append($"<mo>{XmlEscape(openingDelimiter)}</mo>");

        // Process the content inside the delimiters
        mathMLBuilder.Append("<mrow>");
        foreach (var child in delimiter.Elements())
        {
            ProcessMathElementToMathML(child, mathMLBuilder);
        }
        mathMLBuilder.Append("</mrow>");

        mathMLBuilder.Append($"<mo>{XmlEscape(closingDelimiter)}</mo>");
        mathMLBuilder.Append("</mfenced>");
    }
    
    private void ProcessMatrixToMathML(Matrix matrix, StringBuilder mathMLBuilder)
    {
        // Start matrix element
        mathMLBuilder.Append("<mtable>");
        
        // Process each row
        foreach (var row in matrix.Elements<MatrixRow>())
        {
            mathMLBuilder.Append("<mtr>");
            
            // Process each cell in the row
            foreach (var cell in row.Elements<MatrixColumn>())
            {
                mathMLBuilder.Append("<mtd>");
                
                // Process cell content
                foreach (var element in cell.Elements())
                {
                    ProcessMathElementToMathML(element, mathMLBuilder);
                }
                
                mathMLBuilder.Append("</mtd>");
            }
            
            mathMLBuilder.Append("</mtr>");
        }
        
        // End matrix element
        mathMLBuilder.Append("</mtable>");
    }

    private string PostProcessLatexFormatting(string latex, bool isPiecewise)
    {
        if (string.IsNullOrEmpty(latex))
            return latex;

        var result = latex;
        _logger?.LogDebug($"PostProcessLatexFormatting input (isPiecewise={isPiecewise}): {latex}");
        _logger?.LogDebug($"Input contains ' \\\\\\\\ ': {latex.Contains(@" \\ ")}");

        try
        {
            // Fix common formatting issues in mathematical expressions

            // 1. Fix mathematical function spacing - MUST be done before other replacements
            // Fix \timesmin and \timesmax patterns (missing backslash cases)
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\timesmin\b",
                "\\times min");
            _logger?.LogDebug($"After \\timesmin fix: {result}");

            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\timesmax\b",
                "\\times max");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\btimesmin\b",
                "\\times min");
            _logger?.LogDebug($"After timesmin fix: {result}");

            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\btimesmax\b",
                "\\times max");

            // Fix properly spaced \times with functions
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\times\s*min\b",
                "\\times min");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\times\s*max\b",
                "\\times max");

            // 2. Fix subscripts - convert patterns like "Cideal" to "C_{ideal}"
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"([A-Z])([a-z]+)",
                @"$1_{$2}");

            // 3. Fix fractions - comprehensive fraction handling
            result = ProcessFractionExpressions(result);

            // 4. Fix exponents - convert patterns like ")1.5" to ")^{1.5}"
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\)([0-9]+\.?[0-9]*)",
                @")^{$1}");

            // 5. Fix multiplication signs - convert × to \times with proper spacing
            result = result.Replace("×", "\\times ");

            // Fix standalone times that might be missing backslash (but not if already has backslash)
            // Use a more specific approach: only replace "times" that are not preceded by "\"
            _logger?.LogDebug($"Before standalone times fix: {result}");

            // First, temporarily mark existing \times to protect them
            result = result.Replace("\\times", "PROTECTED_TIMES");

            // Then fix standalone times
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\btimes\b",
                "\\times ");

            // Finally, restore the protected ones
            result = result.Replace("PROTECTED_TIMES", "\\times");

            _logger?.LogDebug($"After standalone times fix: {result}");

            // 6. Fix comparison operators with proper spacing
            result = result.Replace("≤", " \\leq ");
            result = result.Replace("≥", " \\geq ");
            result = result.Replace("≠", " \\neq ");

            // Clean up excessive spaces that might be created
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\s+",
                @" ");

            // 7. Fix common mathematical functions - but be careful with context
            // Only convert min/max to LaTeX commands when they appear as standalone functions
            // NOT when they appear after \times (which should remain as plain text)
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"(?<!\\times\s)\bmin\b(?!\s*\()",
                "\\\\min");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"(?<!\\times\s)\bmax\b(?!\s*\()",
                "\\\\max");

            // 8. Fix spacing around operators
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"([a-zA-Z0-9})])([+\-=><])([a-zA-Z0-9{(])",
                @"$1 $2 $3");
            // Additional pass for > and < operators specifically
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"([a-zA-Z0-9})])>([a-zA-Z0-9{(])",
                @"$1 > $2");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"([a-zA-Z0-9})])<([a-zA-Z0-9{(])",
                @"$1 < $2");

            // 9. Clean up excessive spaces and fix common spacing issues
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\s{2,}",
                " ");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\times\s{2,}",
                "\\times ");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\leq\s{2,}",
                "\\leq ");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\geq\s{2,}",
                "\\geq ");

            // Clean up double-nested \text{} patterns - multiple passes to handle complex nesting
            for (int i = 0; i < 3; i++)
            {
                result = System.Text.RegularExpressions.Regex.Replace(result,
                    @"\\text\{\\text\{([^}]+)\}\s*\}",
                    "\\text{$1}");
                result = System.Text.RegularExpressions.Regex.Replace(result,
                    @"\\text\{\\text\{([^}]+)\}",
                    "\\text{$1}");
            }

            // 9. For piecewise functions, ensure proper formatting - MUST be done BEFORE backslash cleanup
            if (isPiecewise)
            {
                result = FixPiecewiseFunctionFormatting(result);
            }

            // 10. Fix excessive backslashes that might be created - BUT preserve \\ for line breaks in piecewise functions
            // Only clean up cases where we have 3 or more backslashes, preserving exactly 2 backslashes (\\)
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\{3,}",
                "\\\\");

        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in PostProcessLatexFormatting");
            return latex; // Return original if processing fails
        }

        return result;
    }

    private string ProcessFractionExpressions(string input)
    {
        var result = input;

        try
        {
            _logger?.LogDebug($"ProcessFractionExpressions 开始处理: '{input}'");
            // Process fractions using a comprehensive approach
            // This handles various fraction patterns including:
            // - Simple variables: a/b
            // - Subscripted variables: C_{ideal}/cap(r)
            // - Parenthesized expressions: (x+y)/(z-w)
            // - Function calls: f(x)/g(y)
            // - Complex expressions: x^2+1/y^2-1

            // Define multiple patterns for different types of fractions
            // Order matters: more specific patterns first, then general ones
            var fractionPatterns = new[]
            {
                // Pattern 1: Both numerator and denominator in parentheses like (x+y)/(z-w)
                @"\(([^)]+)\)/\(([^)]+)\)",

                // Pattern 2: Numerator with parentheses, denominator without like (x+y)/z
                @"\(([^)]+)\)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*)",

                // Pattern 3: Complex denominator with parentheses like 1000/(1 + 1.5N_{conflict})
                // This is the key pattern that was missing!
                @"([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*|[0-9]+(?:\.[0-9]+)*)/\(([^)]+)\)",

                // Pattern 4: Function calls like f(x)/g(y)
                @"([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))/([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))",

                // Pattern 5: Simple variables with subscripts/superscripts like C_{ideal}/cap(r)
                // Stop at common delimiters like comma, ampersand, space
                @"([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\^[^/\s,&]*)*)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*(?:\^[^/\s,&]*)*?)(?=[,&\s]|$)",

                // Pattern 6: Simple variables like a/b
                @"([a-zA-Z_][a-zA-Z0-9_]*)/([a-zA-Z_][a-zA-Z0-9_]*)"
            };

            // Apply each pattern to handle different types of fractions
            for (int patternIndex = 0; patternIndex < fractionPatterns.Length; patternIndex++)
            {
                var pattern = fractionPatterns[patternIndex];
                _logger?.LogDebug($"尝试模式 {patternIndex + 1}: {pattern}");

                int maxIterations = 5; // Prevent infinite loops for each pattern
                int iteration = 0;

                while (System.Text.RegularExpressions.Regex.IsMatch(result, pattern) && iteration < maxIterations)
                {
                    var previousResult = result;
                    result = System.Text.RegularExpressions.Regex.Replace(result, pattern, @"\\frac{$1}{$2}");

                    _logger?.LogDebug($"模式 {patternIndex + 1} 匹配成功，替换: '{previousResult}' -> '{result}'");

                    // If no change occurred, break to prevent infinite loop
                    if (result == previousResult)
                        break;

                    iteration++;
                }
            }

            // Handle edge cases and clean up
            result = CleanupFractionFormatting(result);

            _logger?.LogDebug($"ProcessFractionExpressions 完成处理: '{input}' -> '{result}'");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error processing fraction expressions");
            return input; // Return original if processing fails
        }

        return result;
    }

    private string CleanupFractionFormatting(string input)
    {
        var result = input;

        try
        {
            // Clean up any malformed fractions or edge cases

            // Fix double fractions that might have been created
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\frac\{\\frac\{([^}]+)\}\{([^}]+)\}\}\{([^}]+)\}",
                @"\\frac{\\frac{$1}{$2}}{$3}");

            // Fix fractions with empty numerator or denominator
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\frac\{\}\{([^}]+)\}",
                "$1");
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\\frac\{([^}]+)\}\{\}",
                "$1");

            // Clean up extra spaces around fractions
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\s*\\frac\s*",
                "\\frac");
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error cleaning up fraction formatting");
            return input;
        }

        return result;
    }

    private string FixPiecewiseFunctionFormatting(string latex)
    {
        try
        {
            _logger?.LogDebug($"FixPiecewiseFunctionFormatting input: {latex}");
            _logger?.LogDebug($"Input contains ' \\\\\\\\ ': {latex.Contains(@" \\ ")}");

            // First, fix common issues in the entire latex string
            var result = latex;

            // Fix broken condition expressions that got split incorrectly
            // Pattern: ",ifT_" should be ", & \text{if } T_"
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @",\s*if\s*([A-Z])",
                ", & \\text{if } $1",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // Fix "otherwise" keyword
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"\b(otherwise)\b",
                "\\text{$1}",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // Fix broken conditions that span across & separator
            // Pattern: "ifT_{consec}(t, & d(s))>0" should be "if T_{consec}(t,d(s))>0"
            result = System.Text.RegularExpressions.Regex.Replace(result,
                @"if\s*([A-Z_{}]+)\([^,]+,\s*&\s*([^)]+)\)([><=]+[^\\]+)",
                "\\text{if } $1($2)$3",
                System.Text.RegularExpressions.RegexOptions.IgnoreCase);

            // Split by \\ to get individual cases
            // First try to split by the exact pattern " \\ "
            var cases = result.Split(new[] { @" \\ " }, StringSplitOptions.RemoveEmptyEntries);

            // If that doesn't work, try splitting by single backslash with spaces
            if (cases.Length == 1 && result.Contains(@" \ "))
            {
                cases = result.Split(new[] { @" \ " }, StringSplitOptions.RemoveEmptyEntries);
            }
            var fixedCases = new List<string>();

            foreach (var caseStr in cases)
            {
                var trimmedCase = caseStr.Trim();
                if (string.IsNullOrEmpty(trimmedCase))
                    continue;

                _logger?.LogDebug($"Processing case: {trimmedCase}");

                var fixedCase = FixSingleCase(trimmedCase);
                fixedCases.Add(fixedCase);

                _logger?.LogDebug($"Fixed case: {fixedCase}");
            }

            var finalResult = string.Join(@" \\ ", fixedCases);
            _logger?.LogDebug($"FixPiecewiseFunctionFormatting output: {finalResult}");

            return finalResult;
        }
        catch (Exception ex)
        {
            _logger?.LogError(ex, "Error in FixPiecewiseFunctionFormatting");
            return latex;
        }
    }

    private string FixSingleCase(string caseStr)
    {
        // If already properly formatted with &, just clean it up
        if (caseStr.Contains(" & "))
        {
            var parts = caseStr.Split(new[] { " & " }, 2, StringSplitOptions.None);
            if (parts.Length == 2)
            {
                var expression = parts[0].Trim();
                var condition = parts[1].Trim();

                // Clean up the condition part
                condition = CleanConditionExpression(condition);

                return $"{expression} & {condition}";
            }
        }

        // Try to find and fix comma-separated expression and condition
        var lastCommaIndex = FindLastMeaningfulComma(caseStr);
        if (lastCommaIndex > 0)
        {
            var expression = caseStr.Substring(0, lastCommaIndex).Trim();
            var condition = caseStr.Substring(lastCommaIndex + 1).Trim();

            // Check if condition looks like a mathematical condition
            if (IsConditionExpression(condition))
            {
                condition = CleanConditionExpression(condition);
                return $"{expression}, & {condition}";
            }
        }

        // Try to split by "if" keyword
        var ifMatch = System.Text.RegularExpressions.Regex.Match(caseStr,
            @"^(.*?),?\s*(if\s+.*)$",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        if (ifMatch.Success)
        {
            var expression = ifMatch.Groups[1].Value.Trim().TrimEnd(',');
            var condition = ifMatch.Groups[2].Value.Trim();
            condition = CleanConditionExpression(condition);
            return $"{expression}, & {condition}";
        }

        // If no clear separation found, check if it's just a condition
        if (IsConditionExpression(caseStr))
        {
            var cleanCondition = CleanConditionExpression(caseStr);
            return cleanCondition;
        }

        // Return as is if no pattern matches
        return caseStr;
    }

    private string CleanConditionExpression(string condition)
    {
        var result = condition;

        // First, remove any existing \text{} wrappers to avoid double-wrapping
        result = System.Text.RegularExpressions.Regex.Replace(result,
            @"\\text\{([^}]+)\}",
            "$1");

        // Wrap "if" in \text{}
        result = System.Text.RegularExpressions.Regex.Replace(result,
            @"\bif\b",
            "\\text{if }",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        // Wrap "otherwise" in \text{}
        result = System.Text.RegularExpressions.Regex.Replace(result,
            @"\botherwise\b",
            "\\text{otherwise}",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        // Wrap "when" in \text{}
        result = System.Text.RegularExpressions.Regex.Replace(result,
            @"\bwhen\b",
            "\\text{when }",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        // Wrap "else" in \text{}
        result = System.Text.RegularExpressions.Regex.Replace(result,
            @"\belse\b",
            "\\text{else}",
            System.Text.RegularExpressions.RegexOptions.IgnoreCase);

        return result.Trim();
    }

    private int FindLastMeaningfulComma(string text)
    {
        // Find the last comma that's not inside parentheses or braces
        int parenDepth = 0;
        int braceDepth = 0;
        int lastCommaIndex = -1;

        for (int i = text.Length - 1; i >= 0; i--)
        {
            char c = text[i];

            switch (c)
            {
                case ')':
                    parenDepth++;
                    break;
                case '(':
                    parenDepth--;
                    break;
                case '}':
                    braceDepth++;
                    break;
                case '{':
                    braceDepth--;
                    break;
                case ',':
                    if (parenDepth == 0 && braceDepth == 0)
                    {
                        // Check if this comma is followed by a condition-like expression
                        var afterComma = text.Substring(i + 1).Trim();
                        if (IsConditionExpression(afterComma) ||
                            afterComma.StartsWith("if", StringComparison.OrdinalIgnoreCase) ||
                            afterComma.StartsWith("otherwise", StringComparison.OrdinalIgnoreCase))
                        {
                            return i;
                        }
                    }
                    break;
            }
        }

        return lastCommaIndex;
    }

    private bool IsConditionExpression(string text)
    {
        if (string.IsNullOrEmpty(text))
            return false;

        // Check for condition keywords and operators
        var conditionIndicators = new[] { "if", "when", "otherwise", "else", "<", ">", "≤", "≥", "=", "≠" };

        return conditionIndicators.Any(indicator =>
            text.IndexOf(indicator, StringComparison.OrdinalIgnoreCase) >= 0);
    }
}
