using DocxToMarkdownConverter.Exceptions;
using System.IO;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 错误恢复服务接口
/// </summary>
public interface IErrorRecoveryService
{
    /// <summary>
    /// 执行带重试机制的操作
    /// </summary>
    Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation, RetryPolicy? policy = null);

    /// <summary>
    /// 执行带重试机制的操作（无返回值）
    /// </summary>
    Task ExecuteWithRetryAsync(Func<Task> operation, RetryPolicy? policy = null);

    /// <summary>
    /// 尝试恢复文件操作
    /// </summary>
    Task<bool> TryRecoverFileOperationAsync(string filePath, FileOperationType operationType);

    /// <summary>
    /// 尝试恢复转换操作
    /// </summary>
    Task<bool> TryRecoverConversionAsync(string filePath, ConversionErrorType errorType);

    /// <summary>
    /// 获取错误的建议解决方案
    /// </summary>
    IEnumerable<string> GetRecoverySuggestions(Exception exception);

    /// <summary>
    /// 检查错误是否可以自动恢复
    /// </summary>
    bool CanAutoRecover(Exception exception);
}

/// <summary>
/// 重试策略
/// </summary>
public class RetryPolicy
{
    public int MaxRetries { get; set; } = 3;
    public TimeSpan InitialDelay { get; set; } = TimeSpan.FromSeconds(1);
    public TimeSpan MaxDelay { get; set; } = TimeSpan.FromSeconds(30);
    public double BackoffMultiplier { get; set; } = 2.0;
    public bool UseJitter { get; set; } = true;
    public Func<Exception, bool>? ShouldRetry { get; set; }

    public static RetryPolicy Default => new();

    public static RetryPolicy FileOperations => new()
    {
        MaxRetries = 5,
        InitialDelay = TimeSpan.FromMilliseconds(500),
        MaxDelay = TimeSpan.FromSeconds(10),
        BackoffMultiplier = 1.5,
        ShouldRetry = ex => ex is IOException || ex is UnauthorizedAccessException
    };

    public static RetryPolicy NetworkOperations => new()
    {
        MaxRetries = 3,
        InitialDelay = TimeSpan.FromSeconds(2),
        MaxDelay = TimeSpan.FromSeconds(60),
        BackoffMultiplier = 2.0,
        ShouldRetry = ex => ex is NetworkException || ex is TimeoutException
    };

    public static RetryPolicy ConversionOperations => new()
    {
        MaxRetries = 2,
        InitialDelay = TimeSpan.FromSeconds(1),
        MaxDelay = TimeSpan.FromSeconds(15),
        BackoffMultiplier = 2.0,
        ShouldRetry = ex => ex is ConversionException ce && 
            (ce.ErrorType == ConversionErrorType.ProcessingError || 
             ce.ErrorType == ConversionErrorType.MemoryError)
    };
}

/// <summary>
/// 恢复操作结果
/// </summary>
public class RecoveryResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public Exception? Exception { get; set; }
    public IEnumerable<string> Suggestions { get; set; } = Enumerable.Empty<string>();
    public bool RequiresUserAction { get; set; }
}
