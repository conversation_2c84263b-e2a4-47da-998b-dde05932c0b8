/**
 * 朱雀AI检测对抗优化器
 * 基于朱雀AI检测系统的四大维度设计针对性优化策略
 *
 * 核心原理：
 * 1. 困惑度操控：增加文本的困惑度，使其更接近人类写作
 * 2. 结构化特征消除：打破AI生成的典型结构模式
 * 3. 语义一致性调节：增加词汇多样性，降低主题过度一致性
 * 4. 频域特征优化：调整字符频率分布，模拟人类写作特征
 */

class ZhuqueOptimizer {
    constructor() {
        // 朱雀检测的关键阈值（基于研究分析）- 更严格的标准
        this.thresholds = {
            perplexity: {
                target: 120,     // 提高目标困惑度
                aiRange: [10, 50],
                humanRange: [80, 150] // 扩大人类文本范围
            },
            structural: {
                maxPatterns: 0,   // 完全消除结构化模式
                consistencyLimit: 0.4 // 降低一致性上限
            },
            semantic: {
                minLexicalDiversity: 0.75, // 提高词汇多样性要求
                maxTopicConsistency: 0.6   // 降低主题一致性上限
            },
            frequency: {
                maxTopCharRatio: 0.55,     // 降低高频字符比例
                minEntropy: 4.8            // 提高最小熵值
            }
        };

        // 增强的优化强度配置
        this.aggressivenessConfig = {
            low: {
                structuralIntensity: 0.6,
                semanticIntensity: 0.5,
                perplexityIntensity: 0.4,
                frequencyIntensity: 0.3
            },
            medium: {
                structuralIntensity: 0.8,
                semanticIntensity: 0.7,
                perplexityIntensity: 0.6,
                frequencyIntensity: 0.5
            },
            high: {
                structuralIntensity: 1.0,
                semanticIntensity: 0.9,
                perplexityIntensity: 0.8,
                frequencyIntensity: 0.7
            }
        };

        // AI生成文本的典型特征模式 - 大幅扩展
        this.aiPatterns = {
            // 结构化表达模式
            structuralMarkers: [
                /首先[，,].*?其次[，,].*?最后[，,]/g,
                /一方面[，,].*?另一方面[，,]/g,
                /第[一二三四五六七八九十]+[，,]/g,
                /\d+[\.、]/g,
                /总之[，,]|综上所述[，,]|综合来看[，,]/g,
                /在.*?方面[，,]/g,
                /从.*?角度[，,]/g,
                /根据.*?可以[，,]/g,
                /通过.*?我们[，,]/g,
                /基于.*?分析[，,]/g
            ],

            // AI签名短语 - 大幅扩展
            aiSignatures: [
                '显而易见', '毫无疑问', '不可否认', '值得注意的是',
                '需要指出的是', '可以看出', '由此可见', '综合分析',
                '基于以上分析', '通过研究发现', '实验表明', '数据显示',
                '研究表明', '调查显示', '统计数据表明', '事实证明',
                '众所周知', '不言而喻', '理所当然', '无庸置疑',
                '据统计', '根据调查', '相关研究指出', '专家认为',
                '学者指出', '业内人士表示', '权威机构发布',
                '最新研究发现', '科学研究证实', '大量数据表明'
            ],

            // 过度正式的连接词 - 扩展
            formalConnectors: [
                '因此', '所以', '由此', '故而', '从而', '进而',
                '与此同时', '此外', '另外', '除此之外', '不仅如此',
                '同时', '然而', '但是', '不过', '尽管如此',
                '换言之', '也就是说', '具体来说', '更进一步',
                '综合而言', '总体而言', '整体来看', '从整体上'
            ],

            // AI常用的学术表达
            academicExpressions: [
                '具有重要意义', '发挥重要作用', '产生深远影响',
                '取得显著成效', '呈现良好态势', '实现跨越式发展',
                '推动高质量发展', '促进可持续发展', '构建新发展格局',
                '在...领域', '在...过程中', '在...背景下',
                '随着...的发展', '伴随着...的进步', '在...推动下'
            ]
        };

        // 人类化替换词库 - 大幅扩展
        this.humanizationDict = {
            // 结构化词汇的人性化替换
            structuralReplacements: {
                '首先': ['开始时', '一开始', '起初', '最初', '刚开始', '先说', '第一点'],
                '其次': ['接着', '然后', '紧接着', '随后', '之后', '另外', '还有'],
                '最后': ['最终', '到最后', '结束时', '最后面', '末尾', '总结一下'],
                '总之': ['总的来说', '整体而言', '大体上', '基本上', '简单说', '归根结底'],
                '因此': ['所以', '这样一来', '这么看来', '由此', '这就', '这样'],
                '显而易见': ['很明显', '看得出来', '不难发现', '容易看出', '明摆着', '一目了然'],
                '毫无疑问': ['肯定是', '当然', '必然', '绝对', '无疑', '确定无疑'],
                '值得注意的是': ['要注意', '需要留意', '有意思的是', '关键是', '重要的是', '特别是'],
                '众所周知': ['大家都知道', '人人皆知', '常识告诉我们', '我们都清楚'],
                '不言而喻': ['不用说', '很清楚', '明显', '自然而然'],
                '据统计': ['数据显示', '调查发现', '统计结果', '相关数据'],
                '研究表明': ['研究发现', '调查显示', '实验证明', '分析结果'],
                '专家认为': ['专家表示', '学者指出', '权威人士说', '业内人士认为']
            },

            // 同义词替换库 - 大幅扩展
            synonyms: {
                '研究': ['调查', '探索', '分析', '考察', '观察', '探讨', '钻研', '研讨'],
                '发现': ['找到', '注意到', '观察到', '意识到', '察觉', '发觉', '看出', '识别'],
                '表明': ['说明', '显示', '证明', '反映', '揭示', '体现', '展现', '表示'],
                '提高': ['增强', '改善', '优化', '加强', '提升', '改进', '完善', '强化'],
                '重要': ['关键', '核心', '主要', '重点', '要紧', '关键性', '核心性', '主导'],
                '方法': ['方式', '途径', '手段', '办法', '策略', '措施', '路径', '技巧'],
                '问题': ['难题', '困难', '挑战', '障碍', '麻烦', '困扰', '疑问', '课题'],
                '结果': ['成果', '效果', '产出', '收获', '后果', '成效', '结局', '产物'],
                '影响': ['作用', '效应', '冲击', '效果', '影响力', '感化', '触动'],
                '发展': ['进步', '发展', '成长', '壮大', '演进', '推进', '前进'],
                '技术': ['科技', '工艺', '技能', '技巧', '手段', '方法', '工具'],
                '应用': ['运用', '使用', '采用', '利用', '实施', '执行', '操作'],
                '系统': ['体系', '机制', '框架', '结构', '制度', '模式', '平台'],
                '分析': ['解析', '剖析', '研究', '探讨', '审视', '考察', '检视'],
                '实现': ['达成', '完成', '做到', '获得', '取得', '成就', '达到'],
                '建设': ['构建', '建立', '创建', '搭建', '打造', '建造', '设立'],
                '管理': ['治理', '管控', '监管', '掌控', '操控', '调控', '统筹'],
                '创新': ['革新', '改革', '变革', '突破', '开拓', '探索', '发明'],
                '优化': ['改进', '完善', '提升', '强化', '改善', '精进', '升级'],
                '效率': ['效能', '效果', '成效', '功效', '效用', '作用', '绩效']
            },

            // 口语化替换
            colloquialReplacements: {
                '进行': ['做', '搞', '弄', '开展'],
                '实施': ['执行', '做', '搞', '推行'],
                '开展': ['搞', '做', '进行', '展开'],
                '推进': ['推动', '促进', '加快', '推行'],
                '促进': ['推动', '加快', '助力', '带动'],
                '构建': ['建立', '搭建', '打造', '建设'],
                '完善': ['改进', '优化', '提升', '加强'],
                '提升': ['提高', '改善', '增强', '加强']
            }
        };

        // 人类写作风格特征 - 大幅扩展
        this.humanWritingFeatures = {
            // 口语化表达
            colloquialExpressions: [
                '说实话', '老实说', '坦白讲', '不瞒你说', '实际上',
                '其实', '说起来', '想想看', '你看', '比如说',
                '举个例子', '打个比方', '换句话说', '简单来说',
                '说白了', '直白点说', '通俗点讲', '用大白话说',
                '这么说吧', '怎么说呢', '说来话长', '长话短说',
                '话说回来', '说到这里', '顺便说一下', '插一句',
                '不过话说回来', '换个角度看', '从另一面看'
            ],

            // 情感化词汇
            emotionalWords: [
                '有趣', '令人惊讶', '让人印象深刻', '相当不错', '挺好的',
                '确实', '的确', '真的', '非常', '特别', '尤其', '格外',
                '挺有意思', '蛮不错的', '相当厉害', '还挺好',
                '真心不错', '确实很棒', '相当给力', '挺靠谱',
                '还真是', '确实如此', '真没想到', '挺意外的'
            ],

            // 不确定性表达
            uncertaintyExpressions: [
                '可能', '也许', '大概', '估计', '应该', '似乎',
                '看起来', '感觉上', '个人认为', '我觉得', '在我看来',
                '或许', '大约', '差不多', '基本上', '多半',
                '十有八九', '八成', '很可能', '有可能',
                '我想', '我猜', '我估计', '我感觉', '我认为',
                '据我所知', '就我了解', '从我的经验看'
            ],

            // 个人化表达
            personalExpressions: [
                '我发现', '我注意到', '我观察到', '我意识到',
                '我的理解是', '我的看法是', '我的观点是',
                '在我的经验中', '根据我的观察', '从我的角度',
                '我个人觉得', '我个人认为', '我个人的看法',
                '就我而言', '对我来说', '在我看来'
            ],

            // 犹豫和修正表达
            hesitationExpressions: [
                '嗯', '呃', '这个', '那个', '怎么说呢',
                '或者说', '更准确地说', '准确来说',
                '不对，应该是', '等等，我想想', '让我想想',
                '哦对了', '忘了说', '补充一下', '顺便提一下'
            ],

            // 强调和感叹
            emphasisExpressions: [
                '真的很', '确实很', '相当', '非常', '特别',
                '极其', '十分', '相当地', '格外地',
                '哇', '哎呀', '天哪', '我的天', '真是的',
                '没想到', '想不到', '真没料到', '出乎意料'
            ]
        };
    }

    /**
     * 朱雀对抗优化主函数 - 完全重写版本
     * @param {string} text - 待优化文本
     * @param {Object} options - 优化选项
     * @returns {Object} 优化结果
     */
    async optimizeForZhuque(text, options = {}) {
        const {
            aggressiveness = 'medium',
            preserveSemantics = false,  // 默认不保持语义，更激进
            targetScore = 5,           // 默认目标更低
            enableRealTimeCheck = false // 默认禁用实时检查，避免提前终止
        } = options;

        console.log('🔥 启动激进朱雀对抗优化...');
        console.log('📋 优化配置:', { aggressiveness, preserveSemantics, targetScore });

        try {
            // 第一步：基线检测
            const baselineResult = await this.performBaselineDetection(text);
            console.log('📊 基线检测:', baselineResult.aiProbability + '%');

            // 第二步：强制执行所有优化步骤（不依赖分析结果）
            let optimizedText = text;
            const optimizationSteps = [];
            let currentScore = baselineResult.aiProbability;

            // 定义激进优化流程
            const aggressiveSteps = this.getAggressiveOptimizationSteps(aggressiveness);

            console.log(`🚀 执行 ${aggressiveSteps.length} 个激进优化步骤...`);

            for (let i = 0; i < aggressiveSteps.length; i++) {
                const step = aggressiveSteps[i];
                console.log(`⚡ 步骤 ${i + 1}/${aggressiveSteps.length}: ${step.description}`);

                try {
                    const stepResult = await this.executeAggressiveStep(
                        optimizedText,
                        step,
                        aggressiveness
                    );

                    if (stepResult.success && stepResult.text !== optimizedText) {
                        optimizedText = stepResult.text;
                        optimizationSteps.push(stepResult);

                        console.log(`✅ 步骤完成，应用了 ${stepResult.changes?.length || 0} 项变更`);

                        // 每3步检测一次进度（可选）
                        if (enableRealTimeCheck && (i + 1) % 3 === 0) {
                            const checkResult = await this.performIntermediateCheck(optimizedText);
                            const improvement = currentScore - checkResult.aiProbability;
                            console.log(`📈 中间检测: ${checkResult.aiProbability}% (改进 ${improvement}%)`);
                            currentScore = checkResult.aiProbability;

                            stepResult.intermediateScore = checkResult.aiProbability;
                        }
                    } else {
                        console.log(`⚠️ 步骤跳过或无变更`);
                    }
                } catch (stepError) {
                    console.warn(`❌ 步骤执行失败: ${stepError.message}`);
                    optimizationSteps.push({
                        success: false,
                        step: step,
                        error: stepError.message
                    });
                }
            }

            // 第三步：最终验证
            const finalResult = await this.performFinalValidation(optimizedText);
            const totalImprovement = baselineResult.aiProbability - finalResult.aiProbability;

            console.log(`🎯 优化完成! ${baselineResult.aiProbability}% → ${finalResult.aiProbability}% (改进 ${totalImprovement}%)`);

            return {
                success: true,
                originalText: text,
                optimizedText: optimizedText,
                baseline: baselineResult,
                finalResult: finalResult,
                improvement: totalImprovement,
                optimizationSteps: optimizationSteps,
                metadata: {
                    aggressiveness: aggressiveness,
                    targetScore: targetScore,
                    actualScore: finalResult.aiProbability,
                    targetAchieved: finalResult.aiProbability <= targetScore,
                    totalSteps: aggressiveSteps.length,
                    successfulSteps: optimizationSteps.filter(s => s.success).length,
                    processingTime: Date.now()
                }
            };

        } catch (error) {
            console.error('❌ 朱雀优化失败:', error);
            return {
                success: false,
                error: error.message,
                originalText: text,
                optimizedText: text
            };
        }
    }

    /**
     * 获取激进优化步骤序列
     */
    getAggressiveOptimizationSteps(aggressiveness) {
        const baseSteps = [
            { type: 'ai_signature_removal', description: '移除AI签名短语', priority: 10 },
            { type: 'structural_pattern_breaking', description: '打破结构化模式', priority: 9 },
            { type: 'academic_expression_removal', description: '移除学术表达', priority: 8 },
            { type: 'massive_synonym_replacement', description: '大规模同义词替换', priority: 7 },
            { type: 'colloquial_injection', description: '注入口语化表达', priority: 6 },
            { type: 'personal_expression_insertion', description: '插入个人化表达', priority: 5 },
            { type: 'hesitation_marker_addition', description: '添加犹豫标记', priority: 4 },
            { type: 'sentence_randomization', description: '句子结构随机化', priority: 3 },
            { type: 'punctuation_chaos', description: '标点符号混淆', priority: 2 },
            { type: 'filler_word_injection', description: '注入填充词', priority: 1 }
        ];

        // 根据强度调整步骤
        const intensityMultiplier = {
            'low': 0.7,
            'medium': 1.0,
            'high': 1.5
        }[aggressiveness] || 1.0;

        if (aggressiveness === 'high') {
            // 高强度模式添加极端步骤
            baseSteps.push(
                { type: 'extreme_colloquialization', description: '极端口语化', priority: 11 },
                { type: 'dialect_insertion', description: '插入方言词汇', priority: 12 },
                { type: 'emotional_marker_injection', description: '注入情感标记', priority: 13 }
            );
        }

        return baseSteps.sort((a, b) => b.priority - a.priority);
    }

    /**
     * 执行激进优化步骤
     */
    async executeAggressiveStep(text, step, aggressiveness) {
        const changes = [];
        let optimizedText = text;

        try {
            switch (step.type) {
                case 'ai_signature_removal':
                    optimizedText = this.aggressiveRemoveAISignatures(optimizedText, changes);
                    break;
                case 'structural_pattern_breaking':
                    optimizedText = this.aggressiveBreakStructuralPatterns(optimizedText, changes);
                    break;
                case 'academic_expression_removal':
                    optimizedText = this.aggressiveRemoveAcademicExpressions(optimizedText, changes);
                    break;
                case 'massive_synonym_replacement':
                    optimizedText = this.aggressiveSynonymReplacement(optimizedText, changes);
                    break;
                case 'colloquial_injection':
                    optimizedText = this.aggressiveColloquialInjection(optimizedText, changes);
                    break;
                case 'personal_expression_insertion':
                    optimizedText = this.aggressivePersonalExpressionInsertion(optimizedText, changes);
                    break;
                case 'hesitation_marker_addition':
                    optimizedText = this.aggressiveHesitationMarkerAddition(optimizedText, changes);
                    break;
                case 'sentence_randomization':
                    optimizedText = this.aggressiveSentenceRandomization(optimizedText, changes);
                    break;
                case 'punctuation_chaos':
                    optimizedText = this.aggressivePunctuationChaos(optimizedText, changes);
                    break;
                case 'filler_word_injection':
                    optimizedText = this.aggressiveFillerWordInjection(optimizedText, changes);
                    break;
                case 'extreme_colloquialization':
                    optimizedText = this.extremeColloquialization(optimizedText, changes);
                    break;
                case 'dialect_insertion':
                    optimizedText = this.aggressiveDialectInsertion(optimizedText, changes);
                    break;
                case 'emotional_marker_injection':
                    optimizedText = this.aggressiveEmotionalMarkerInjection(optimizedText, changes);
                    break;
                default:
                    console.warn(`未知的优化步骤类型: ${step.type}`);
                    return { success: false, text: text, changes: [] };
            }

            return {
                success: true,
                text: optimizedText,
                step: step,
                changes: changes,
                changeCount: changes.length
            };

        } catch (error) {
            console.error(`步骤执行失败 (${step.type}):`, error);
            return {
                success: false,
                text: text,
                step: step,
                error: error.message,
                changes: []
            };
        }
    }

    /**
     * 激进移除AI签名短语
     */
    aggressiveRemoveAISignatures(text, changes) {
        let optimizedText = text;

        // 扩展的AI签名短语列表
        const extendedAISignatures = [
            ...this.aiPatterns.aiSignatures,
            '根据研究', '数据表明', '调查显示', '实验证明', '分析结果显示',
            '相关研究指出', '学者认为', '专家表示', '业内人士指出',
            '最新研究发现', '科学研究证实', '大量数据表明', '统计结果显示',
            '研究报告指出', '调查结果表明', '实验数据显示', '分析报告指出'
        ];

        for (const signature of extendedAISignatures) {
            if (optimizedText.includes(signature)) {
                // 使用更自然的替换
                const naturalReplacements = [
                    '我觉得', '个人认为', '在我看来', '我的理解是',
                    '据我了解', '从我的经验看', '我注意到', '我发现',
                    '说实话', '老实说', '坦白讲', '实际上'
                ];

                const replacement = naturalReplacements[Math.floor(Math.random() * naturalReplacements.length)];
                optimizedText = optimizedText.replace(new RegExp(signature, 'g'), replacement);

                changes.push({
                    type: 'ai_signature_removal',
                    original: signature,
                    replacement: replacement,
                    description: `AI签名移除: ${signature} → ${replacement}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 激进打破结构化模式
     */
    aggressiveBreakStructuralPatterns(text, changes) {
        let optimizedText = text;

        // 打破"首先-其次-最后"模式
        optimizedText = optimizedText.replace(
            /首先[，,]([^。！？]*)[。！？]其次[，,]([^。！？]*)[。！？]最后[，,]([^。！？]*)/g,
            (match, first, second, third) => {
                const alternatives = [
                    `说实话，${first.trim()}。然后呢，${second.trim()}。到最后，${third.trim()}`,
                    `我觉得${first.trim()}。另外，${second.trim()}。最终${third.trim()}`,
                    `${first.trim()}。接下来${second.trim()}。结果${third.trim()}`
                ];
                const chosen = alternatives[Math.floor(Math.random() * alternatives.length)];
                changes.push({
                    type: 'structural_pattern_breaking',
                    original: match,
                    replacement: chosen,
                    description: '打破首先-其次-最后模式'
                });
                return chosen;
            }
        );

        // 打破数字列表
        optimizedText = optimizedText.replace(
            /(\d+)[\.、]([^。！？]*)/g,
            (match, num, content) => {
                const alternatives = [
                    `还有就是${content.trim()}`,
                    `另外${content.trim()}`,
                    `顺便说一下，${content.trim()}`,
                    `${content.trim()}`
                ];
                const chosen = alternatives[Math.floor(Math.random() * alternatives.length)];
                changes.push({
                    type: 'list_breaking',
                    original: match,
                    replacement: chosen,
                    description: `数字列表重构: ${num}. → 自然表达`
                });
                return chosen;
            }
        );

        return optimizedText;
    }

    /**
     * 激进移除学术表达
     */
    aggressiveRemoveAcademicExpressions(text, changes) {
        let optimizedText = text;

        const academicToNatural = {
            '具有重要意义': '很重要',
            '发挥重要作用': '很有用',
            '产生深远影响': '影响很大',
            '取得显著成效': '效果不错',
            '呈现良好态势': '发展得不错',
            '实现跨越式发展': '发展很快',
            '推动高质量发展': '促进发展',
            '促进可持续发展': '长期发展',
            '构建新发展格局': '建立新模式',
            '在...领域': '在...方面',
            '在...过程中': '在...时候',
            '在...背景下': '在...情况下',
            '随着...的发展': '随着...变化',
            '伴随着...的进步': '随着...改进',
            '在...推动下': '因为...'
        };

        for (const [academic, natural] of Object.entries(academicToNatural)) {
            if (optimizedText.includes(academic)) {
                optimizedText = optimizedText.replace(new RegExp(academic, 'g'), natural);
                changes.push({
                    type: 'academic_removal',
                    original: academic,
                    replacement: natural,
                    description: `学术表达简化: ${academic} → ${natural}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 激进同义词替换
     */
    aggressiveSynonymReplacement(text, changes) {
        let optimizedText = text;

        // 扩展的同义词库
        const aggressiveSynonyms = {
            '非常': ['特别', '相当', '十分', '极其', '格外', '超级', '挺', '蛮', '还挺'],
            '很': ['特别', '相当', '挺', '蛮', '还挺', '比较', '还算', '超级'],
            '但是': ['不过', '可是', '只是', '然而', '但', '不过话说回来', '话说回来'],
            '如果': ['要是', '假如', '倘若', '万一', '如果说', '假设'],
            '因为': ['由于', '既然', '鉴于', '考虑到', '因为'],
            '所以': ['因此', '这样', '这么一来', '于是', '结果', '这就'],
            '可以': ['能够', '能', '可', '行', '没问题', '可以的'],
            '应该': ['应当', '该', '得', '需要', '要', '估计'],
            '必须': ['一定要', '必需', '得', '务必', '非得', '肯定要'],
            '能够': ['可以', '能', '有能力', '有办法', '做得到'],
            '研究': ['调查', '探索', '分析', '考察', '观察', '探讨', '钻研'],
            '发现': ['找到', '注意到', '观察到', '意识到', '察觉', '发觉', '看出'],
            '表明': ['说明', '显示', '证明', '反映', '揭示', '体现', '展现'],
            '提高': ['增强', '改善', '优化', '加强', '提升', '改进', '完善'],
            '重要': ['关键', '核心', '主要', '重点', '要紧', '关键性', '主导'],
            '方法': ['方式', '途径', '手段', '办法', '策略', '措施', '路径'],
            '问题': ['难题', '困难', '挑战', '障碍', '麻烦', '困扰', '疑问'],
            '结果': ['成果', '效果', '产出', '收获', '后果', '成效', '结局']
        };

        for (const [original, synonyms] of Object.entries(aggressiveSynonyms)) {
            const regex = new RegExp(original, 'g');
            if (regex.test(optimizedText)) {
                // 随机选择同义词替换
                const synonym = synonyms[Math.floor(Math.random() * synonyms.length)];
                optimizedText = optimizedText.replace(regex, synonym);
                changes.push({
                    type: 'aggressive_synonym',
                    original: original,
                    replacement: synonym,
                    description: `激进同义词替换: ${original} → ${synonym}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 激进口语化注入
     */
    aggressiveColloquialInjection(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.6) { // 60%概率添加口语化
                const colloquialPrefixes = [
                    '说实话', '老实说', '坦白讲', '不瞒你说', '实际上',
                    '其实', '说起来', '想想看', '比如说', '举个例子',
                    '这么说吧', '怎么说呢', '说白了', '直白点说',
                    '我觉得', '个人认为', '在我看来', '据我了解'
                ];

                const prefix = colloquialPrefixes[Math.floor(Math.random() * colloquialPrefixes.length)];
                sentences[i] = prefix + '，' + sentences[i];

                changes.push({
                    type: 'colloquial_injection',
                    prefix: prefix,
                    description: `口语化注入: ${prefix}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 激进个人化表达插入
     */
    aggressivePersonalExpressionInsertion(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.4) { // 40%概率添加个人化表达
                const personalExpressions = [
                    '我发现', '我注意到', '我观察到', '我意识到',
                    '我的理解是', '我的看法是', '我的观点是',
                    '在我的经验中', '根据我的观察', '从我的角度',
                    '我个人觉得', '我个人认为', '就我而言', '对我来说'
                ];

                const expression = personalExpressions[Math.floor(Math.random() * personalExpressions.length)];
                sentences[i] = expression + '，' + sentences[i];

                changes.push({
                    type: 'personal_expression',
                    expression: expression,
                    description: `个人化表达: ${expression}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 激进犹豫标记添加
     */
    aggressiveHesitationMarkerAddition(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.3) { // 30%概率添加犹豫标记
                const hesitationMarkers = [
                    '嗯', '呃', '这个', '那个', '怎么说呢', '让我想想',
                    '或者说', '更准确地说', '准确来说', '哦对了',
                    '忘了说', '补充一下', '顺便提一下'
                ];

                const marker = hesitationMarkers[Math.floor(Math.random() * hesitationMarkers.length)];

                // 在句子中间插入
                const words = sentences[i].split(/[，,]/);
                if (words.length > 1) {
                    const insertIndex = Math.floor(words.length / 2);
                    words.splice(insertIndex, 0, marker);
                    sentences[i] = words.join('，');

                    changes.push({
                        type: 'hesitation_marker',
                        marker: marker,
                        description: `犹豫标记: ${marker}`
                    });
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 激进句子随机化
     */
    aggressiveSentenceRandomization(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length - 1; i++) {
            if (Math.random() < 0.4) { // 40%概率进行句子操作
                if (sentences[i].length < 20 && sentences[i + 1].length < 20) {
                    // 合并短句
                    const connectors = ['，而且', '，另外', '，还有', '，同时'];
                    const connector = connectors[Math.floor(Math.random() * connectors.length)];
                    const merged = sentences[i] + connector + sentences[i + 1];
                    sentences[i] = merged;
                    sentences.splice(i + 1, 1);

                    changes.push({
                        type: 'sentence_merge',
                        connector: connector,
                        description: `句子合并: ${connector}`
                    });
                } else if (sentences[i].length > 40) {
                    // 分割长句
                    const splitPoints = ['，', '、', '；'];
                    for (const point of splitPoints) {
                        const splitIndex = sentences[i].indexOf(point, sentences[i].length / 2);
                        if (splitIndex > 0) {
                            const part1 = sentences[i].substring(0, splitIndex);
                            const part2 = sentences[i].substring(splitIndex + 1);
                            sentences[i] = part1;
                            sentences.splice(i + 1, 0, part2);

                            changes.push({
                                type: 'sentence_split',
                                splitPoint: point,
                                description: `句子分割: ${point}`
                            });
                            break;
                        }
                    }
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 激进标点混淆
     */
    aggressivePunctuationChaos(text, changes) {
        let optimizedText = text;

        // 随机替换标点符号
        const punctuationMap = {
            '，': ['、', '，', '...', '——'],
            '。': ['。', '！', '...'],
            '；': ['，', '；', '——'],
            '：': ['：', '——', '...']
        };

        for (const [original, alternatives] of Object.entries(punctuationMap)) {
            optimizedText = optimizedText.replace(new RegExp('\\' + original, 'g'), (match) => {
                if (Math.random() < 0.2) { // 20%概率替换
                    const alternative = alternatives[Math.floor(Math.random() * alternatives.length)];
                    changes.push({
                        type: 'punctuation_chaos',
                        original: original,
                        replacement: alternative,
                        description: `标点混淆: ${original} → ${alternative}`
                    });
                    return alternative;
                }
                return match;
            });
        }

        return optimizedText;
    }

    /**
     * 激进填充词注入
     */
    aggressiveFillerWordInjection(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.4) { // 40%概率添加填充词
                const fillerWords = [
                    '嗯', '呃', '这个', '那个', '怎么说呢', '让我想想',
                    '哎呀', '哇', '天哪', '我的天', '真是的', '没想到',
                    '想不到', '真没料到', '出乎意料', '有意思', '挺有趣'
                ];

                const filler = fillerWords[Math.floor(Math.random() * fillerWords.length)];

                // 在句子开头、中间或结尾插入
                const position = Math.random();
                if (position < 0.4) {
                    // 开头
                    sentences[i] = filler + '，' + sentences[i];
                } else if (position < 0.8) {
                    // 中间
                    const commaIndex = sentences[i].indexOf('，');
                    if (commaIndex > 0) {
                        sentences[i] = sentences[i].substring(0, commaIndex + 1) +
                                     filler + '，' +
                                     sentences[i].substring(commaIndex + 1);
                    }
                } else {
                    // 结尾
                    sentences[i] = sentences[i] + '，' + filler;
                }

                changes.push({
                    type: 'filler_injection',
                    filler: filler,
                    description: `填充词注入: ${filler}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 极端口语化
     */
    extremeColloquialization(text, changes) {
        let optimizedText = text;

        const extremeReplacements = {
            '这样': '这样子',
            '那样': '那样子',
            '怎样': '怎么样',
            '什么': '啥',
            '为什么': '为啥',
            '怎么': '咋',
            '非常': '超级',
            '特别': '特别特别',
            '真的': '真的真的',
            '确实': '确实是这样',
            '知道': '晓得',
            '没有': '木有',
            '这个': '这',
            '那个': '那'
        };

        for (const [standard, extreme] of Object.entries(extremeReplacements)) {
            if (optimizedText.includes(standard)) {
                optimizedText = optimizedText.replace(new RegExp(standard, 'g'), extreme);
                changes.push({
                    type: 'extreme_colloquial',
                    original: standard,
                    replacement: extreme,
                    description: `极端口语化: ${standard} → ${extreme}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 激进方言插入
     */
    aggressiveDialectInsertion(text, changes) {
        let optimizedText = text;

        const dialectReplacements = {
            '很': '蛮',
            '的': '滴',
            '什么': '啥子',
            '怎么': '咋个',
            '知道': '晓得',
            '没有': '木有',
            '这里': '这儿',
            '那里': '那儿',
            '哪里': '哪儿'
        };

        for (const [standard, dialect] of Object.entries(dialectReplacements)) {
            if (optimizedText.includes(standard) && Math.random() < 0.3) {
                optimizedText = optimizedText.replace(new RegExp(standard, 'g'), dialect);
                changes.push({
                    type: 'dialect_insertion',
                    original: standard,
                    replacement: dialect,
                    description: `方言插入: ${standard} → ${dialect}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 激进情感标记注入
     */
    aggressiveEmotionalMarkerInjection(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.25) { // 25%概率添加情感标记
                const emotionalMarkers = [
                    '真的很', '确实很', '相当', '非常', '特别',
                    '哇', '哎呀', '天哪', '我的天', '真是的',
                    '没想到', '想不到', '真没料到', '出乎意料',
                    '有意思', '挺有趣', '蛮不错', '还挺好'
                ];

                const marker = emotionalMarkers[Math.floor(Math.random() * emotionalMarkers.length)];
                sentences[i] = marker + '，' + sentences[i];

                changes.push({
                    type: 'emotional_marker',
                    marker: marker,
                    description: `情感标记: ${marker}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 执行基线检测
     */
    async performBaselineDetection(text) {
        if (typeof zhuqueDetector !== 'undefined') {
            return zhuqueDetector.detectWithZhuque(text);
        } else if (typeof aiDetector !== 'undefined') {
            const result = aiDetector.detectAI(text);
            return {
                aiProbability: result.score || result.aiProbability || 50,
                confidence: result.confidence || 0.5,
                algorithm: 'fallback'
            };
        } else {
            return {
                aiProbability: 75, // 假设较高的AI概率
                confidence: 0.3,
                algorithm: 'estimated'
            };
        }
    }

    /**
     * 四维度分析
     */
    analyzeFourDimensions(text) {
        return {
            perplexity: this.analyzePerplexityDimension(text),
            structural: this.analyzeStructuralDimension(text),
            semantic: this.analyzeSemanticDimension(text),
            frequency: this.analyzeFrequencyDimension(text)
        };
    }

    /**
     * 困惑度维度分析
     */
    analyzePerplexityDimension(text) {
        const tokens = this.tokenize(text);
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        // 简化的困惑度估算
        const avgTokensPerSentence = tokens.length / sentences.length;
        const estimatedPerplexity = Math.max(10, Math.min(120, avgTokensPerSentence * 3.5));

        const isLowPerplexity = estimatedPerplexity < this.thresholds.perplexity.aiRange[1];

        return {
            estimatedPerplexity: estimatedPerplexity,
            isProblematic: isLowPerplexity,
            needsOptimization: isLowPerplexity,
            targetPerplexity: this.thresholds.perplexity.target,
            analysis: {
                avgTokensPerSentence: avgTokensPerSentence,
                sentenceCount: sentences.length,
                tokenCount: tokens.length
            }
        };
    }

    /**
     * 结构化特征维度分析
     */
    analyzeStructuralDimension(text) {
        let patternCount = 0;
        const detectedPatterns = [];

        // 检测结构化模式
        for (const pattern of this.aiPatterns.structuralMarkers) {
            const matches = text.match(pattern);
            if (matches) {
                patternCount += matches.length;
                detectedPatterns.push({
                    pattern: pattern.source,
                    matches: matches
                });
            }
        }

        // 检测AI签名短语
        let signatureCount = 0;
        for (const signature of this.aiPatterns.aiSignatures) {
            if (text.includes(signature)) {
                signatureCount++;
                detectedPatterns.push({
                    type: 'signature',
                    phrase: signature
                });
            }
        }

        // 句子长度一致性分析
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        const lengths = sentences.map(s => s.length);
        const avgLength = lengths.reduce((a, b) => a + b, 0) / lengths.length;
        const variance = lengths.reduce((sum, len) => sum + Math.pow(len - avgLength, 2), 0) / lengths.length;
        const stdDev = Math.sqrt(variance);
        const consistency = 1 - (stdDev / avgLength);

        const isProblematic = patternCount > this.thresholds.structural.maxPatterns ||
                             consistency > this.thresholds.structural.consistencyLimit;

        return {
            patternCount: patternCount,
            signatureCount: signatureCount,
            detectedPatterns: detectedPatterns,
            sentenceConsistency: consistency,
            isProblematic: isProblematic,
            needsOptimization: isProblematic,
            analysis: {
                avgSentenceLength: avgLength,
                sentenceLengthStdDev: stdDev,
                consistencyRatio: consistency
            }
        };
    }

    /**
     * 语义一致性维度分析
     */
    analyzeSemanticDimension(text) {
        const tokens = this.tokenize(text);
        const uniqueTokens = new Set(tokens);
        const lexicalDiversity = uniqueTokens.size / tokens.length;

        // 简化的主题一致性计算
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        let topicConsistency = 0.7; // 默认值

        if (sentences.length > 1) {
            let similaritySum = 0;
            for (let i = 1; i < sentences.length; i++) {
                const similarity = this.calculateSentenceSimilarity(sentences[i-1], sentences[i]);
                similaritySum += similarity;
            }
            topicConsistency = similaritySum / (sentences.length - 1);
        }

        const isProblematic = lexicalDiversity < this.thresholds.semantic.minLexicalDiversity ||
                             topicConsistency > this.thresholds.semantic.maxTopicConsistency;

        return {
            lexicalDiversity: lexicalDiversity,
            topicConsistency: topicConsistency,
            isProblematic: isProblematic,
            needsOptimization: isProblematic,
            analysis: {
                totalTokens: tokens.length,
                uniqueTokens: uniqueTokens.size,
                repetitionRate: 1 - lexicalDiversity
            }
        };
    }

    /**
     * 频域特征维度分析
     */
    analyzeFrequencyDimension(text) {
        const chars = text.split('');
        const charFreq = {};

        // 计算字符频率
        for (const char of chars) {
            charFreq[char] = (charFreq[char] || 0) + 1;
        }

        // 计算熵值
        const totalChars = chars.length;
        let entropy = 0;
        for (const freq of Object.values(charFreq)) {
            const prob = freq / totalChars;
            entropy -= prob * Math.log2(prob);
        }

        // 高频字符比例
        const sortedFreq = Object.entries(charFreq).sort((a, b) => b[1] - a[1]);
        const topCharRatio = sortedFreq.slice(0, 10).reduce((sum, [char, freq]) => sum + freq, 0) / totalChars;

        const isProblematic = entropy < this.thresholds.frequency.minEntropy ||
                             topCharRatio > this.thresholds.frequency.maxTopCharRatio;

        return {
            entropy: entropy,
            topCharRatio: topCharRatio,
            isProblematic: isProblematic,
            needsOptimization: isProblematic,
            analysis: {
                uniqueChars: Object.keys(charFreq).length,
                totalChars: totalChars,
                avgCharFreq: totalChars / Object.keys(charFreq).length
            }
        };
    }

    // 辅助方法
    tokenize(text) {
        return text.replace(/[^\u4e00-\u9fa5a-zA-Z0-9]/g, ' ')
                  .split(/\s+/)
                  .filter(word => word.length > 0);
    }

    calculateSentenceSimilarity(sent1, sent2) {
        const words1 = new Set(this.tokenize(sent1));
        const words2 = new Set(this.tokenize(sent2));
        const intersection = new Set([...words1].filter(x => words2.has(x)));
        const union = new Set([...words1, ...words2]);
        return intersection.size / union.size;
    }

    /**
     * 生成优化策略计划
     */
    generateOptimizationPlan(dimensionAnalysis, aggressiveness, targetScore) {
        const steps = [];
        const priorities = this.calculateOptimizationPriorities(dimensionAnalysis, targetScore);
        const config = this.aggressivenessConfig[aggressiveness] || this.aggressivenessConfig.medium;

        // 根据优化强度调整策略 - 更激进的配置
        const intensityMultiplier = {
            'low': 1.2,    // 提高基础强度
            'medium': 1.8, // 大幅提高中度强度
            'high': 2.5    // 极大提高高强度
        }[aggressiveness] || 1.8;

        // 强制执行所有优化步骤，不管是否检测到问题
        console.log('🔥 生成激进优化策略，目标分数:', targetScore);

        // 第一轮：基础结构化优化（必须执行）
        steps.push({
            type: 'structural_basic',
            priority: 10 * intensityMultiplier,
            methods: ['removeAISignatures', 'breakStructuralPatterns', 'removeAcademicExpressions'],
            description: '基础结构化特征消除',
            shouldCheck: true,
            intensity: config.structuralIntensity
        });

        // 第二轮：深度语义优化（必须执行）
        steps.push({
            type: 'semantic_deep',
            priority: 9 * intensityMultiplier,
            methods: ['massiveLexicalDiversification', 'insertPersonalExpressions', 'addHesitationMarkers'],
            description: '深度语义多样化处理',
            shouldCheck: false,
            intensity: config.semanticIntensity
        });

        // 第三轮：困惑度大幅提升（必须执行）
        steps.push({
            type: 'perplexity_boost',
            priority: 8 * intensityMultiplier,
            methods: ['injectComplexity', 'addRandomVariations', 'insertFillerWords'],
            description: '大幅提升文本困惑度',
            shouldCheck: true,
            intensity: config.perplexityIntensity
        });

        // 第四轮：句子结构重组（必须执行）
        steps.push({
            type: 'sentence_restructure',
            priority: 7 * intensityMultiplier,
            methods: ['randomizeSentenceLength', 'insertBreaks', 'addEmotionalMarkers'],
            description: '句子结构随机化重组',
            shouldCheck: false,
            intensity: config.structuralIntensity
        });

        // 第五轮：频域特征混淆（必须执行）
        steps.push({
            type: 'frequency_chaos',
            priority: 6 * intensityMultiplier,
            methods: ['chaosCharacterDistribution', 'insertRandomPunctuation', 'addTypos'],
            description: '频域特征混淆处理',
            shouldCheck: false,
            intensity: config.frequencyIntensity
        });

        // 如果目标分数很低，添加极端优化步骤
        if (targetScore <= 15) {
            steps.push({
                type: 'extreme_humanization',
                priority: 11 * intensityMultiplier,
                methods: ['extremeColloquialization', 'insertDialectWords', 'addPersonalStories'],
                description: '极端人性化处理',
                shouldCheck: true,
                intensity: 1.0
            });
        }

        // 按优先级排序
        steps.sort((a, b) => b.priority - a.priority);

        console.log('📋 生成优化计划:', steps.length, '个步骤');

        return {
            steps: steps,
            estimatedImprovement: this.estimateImprovement(steps, dimensionAnalysis) * 2, // 提高预期改进
            aggressiveness: aggressiveness,
            targetScore: targetScore,
            totalSteps: steps.length
        };
    }

    /**
     * 计算优化优先级
     */
    calculateOptimizationPriorities(dimensionAnalysis, targetScore) {
        const basePriorities = {
            structural: 1.0,  // 结构化特征影响最大
            semantic: 0.8,    // 语义一致性次之
            perplexity: 0.6,  // 困惑度影响中等
            frequency: 0.4    // 频域特征影响较小
        };

        // 根据问题严重程度调整优先级
        const adjustedPriorities = {};
        for (const [dimension, analysis] of Object.entries(dimensionAnalysis)) {
            let adjustment = 1.0;

            if (dimension === 'structural' && analysis.patternCount > 3) {
                adjustment = 1.5; // 结构化模式过多，提高优先级
            } else if (dimension === 'semantic' && analysis.lexicalDiversity < 0.5) {
                adjustment = 1.3; // 词汇多样性过低，提高优先级
            } else if (dimension === 'perplexity' && analysis.estimatedPerplexity < 30) {
                adjustment = 1.2; // 困惑度过低，提高优先级
            }

            adjustedPriorities[dimension] = basePriorities[dimension] * adjustment;
        }

        return adjustedPriorities;
    }

    /**
     * 执行优化步骤
     */
    async executeOptimizationStep(text, step, preserveSemantics) {
        let optimizedText = text;
        const appliedMethods = [];
        const changes = [];

        try {
            for (const method of step.methods) {
                const methodResult = await this.applyOptimizationMethod(
                    optimizedText,
                    method,
                    step.type,
                    preserveSemantics
                );

                if (methodResult.success) {
                    optimizedText = methodResult.text;
                    appliedMethods.push(method);
                    changes.push(...methodResult.changes);
                }
            }

            return {
                success: true,
                text: optimizedText,
                step: step,
                appliedMethods: appliedMethods,
                changes: changes,
                shouldCheck: step.shouldCheck
            };

        } catch (error) {
            console.warn(`优化步骤执行失败 (${step.type}):`, error);
            return {
                success: false,
                text: text, // 返回原文本
                step: step,
                error: error.message,
                shouldCheck: false
            };
        }
    }

    /**
     * 应用具体的优化方法
     */
    async applyOptimizationMethod(text, method, type, preserveSemantics) {
        const changes = [];
        let optimizedText = text;

        switch (method) {
            // 基础优化方法
            case 'removeAISignatures':
                optimizedText = this.removeAISignatures(optimizedText, changes);
                break;
            case 'breakStructuralPatterns':
                optimizedText = this.breakStructuralPatterns(optimizedText, changes);
                break;
            case 'diversifySentenceLength':
                optimizedText = this.diversifySentenceLength(optimizedText, changes);
                break;
            case 'increaseLexicalDiversity':
                optimizedText = this.increaseLexicalDiversity(optimizedText, changes, preserveSemantics);
                break;
            case 'addUncertaintyExpressions':
                optimizedText = this.addUncertaintyExpressions(optimizedText, changes);
                break;
            case 'insertColloquialisms':
                optimizedText = this.insertColloquialisms(optimizedText, changes);
                break;
            case 'addComplexSentences':
                optimizedText = this.addComplexSentences(optimizedText, changes);
                break;
            case 'insertRareWords':
                optimizedText = this.insertRareWords(optimizedText, changes, preserveSemantics);
                break;
            case 'createSyntacticVariation':
                optimizedText = this.createSyntacticVariation(optimizedText, changes);
                break;
            case 'adjustCharacterDistribution':
                optimizedText = this.adjustCharacterDistribution(optimizedText, changes);
                break;
            case 'addPunctuation':
                optimizedText = this.addPunctuation(optimizedText, changes);
                break;
            case 'insertSpecialChars':
                optimizedText = this.insertSpecialChars(optimizedText, changes);
                break;

            // 新增激进优化方法
            case 'removeAcademicExpressions':
                optimizedText = this.removeAcademicExpressions(optimizedText, changes);
                break;
            case 'massiveLexicalDiversification':
                optimizedText = this.massiveLexicalDiversification(optimizedText, changes);
                break;
            case 'insertPersonalExpressions':
                optimizedText = this.insertPersonalExpressions(optimizedText, changes);
                break;
            case 'addHesitationMarkers':
                optimizedText = this.addHesitationMarkers(optimizedText, changes);
                break;
            case 'injectComplexity':
                optimizedText = this.injectComplexity(optimizedText, changes);
                break;
            case 'addRandomVariations':
                optimizedText = this.addRandomVariations(optimizedText, changes);
                break;
            case 'insertFillerWords':
                optimizedText = this.insertFillerWords(optimizedText, changes);
                break;
            case 'randomizeSentenceLength':
                optimizedText = this.randomizeSentenceLength(optimizedText, changes);
                break;
            case 'insertBreaks':
                optimizedText = this.insertBreaks(optimizedText, changes);
                break;
            case 'addEmotionalMarkers':
                optimizedText = this.addEmotionalMarkers(optimizedText, changes);
                break;
            case 'chaosCharacterDistribution':
                optimizedText = this.chaosCharacterDistribution(optimizedText, changes);
                break;
            case 'insertRandomPunctuation':
                optimizedText = this.insertRandomPunctuation(optimizedText, changes);
                break;
            case 'addTypos':
                optimizedText = this.addTypos(optimizedText, changes);
                break;
            case 'extremeColloquialization':
                optimizedText = this.extremeColloquialization(optimizedText, changes);
                break;
            case 'insertDialectWords':
                optimizedText = this.insertDialectWords(optimizedText, changes);
                break;
            case 'addPersonalStories':
                optimizedText = this.addPersonalStories(optimizedText, changes);
                break;

            default:
                console.warn(`未知的优化方法: ${method}`);
                return { success: false, text: text, changes: [] };
        }

        return {
            success: true,
            text: optimizedText,
            changes: changes,
            method: method
        };
    }

    /**
     * 移除AI签名短语 - 增强版
     */
    removeAISignatures(text, changes) {
        let optimizedText = text;

        // 处理所有AI签名短语
        for (const signature of this.aiPatterns.aiSignatures) {
            const regex = new RegExp(signature, 'g');
            if (regex.test(optimizedText)) {
                const replacements = this.humanizationDict.structuralReplacements[signature];
                if (replacements && replacements.length > 0) {
                    const replacement = replacements[Math.floor(Math.random() * replacements.length)];
                    optimizedText = optimizedText.replace(new RegExp(signature, 'g'), replacement);
                    changes.push({
                        type: 'signature_replacement',
                        original: signature,
                        replacement: replacement,
                        description: `替换AI签名短语: ${signature} → ${replacement}`
                    });
                } else {
                    // 直接删除或用口语化表达替换
                    const casualReplacements = ['说实话', '其实', '我觉得', '个人认为', '在我看来'];
                    const casualReplacement = casualReplacements[Math.floor(Math.random() * casualReplacements.length)];
                    optimizedText = optimizedText.replace(new RegExp(signature + '[，,]?', 'g'), casualReplacement + '，');
                    changes.push({
                        type: 'signature_casualization',
                        original: signature,
                        replacement: casualReplacement,
                        description: `AI签名短语口语化: ${signature} → ${casualReplacement}`
                    });
                }
            }
        }

        // 处理学术表达
        for (const expression of this.aiPatterns.academicExpressions) {
            if (optimizedText.includes(expression)) {
                // 简化学术表达
                const simplified = expression.replace(/具有重要意义/g, '很重要')
                                            .replace(/发挥重要作用/g, '很有用')
                                            .replace(/产生深远影响/g, '影响很大')
                                            .replace(/取得显著成效/g, '效果不错')
                                            .replace(/呈现良好态势/g, '发展得不错');

                if (simplified !== expression) {
                    optimizedText = optimizedText.replace(expression, simplified);
                    changes.push({
                        type: 'academic_simplification',
                        original: expression,
                        replacement: simplified,
                        description: `学术表达简化: ${expression} → ${simplified}`
                    });
                }
            }
        }

        return optimizedText;
    }

    /**
     * 打破结构化模式
     */
    breakStructuralPatterns(text, changes) {
        let optimizedText = text;

        // 处理"首先...其次...最后"模式
        optimizedText = optimizedText.replace(
            /首先[，,]([^。！？]*)[。！？]其次[，,]([^。！？]*)[。！？]最后[，,]([^。！？]*)/g,
            (match, first, second, third) => {
                const alternatives = [
                    `${first.trim()}。另外，${second.trim()}。还有就是${third.trim()}`,
                    `一开始${first.trim()}，然后${second.trim()}，到最后${third.trim()}`,
                    `${first.trim()}。接下来${second.trim()}。最终${third.trim()}`
                ];
                const chosen = alternatives[Math.floor(Math.random() * alternatives.length)];
                changes.push({
                    type: 'pattern_breaking',
                    original: match,
                    replacement: chosen,
                    description: '打破"首先-其次-最后"结构模式'
                });
                return chosen;
            }
        );

        // 处理数字列表
        optimizedText = optimizedText.replace(
            /(\d+)[\.、]([^。！？]*)/g,
            (match, num, content) => {
                const alternatives = [
                    `另外，${content.trim()}`,
                    `还有${content.trim()}`,
                    `除此之外，${content.trim()}`,
                    `${content.trim()}`
                ];
                const chosen = alternatives[Math.floor(Math.random() * alternatives.length)];
                changes.push({
                    type: 'list_restructure',
                    original: match,
                    replacement: chosen,
                    description: `重构数字列表项: ${num}. → 自然表达`
                });
                return chosen;
            }
        );

        return optimizedText;
    }

    /**
     * 增加句子长度多样性
     */
    diversifySentenceLength(text, changes) {
        const sentences = text.split(/([。！？])/).filter(s => s.trim().length > 0);
        let optimizedSentences = [];

        for (let i = 0; i < sentences.length; i += 2) {
            const sentence = sentences[i];
            const punctuation = sentences[i + 1] || '。';

            if (sentence.length > 30 && Math.random() < 0.3) {
                // 长句子分割
                const midPoint = sentence.length / 2;
                const splitPoint = sentence.indexOf('，', midPoint) || sentence.indexOf('、', midPoint) || midPoint;

                if (splitPoint > 0 && splitPoint < sentence.length - 5) {
                    const part1 = sentence.substring(0, splitPoint);
                    const part2 = sentence.substring(splitPoint + 1);

                    optimizedSentences.push(part1 + '。');
                    optimizedSentences.push(part2 + punctuation);

                    changes.push({
                        type: 'sentence_split',
                        original: sentence + punctuation,
                        replacement: part1 + '。' + part2 + punctuation,
                        description: '分割长句增加长度多样性'
                    });
                } else {
                    optimizedSentences.push(sentence + punctuation);
                }
            } else if (sentence.length < 15 && i < sentences.length - 2 && Math.random() < 0.2) {
                // 短句子合并
                const nextSentence = sentences[i + 2];
                const nextPunctuation = sentences[i + 3] || '。';

                if (nextSentence && nextSentence.length < 20) {
                    const merged = sentence + '，' + nextSentence + nextPunctuation;
                    optimizedSentences.push(merged);
                    i += 2; // 跳过下一个句子

                    changes.push({
                        type: 'sentence_merge',
                        original: sentence + punctuation + nextSentence + nextPunctuation,
                        replacement: merged,
                        description: '合并短句增加长度多样性'
                    });
                } else {
                    optimizedSentences.push(sentence + punctuation);
                }
            } else {
                optimizedSentences.push(sentence + punctuation);
            }
        }

        return optimizedSentences.join('');
    }

    /**
     * 增加词汇多样性 - 增强版
     */
    increaseLexicalDiversity(text, changes, preserveSemantics) {
        let optimizedText = text;

        // 激进的同义词替换
        for (const [original, synonyms] of Object.entries(this.humanizationDict.synonyms)) {
            const regex = new RegExp(original, 'g');
            const matches = optimizedText.match(regex);

            if (matches && matches.length >= 1) {
                // 替换更多重复词汇，提高替换率
                let replacementCount = 0;
                optimizedText = optimizedText.replace(regex, (match) => {
                    if (replacementCount < matches.length && Math.random() < 0.8) { // 提高替换概率
                        replacementCount++;
                        const synonym = synonyms[Math.floor(Math.random() * synonyms.length)];
                        changes.push({
                            type: 'synonym_replacement',
                            original: match,
                            replacement: synonym,
                            description: `同义词替换: ${match} → ${synonym}`
                        });
                        return synonym;
                    }
                    return match;
                });
            }
        }

        // 口语化替换
        for (const [original, colloquials] of Object.entries(this.humanizationDict.colloquialReplacements)) {
            if (optimizedText.includes(original)) {
                const colloquial = colloquials[Math.floor(Math.random() * colloquials.length)];
                optimizedText = optimizedText.replace(new RegExp(original, 'g'), colloquial);
                changes.push({
                    type: 'colloquial_replacement',
                    original: original,
                    replacement: colloquial,
                    description: `口语化替换: ${original} → ${colloquial}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 大规模词汇多样化 - 新增激进方法
     */
    massiveLexicalDiversification(text, changes) {
        let optimizedText = text;

        // 更激进的词汇替换
        const aggressiveReplacements = {
            '非常': ['特别', '相当', '十分', '极其', '格外', '超级', '挺'],
            '很': ['特别', '相当', '挺', '蛮', '还挺', '比较', '还算'],
            '但是': ['不过', '可是', '只是', '然而', '但', '不过话说回来'],
            '如果': ['要是', '假如', '倘若', '万一', '如果说'],
            '因为': ['由于', '因为', '既然', '鉴于', '考虑到'],
            '所以': ['因此', '这样', '这么一来', '于是', '结果'],
            '可以': ['能够', '能', '可', '行', '没问题'],
            '应该': ['应当', '该', '得', '需要', '要'],
            '必须': ['一定要', '必需', '得', '务必', '非得'],
            '能够': ['可以', '能', '有能力', '有办法', '做得到']
        };

        for (const [original, replacements] of Object.entries(aggressiveReplacements)) {
            const regex = new RegExp(original, 'g');
            if (regex.test(optimizedText)) {
                const replacement = replacements[Math.floor(Math.random() * replacements.length)];
                optimizedText = optimizedText.replace(regex, replacement);
                changes.push({
                    type: 'aggressive_replacement',
                    original: original,
                    replacement: replacement,
                    description: `激进词汇替换: ${original} → ${replacement}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 添加不确定性表达
     */
    addUncertaintyExpressions(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.25) { // 25%的概率添加不确定性表达
                const expressions = this.humanWritingFeatures.uncertaintyExpressions;
                const expression = expressions[Math.floor(Math.random() * expressions.length)];

                // 在句子开头或中间插入
                const insertPosition = Math.random() < 0.7 ? 'beginning' : 'middle';

                if (insertPosition === 'beginning') {
                    sentences[i] = expression + '，' + sentences[i];
                } else {
                    const commaIndex = sentences[i].indexOf('，');
                    if (commaIndex > 0) {
                        sentences[i] = sentences[i].substring(0, commaIndex + 1) +
                                     expression + '，' +
                                     sentences[i].substring(commaIndex + 1);
                    }
                }

                changes.push({
                    type: 'uncertainty_addition',
                    expression: expression,
                    position: insertPosition,
                    description: `添加不确定性表达: ${expression}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 插入口语化表达
     */
    insertColloquialisms(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.2) { // 20%的概率添加口语化表达
                const expressions = this.humanWritingFeatures.colloquialExpressions;
                const expression = expressions[Math.floor(Math.random() * expressions.length)];

                sentences[i] = expression + '，' + sentences[i];

                changes.push({
                    type: 'colloquial_addition',
                    expression: expression,
                    description: `添加口语化表达: ${expression}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 添加复杂句式
     */
    addComplexSentences(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (sentences[i].length < 20 && Math.random() < 0.3) {
                // 为短句添加从句或修饰语
                const complexifiers = [
                    '虽然如此，但是',
                    '尽管存在一些争议，',
                    '从某种程度上来说，',
                    '考虑到各种因素，',
                    '在这种情况下，'
                ];

                const complexifier = complexifiers[Math.floor(Math.random() * complexifiers.length)];
                sentences[i] = complexifier + sentences[i];

                changes.push({
                    type: 'complexity_addition',
                    complexifier: complexifier,
                    description: `添加复杂句式: ${complexifier}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 插入罕见词汇
     */
    insertRareWords(text, changes, preserveSemantics) {
        if (!preserveSemantics) return text; // 保持语义时才进行此优化

        let optimizedText = text;

        // 简单的罕见词替换（保持语义）
        const rareWordReplacements = {
            '使用': '运用',
            '帮助': '协助',
            '改变': '变更',
            '增加': '增添',
            '减少': '削减',
            '开始': '启动',
            '结束': '终止',
            '继续': '持续'
        };

        for (const [common, rare] of Object.entries(rareWordReplacements)) {
            if (optimizedText.includes(common) && Math.random() < 0.4) {
                optimizedText = optimizedText.replace(new RegExp(common, 'g'), rare);
                changes.push({
                    type: 'rare_word_replacement',
                    original: common,
                    replacement: rare,
                    description: `使用罕见词汇: ${common} → ${rare}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 创建句法变化
     */
    createSyntacticVariation(text, changes) {
        let optimizedText = text;

        // 被动语态转主动语态
        optimizedText = optimizedText.replace(
            /被([^，。！？]*)/g,
            (match, content) => {
                if (Math.random() < 0.5) {
                    const activeForm = content + '了';
                    changes.push({
                        type: 'voice_change',
                        original: match,
                        replacement: activeForm,
                        description: `被动转主动: ${match} → ${activeForm}`
                    });
                    return activeForm;
                }
                return match;
            }
        );

        return optimizedText;
    }

    /**
     * 调整字符分布
     */
    adjustCharacterDistribution(text, changes) {
        let optimizedText = text;

        // 添加一些变化的标点符号
        optimizedText = optimizedText.replace(/，/g, (match) => {
            if (Math.random() < 0.1) {
                changes.push({
                    type: 'punctuation_variation',
                    original: '，',
                    replacement: '、',
                    description: '标点符号变化'
                });
                return '、';
            }
            return match;
        });

        return optimizedText;
    }

    /**
     * 添加标点符号
     */
    addPunctuation(text, changes) {
        let optimizedText = text;

        // 在适当位置添加省略号
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (sentences[i].length > 25 && Math.random() < 0.15) {
                const midPoint = sentences[i].length / 2;
                const insertPoint = sentences[i].indexOf('，', midPoint);

                if (insertPoint > 0) {
                    sentences[i] = sentences[i].substring(0, insertPoint) +
                                  '……' +
                                  sentences[i].substring(insertPoint);

                    changes.push({
                        type: 'ellipsis_addition',
                        description: '添加省略号增加表达变化'
                    });
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 插入特殊字符
     */
    insertSpecialChars(text, changes) {
        // 谨慎使用，避免影响可读性
        return text; // 暂时不实现，避免过度优化
    }

    /**
     * 执行中间检测
     */
    async performIntermediateCheck(text) {
        return await this.performBaselineDetection(text);
    }

    /**
     * 执行最终验证
     */
    async performFinalValidation(text) {
        const result = await this.performBaselineDetection(text);

        // 添加额外的验证指标
        const dimensionAnalysis = this.analyzeFourDimensions(text);

        return {
            ...result,
            finalDimensionAnalysis: dimensionAnalysis,
            passesZhuqueThresholds: this.checkZhuqueThresholds(dimensionAnalysis),
            readabilityScore: this.calculateReadabilityScore(text),
            semanticIntegrity: this.checkSemanticIntegrity(text)
        };
    }

    /**
     * 检查是否通过朱雀阈值
     */
    checkZhuqueThresholds(dimensionAnalysis) {
        const checks = {
            perplexity: dimensionAnalysis.perplexity.estimatedPerplexity >= this.thresholds.perplexity.target,
            structural: dimensionAnalysis.structural.patternCount <= this.thresholds.structural.maxPatterns,
            semantic: dimensionAnalysis.semantic.lexicalDiversity >= this.thresholds.semantic.minLexicalDiversity,
            frequency: dimensionAnalysis.frequency.entropy >= this.thresholds.frequency.minEntropy
        };

        const passedCount = Object.values(checks).filter(Boolean).length;

        return {
            checks: checks,
            passedCount: passedCount,
            totalChecks: Object.keys(checks).length,
            overallPass: passedCount >= 3 // 至少通过3个维度
        };
    }

    /**
     * 计算可读性分数
     */
    calculateReadabilityScore(text) {
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        const words = this.tokenize(text);

        const avgWordsPerSentence = words.length / sentences.length;
        const avgCharsPerWord = text.length / words.length;

        // 简化的可读性评分
        let score = 100;

        if (avgWordsPerSentence > 25) score -= 10; // 句子过长
        if (avgWordsPerSentence < 8) score -= 5;   // 句子过短
        if (avgCharsPerWord > 4) score -= 5;       // 词汇过于复杂

        return Math.max(0, Math.min(100, score));
    }

    /**
     * 检查语义完整性
     */
    checkSemanticIntegrity(text) {
        // 简单的语义完整性检查
        const hasSubject = /[我你他她它们我们您]/.test(text);
        const hasPredicate = /[是有做说想要能会]/.test(text);
        const hasLogicalFlow = text.includes('因为') || text.includes('所以') || text.includes('但是');

        return {
            hasSubject: hasSubject,
            hasPredicate: hasPredicate,
            hasLogicalFlow: hasLogicalFlow,
            score: (hasSubject + hasPredicate + hasLogicalFlow) / 3
        };
    }

    /**
     * 估算改进效果
     */
    estimateImprovement(steps, dimensionAnalysis) {
        let estimatedImprovement = 0;

        for (const step of steps) {
            switch (step.type) {
                case 'structural':
                    estimatedImprovement += dimensionAnalysis.structural.patternCount * 8;
                    break;
                case 'semantic':
                    estimatedImprovement += (0.7 - dimensionAnalysis.semantic.lexicalDiversity) * 20;
                    break;
                case 'perplexity':
                    estimatedImprovement += Math.max(0, 60 - dimensionAnalysis.perplexity.estimatedPerplexity) * 0.3;
                    break;
                case 'frequency':
                    estimatedImprovement += Math.max(0, 4.2 - dimensionAnalysis.frequency.entropy) * 5;
                    break;
            }
        }

        return Math.min(50, estimatedImprovement); // 最大改进50分
    }

    /**
     * 移除学术表达 - 新增方法
     */
    removeAcademicExpressions(text, changes) {
        let optimizedText = text;

        for (const expression of this.aiPatterns.academicExpressions) {
            if (optimizedText.includes(expression)) {
                // 用更自然的表达替换
                const naturalReplacements = {
                    '具有重要意义': '很重要',
                    '发挥重要作用': '很有用',
                    '产生深远影响': '影响很大',
                    '取得显著成效': '效果不错',
                    '呈现良好态势': '发展得不错',
                    '实现跨越式发展': '发展很快',
                    '推动高质量发展': '促进发展',
                    '促进可持续发展': '长期发展',
                    '构建新发展格局': '建立新模式'
                };

                const replacement = naturalReplacements[expression] || '很好';
                optimizedText = optimizedText.replace(expression, replacement);
                changes.push({
                    type: 'academic_removal',
                    original: expression,
                    replacement: replacement,
                    description: `移除学术表达: ${expression} → ${replacement}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 插入个人化表达
     */
    insertPersonalExpressions(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.4) { // 40%概率添加个人化表达
                const expressions = this.humanWritingFeatures.personalExpressions;
                const expression = expressions[Math.floor(Math.random() * expressions.length)];

                sentences[i] = expression + '，' + sentences[i];

                changes.push({
                    type: 'personal_expression',
                    expression: expression,
                    description: `添加个人化表达: ${expression}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 添加犹豫标记
     */
    addHesitationMarkers(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.25) { // 25%概率添加犹豫表达
                const hesitations = this.humanWritingFeatures.hesitationExpressions;
                const hesitation = hesitations[Math.floor(Math.random() * hesitations.length)];

                // 在句子中间插入
                const words = sentences[i].split(/[，,]/);
                if (words.length > 1) {
                    const insertIndex = Math.floor(words.length / 2);
                    words.splice(insertIndex, 0, hesitation);
                    sentences[i] = words.join('，');

                    changes.push({
                        type: 'hesitation_marker',
                        marker: hesitation,
                        description: `添加犹豫标记: ${hesitation}`
                    });
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 注入复杂性
     */
    injectComplexity(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (sentences[i].length < 25 && Math.random() < 0.5) {
                // 为短句添加复杂修饰
                const complexModifiers = [
                    '从某种程度上来说',
                    '在一定条件下',
                    '考虑到各种因素',
                    '基于我的理解',
                    '根据我的观察',
                    '从我的角度来看',
                    '在我的经验中'
                ];

                const modifier = complexModifiers[Math.floor(Math.random() * complexModifiers.length)];
                sentences[i] = modifier + '，' + sentences[i];

                changes.push({
                    type: 'complexity_injection',
                    modifier: modifier,
                    description: `注入复杂性: ${modifier}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 添加随机变化
     */
    addRandomVariations(text, changes) {
        let optimizedText = text;

        // 随机替换标点符号
        optimizedText = optimizedText.replace(/，/g, (match) => {
            if (Math.random() < 0.15) {
                const alternatives = ['、', '，', '...', '——'];
                const alt = alternatives[Math.floor(Math.random() * alternatives.length)];
                changes.push({
                    type: 'punctuation_variation',
                    original: '，',
                    replacement: alt,
                    description: `标点变化: ， → ${alt}`
                });
                return alt;
            }
            return match;
        });

        return optimizedText;
    }

    /**
     * 插入填充词
     */
    insertFillerWords(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.3) {
                const fillers = ['嗯', '呃', '这个', '那个', '怎么说呢', '让我想想'];
                const filler = fillers[Math.floor(Math.random() * fillers.length)];

                // 在句子开头或中间插入
                if (Math.random() < 0.5) {
                    sentences[i] = filler + '，' + sentences[i];
                } else {
                    const commaIndex = sentences[i].indexOf('，');
                    if (commaIndex > 0) {
                        sentences[i] = sentences[i].substring(0, commaIndex + 1) +
                                     filler + '，' +
                                     sentences[i].substring(commaIndex + 1);
                    }
                }

                changes.push({
                    type: 'filler_word',
                    filler: filler,
                    description: `插入填充词: ${filler}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 随机化句子长度
     */
    randomizeSentenceLength(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length - 1; i++) {
            if (Math.random() < 0.3) {
                // 随机合并或分割句子
                if (sentences[i].length < 20 && sentences[i + 1].length < 20) {
                    // 合并短句
                    const merged = sentences[i] + '，而且' + sentences[i + 1];
                    sentences[i] = merged;
                    sentences.splice(i + 1, 1);

                    changes.push({
                        type: 'sentence_merge',
                        description: '随机合并短句'
                    });
                } else if (sentences[i].length > 40) {
                    // 分割长句
                    const midPoint = sentences[i].length / 2;
                    const splitPoint = sentences[i].indexOf('，', midPoint);

                    if (splitPoint > 0) {
                        const part1 = sentences[i].substring(0, splitPoint);
                        const part2 = sentences[i].substring(splitPoint + 1);

                        sentences[i] = part1;
                        sentences.splice(i + 1, 0, part2);

                        changes.push({
                            type: 'sentence_split',
                            description: '随机分割长句'
                        });
                    }
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 插入断句
     */
    insertBreaks(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (sentences[i].length > 30 && Math.random() < 0.4) {
                // 在长句中插入停顿
                const breakPoints = ['...', '——', '，嗯，', '，对了，'];
                const breakPoint = breakPoints[Math.floor(Math.random() * breakPoints.length)];

                const midPoint = sentences[i].length / 2;
                const insertPoint = sentences[i].indexOf('，', midPoint);

                if (insertPoint > 0) {
                    sentences[i] = sentences[i].substring(0, insertPoint) +
                                  breakPoint +
                                  sentences[i].substring(insertPoint + 1);

                    changes.push({
                        type: 'break_insertion',
                        breakPoint: breakPoint,
                        description: `插入断句: ${breakPoint}`
                    });
                }
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 添加情感标记
     */
    addEmotionalMarkers(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        for (let i = 0; i < sentences.length; i++) {
            if (Math.random() < 0.2) {
                const emotions = this.humanWritingFeatures.emphasisExpressions;
                const emotion = emotions[Math.floor(Math.random() * emotions.length)];

                sentences[i] = emotion + '，' + sentences[i];

                changes.push({
                    type: 'emotional_marker',
                    emotion: emotion,
                    description: `添加情感标记: ${emotion}`
                });
            }
        }

        return sentences.join('。') + '。';
    }

    /**
     * 混淆字符分布
     */
    chaosCharacterDistribution(text, changes) {
        let optimizedText = text;

        // 随机插入空格和特殊字符
        const chars = optimizedText.split('');
        for (let i = 0; i < chars.length; i += 20) {
            if (Math.random() < 0.1) {
                const specialChars = ['…', '——', '、'];
                const specialChar = specialChars[Math.floor(Math.random() * specialChars.length)];
                chars.splice(i, 0, specialChar);

                changes.push({
                    type: 'character_chaos',
                    char: specialChar,
                    description: `插入特殊字符: ${specialChar}`
                });
            }
        }

        return chars.join('');
    }

    /**
     * 插入随机标点
     */
    insertRandomPunctuation(text, changes) {
        let optimizedText = text;

        // 在适当位置插入随机标点
        optimizedText = optimizedText.replace(/([，。])/g, (match, punct) => {
            if (Math.random() < 0.05) {
                const randomPuncts = ['...', '——', '、', '；'];
                const randomPunct = randomPuncts[Math.floor(Math.random() * randomPuncts.length)];

                changes.push({
                    type: 'random_punctuation',
                    original: punct,
                    replacement: randomPunct,
                    description: `随机标点: ${punct} → ${randomPunct}`
                });

                return randomPunct;
            }
            return match;
        });

        return optimizedText;
    }

    /**
     * 添加轻微错别字（谨慎使用）
     */
    addTypos(text, changes) {
        // 暂时不实现，避免影响可读性
        return text;
    }

    /**
     * 极端口语化
     */
    extremeColloquialization(text, changes) {
        let optimizedText = text;

        const extremeReplacements = {
            '这样': '这样子',
            '那样': '那样子',
            '怎样': '怎么样',
            '什么': '啥',
            '为什么': '为啥',
            '怎么': '咋',
            '非常': '超级',
            '特别': '特别特别',
            '真的': '真的真的',
            '确实': '确实是这样'
        };

        for (const [original, extreme] of Object.entries(extremeReplacements)) {
            if (optimizedText.includes(original)) {
                optimizedText = optimizedText.replace(new RegExp(original, 'g'), extreme);
                changes.push({
                    type: 'extreme_colloquial',
                    original: original,
                    replacement: extreme,
                    description: `极端口语化: ${original} → ${extreme}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 插入方言词汇
     */
    insertDialectWords(text, changes) {
        let optimizedText = text;

        const dialectWords = {
            '很': '蛮',
            '的': '滴',
            '这个': '这',
            '那个': '那',
            '什么': '啥子',
            '怎么': '咋个',
            '知道': '晓得',
            '没有': '木有'
        };

        for (const [standard, dialect] of Object.entries(dialectWords)) {
            if (optimizedText.includes(standard) && Math.random() < 0.3) {
                optimizedText = optimizedText.replace(new RegExp(standard, 'g'), dialect);
                changes.push({
                    type: 'dialect_insertion',
                    original: standard,
                    replacement: dialect,
                    description: `插入方言: ${standard} → ${dialect}`
                });
            }
        }

        return optimizedText;
    }

    /**
     * 添加个人故事
     */
    addPersonalStories(text, changes) {
        let optimizedText = text;
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        if (sentences.length > 2 && Math.random() < 0.3) {
            const personalStories = [
                '我之前遇到过类似的情况',
                '记得有一次我也碰到这个问题',
                '我朋友跟我说过',
                '我在网上看到过',
                '我听别人讲过',
                '我自己的经历告诉我'
            ];

            const story = personalStories[Math.floor(Math.random() * personalStories.length)];
            const insertIndex = Math.floor(sentences.length / 2);

            sentences.splice(insertIndex, 0, story);

            changes.push({
                type: 'personal_story',
                story: story,
                description: `添加个人故事: ${story}`
            });
        }

        return sentences.join('。') + '。';
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ZhuqueOptimizer };
}

// 创建全局实例 - 延迟初始化
window.addEventListener('DOMContentLoaded', function() {
    try {
        if (typeof window.zhuqueOptimizer === 'undefined') {
            window.zhuqueOptimizer = new ZhuqueOptimizer();
            console.log('✅ ZhuqueOptimizer 实例已创建');
        }
    } catch (error) {
        console.error('创建朱雀优化器实例时出错:', error);
    }
});