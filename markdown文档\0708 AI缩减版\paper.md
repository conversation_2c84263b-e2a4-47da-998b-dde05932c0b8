# 基于多策略融合协同式自适应进化算法系统的高校排课优化研究

<center>陆圣安</center>

<center>（江西水利电力大学 信息工程学院 南昌 330099）</center>

**摘要**：高校排课问题因其多维度约束交织与资源配置优化的高度复杂性，一直是组合优化领域的挑战。为应对此难题，本研究提出并实现了一个多策略融合协同式自adaptive进化排课系统（MSCAES）。该系统独创性地构建了"局部约束消解-全局搜索优化-鲁棒性增强"三级递进式优化架构。第一阶段，通过面向特殊需求的启发式引导贪心初始化算法（HGIS-SR），优先调度高约束课程；第二阶段，采用启发式引导初始化与冲突感知变异的快速自适应遗传算法（HGI-CAMAR），进行全局资源优化配置；第三阶段，借助基于动态局部修复的贪心再调度与冲突消解算法（GRCR-DLR），有效清除残余冲突。系统性创新包括：融合多维度优先级成数改进密度计算，引入二元组染色体编码，以及设计多目标适应度函数以均衡全局资源，从而高效求解课程-教室-时间的三维匹配问题。在真实世界数据集上的实验结果表明，MSCAES的排课成功率高达97.40%，相较于传统遗传算法提升近10个百分点，在人工数据集上更是实现了99.50%的近乎完全排课。同时，其资源分布均匀性得分较基准方法提升了29.38%至178.44%，显著优化了教师、班级和教室的负载均衡。本研究为复杂约束下的课程调度问题提供了一个可扩展、实用且高效的解决方案。

**关键词**：智能排课；多策略协同进化；贪心-遗传混合算法；冲突修复；自适应进化；资源均衡优化​

中国分类号：		文献标志码：

# A Multi-Strategy Collaborative Adaptive Evolutionary Algorithm System for University Course Timetabling Optimization

<center>LU Sheng-An</center>

<center>（School of Information Engineering, Jiangxi University of Water Resources and Electric Power ,Nanchang,330099）</center>

**Abstract**: University course timetabling presents a persistent challenge due to a complex web of multi-dimensional constraints and the need for optimal resource allocation. To address this, we introduce a Multi-Strategy Collaborative Adaptive Evolutionary Scheduling System (MSCAES). The system features an innovative three-stage hierarchical optimization architecture: "local constraint resolution, global search optimization, and robustness enhancement." In the first stage, a Heuristic-Guided Greedy Initialization Algorithm for Special Requirements (HGIS-SR) prioritizes the scheduling of highly constrained courses. The second stage employs a fast adaptive genetic algorithm with Heuristic-Guided Initialization and Conflict-Aware Mutation (HGI-CAMAR) for global resource allocation. In the final stage, a Greedy Rescheduling and Conflict Resolution algorithm with Dynamic Local Repair (GRCR-DLR) eliminates residual conflicts. Key innovations include a multi-dimensional priority function to refine density calculations, a dual-tuple chromosome encoding scheme for unified resource representation, and a multi-objective fitness function to balance global resource utilization. This design effectively solves the three-dimensional matching problem of courses, classrooms, and time slots. Experimental results on a real-world dataset show that MSCAES achieves a 97.40% success rate, an improvement of nearly 10 percentage points over traditional genetic algorithms, and reaches 99.50% completion on an artificial dataset. Furthermore, resource distribution uniformity scores improved by 29.38% to 178.44% compared to baseline methods, indicating a significantly more balanced load distribution among teachers, classes, and classrooms. This research provides a scalable, practical, and efficient solution for course scheduling under complex constraints.

**Keywords**: Intelligent Timetabling; Multi-Strategy Collaborative Evolution; Greedy-Genetic Hybrid Algorithm; Conflict Resolution; Adaptive Evolution; Resource Balancing and Optimization

## 1 引言

高校课程排课（University Course Timetabling, UCT）是一项精细且复杂的管理任务，其核心挑战在于，如何在有限的时空资源内，高效协调教师、班级、课程与教室间的关系。这些约束相互交织，覆盖了从规避教师时间冲突、满足专业课程的特殊时段需求，到适配教室类型与容量、最小化学生班级课时冲突，直至实现整体资源负载均衡的多重目标。由于其内在的组合复杂性，UCT问题已被学界公认为一类经典的NP-难问题[1,2]。

尽管现有研究，如启发式贪心算法及以遗传算法为代表的元启发式方法，在处理特定目标上取得了显著进展[3,4]，但仍存在若干关键瓶颈。首先，现有方法常难以将局部约束处理、全局搜索优化及鲁棒性修复机制集成于一个统一的协同框架内，多数方案侧重于单一层面，未能系统性地应对排课问题的多层次挑战[5,6]。其次，在资源高负载或约束高度耦合的条件下，传统算法生成的解在均衡性与稳定性上常表现不佳，难以有效抑制因局部调整引发的"多米诺效应"。最后，面对大规模、高复杂度的真实场景，现有算法的排课成功率与资源利用均匀性仍有较大的提升空间。

为突破上述研究瓶颈，本研究设计并实现了多策略融合协同式自适应进化排课系统（Multi-Strategy Collaborative Adaptive Evolutionary Scheduling System, MSCAES）。该系统旨在通过一个创新的三阶段协同优化架构，系统性地应对排课问题中的多层次挑战。我们提出了一系列核心算法与创新组件，包括：融合启发式贪心策略以处理高优先级课程；开发冲突感知变异的快速自适应遗传算法以实现全局资源优化；以及创新动态局部修复机制以精准消解残余冲突。此外，系统还引入了多维度优先级函数、二元组染色体编码和多目标适应度函数等关键技术，以支持全局资源的均衡分配。

本研究的理论意义在于，MSCAES通过创新的多策略融合与系统架构，为求解复杂约束下的组合优化问题提供了新的范式。其实践价值则体现在，该系统在真实与人工数据集中均表现出卓越的性能，不仅显著提升了排课成功率与资源负载均衡性，还有效抑制了冲突的连锁反应，为高校教务管理提供了一个兼具高效性与实用性的新一代智能排课解决方案。

本文的结构安排如下：第二节评述相关算法的研究现状与局限；第三节详细阐述MSCAES系统的核心架构、混合算法策略及创新技术；第四节展示并分析在真实与人工数据集上的实验结果，量化评估MSCAES的性能；第五节对全文工作进行总结。

## 2 相关工作

高校课程排课问题（UCT）因其高度的约束耦合与NP难特性，长期以来都是运筹学和人工智能领域的研究热点[1,2]。现有求解方法大致可归为三类：启发式与构造性算法、以进化算法为代表的元启发式算法，以及近年来兴起的多策略融合与超启发式框架。

### 2.1 启发式与构造性算法研究进展

启发式算法因其实现简单、计算速度快，常被用于生成排课问题的初始解。早期的研究多聚焦于图着色模型[7]和顺序构造方法。例如，Burke等人[7]提出的基于超节点着色的贪心框架，为后续研究奠定了理论基础。然而，传统的构造性策略，如Obit等人[8]开发的固定约束优先方法，其核心缺陷在于优先级模型过于简化。这些策略通常仅依赖单一的静态规则（如课程是否有固定的时间或教室），未能系统性地融合课程的多维属性（如学生规模、周次跨度、合班复杂性、特殊约束强度等）来构建综合决策机制。尽管后续改进尝试纳入了部分软约束考量，但在处理多班级协同调度等复杂场景时，由于缺乏跨班级冲突的主动规避机制，极易导致合班安排失败或特定资源（如大容量教室）的分配不均。这些局限性促使本研究提出动态多维度优先级函数，通过非线性加权和特殊约束放大机制，实现对高复杂度课程的智能识别与优先排布，从而显著提升初始解的质量，克服了传统规则导向算法的瓶颈。

### 2.2 元启发式算法的应用瓶颈

元启发式算法通过模拟自然现象或物理过程，在全局范围内搜索高质量解，已在UCT领域得到广泛应用。

模拟退火（Simulated Annealing, SA）算法[4]，通过其接受"差解"的概率性跳出机制来避免陷入局部最优。Abramson[9]等人将其成功应用于排课问题，展示了其强大的全局探索潜力。然而，SA的性能高度依赖于精细的参数调优，在求解约束复杂的UCT问题时，其收敛速度往往不尽人意。

禁忌搜索（Tabu Search, TS）算法[10]通过维护一个禁忌列表来避免搜索过程中的循环，并引导算法向未探索的区域前进。Costa[11]和Hertz[12]通过设计精巧的邻域结构和禁忌准则，在排课问题上取得了良好效果。但TS的挑战在于如何设计高效且问题相关的邻域操作，以及如何平衡搜索的"集中性"与"疏散性"。

遗传算法（Genetic Algorithm, GA）是UCT领域应用最广泛的元启发式算法之一[13,14]。然而，传统GA在应用中存在资源表征与搜索效率的瓶颈。例如，刘莉和栗超[13]提出的GA采用二进制编码描述时间槽分配，却忽视了教室资源的多维特性（如类型、容量），导致算法在资源分配时需进行繁琐的隐式映射与冲突检测。虽然Han等人[15]尝试通过多目标适应度函数将冲突次数与教室利用率纳入评估，但其对关键软约束（如教师工作负荷的均衡性、班级课程序列的合理性）的建模普遍不足，导致获得的"优化解"在实用性上大打折扣。

为弥补上述缺陷，模因算法（Memetic Algorithm, MA）[16,17]将局部搜索（如爬山法）作为一种文化基因嵌入进化框架，旨在结合GA的全局探索能力和局部搜索的精细优化能力。但如何有效平衡全局探索与局部开发的计算资源，始终是MA设计的核心挑战[18]。本研究创新性地引入了"教室-时间槽"二元组染色体编码模型，实现了时空资源的联合显式表征。同时，我们设计了集成了多维度约束（硬约束惩罚+软约束均匀性度量）的精细化多目标适应度函数，这不仅弥补了现有适应度函数对软约束建模的不足，更显著提升了对高质量解的引导能力与搜索效率。

### 2.3 多策略融合与超启发式框架的研究现状

为克服单一算法的局限性，"贪心初始化 + 遗传优化"的两阶段框架已成为处理复杂UCT问题的主流方案[1,19]。然而，这类框架的关键短板在于其冲突修复环节的粗放性。它们通常将遗传优化后未能安排的"剩余课程"视为同质集合，采用统一的规则进行再调度。这种"一刀切"的方式忽视了课程间巨大的复杂度差异，导致高难度课程（如跨多周次、涉及大合班的课程）因未能得到优先处理，其所需资源易被低难度课程抢占，最终陷入修复困境，形成"多米诺骨牌效应"。

为了实现更深层次的策略融合，超启发式（Hyper-Heuristics, HH）框架应运而生，其目标是"选择或生成启发式"[20,21]。在UCT领域，选择性超启发式更为常见，它通过一个高层策略从一个预定义的低层启发式集合中动态选择最适合当前问题状态的算法来执行[22,23]。例如，Burke等人[24]提出的基于图的超启发式，可根据问题的实时特征选择不同的图着色启发式来构造课表。

尽管Song等人[25]和Kohshori与Abadeh[26]提出了基于预定义规则的修复算子，但这些方法通常固守于原始的刚性约束边界（如严格的教室容量匹配），缺乏在资源紧张时进行动态松弛适配的能力（如放宽容量阈值、优先复用空闲时段），难以应对真实场景中的密集冲突。

本研究提出的MSCAES系统，可视为一种领域知识驱动的、确定性的多策略融合框架。它未采用HH的随机或学习选择机制，而是根据排课问题的内在逻辑（先处理特殊约束，再全局优化，最后修复残余冲突）设计了一个固定的三阶段流程，并为每个阶段精心设计了专门的混合算法。其核心创新在于：构建三维课程难度指数模型以指导修复优先级排序，并引入资源约束松弛策略（如容量弹性匹配与时间槽复用增强）。这种"难度优先分拣 - 松弛资源匹配"的协同修复框架，突破了传统修复方法的刚性约束边界，旨在显著提升在约束严苛场景下的最终排课成功率与资源利用效率。

## 3 多策略融合协同式自适应进化排课系统设计

### 3.1 算法框架

MSCAES采用一种三级递进式优化框架，通过策略协同与阶段级联，实现"约束消解-资源优化-鲁棒修复"的全流程闭环。该架构创新性地整合了三大核心算法引擎：面向特殊需求的启发式引导贪心初始化算法（HGIS-SR）、启发式引导初始化与冲突感知变异的快速自适应遗传算法（HGI-CAMAR），以及动态局部修复的贪心再调度与冲突消解算法（GRCR-DLR），形成分阶段精细化求解的协同范式。

**阶段一：HGIS-SR**，负责处理具有特殊需求的课程（如固定教室/时间、多班级合班课）。此阶段构建了多维度优先级函数对课程进行智能分拣，并通过结合冲突预检与混合评分模型（时段偏好+负载均衡+资源复用），为这些高约束课程生成高质量的初始调度方案。此阶段的核心突破在于：
*   建立动态优先级模型，集成了课程基础优先级、学生规模、合班数量、周次跨度及特殊约束强度等多重因子。
*   设计时空双维资源适配机制，将硬约束优先匹配与软约束加权评分相结合进行协同决策。

**阶段二：HGI-CAMAR**，针对普通课程进行全局优化。本阶段首创了（教室-时间槽）二元组染色体编码，从根本上解决了传统编码的资源维度失配问题。进化过程采用四重优化策略：
*   **自适应种群生成**：基于启发式评分构造具有高潜力的初始解。
*   **冲突驱动的遗传操作**：通过动态交叉点选择与冲突感知变异，精准优化高冲突区域。
*   **多目标适应度引导**：融合硬约束冲突惩罚与软约束均匀性度量，实现向帕累托最优前沿的搜索。
*   **收敛加速机制**：采用并行适应度评估与冲突率驱动的参数自适应整定。

**阶段三：GRCR-DLR**，专注于处理前两阶段未能成功安排的课程。此阶段构建了课程难度指数模型以实现分层修复，并引入了松弛资源匹配策略，显著增强了算法对高难度课程的鲁棒性处理能力。其突破性在于：
*   **松弛资源匹配**：通过容量弹性阈值与时间槽复用权重提升资源适配的灵活性。
*   **负载敏感调度**：引入教师/班级单日负载奖惩系数，以抑制调度过程中的多米诺效应。

该三级架构通过策略的优势互补与技术的有机衔接（HGIS-SR输出初始解 → HGI-CAMAR优化全局资源分配 → GRCR-DLR消化残余冲突），系统性地覆盖了排课流程的"预处理-优化-修复"全环节，形成了一个多策略深度融合的智能调度体系。

### 3.2 异构教学数据的标准化解析与时序建模机制

为应对排课系统中跨周次时空数据的异构性与多维度资源约束的强耦合问题，本研究提出一个结构化的数据解析框架。该框架通过三级处理流程，将原始数据精准映射为可计算的标准化单元，并创新性地设计了多周期学时智能拆分机制与时空解耦编码方案，为核心算法提供了规整的输入，从而显著降低了优化复杂度。

其主要技术突破包括：
*   **自适应周次解析算法**：针对"1-8:2,9-16:4"这类异构周次数据，算法通过正则表达式提取起始周*w*s、终止周*w*e及周学时*h*w。基于排课单元的基准长度*L*u（默认为2学时），计算出所需的排课单元数量*N*u，并最终生成一系列独立的、标准化的排课单元集 {*u*1, *u*2, ..., *u*n}。
*   **时空解耦建模机制**：我们创新性地引入了时间槽（Time Slot）编码模型（如表3-1所示），实现了时间资源的标准化表征。时间槽编码*s*与星期几*d*(*s*)及当日节次*P*(*s*)的转换关系如下：
  
  (1)  
  
  (2)

其中，MAX\_PERIOD\_SLOT代表每日的最大节次数量，⌊⋅⌋表示向下取整，mod为取模运算符。

<center>表3-1 时间槽映射模型（MAX_PERIOD_SLOT = 5）</center>
| 节数范围 | 节次编码 | 周一 | 周二 | 周三 | 周四 | 周五 | 周六 | 周日 |
| :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- | :--- |
| 1-2 | 1 | 1 | 6 | 11 | 16 | 21 | 26 | 31 |
| 3-4 | 2 | 2 | 7 | 12 | 17 | 22 | 27 | 32 |
| 5-6 | 3 | 3 | 8 | 13 | 18 | 23 | 28 | 33 |
| 7-8 | 4 | 4 | 9 | 14 | 19 | 24 | 29 | 34 |
| 9-10 | 5 | 5 | 10 | 15 | 20 | 25 | 30 | 35 |

通过上述处理，复杂的原始课程需求被转化为一系列标准化的排课单元。该机制为后续算法提供了高度一致且规整的输入，使得算法能够专注于为每个独立的排课单元寻找最优的时间与空间位置，从而大大简化了问题的求解复杂度。

### 3.3 阶段一：面向特殊需求的启发式引导贪心初始化算法（HGIS-SR）

#### 3.3.1 多维度优先级建模

为精准量化课程的调度紧迫性与约束复杂度，本研究构建了一个综合优先级函数*Fp*（公式3），它突破了传统单维度决策的局限，集成了五类关键的课程约束因子：

(3)

其中，*P*为基础优先级，依据课程属性（如核心课/选修课）预设，数值越高代表优先级越高。*M*为多班级数量，反映合班课程的协同排课复杂度。*S*为学生人数，经过归一化处理以凸显大班课程对资源的更高需求。*W*为周次跨度，长周期课程会显著提升排课难度。*R*为特殊要求数量，通过非线性耦合机制以避免权重失衡。该函数通过非线性加权，将多维度约束转化为一个可量化比较的优先级指标，为贪心策略提供了更全面、更精准的决策依据。

#### 3.3.2 冲突规避的资源分配

时间槽的选择遵循"硬约束优先 + 软约束优化"的原则。算法首先筛选出满足所有硬性时间约束的可用时间槽集合*S*avail。随后，通过一个软约束评分模型，对*S*avail中的每个时间槽*s*计算其综合得分*Score*(*s*)，该得分综合考量了时段偏好、连续课程惩罚、天数均衡性、时间槽复用奖励以及特殊教室需求等多个维度。

设MAX\_DAYS\_SLOT为一天的最大时间槽数，*d*(*s*)为时间槽*s*所在的天数，*p*(*s*)为时间槽*s*所在的节次，*T*load(*t*,*d*)为教师*t*在第*d*天的课程负载，*T*consec(*t*,*d*)为教师*t*在第*d*天的连续课程节数，*C*load(*c*,*d*)是班级*c*在第*d*天的课程负载，*C*consec(*c*,*d*)为班级*c*在第*d*天的连续课程节数，*C*为课程涉及的班级集合，*W*为课程的周次跨度，*R*表示是否有特殊教室要求，T¯load为教师的日均课程负载，C¯load为班级的日均课程负载，A(s)表示时段槽s是否已安排周次不冲突的课程。可用时间槽的综合得分*Score*(*s*)表达式如下：

(4)

(5)

(6)

(7)

其中，各个评分项的定义如下：
*   ***P***period(*s*)：**时段偏好权重**。中间时段（如上午3-4节，下午5-6节）因师生精力更集中，被赋予较高的权重（1.4），而早晚时段权重则较低（0.8）。
*   ***P***consec(*s*)：**连续课程惩罚**。该项通过一个分层约束模型（公式8-10），分别对教师和班级的当日连续授课/上课节数进行惩罚，以避免教学负荷过度集中。

  (8)
  
  (9)
  
  (10)
*   ***P***balance(*s*)：**天数均衡性得分**。此项通过三重维度（教师负载、班级负载、教学日偏好）对课程分布进行优化（公式11），旨在抑制单日高强度排课，并鼓励课程集中在常规教学日。

  (11)
*   ***P***reuse(*s*)：**时间槽复用奖励**。对于周次不冲突的同班级课程，通过给予复用时间槽极高的权重（100倍），以强化资源复用，减少课表碎片化。

最后，当课程存在特殊教室需求（*R*=1）时，总得分将乘以一个增益系数（1.2），以优先保障特殊资源的满足。该综合评分模型通过多目标函数的结构化集成，实现了对可用时间槽的量化排序，为生成高质量的初始解提供了科学决策依据。

教室分配同样采用多维度评分模型进行智能筛选。若无指定教室，算法将综合考量容量适配度、资源复用潜力、使用效率及负载均衡等核心指标，为每个可用教室*r*计算综合得分*Score*(*r*)。

设*cap*(*r*)为教室*r*的容量，*S*stu为学生规模，*C*min为课程所需最小容量（即*S*stu），*C*ideal为理想容量（1.3倍*S*stu），*W*为周次跨度，*A*(*r*)表示教室*r*是否已安排周次不冲突的课程，*N*(*r*)为教室*r*的历史使用次数。*Score*(*r*)的表达式如下：

(12)

各评分项定义为：
*   ***f***cap(*r*)：**容量适配度**。容量不足的教室被直接排除。容量在理想区间内赋予最高分；超出理想区间则通过指数函数进行惩罚，以抑制资源浪费（公式13）。

  (13)
*   ***f***reuse(*r*)：**教室复用奖励**。若教室可被复用，则赋予高额奖励分（150），以促进资源整合。
*   ***f***eff(*r*)：**使用效率**。基于学生人数与教室容量的比值进行评分，鼓励选择容量匹配度高的教室（公式14）。

  (14)
*   ***f***bal(*r*)：**使用均衡性**。通过反比例函数抑制对高频使用教室的选择，随历史使用次数增加，评分指数衰减，以平衡教室资源的使用频率（公式15）。

  (15)

该评分模型通过加权几何平均整合各维度，其中容量适配度被赋予最高权重（0.4），确保在满足基本需求的前提下，系统性地优化教室资源分配的效率与均衡性。

### 3.4 阶段二：启发式引导初始化与冲突感知变异的快速自适应遗传排课算法（HGI-CAMAR）

#### 3.4.1 染色体编码与初始种群生成

本算法采用二元组（tuple）染色体编码，将每个课程的排课决策表示为一个*n*×2矩阵，每行基因`[(r1, t1), (r2, t2), ..., (rn, tn)]`分别对应课程所需的"教室-时间槽"分配。这种编码方式直接映射了排课的核心决策变量。对于合班授课的课程，各班级共享同一组基因 `(ri, ti)`，天然地保证了时间与空间分配的一致性。

在构建初始种群时，我们摒弃了完全随机的初始化方式。算法借助预处理好的教室资源索引，通过并行计算，利用一套启发式评分策略为每门课程动态选择高质量的（教室，时间槽）组合，从而生成多样化且具有高潜力的初始解。时间槽与教室的启发式评分策略如下：
*   **时间槽总得分 *Score(s)*（公式16）综合了三方面因素：
  
  (16)
  
  *   ***P***balance(*s*)：**天数均衡性**（公式17），通过协同优化教师负载、班级负载和教学日分布，规避资源在时间上的集中。
  
  (17)
  
  *   ***P***reuse(*s*)：**时间槽复用奖励**，对短期课程给予高额复用奖励（200分），以减少课表碎片。
  *   ***P***period(*s*)：**时段偏好**，为不同教学效率的时段赋予差异化权重（黄金时段1.2，早晚时段0.8）。
*   **教室综合得分 *Score(r)***（公式18）则由基础分与三个乘积项构成：

  (18)

  *   ***f***reuse(*r*)：**周次跨度优化**，鼓励短期课程复用教室（权重10）。
  *   ***f***cap(*r*)：**容量匹配度**，为容量处于理想区间的教室赋予高分，对超出上限的进行惩罚（公式19）。
  
  (19)

  *   ***f***bal(*r*)：**使用均衡性**，通过反比例函数调节教室使用频率，避免热门教室被过度使用（公式20）。

  (20)

#### 3.4.2 多目标适应度函数

为实现冲突最小化与课程分布均衡化的双重优化目标，本研究构建了如下的多目标适应度函数*F*total：

(21)

其中，*F*conflict为**冲突适应度**。当排课方案无任何硬性冲突（*N*conflict=0）时，该项赋予理想分1000；否则，通过反比例函数进行动态惩罚，冲突越多，分值越低（公式22）。

(22)

*E*combined为**课程分布均匀性得分**，它由教师（权重0.4）、班级（权重0.4）和教室（权重0.2）三个维度的均匀性得分加权求和得到（公式23）。每个维度的均匀性*E*x通过其对应的变异系数CVx计算得出（公式24-25）。变异系数越小，表示课程分布越均匀，均匀性得分*E*x也越高。这种设计引导进化过程向着既无冲突又高度均衡的解空间探索。

(23)

(24)

(25)
（*σ*x和*µ*x分别为实体*x*每日课程数量集合的标准差和平均值。）

#### 3.4.3 进化操作优化

*   **选择**：采用**精英保留锦标赛选择**。每代保留适应度最高的25%个体（精英比例*α*=0.25）直接进入下一代，确保优质基因不丢失。剩余个体则通过锦标赛选择产生，锦标赛大小*T*根据种群规模*|P|*动态调整（公式26），从而平衡了精英引导与种群多样性。

  (26)

*   **交叉**：基于有效基因位（代表实际需排课程）的数量，**动态确定交叉点数** *n*p（公式27）。对多班级课程实施协同基因交换，确保合班所需的时间与教室在交叉后保持一致，有效避免了因交叉操作引入的新冲突。
  
  (27)

*   **变异**：引入**冲突感知变异**机制。算法会优先调整导致冲突的基因位（有80%的概率），进行靶向修复。变异位置的数量*n*m同样根据冲突位置集合*C*p或有效变异位置集合*V*的大小动态调整（公式28），实现了对高冲突区域的重点优化。

  (28)
  
  在为变异基因选择新资源时，采用**教室容量弹性**策略，即根据课程优先级*p*调整容量匹配的严格程度（公式29），并结合时间槽复用策略，以增强局部搜索的有效性。

  (29)

#### 3.4.4 遗传主函数优化策略

*   **自适应参数调整**：交叉率*r*cross与变异率*r*mut不再是固定值，而是随种群的平均冲突率*r*conflict动态调整（公式30-32）。当冲突率较高时，系统自动增大交叉与变异概率以增强搜索的广度与深度；反之，则降低参数以保护种群中的优质解。

  (30)
  
  (31)
  
  (32)

*   **并行计算与提前终止**：利用线程池并行计算子代适应度，当种群规模较大时启用分块处理（公式33），显著降低了评估过程的耗时。同时，设置了连续无改进代数阈值（*n*imp=3），若最佳适应度连续3代未提升或已找到无冲突解（*f*best≥1000），则提前终止进化，避免不必要的计算开销。

  (33)

*   **多样性维持**：在种群更新阶段，采用"最优选择+随机抽样"的策略。保留80%的最优个体以保证收敛方向，同时随机抽取20%的非最优个体以维持种群多样性，有效防止算法陷入早熟。

通过上述多方位的设计优化，阶段二在保持种群多样性的同时加速了收敛，实现了遗传算法在求解效率与成功率上的双重提升。

### 3.5 阶段三：动态局部修复的贪心再调度与冲突消解算法（GRCR-DLR）

#### 3.5.1 课程难度评估模型

为有效指导冲突课程的修复顺序，本研究定义了一个综合性的**课程难度指数 *D*i***，其表达式如下：

(34)

其中，*P*i为课程优先级，*S*i为学生数（归一化），*M*i为合班数量，*W*i为周次跨度，*R*i为特殊约束因子（若有特殊约束，取值1.3，否则为1）。该指数全面量化了课程的调度复杂性，确保在修复阶段，调度难度最高的课程能够被优先处理，从而提高了整体修复的成功率。

#### 3.5.2 松弛资源匹配策略

在冲突修复阶段，本算法采用松弛资源匹配策略，通过动态调整教室与时间的约束边界，构建多维度弹性匹配模型，以在资源利用效率与排课质量间寻求平衡。

**时间分配**采用带负载奖惩的加权选择机制。对于每个候选时间槽*s*，其综合得分*Score(s)*（公式35）整合了基础均匀性、教师/班级负载均衡、时段偏好、天数分布以及时间槽复用等多个维度。该机制在优先复用兼容时间的同时，通过对教师（*L*teacher）和班级（*L*class）的单日负载进行奖惩，有效避免了因修复操作导致新的负载失衡。

(35)
*   *P*base：**基础均匀性得分**，通过反比例函数度量单日课程负载，优先选择负载较低的日期（公式36）。

  (36)
*   *P*teacher\_load & *P*class\_load：**教师/班级负载均衡得分**，对过载日进行惩罚（系数0.7/0.8），对空闲日给予奖励（系数1.3/1.2）。
*   *P*period\_preference & *P*day\_balance：**时段与天数偏好得分**，鼓励选择黄金教学时段与常规教学日。
*   *P*slot\_reuse：**时间槽复用得分**，对短期课程给予极高的复用奖励（100倍权重），以减少碎片化排课。

**教室资源分配**则构建了一个二维评分模型（公式37），通过线性加权突出复用策略与容量适配的协同优化。

(37)
*   *f*reuse：**教室复用得分**，赋予复用教室显著的权重优势（10倍），以提升时空资源的协同利用率。
*   *f*cap：**容量适配得分**，基于教室容量与课程需求的匹配度设计反比例函数（公式38），通过最小化容量差，实现"小容量教室优先"的精细化分配，避免资源浪费。

(38)

本策略通过"量化评分 - 优先级排序 - 动态调整"的闭环机制，为复杂约束下的资源匹配提供了可解释、可优化的解决方案，显著提升了排课系统的鲁棒性与资源利用效率。

### 3.6 算法步骤

本研究提出的多策略融合协同式自适应进化排课系统（MSCAES）（图3-1）旨在系统性地解决高校排课问题。该系统采用分阶段、多策略协同的框架，将复杂的排课问题分解为三个核心阶段，分别应对具有不同约束特征的课程集合，以期在满足全部硬约束的前提下，最大化地优化教学资源利用率和课表均衡度。

**输入**:
*   课程数据集 *C* = {*c*1, *c*2, ..., *c*n}
*   教室数据集 *R* = {*r*1, *r*2, ..., *r*m}
*   算法超参数集合 *θ*

**输出**:
*   最优排课方案 *S***
*   未成功排课的课程集合 *C*f
*   综合性能评估指标 *M*

**算法流程**:
1.  **数据预处理与初始化**: 对原始课程与教室数据进行清洗、标准化编码，并构建高效的教室资源索引管理器。
2.  **阶段一 (HGIS-SR)**: 优先处理具有强约束的特殊课程。算法基于多维度优先级评分模型对这些课程排序，随后通过启发式贪心策略为其分配最优资源，生成无冲突的局部最优解*S*1。
3.  **阶段二 (HGI-CAMAR)**: 对剩余的常规课程进行全局搜索与优化。作为系统的核心引擎，该算法通过启发式方法生成高质量初始种群，并引入冲突感知的交叉与变异算子，旨在寻求全局最优解*S*2。
4.  **阶段三 (GRCR-DLR)**: 针对前序阶段未能成功排课的课程进行补充修复。算法首先对失败课程按调度难度排序，然后动态识别可用资源"缝隙"，尝试通过资源松弛或局部调整等策略进行插入式调度。
5.  **结果整合与评估**: 整合各阶段生成的排课结果，形成最终课表*S***。输出详细的排课报告，包括最终课表、失败课程列表*C*f及其原因，以及一系列关键性能指标*M*。

| Algorithm 1: MSCAES - 多策略融合协同式自适应进化算法系统 |
| :--- |
| **Input**: 课程数据集 C，教室数据集 R，算法参数 θ |
| **Output**: 最终排课方案 S*, 失败课程集合 C_f，性能指标 M |
| **function** MSCAES\_MAIN(C, R, θ) |
|     // 步骤 1：数据预处理与初始化 |
|     C\_encoded ← PreprocessCourses(C) |
|     R\_manager ← BuildClassroomManager(R) |
|     // 步骤 2: 面向特殊需求的启发式引导贪心初始化阶段 HGIS-SR |
|     C\_special, C\_regular ← PartitionCoursesByConstraints(C\_encoded) |
|     C\_priority ← RankCoursesByPriority(C\_special, θ) |
|     S₁, C\_rem₁, Conflicts₁ ← HGIS\_SR(C\_priority, R\_manager, θ) |
|     // 步骤 3: 启发式引导初始化与冲突感知变异的快速自适应遗传排课阶段 HGI-CAMAR |
|     C\_to\_optimize ← C\_regular ∪ C\_rem₁ |
|     **IF** C\_to\_optimize is not empty **THEN** |
|         best\_chromosome ← HGI\_CAMAR(C\_to\_optimize, R\_manager, Conflicts₁, θ) |
|         S₂, C\_f\_temp ← DecodeChromosome(best\_chromosome, C\_to\_optimize) |
|     **ELSE** |
|         S₂ ← ∅, C\_f\_temp ← ∅ |
|     **END IF** |
|     // 步骤 4: 动态局部修复的贪心再调度与冲突消解算法阶段 GRCR-DLR |
|     S\_partial ← S₁ ∪ S₂ |
|     **IF** C\_f\_temp is not empty **THEN** |
|         C\_failed\_ranked ← RankCoursesByDifficulty(C\_f\_temp, θ) |
|         S₃, C\_f ← GRCR\_DLR(C\_failed\_ranked, S\_partial, R\_manager, θ) |
|         S* ← S\_partial ∪ S₃ |
|     **ELSE** |
|         S* ← S\_partial, C\_f ← ∅ |
|     **END IF** |
|     // 步骤 5：结果整合与评估 |
|     M ← EvaluateSchedule(S*, C\_f, C) |
|     **return** S*, C\_f, M |
| **end function** |

![图片](images/image_001.png)
<center>图3-1 多策略融合协同式自适应进化排课系统流程图</center>

## 4 实验结果与分析

### 4.1 实验设置

为系统性地评估本研究提出的算法在不同约束强度、课程规模及资源异质性场景下的性能边界，我们构建了"人工构造-真实业务"双层数据集体系，并选取了七类代表性算法进行横向对比。

#### 4.1.1 数据集

实验采用三类梯度化难度的数据集，以覆盖排课问题的"约束复杂度-场景真实性"谱系：
*   **高约束人工数据集 (Db-2152)**：该数据集为模拟强约束场景而设计，包含2,152门课程。其特点是集成了三类非线性耦合约束：100%的课程需同步满足特定的教室-时间双重绑定；100%的课程被强制限定在特定校区及教室；24.43%的课程兼具高优先级与多班合授特征。该数据集旨在探究算法在NP-hard问题极端场景下的求解能力。
*   **低约束基准数据集 (Regular-795)**：该数据集基于795个排课单元构建，作为算法基础性能的评估基准。它仅保留了教师冲突规避和教室容量适配两类核心约束，所有课程对时间、地点无特殊要求，构成了一个接近理想化的低约束环境。
*   **真实教学数据集 (Real-2152)**：该数据集源自国内某综合性大学2024-2025学年的真实教务数据，经标准化处理后包含2,152门课程。数据特征严格遵循高校实际教学场景，完整保留了真实世界的约束条件与资源分布特征（如多校区、多类型教室、复杂的管理关系等），为算法提供了高保真的验证环境。

#### 4.1.2 对比算法

我们选取了七类代表性排课算法进行对比，覆盖了"单一启发式-进化算法-群智能-混合优化"四大范式：
*   **纯贪心算法 (Greedy)**：基于课程复合优先级进行顺序资源分配的基准算法。
*   **纯遗传算法 (Genetic Algorithm, GA)**：遵循文献[13]设计的经典GA实现，具备全局搜索能力但收敛效率受限。
*   **贪心-遗传混合算法 (Greedy-Genetic Hybrid Algorithm, GGHA)**：融合贪心初始化与遗传优化的两阶段框架，旨在平衡决策效率与搜索能力。
*   **蚁群算法 (Ant Colony Optimization, ACO)**：依据文献[28]的框架实现，将排课问题建模为路径优化问题。
*   **基于多元信息引导的人工蜂群算法 (MIG-ABC)**：借鉴文献[30]的思想，采用三阶段协同优化框架，但在处理大规模问题时计算复杂度较高。
*   **启发式引导初始化与冲突感知变异的快速自适应遗传排课算法 (HGI-CAMAR)**：本研究的核心组件之一，集成了启发式初始化、冲突感知变异和自适应参数调节机制。
*   **多策略融合协同式自适应进化排课系统 (MSCAES)**：本研究提出的完整三阶段协同框架。

#### 4.1.3 评价指标

为全面衡量算法性能，我们从课表质量与算法效率两个维度构建了评价体系：
*   **课表质量指标**：包括教师均匀性（UTE）、班级均匀性（UCL）、教室均匀性（URO）以及由这三者加权平均得到的综合均匀性得分（UOV），用于量化资源的均衡分布程度。
*   **算法效率指标**：包括排课成功率（SC）、运行时间（RT）和处理效率（EP，单位：门/秒），用于衡量算法的求解能力与计算开销。

### 4.2 高约束人工数据集（Db-2152）：鲁棒性与极限求解能力验证

在高约束数据集上的实验模拟了最严苛的排课环境，其结果是衡量算法鲁棒性的关键。

<center>**表 4-1 各算法在高约束人工数据集（Db-2152）上的效率指标对比**</center>

| 算法名称 | 总课程数 | 成功排课数 | 排课成功率<br>（SC，%） | 运行时间<br>（RT，秒） | 处理效率<br>（EP，门 / 秒） |
| --- | --- | --- | --- | --- | --- |
| Greedy | 2150 | 1684 | 78.33 | 0.05 | 43485.88 |
| GA | 2152 | 1771 | 82.30 | 612.46 | 3.51 |
| GGHA | 2150 | 1827 | 84.98 | 14.25 | 150.87 |
| ACO | 2152 | 1929 | 89.64 | 384.77 | 5.59 |
| MIG-ABC | 2152 | 1943 | 90.29 | 2293.42 | 0.94 |
| HGI-CAMAR | 2152 | 1927 | 89.54 | 3.01 | 715.26 |
| MSCAES | 2152 | 2104 | 97.77 | 4.11 | 523.25 |

<center>**表 4-2 各算法在高约束人工数据集（Db-2152）上的课表质量指标对比**</center>

| 算法名称 | 教师均匀性<br>（UTE） | 班级均匀性<br>（UCL） | 教室均匀性<br>（URO） | 综合均匀性得分<br>（UOV） |
| --- | --- | --- | --- | --- |
| Greedy | 0.2190 | 0.2891 | 0.3506 | 0.2734 |
| GA | 0.2372 | 0.3898 | 0.3833 | 0.3275 |
| GGHA | 0.1884 | 0.3434 | 0.3281 | 0.2784 |
| ACO | 0.2465 | 0.4067 | 0.4017 | 0.3416 |
| MIG-ABC | 0.2314 | 0.4133 | 0.4080 | 0.3395 |
| HGI-CAMAR | 0.2537 | 0.3914 | 0.3959 | 0.3372 |
| MSCAES | 0.3430 | 0.4972 | 0.4642 | 0.4289 |

如表4-1所示，在排课成功率（SC）上，MSCAES取得了97.77%的成绩，将未排课程压缩至48门，表现远超其他算法。次优的MIG-ABC成功率为90.29%，与MSCAES存在7.48个百分点的显著差距。这揭示了MSCAES三阶段协同机制的内在优势：在高冲突环境中，第一阶段HGIS-SR确保了关键课程的优先满足，为后续优化提供了稳定的"锚点"；第二阶段HGI-CAMAR在约束化的解空间内进行高效全局搜索；而至关重要的第三阶段GRCR-DLR，通过动态搜索资源"缝隙"，成功修复了大量在其他算法中被放弃的课程。

在时间效率上，MSCAES在4.11秒内完成计算，实现了速度与成功率的理想平衡。相比之下，传统元启发式算法（如GA和MIG-ABC）陷入了"时间换成功率"的困境，耗时过长，实用性欠佳。MSCAES的高效率得益于其启发式引导初始化与并行计算优化，使其处理效率（EP）达到523.25门/秒，远高于其他全局优化算法。

在课表质量方面（表4-2），MSCAES的综合均匀性得分（UOV）高达0.4289，较次优的ACO（0.3416）提升了25.56%，在教师、班级、教室三个维度的均匀性上均表现最佳。这主要归功于其贯穿于三个阶段的均衡性优化策略：HGI-CAMAR的适应度函数直接优化分布均衡性，而HGIS-SR和GRCR-DLR的资源选择模型中也内嵌了负载敏感的奖惩机制。这使得MSCAES在满足硬约束的同时，能够生成资源分配更合理的课表。

### 4.3 低约束基准数据集（Regular-795）：自适应性与效率验证

当系统约束放宽，对算法的主要考验转变为能否快速收敛，并利用富余资源进一步提升课表质量。

<center>**表 4-3 各算法在低约束基准数据集（Regular-795）上的效率指标对比**</center>

| 算法名称 | 总课程数 | 成功排课数 | 排课成功率<br>（SC，%） | 运行时间<br>（RT，秒） | 处理效率<br>（EP，门 / 秒） |
| --- | --- | --- | --- | --- | --- |
| Greedy | 795 | 609 | 76.60 | 0.01 | 64497.80 |
| GA | 795 | 728 | 91.57 | 150.10 | 5.30 |
| GGHA | 795 | 730 | 91.82 | 2.90 | 274.32 |
| ACO | 795 | 721 | 90.69 | 110.19 | 7.21 |
| MIG-ABC | 795 | 748 | 94.09 | 764.51 | 1.04 |
| HGI-CAMAR | 795 | 748 | 94.09 | 2.35 | 338.25 |
| MSCAES | 795 | 791 | 99.50 | 2.94 | 269.95 |

<center>**表 4-4 各算法在低约束基准数据集（Regular-795）上的课表质量指标对比**</center>

| 算法名称 | 教师均匀性<br>（UTE） | 班级均匀性<br>（UCL） | 教室均匀性<br>（URO） | 综合均匀性得分<br>（UOV） |
| --- | --- | --- | --- | --- |
| Greedy | 0.1682 | 0.3402 | 0.2803 | 0.2594 |
| GA | 0.1865 | 0.4692 | 0.3139 | 0.3251 |
| GGHA | 0.1625 | 0.3407 | 0.2509 | 0.2515 |
| ACO | 0.1889 | 0.4286 | 0.2937 | 0.3057 |
| MIG-ABC | 0.1881 | 0.4515 | 0.3080 | 0.3175 |
| HGI-CAMAR | 0.1923 | 0.4422 | 0.3142 | 0.3166 |
| MSCAES | 0.2384 | 0.5010 | 0.3599 | 0.3677 |

由表4-3可知，MSCAES在此场景下成功率接近完美（99.50%），运行时间保持在2.94秒。这充分展示了其自适应参数调整机制的有效性。系统能识别出低约束环境，并自动降低交叉和变异率以保护优良基因，从而快速收敛，避免了传统元启发式算法的"过度探索"问题。

在课表质量上（表4-4），MSCAES的UOV得分（0.3677）同样领先，较次优的GA（0.3251）提升了13.10%。这表明当解空间扩大时，MSCAES的多目标适应度函数能更有效地引导搜索向帕累托最优前沿逼近，尤其在班级均匀性（UCL）上表现突出，能为班级规划出分布更均匀、节奏更合理的课表。

### 4.4 真实教学数据集（Real-2152）:综合效能与应用价值验证

最后，在取自真实教务系统的Real-2152数据集上进行测试，以评估算法在混合约束、噪声数据和不规则需求下的综合应用效能。

<center>**表 4-5 各算法在真实教学数据集（Real-2152）上的效率指标对比**</center>

| 算法名称 | 总课程数 | 成功排课数 | 排课成功率<br>（SC，%） | 运行时间<br>（RT，秒） | 处理效率<br>（EP，门 / 秒） |
|---|---|---|---|---|---|
| Greedy | 2150 | 1702 | 79.16 | 0.08 | 25578.14 |
| GA | 2152 | 1753 | 81.46 | 662.07 | 3.25 |
| GGHA | 2150 | 1842 | 85.67 | 6.01 | 357.82 |
| ACO | 2152 | 1932 | 89.78 | 526.49 | 4.09 |
| MIG-ABC | 2152 | 1935 | 89.92 | 2637.75 | 0.82 |
| HGI-CAMAR | 2152 | 1917 | 89.08 | 3.39 | 634.08 |
| MSCAES | 2152 | 2096 | 97.40 | 4.24 | 507.85 |

<center>**表 4-6 各算法在真实教学数据集（Real-2152）上的课表质量指标对比**</center>

| 算法名称 | 教师均匀性<br>（UTE） | 班级均匀性<br>（UCL） | 教室均匀性<br>（URO） | 综合均匀性得分<br>（UOV） |
| --- | --- | --- | --- | --- |
| Greedy | 0.2339 | 0.2869 | 0.3393 | 0.2762 |
| GA | 0.2404 | 0.3833 | 0.3841 | 0.3263 |
| GGHA | 0.1858 | 0.3343 | 0.3313 | 0.2743 |
| ACO | 0.2366 | 0.3875 | 0.4006 | 0.3298 |
| MIG-ABC | 0.2552 | 0.3927 | 0.3889 | 0.3369 |
| HGI-CAMAR | 0.2427 | 0.3894 | 0.3934 | 0.3315 |
| MSCAES | 0.3409 | 0.4841 | 0.4702 | 0.4241 |

在最能反映实际应用价值的真实数据集上，MSCAES的综合性能优势依然显著。在核心的排课成功率（SC）上，MSCAES达到了97.40%，再次证明了其三阶段架构与真实业务需求的深度契合。在保证极高成功率的前提下，其4.24秒的运行时间使其具备了在实际教务系统中快速响应的能力。

在课表质量方面（表4-6），MSCAES的综合均匀性得分（UOV）达到了0.4241，较次优的MIG-ABC（0.3369）提升了25.88%，较纯贪心算法更是提升了53.55%。这一优势源于其多目标协同优化机制在整个求解过程中的持续作用，确保了算法在满足硬约束的同时，始终以提升资源分配的均衡性为导向。

### 4.5 分析与讨论

综合三个梯度化场景的实验数据，MSCAES在求解效率、成功率与解的质量（均衡性）三个核心维度上，均表现出相较于其他对比算法的显著优势，这与其他文献中混合策略优于单一策略的发现相一致[1,31]。

![图片](images/image_002.png)
<center>图4-1 各算法在不同数据集上排课成功率对比</center>

**"分而治之"架构带来鲁棒性与高成功率**。如图4-1所示，MSCAES在所有数据集中均取得了最高的排课成功率，尤其是在高约束和真实世界场景下，其优势尤为突出。这一成果直接验证了其三阶段"分而治之"架构的有效性。与传统单次优化或简单的两阶段混合算法不同，MSCAES通过任务分解，实现了对不同难度课程的精准处理：第一阶段HGIS-SR扮演了"稳定器"的角色，优先解决"硬骨头"课程；第二阶段HGI-CAMAR作为核心引擎高效发挥；第三阶段GRCR-DLR则是提升成功率的"临门一脚"，其针对性的修复机制成功救回了大量在其他算法中被放弃的课程。

![图片](images/image_003.png)
<center>图4-2 各算法在不同数据集上运行时间对比</center>

![图片](images/image_004.png)
<center>图4-3 各算法在不同数据集上处理效率对比</center>

**搜索策略与计算资源利用的平衡**。如图4-2与4-3所示，运行时间与处理效率的对比揭示了算法在"速度"与"质量"间的权衡。纯贪心算法牺牲了成功率，而传统元启发式算法则陷入了"时间换质量"的困境。MSCAES则实现了二者的理想平衡，其高处理效率源于：启发式引导初始化、冲突感知进化算子、自适应参数调节以及并行计算的综合运用。

![图片](images/image_005.png)
<center>图4-4 各算法在不同数据集上综合均匀性得分对比</center>

**多目标优化带来高质量均衡解**。如图4-4所示，综合均匀性得分（UOV）直观地反映了课表的最终实用质量。MSCAES在此项指标上全面领先，表明它不仅能"排出课"，更能"排好课"。其核心优势在于将均衡性作为核心优化目标贯穿于算法始终。在HGI-CAMAR阶段，适应度函数不仅惩罚硬约束冲突，还直接量化并奖励教师、班级、教室三个维度的分布均匀性（以变异系数CV度量），引导进化朝向帕累托最优前沿。这种将多个软约束目标整合到适应度函数中的做法，是现代元启发式算法设计的趋势[15,32]。在HGIS-SR和GRCR-DLR阶段，资源（时间槽/教室）的选择评分模型中内嵌了对教师/班级日均负载的奖惩因子，使得即便在贪心决策中，也蕴含了对全局均衡性的考量。 反观其他算法，大多仍停留在以满足硬约束为主、软约束为辅的传统模式[8,13]，导致其即便排课成功，课表的实用性与合理性也大打折扣。

MSCAES的成功并非单一技术的突破，而是系统性架构创新与多策略深度融合的成果。它通过将复杂的排课问题分解为"约束消解-全局优化-鲁棒修复"三个逻辑递进的子问题，并为每个子问题匹配最优的算法策略，最终在求解成功率、运行效率和课表质量三个维度上实现了"三赢"，为解决NP-hard的组合优化问题提供了一个高效且实用的范例。其设计理念与超启发式（Hyper-Heuristics）的研究方向[20,21]相契合，即通过高层次的策略组合来提升算法的通用性和鲁棒性。

## 5 结论

针对高校排课问题固有的高维度、多约束、NP难特性，本研究提出并实现了一种多策略融合协同式自适应进化排课系统（MSCAES）。该系统通过创新的三阶段协同架构，将复杂的排课任务分解为局部约束消解、全局搜索优化与鲁棒性增强三个环节，并深度融合了启发式贪心、自适应遗传算法与动态修复等多种策略，旨在实现排课成功率与资源分配均衡性的双重最优化。

arch, Modern heuristic techniques for combinatorial problems, 1993: 70–150.
[4] Kirkpatrick S, Gelatt Jr C D, Vecchi M P. Optimization by simulated annealing[J]. science, 1983, 220(4598): 671–680.
[5] Burke E K, Petrovic S. Recent research directions in automated timetabling[J]. European Journal of Operational Research, 2002, 140(2): 266–280.
[6] Lewis R. A survey of metaheuristic-based techniques for university timetabling problems[J]. OR spectrum, 2008, 30(1): 167–190.
[7] Burke E K, Mareček J, Parkes A J, et al. A supernodal formulation of vertex colouring with applications in course timetabling[J]. Annals of Operations Research, 2010, 179: 105–130.
[8] Henry Obit J. Developing novel meta-heuristic, hyper-heuristic and cooperative search for course timetabling problems, 2010.
[9] Abramson D. Constructing school timetables using simulated annealing: sequential and parallel algorithms[J]. Management science, 1991, 37(1): 98–113.
[10] Glover F. Tabu search—part I[J]. ORSA Journal on computing, 1989, 1(3): 190–206.
[11] Costa D. A tabu search algorithm for computing an operational timetable[J]. European Journal of Operational Research, 1994, 76(1): 98–110.
[12] Hertz A. Tabu search for large scale timetabling problems[J]. European journal of operational research, 1991, 54(1): 39–47.
[13] 刘莉, 栗超. 基于遗传算法的智能排课系统的设计[J]. 成都工业学院学报, 2023, 26(06): 52–55.
[14] Holland J H. Adaptation in Natural and Artificial Systems: An Introductory Analysis with Applications to Biology, Control, and Artificial Intelligence[M]. The MIT Press, 1992.
[15] Han X, Wang D. Gradual Optimization of University Course Scheduling Problem Using Genetic Algorithm and Dynamic Programming[J]. Algorithms, 2025, 18(3): 158.
[16] Moscato P. On Evolution, Search, Optimization, Genetic Algorithms and Martial Arts - Towards Memetic Algorithms[J]. Caltech Concurrent Computation Program, 2000.
[17] Burke E K, Newall J P, Weare R F. A memetic algorithm for university exam timetabling[C]. Practice and Theory of Automated Timetabling: First International Conference Edinburgh, UK, August 29–September 1, 1995 Selected Papers 1, 1996: 241–250.
[18] Ong Y, Lim M-H, Zhu N, et al. Classification of adaptive memetic algorithms: A comparative study[J]. IEEE transactions on systems, man, and cybernetics. Part B, Cybernetics : a publication of the IEEE Systems, Man, and Cybernetics Society, 2006, 36: 141–52.
[19] Ghaffar A, Din I U, Tariq A, et al. Hybridization and artificial intelligence in optimizing university examination timetabling problem: A systematic review[J]. Review of Education, 2025, 13(2): e70071.
[20] Burke E, Kendall G, Newall J, et al. Hyper-heuristics: An emerging direction in modern search technology[J]. Handbook of metaheuristics, 2003: 457–474.
[21] Burke E K, Gendreau M, Hyde M, et al. Hyper-heuristics: A survey of the state of the art[J]. Journal of the Operational Research Society, 2013, 64(12): 1695–1724.
[22] Qu R, Burke E K. Hybridizations within a graph-based hyper-heuristic framework for university timetabling problems[J]. Journal of the Operational Research Society, 2009, 60(9): 1273–1285.
[23] Soria-Alcaraz J A, Ochoa G, Swan J, et al. Effective learning hyper-heuristics for the course timetabling problem[J]. European Journal of Operational Research, 2014, 238(1): 77–86.
[24] Burke E K, Mccollum B, Meisels A, et al. A graph-based hyper-heuristic for educational timetabling problems[J]. European Journal of Operational Research, 2007, 176(1): 177–192.
[25] Song T, Chen M, Xu Y, et al. Competition-guided multi-neighborhood local search algorithm for the university course timetabling problem[J]. Applied Soft Computing, 2021, 110: 107624.
[26] Kohshori M S, Abadeh M S. Hybrid genetic algorithms for university course timetabling[J]. International Journal of Computer Science Issues (IJCSI), 2012, 9(2): 446.
[27] Burke E K, Newall J P. A multistage evolutionary algorithm for the timetable problem[J]. IEEE transactions on evolutionary computation, 1999, 3(1): 63–74.
[28] 韦芳萍. 基于蚁群算法的高校排课问题的研究[J]. 电脑编程技巧与维护, 2023(07): 32–34+44.
[29] Socha K, Knowles J, Sampels M. A max-min ant system for the university course timetabling problem[C]. Ant Algorithms: Third International Workshop, ANTS 2002 Brussels, Belgium, September 12–14, 2002 Proceedings 3, 2002: 1–13.
[30] 周新宇, 刘颖, 吴艳林, et al. 基于多元信息引导的人工蜂群算法[J]. 电子学报, 2024, 52(04): 1349–1363.
[31] Abdullah S, Turabieh H. On the use of multi neighbourhood structures within a Tabu-based memetic approach to university timetabling problems[J]. information sciences, 2012, 191: 146–168.
[32] Zitzler E, Laumanns M, Thiele L. SPEA2: Improving the strength Pareto evolutionary algorithm[J]. TIK report, 2001, 103.
[33] Teoh C K, Wibowo A, Ngadiman M S. Review of state of the art for metaheuristic techniques in Academic Scheduling Problems[J]. Artificial Intelligence Review, 2015, 44: 1–21.
[34] Bashab A, Ibrahim A O, Abedelgabar E E, et al. A systematic mapping study on solving university timetabling problems using meta-heuristic algorithms[J]. Neural Computing and Applications, 2020, 32: 17397–17432.

