using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;
using System.Windows;
using System.Windows.Controls;
using DocxToMarkdownConverter.Services;
using Microsoft.Extensions.DependencyInjection;
using MessageBox = System.Windows.MessageBox;
using Application = System.Windows.Application;
using Button = System.Windows.Controls.Button;
using HorizontalAlignment = System.Windows.HorizontalAlignment;

namespace DocxToMarkdownConverter.Views;

/// <summary>
/// 帮助窗口
/// </summary>
public partial class HelpWindow : Window
{
    private readonly IUpdateService? _updateService;

    public HelpWindow()
    {
        InitializeComponent();
        InitializeVersionInfo();
        
        // 尝试获取更新服务
        try
        {
            var app = Application.Current as App;
            _updateService = app?.ServiceProvider?.GetService<IUpdateService>();
        }
        catch
        {
            // 忽略服务获取错误
        }
    }

    private void InitializeVersionInfo()
    {
        try
        {
            var assembly = Assembly.GetExecutingAssembly();
            var version = assembly.GetName().Version?.ToString() ?? "1.0.0.0";
            var assemblyPath = Path.Combine(AppContext.BaseDirectory, "DocxToMarkdownConverter.exe");
            var buildDate = File.GetLastWriteTime(assemblyPath).ToString("yyyy-MM-dd");
            
            VersionInfoTextBlock.Text = $"版本: {version}\n构建日期: {buildDate}\n版权所有 © 2024 Augment Code";
        }
        catch (Exception ex)
        {
            VersionInfoTextBlock.Text = $"版本信息获取失败: {ex.Message}";
        }
    }

    private void ViewFullDocumentation_Click(object sender, RoutedEventArgs e)
    {
        try
        {
            // 尝试打开完整文档
            var documentationPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Documentation", "UserGuide.md");
            
            if (File.Exists(documentationPath))
            {
                // 使用默认程序打开 Markdown 文件
                Process.Start(new ProcessStartInfo
                {
                    FileName = documentationPath,
                    UseShellExecute = true
                });
            }
            else
            {
                // 如果本地文档不存在，打开在线文档
                Process.Start(new ProcessStartInfo
                {
                    FileName = "https://augmentcode.com/docx-converter/docs",
                    UseShellExecute = true
                });
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"无法打开文档: {ex.Message}", "错误", 
                MessageBoxButton.OK, MessageBoxImage.Warning);
        }
    }

    private async void CheckForUpdates_Click(object sender, RoutedEventArgs e)
    {
        if (_updateService == null)
        {
            MessageBox.Show("更新服务不可用", "提示", 
                MessageBoxButton.OK, MessageBoxImage.Information);
            return;
        }

        try
        {
            var button = sender as Button;
            if (button != null)
            {
                button.IsEnabled = false;
                button.Content = "检查中...";
            }

            var updateInfo = await _updateService.CheckForUpdatesAsync();
            
            if (updateInfo.IsAvailable)
            {
                var result = MessageBox.Show(
                    $"发现新版本 {updateInfo.Version}\n\n{updateInfo.ReleaseNotes}\n\n是否立即下载更新？",
                    "发现更新",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Information);

                if (result == MessageBoxResult.Yes)
                {
                    // 显示更新下载窗口
                    var updateWindow = new UpdateDownloadWindow(updateInfo, _updateService);
                    updateWindow.Owner = this;
                    updateWindow.ShowDialog();
                }
            }
            else
            {
                MessageBox.Show("当前已是最新版本", "检查更新", 
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            MessageBox.Show($"检查更新失败: {ex.Message}", "错误", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
        finally
        {
            var button = sender as Button;
            if (button != null)
            {
                button.IsEnabled = true;
                button.Content = "检查更新";
            }
        }
    }

    private void Close_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    protected override void OnSourceInitialized(EventArgs e)
    {
        base.OnSourceInitialized(e);
        
        // 应用主题
        try
        {
            var app = Application.Current as App;
            var themeManager = app?.ServiceProvider?.GetService<IThemeManager>();
            if (themeManager != null)
            {
                _ = themeManager.ApplyThemeAsync(themeManager.CurrentTheme, false);
            }
        }
        catch
        {
            // 忽略主题应用错误
        }
    }
}

/// <summary>
/// 更新下载窗口
/// </summary>
public partial class UpdateDownloadWindow : Window
{
    private readonly UpdateInfo _updateInfo;
    private readonly IUpdateService _updateService;

    public UpdateDownloadWindow(UpdateInfo updateInfo, IUpdateService updateService)
    {
        _updateInfo = updateInfo ?? throw new ArgumentNullException(nameof(updateInfo));
        _updateService = updateService ?? throw new ArgumentNullException(nameof(updateService));
        
        InitializeComponent();
        InitializeDownload();
    }

    private void InitializeComponent()
    {
        Title = "下载更新";
        Width = 400;
        Height = 200;
        WindowStartupLocation = WindowStartupLocation.CenterOwner;
        ResizeMode = ResizeMode.NoResize;

        var grid = new Grid();
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });
        grid.RowDefinitions.Add(new RowDefinition { Height = GridLength.Auto });

        var titleText = new System.Windows.Controls.TextBlock
        {
            Text = $"正在下载版本 {_updateInfo.Version}",
            FontSize = 16,
            FontWeight = FontWeights.Bold,
            Margin = new Thickness(20, 20, 20, 10),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        Grid.SetRow(titleText, 0);
        grid.Children.Add(titleText);

        var progressBar = new System.Windows.Controls.ProgressBar
        {
            Name = "DownloadProgressBar",
            Height = 20,
            Margin = new Thickness(20, 10, 20, 10)
        };
        Grid.SetRow(progressBar, 1);
        grid.Children.Add(progressBar);

        var statusText = new System.Windows.Controls.TextBlock
        {
            Name = "StatusTextBlock",
            Text = "准备下载...",
            Margin = new Thickness(20, 5, 20, 10),
            HorizontalAlignment = HorizontalAlignment.Center
        };
        Grid.SetRow(statusText, 2);
        grid.Children.Add(statusText);

        var buttonPanel = new System.Windows.Controls.StackPanel
        {
            Orientation = System.Windows.Controls.Orientation.Horizontal,
            HorizontalAlignment = HorizontalAlignment.Center,
            Margin = new Thickness(20, 10, 20, 20)
        };

        var cancelButton = new System.Windows.Controls.Button
        {
            Content = "取消",
            Padding = new Thickness(20, 8, 20, 8),
            Margin = new Thickness(5, 0, 5, 0)
        };
        cancelButton.Click += (s, e) => Close();
        buttonPanel.Children.Add(cancelButton);

        Grid.SetRow(buttonPanel, 3);
        grid.Children.Add(buttonPanel);

        Content = grid;

        // 保存控件引用
        _progressBar = progressBar;
        _statusTextBlock = statusText;
    }

    private System.Windows.Controls.ProgressBar _progressBar = null!;
    private System.Windows.Controls.TextBlock _statusTextBlock = null!;

    private async void InitializeDownload()
    {
        try
        {
            var progress = new Progress<DownloadProgress>(OnDownloadProgress);
            var success = await _updateService.DownloadUpdateAsync(_updateInfo, progress);

            if (success)
            {
                _statusTextBlock.Text = "下载完成，准备安装...";
                
                var result = MessageBox.Show(
                    "更新下载完成，是否立即安装？\n\n注意：安装过程中应用程序将会重启。",
                    "安装更新",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // 这里应该启动安装过程
                    MessageBox.Show("更新安装功能将在后续版本中实现", "提示", 
                        MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            else
            {
                _statusTextBlock.Text = "下载失败";
                MessageBox.Show("更新下载失败，请稍后重试", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
        catch (Exception ex)
        {
            _statusTextBlock.Text = "下载出错";
            MessageBox.Show($"下载更新时发生错误: {ex.Message}", "错误", 
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private void OnDownloadProgress(DownloadProgress progress)
    {
        Dispatcher.Invoke(() =>
        {
            _progressBar.Value = progress.ProgressPercentage;
            
            var speedMB = progress.DownloadSpeed / (1024 * 1024);
            var receivedMB = progress.BytesReceived / (1024.0 * 1024);
            var totalMB = progress.TotalBytes / (1024.0 * 1024);
            
            _statusTextBlock.Text = $"已下载 {receivedMB:F1} MB / {totalMB:F1} MB ({speedMB:F1} MB/s)";
        });
    }
}
