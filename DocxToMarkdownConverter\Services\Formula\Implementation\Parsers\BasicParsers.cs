using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Parsers;

/// <summary>
/// 根式解析器
/// </summary>
public class RadicalParser : FormulaParserBase
{
    public RadicalParser(ILogger<RadicalParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.Radical };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Parsing radical element: {ElementId}", element.Id);

            if (element.SourceElement is not Radical radical)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a Radical");
            }

            var structure = await ParseRadicalAsync(radical);
            var components = await CreateRadicalComponentsAsync(radical);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse radical: {ex.Message}", ex);
        }
    }

    private async Task<RadicalStructure> ParseRadicalAsync(Radical radical)
    {
        var structure = new RadicalStructure();

        // 解析被开方数
        var radicand = radical.GetFirstChild<Base>();
        if (radicand != null)
        {
            structure.Radicand = await ParseRadicalPartAsync(radicand, "Radicand");
        }

        // 解析根指数（如果存在）
        var degree = radical.GetFirstChild<Degree>();
        if (degree != null)
        {
            structure.Index = await ParseRadicalPartAsync(degree, "Index");
        }

        return structure;
    }

    private async Task<FormulaComponent> ParseRadicalPartAsync(DocumentFormat.OpenXml.OpenXmlElement part, string partType)
    {
        var component = CreateComponent(partType);
        
        if (part.HasChildren)
        {
            var children = await ParseChildrenAsync(part);
            foreach (var child in children)
            {
                component.Children.Add(child);
            }
            var combinedText = string.Join("", children.Select(c => c.Data?.ToString() ?? ""));
            component.Data = CleanText(combinedText);
        }
        else
        {
            component.Data = CleanText(ExtractTextSafely(part));
        }

        return component;
    }

    private async Task<IList<FormulaComponent>> CreateRadicalComponentsAsync(Radical radical)
    {
        var components = new List<FormulaComponent>();

        var radicand = radical.GetFirstChild<Base>();
        if (radicand != null)
        {
            components.Add(await ParseRadicalPartAsync(radicand, "Radicand"));
        }

        var degree = radical.GetFirstChild<Degree>();
        if (degree != null)
        {
            components.Add(await ParseRadicalPartAsync(degree, "Index"));
        }

        return components;
    }
}

/// <summary>
/// 上下标解析器
/// </summary>
public class ScriptParser : FormulaParserBase
{
    public ScriptParser(ILogger<ScriptParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] 
    { 
        FormulaType.Superscript, 
        FormulaType.Subscript, 
        FormulaType.SubSuperscript 
    };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Parsing script element: {ElementId}", element.Id);

            var structure = element.SourceElement switch
            {
                Superscript superscript => await ParseSuperscriptAsync(superscript),
                Subscript subscript => await ParseSubscriptAsync(subscript),
                SubSuperscript subSuperscript => await ParseSubSuperscriptAsync(subSuperscript),
                _ => throw new ArgumentException("Invalid script element type")
            };

            var components = await CreateScriptComponentsAsync(element.SourceElement);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse script: {ex.Message}", ex);
        }
    }

    private async Task<ScriptStructure> ParseSuperscriptAsync(Superscript superscript)
    {
        var structure = new ScriptStructure { Type = FormulaType.Superscript };

        var baseElement = superscript.GetFirstChild<Base>();
        if (baseElement != null)
        {
            structure.Base = await ParseScriptPartAsync(baseElement, "Base");
        }

        var supElement = superscript.GetFirstChild<SuperscriptText>();
        if (supElement != null)
        {
            structure.Superscript = await ParseScriptPartAsync(supElement, "Superscript");
        }

        return structure;
    }

    private async Task<ScriptStructure> ParseSubscriptAsync(Subscript subscript)
    {
        var structure = new ScriptStructure { Type = FormulaType.Subscript };

        var baseElement = subscript.GetFirstChild<Base>();
        if (baseElement != null)
        {
            structure.Base = await ParseScriptPartAsync(baseElement, "Base");
        }

        var subElement = subscript.GetFirstChild<SubscriptText>();
        if (subElement != null)
        {
            structure.Subscript = await ParseScriptPartAsync(subElement, "Subscript");
        }

        return structure;
    }

    private async Task<ScriptStructure> ParseSubSuperscriptAsync(SubSuperscript subSuperscript)
    {
        var structure = new ScriptStructure { Type = FormulaType.SubSuperscript };

        var baseElement = subSuperscript.GetFirstChild<Base>();
        if (baseElement != null)
        {
            structure.Base = await ParseScriptPartAsync(baseElement, "Base");
        }

        var subElement = subSuperscript.GetFirstChild<SubscriptText>();
        if (subElement != null)
        {
            structure.Subscript = await ParseScriptPartAsync(subElement, "Subscript");
        }

        var supElement = subSuperscript.GetFirstChild<SuperscriptText>();
        if (supElement != null)
        {
            structure.Superscript = await ParseScriptPartAsync(supElement, "Superscript");
        }

        return structure;
    }

    private async Task<FormulaComponent> ParseScriptPartAsync(DocumentFormat.OpenXml.OpenXmlElement part, string partType)
    {
        var component = CreateComponent(partType);
        
        if (part.HasChildren)
        {
            var children = await ParseChildrenAsync(part);
            foreach (var child in children)
            {
                component.Children.Add(child);
            }
            var combinedText = string.Join("", children.Select(c => c.Data?.ToString() ?? ""));
            component.Data = CleanText(combinedText);
        }
        else
        {
            component.Data = CleanText(ExtractTextSafely(part));
        }

        return component;
    }

    private async Task<IList<FormulaComponent>> CreateScriptComponentsAsync(object scriptElement)
    {
        var components = new List<FormulaComponent>();

        switch (scriptElement)
        {
            case Superscript superscript:
                var baseElement = superscript.GetFirstChild<Base>();
                if (baseElement != null)
                {
                    components.Add(await ParseScriptPartAsync(baseElement, "Base"));
                }
                var supElement = superscript.GetFirstChild<SuperscriptText>();
                if (supElement != null)
                {
                    components.Add(await ParseScriptPartAsync(supElement, "Superscript"));
                }
                break;

            case Subscript subscript:
                baseElement = subscript.GetFirstChild<Base>();
                if (baseElement != null)
                {
                    components.Add(await ParseScriptPartAsync(baseElement, "Base"));
                }
                var subElement = subscript.GetFirstChild<SubscriptText>();
                if (subElement != null)
                {
                    components.Add(await ParseScriptPartAsync(subElement, "Subscript"));
                }
                break;

            case SubSuperscript subSuperscript:
                baseElement = subSuperscript.GetFirstChild<Base>();
                if (baseElement != null)
                {
                    components.Add(await ParseScriptPartAsync(baseElement, "Base"));
                }
                subElement = subSuperscript.GetFirstChild<SubscriptText>();
                if (subElement != null)
                {
                    components.Add(await ParseScriptPartAsync(subElement, "Subscript"));
                }
                supElement = subSuperscript.GetFirstChild<SuperscriptText>();
                if (supElement != null)
                {
                    components.Add(await ParseScriptPartAsync(supElement, "Superscript"));
                }
                break;
        }

        return components;
    }
}

/// <summary>
/// N元运算符解析器
/// </summary>
public class NaryParser : FormulaParserBase
{
    public NaryParser(ILogger<NaryParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.Nary };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            _logger.LogDebug("Parsing nary element: {ElementId}", element.Id);

            if (element.SourceElement is not Nary nary)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a Nary");
            }

            var structure = await ParseNaryAsync(nary);
            var components = await CreateNaryComponentsAsync(nary);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse nary: {ex.Message}", ex);
        }
    }

    private async Task<FormulaStructure> ParseNaryAsync(Nary nary)
    {
        // 这里可以创建一个专门的NaryStructure类
        // 为了简化，我们使用基础的FormulaStructure
        var structure = new FormulaStructure
        {
            Type = FormulaType.Nary
        };

        return structure;
    }

    private async Task<IList<FormulaComponent>> CreateNaryComponentsAsync(Nary nary)
    {
        var components = new List<FormulaComponent>();

        // 解析运算符
        var naryProperties = nary.GetFirstChild<NaryProperties>();
        if (naryProperties != null)
        {
            var operatorComponent = CreateComponent("Operator");
            // 这里可以解析具体的运算符类型
            components.Add(operatorComponent);
        }

        // 解析下限
        var subElement = nary.GetFirstChild<SubArgument>();
        if (subElement != null)
        {
            var subComponent = CreateComponent("LowerLimit", ExtractTextSafely(subElement));
            components.Add(subComponent);
        }

        // 解析上限
        var supElement = nary.GetFirstChild<SuperArgument>();
        if (supElement != null)
        {
            var supComponent = CreateComponent("UpperLimit", ExtractTextSafely(supElement));
            components.Add(supComponent);
        }

        // 解析表达式
        var baseElement = nary.GetFirstChild<Base>();
        if (baseElement != null)
        {
            var baseComponent = CreateComponent("Expression", ExtractTextSafely(baseElement));
            components.Add(baseComponent);
        }

        return components;
    }
}

/// <summary>
/// 分隔符解析器
/// </summary>
public class DelimiterParser : FormulaParserBase
{
    public DelimiterParser(ILogger<DelimiterParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.Delimiter };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            if (element.SourceElement is not Delimiter delimiter)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a Delimiter");
            }

            var structure = new FormulaStructure { Type = FormulaType.Delimiter };
            var components = await CreateDelimiterComponentsAsync(delimiter);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse delimiter: {ex.Message}", ex);
        }
    }

    private async Task<IList<FormulaComponent>> CreateDelimiterComponentsAsync(Delimiter delimiter)
    {
        var components = new List<FormulaComponent>();

        // 解析分隔符属性
        var delimiterProperties = delimiter.GetFirstChild<DelimiterProperties>();
        if (delimiterProperties != null)
        {
            var propertiesComponent = CreateComponent("DelimiterProperties");
            components.Add(propertiesComponent);
        }

        // 解析内容
        var baseElement = delimiter.GetFirstChild<Base>();
        if (baseElement != null)
        {
            var contentComponent = CreateComponent("Content", ExtractTextSafely(baseElement));
            components.Add(contentComponent);
        }

        return components;
    }
}

/// <summary>
/// 数学函数解析器
/// </summary>
public class FunctionParser : FormulaParserBase
{
    public FunctionParser(ILogger<FunctionParser> logger) : base(logger)
    {
    }

    public override FormulaType[] SupportedTypes => new[] { FormulaType.MathFunction };

    public override async Task<FormulaParseResult> ParseAsync(FormulaElement element)
    {
        var startTime = DateTime.UtcNow;

        try
        {
            if (element.SourceElement is not MathFunction function)
            {
                return CreateFailureResult("INVALID_ELEMENT", "Element is not a MathFunction");
            }

            var structure = new FormulaStructure { Type = FormulaType.MathFunction };
            var components = await CreateFunctionComponentsAsync(function);

            var result = CreateSuccessResult(structure, components);
            result.ParseTimeMs = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

            return result;
        }
        catch (Exception ex)
        {
            return CreateFailureResult("PARSE_ERROR", $"Failed to parse function: {ex.Message}", ex);
        }
    }

    private async Task<IList<FormulaComponent>> CreateFunctionComponentsAsync(MathFunction function)
    {
        var components = new List<FormulaComponent>();

        // 解析函数名
        var functionName = function.GetFirstChild<FunctionName>();
        if (functionName != null)
        {
            var nameComponent = CreateComponent("FunctionName", ExtractTextSafely(functionName));
            components.Add(nameComponent);
        }

        // 解析参数
        var baseElement = function.GetFirstChild<Base>();
        if (baseElement != null)
        {
            var argumentComponent = CreateComponent("Argument", ExtractTextSafely(baseElement));
            components.Add(argumentComponent);
        }

        return components;
    }
}
