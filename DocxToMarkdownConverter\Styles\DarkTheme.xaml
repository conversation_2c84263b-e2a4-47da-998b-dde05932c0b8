<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <ResourceDictionary.MergedDictionaries>
        <!-- Material Design Dark Theme with Modern Colors -->
        <materialDesign:BundledTheme BaseTheme="Dark" PrimaryColor="Blue" SecondaryColor="Cyan" />
        <ResourceDictionary Source="pack://application:,,,/MaterialDesignThemes.Wpf;component/Themes/MaterialDesignTheme.Defaults.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <!-- Modern Dark Theme Custom Colors -->
    <SolidColorBrush x:Key="AppBackgroundBrush" Color="#0D1117"/>
    <SolidColorBrush x:Key="AppSurfaceBrush" Color="#161B22"/>
    <SolidColorBrush x:Key="AppCardBrush" Color="#21262D"/>
    <SolidColorBrush x:Key="AppTextPrimaryBrush" Color="#F0F6FC"/>
    <SolidColorBrush x:Key="AppTextSecondaryBrush" Color="#8B949E"/>
    <SolidColorBrush x:Key="AppDividerBrush" Color="#30363D"/>
    <SolidColorBrush x:Key="AppHoverBrush" Color="#262C36"/>
    <SolidColorBrush x:Key="AppSelectedBrush" Color="#2F3349"/>
    <SolidColorBrush x:Key="AppBorderBrush" Color="#30363D"/>

    <!-- Shadow and elevation colors for dark theme -->
    <Color x:Key="MaterialDesignShadow">#000000</Color>
    <SolidColorBrush x:Key="MaterialDesignShadowBrush" Color="{StaticResource MaterialDesignShadow}"/>
    
    <!-- Enhanced Navigation Colors for Better Contrast -->
    <SolidColorBrush x:Key="NavigationBackgroundBrush" Color="#0D1117"/>
    <SolidColorBrush x:Key="NavigationHeaderBrush" Color="#161B22"/>
    <SolidColorBrush x:Key="NavigationItemDefaultBrush" Color="Transparent"/>
    <SolidColorBrush x:Key="NavigationItemHoverBrush" Color="#21262D"/>
    <SolidColorBrush x:Key="NavigationItemSelectedBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="NavigationItemActiveBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="NavigationTextDefaultBrush" Color="#8B949E"/>
    <SolidColorBrush x:Key="NavigationTextHoverBrush" Color="#F0F6FC"/>
    <SolidColorBrush x:Key="NavigationTextSelectedBrush" Color="#FFFFFF"/>
    <SolidColorBrush x:Key="NavigationIconDefaultBrush" Color="#8B949E"/>
    <SolidColorBrush x:Key="NavigationIconHoverBrush" Color="#F0F6FC"/>
    <SolidColorBrush x:Key="NavigationIconSelectedBrush" Color="#FFFFFF"/>

    <!-- Progress Colors -->
    <SolidColorBrush x:Key="ProgressBackgroundBrush" Color="#30363D"/>
    <SolidColorBrush x:Key="ProgressForegroundBrush" Color="#0078D4"/>

    <!-- Modern Status Colors -->
    <SolidColorBrush x:Key="SuccessBrush" Color="#3FB950"/>
    <SolidColorBrush x:Key="WarningBrush" Color="#D29922"/>
    <SolidColorBrush x:Key="ErrorBrush" Color="#F85149"/>
    <SolidColorBrush x:Key="InfoBrush" Color="#58A6FF"/>

    <!-- Accent Colors -->
    <SolidColorBrush x:Key="AccentBrush" Color="#0078D4"/>
    <SolidColorBrush x:Key="AccentHoverBrush" Color="#106EBE"/>
    <SolidColorBrush x:Key="AccentPressedBrush" Color="#005A9E"/>
    
    <!-- Enhanced Shadow Effects for Dark Theme -->
    <DropShadowEffect x:Key="CardShadowEffect" Color="Black" Opacity="0.4" ShadowDepth="4" BlurRadius="12"/>
    <DropShadowEffect x:Key="ButtonShadowEffect" Color="Black" Opacity="0.5" ShadowDepth="2" BlurRadius="8"/>
    <DropShadowEffect x:Key="ElevatedShadowEffect" Color="Black" Opacity="0.6" ShadowDepth="8" BlurRadius="20"/>
    <DropShadowEffect x:Key="SubtleShadowEffect" Color="Black" Opacity="0.2" ShadowDepth="1" BlurRadius="4"/>

    <!-- Glow Effects for Accent Elements -->
    <DropShadowEffect x:Key="AccentGlowEffect" Color="#0078D4" Opacity="0.3" ShadowDepth="0" BlurRadius="8"/>
    <DropShadowEffect x:Key="SuccessGlowEffect" Color="#3FB950" Opacity="0.3" ShadowDepth="0" BlurRadius="6"/>
    <DropShadowEffect x:Key="ErrorGlowEffect" Color="#F85149" Opacity="0.3" ShadowDepth="0" BlurRadius="6"/>

</ResourceDictionary>