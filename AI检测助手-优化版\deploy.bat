@echo off
chcp 65001 >nul
echo ========================================
echo    AI检测助手本地部署脚本 v2.1.0
echo ========================================
echo.

:: 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ 错误: 未检测到Node.js
    echo 💡 请先安装Node.js: https://nodejs.org/
    pause
    exit /b 1
)

echo ✅ Node.js已安装
node --version

:: 检查端口是否被占用
netstat -an | findstr ":3000" >nul
if %errorlevel% equ 0 (
    echo ⚠️  警告: 端口3000已被占用
    echo 💡 将使用端口3001启动服务
    set PORT=3001
) else (
    set PORT=3000
)

:: 配置hosts文件提示
echo.
echo 📝 配置hosts文件 (需要管理员权限):
echo    1. 以管理员身份打开记事本
echo    2. 打开文件: C:\Windows\System32\drivers\etc\hosts
echo    3. 添加以下行:
echo       127.0.0.1 ai-detector.local
echo    4. 保存文件
echo.

set /p HOSTS_CONFIGURED="是否已配置hosts文件? (y/n): "
if /i "%HOSTS_CONFIGURED%" neq "y" (
    echo.
    echo 💡 您可以稍后配置hosts文件，现在使用localhost访问
    echo.
)

:: 启动服务器
echo 🚀 正在启动AI检测助手服务器...
echo.

if "%PORT%"=="3001" (
    node server.js --port 3001 --domain ai-detector.local
) else (
    node server.js --port 3000 --domain ai-detector.local
)

pause
