using Microsoft.Extensions.Logging;
using Moq;
using Xunit;
using DocumentFormat.OpenXml.Wordprocessing;
using DocumentFormat.OpenXml;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.Models;
using System.Text;

namespace DocxToMarkdownConverter.Tests;

/// <summary>
/// 表格处理器单元测试
/// 验证简化后的表格转换功能是否正确工作
/// </summary>
public class TableProcessorTests
{
    private readonly Mock<ITextProcessor> _mockTextProcessor;
    private readonly Mock<ILogger<TableProcessor>> _mockLogger;
    private readonly TableProcessor _tableProcessor;

    public TableProcessorTests()
    {
        _mockTextProcessor = new Mock<ITextProcessor>();
        _mockLogger = new Mock<ILogger<TableProcessor>>();
        _tableProcessor = new TableProcessor(_mockTextProcessor.Object, _mockLogger.Object);
    }

    [Fact]
    public void ProcessTable_WithNullTable_ReturnsEmpty()
    {
        // Arrange
        var options = new ConversionOptions();

        // Act
        var result = _tableProcessor.ProcessTable(null, options);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void ProcessTable_WithInvalidTableType_ReturnsEmpty()
    {
        // Arrange
        var options = new ConversionOptions();
        var invalidTable = "not a table";

        // Act
        var result = _tableProcessor.ProcessTable(invalidTable, options);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void ProcessTableRow_WithNullRow_ReturnsEmpty()
    {
        // Act
        var result = _tableProcessor.ProcessTableRow(null);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void ProcessTableCell_WithNullCell_ReturnsEmpty()
    {
        // Act
        var result = _tableProcessor.ProcessTableCell(null);

        // Assert
        Assert.Equal(string.Empty, result);
    }

    [Fact]
    public void ProcessTableCell_WithEmptyCell_ReturnsSpace()
    {
        // Arrange
        var cell = CreateTableCell("");
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns("");

        // Act
        var result = _tableProcessor.ProcessTableCell(cell);

        // Assert
        Assert.Equal(" ", result);
    }

    [Fact]
    public void ProcessTableCell_WithTextContent_ReturnsCleanedText()
    {
        // Arrange
        var cell = CreateTableCell("测试内容");
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns("测试内容");

        // Act
        var result = _tableProcessor.ProcessTableCell(cell);

        // Assert
        Assert.Equal("测试内容", result);
    }

    [Fact]
    public void ProcessTableCell_WithPipeCharacter_EscapesPipe()
    {
        // Arrange
        var cell = CreateTableCell("列1|列2");
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns("列1|列2");

        // Act
        var result = _tableProcessor.ProcessTableCell(cell);

        // Assert
        Assert.Equal("列1\\|列2", result);
    }

    [Fact]
    public void ProcessTableCell_WithNewlines_ReplacesWithSpaces()
    {
        // Arrange
        var cell = CreateTableCell("第一行\n第二行");
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns("第一行\n第二行");

        // Act
        var result = _tableProcessor.ProcessTableCell(cell);

        // Assert
        Assert.Equal("第一行 第二行", result);
    }

    [Fact]
    public void ProcessTableCell_WithMergedCell_AddsSpanNotation()
    {
        // Arrange
        var cell = CreateTableCellWithSpan("合并单元格", 2);
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns("合并单元格");

        // Act
        var result = _tableProcessor.ProcessTableCell(cell);

        // Assert
        Assert.Contains("[跨2列]", result);
        Assert.Contains("合并单元格", result);
    }

    /// <summary>
    /// 创建测试用的表格单元格
    /// </summary>
    private TableCell CreateTableCell(string text)
    {
        var cell = new TableCell();
        var paragraph = new Paragraph();
        var run = new Run();
        var textElement = new Text(text);
        
        run.Append(textElement);
        paragraph.Append(run);
        cell.Append(paragraph);
        
        return cell;
    }

    /// <summary>
    /// 创建带有跨列属性的表格单元格
    /// </summary>
    private TableCell CreateTableCellWithSpan(string text, int span)
    {
        var cell = CreateTableCell(text);
        var cellProperties = new TableCellProperties();
        var gridSpan = new GridSpan() { Val = span };
        cellProperties.Append(gridSpan);
        cell.Append(cellProperties);
        
        return cell;
    }

    [Fact]
    public void ProcessTableRow_WithValidCells_ReturnsMarkdownRow()
    {
        // Arrange
        var row = CreateTableRow(new[] { "列1", "列2", "列3" });
        _mockTextProcessor.Setup(x => x.ProcessParagraph(It.IsAny<Paragraph>(), It.IsAny<ConversionOptions>()))
                         .Returns<Paragraph, ConversionOptions>((p, o) => p.InnerText);

        // Act
        var result = _tableProcessor.ProcessTableRow(row);

        // Assert
        Assert.Equal("| 列1 | 列2 | 列3 |", result);
    }

    /// <summary>
    /// 创建测试用的表格行
    /// </summary>
    private TableRow CreateTableRow(string[] cellTexts)
    {
        var row = new TableRow();
        
        foreach (var text in cellTexts)
        {
            var cell = CreateTableCell(text);
            row.Append(cell);
        }
        
        return row;
    }

    [Theory]
    [InlineData(1, "| --- |")]
    [InlineData(2, "| --- | --- |")]
    [InlineData(3, "| --- | --- | --- |")]
    public void CreateMarkdownSeparator_WithDifferentColumnCounts_ReturnsCorrectFormat(int columnCount, string expected)
    {
        // 使用反射访问私有方法进行测试
        var method = typeof(TableProcessor).GetMethod("CreateMarkdownSeparator", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        
        // Act
        var result = method?.Invoke(_tableProcessor, new object[] { columnCount }) as string;

        // Assert
        Assert.Equal(expected, result);
    }
}
