<UserControl x:Class="DocxToMarkdownConverter.Controls.LogViewerControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="400" d:DesignWidth="800">
    
    <UserControl.Resources>
        <!-- 转换器 -->
        <BooleanToVisibilityConverter x:Key="BooleanToVisibilityConverter"/>
        
        <!-- 空值到可见性转换器 -->
        <Style x:Key="NullToVisibilityConverter" TargetType="TextBlock">
            <Style.Triggers>
                <DataTrigger Binding="{Binding Exception}" Value="{x:Null}">
                    <Setter Property="Visibility" Value="Collapsed"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
        
        <!-- 日志级别颜色样式 -->
        <Style x:Key="DebugLogStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
            <Setter Property="FontStyle" Value="Italic"/>
        </Style>
        
        <Style x:Key="InfoLogStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
        </Style>
        
        <Style x:Key="WarningLogStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="#FF9800"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>
        
        <Style x:Key="ErrorLogStyle" TargetType="TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource ValidationErrorBrush}"/>
            <Setter Property="FontWeight" Value="Bold"/>
        </Style>

        <!-- 日志条目模板 -->
        <DataTemplate x:Key="LogEntryTemplate">
            <Border Padding="8,4" 
                    Background="{DynamicResource MaterialDesignCardBackground}"
                    BorderBrush="{DynamicResource MaterialDesignDivider}"
                    BorderThickness="0,0,0,1">
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- 时间戳 -->
                    <TextBlock Grid.Column="0"
                              Text="{Binding Timestamp, StringFormat=HH:mm:ss.fff}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              Margin="0,0,8,0"
                              VerticalAlignment="Top"/>

                    <!-- 级别图标 -->
                    <materialDesign:PackIcon Grid.Column="1"
                                           Width="16" Height="16"
                                           VerticalAlignment="Top"
                                           Margin="0,0,8,0">
                        <materialDesign:PackIcon.Style>
                            <Style TargetType="materialDesign:PackIcon">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding Level}" Value="Debug">
                                        <Setter Property="Kind" Value="Bug"/>
                                        <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Information">
                                        <Setter Property="Kind" Value="Information"/>
                                        <Setter Property="Foreground" Value="{DynamicResource PrimaryHueMidBrush}"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Warning">
                                        <Setter Property="Kind" Value="Alert"/>
                                        <Setter Property="Foreground" Value="#FF9800"/>
                                    </DataTrigger>
                                    <DataTrigger Binding="{Binding Level}" Value="Error">
                                        <Setter Property="Kind" Value="AlertCircle"/>
                                        <Setter Property="Foreground" Value="{DynamicResource ValidationErrorBrush}"/>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </materialDesign:PackIcon.Style>
                    </materialDesign:PackIcon>

                    <!-- 类别 -->
                    <Border Grid.Column="2"
                           Background="{DynamicResource PrimaryHueLightBrush}"
                           CornerRadius="2"
                           Padding="4,2"
                           Margin="0,0,8,0"
                           VerticalAlignment="Top">
                        <TextBlock Text="{Binding Category}"
                                  Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                  Foreground="{DynamicResource PrimaryHueDarkBrush}"
                                  FontSize="10"/>
                    </Border>

                    <!-- 消息内容 -->
                    <StackPanel Grid.Column="3">
                        <TextBlock Text="{Binding Message}"
                                  TextWrapping="Wrap"
                                  FontFamily="Consolas, Courier New"
                                  FontSize="12">
                            <TextBlock.Style>
                                <Style TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignBody2TextBlock}">
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding Level}" Value="Debug">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBodyLight}"/>
                                            <Setter Property="FontStyle" Value="Italic"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="Information">
                                            <Setter Property="Foreground" Value="{DynamicResource MaterialDesignBody}"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="Warning">
                                            <Setter Property="Foreground" Value="#FF9800"/>
                                            <Setter Property="FontWeight" Value="Medium"/>
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Level}" Value="Error">
                                            <Setter Property="Foreground" Value="{DynamicResource ValidationErrorBrush}"/>
                                            <Setter Property="FontWeight" Value="Bold"/>
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </TextBlock.Style>
                        </TextBlock>

                        <!-- 异常信息 -->
                        <TextBlock Text="{Binding Exception.Message}"
                                  TextWrapping="Wrap"
                                  FontFamily="Consolas, Courier New"
                                  FontSize="11"
                                  Foreground="{DynamicResource ValidationErrorBrush}"
                                  Margin="0,4,0,0"
                                  Style="{StaticResource NullToVisibilityConverter}"/>
                    </StackPanel>
                </Grid>
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 工具栏 -->
        <materialDesign:Card Grid.Row="0" Margin="0,0,0,8" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- 过滤选项 -->
                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="显示级别:"
                              Style="{StaticResource MaterialDesignBody2TextBlock}"
                              VerticalAlignment="Center"
                              Margin="0,0,8,0"/>

                    <CheckBox Content="调试"
                             IsChecked="{Binding ShowDebugLogs}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,12,0"/>

                    <CheckBox Content="信息"
                             IsChecked="{Binding ShowInfoLogs}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,12,0"/>

                    <CheckBox Content="警告"
                             IsChecked="{Binding ShowWarningLogs}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,12,0"/>

                    <CheckBox Content="错误"
                             IsChecked="{Binding ShowErrorLogs}"
                             Style="{StaticResource MaterialDesignCheckBox}"
                             Margin="0,0,12,0"/>

                    <Separator Style="{StaticResource {x:Static ToolBar.SeparatorStyleKey}}" 
                              Margin="8,0"/>

                    <CheckBox Content="自动滚动"
                             IsChecked="{Binding AutoScrollEnabled}"
                             Style="{StaticResource MaterialDesignCheckBox}"/>
                </StackPanel>

                <!-- 操作按钮 -->
                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <Button Content="清空"
                           Command="{Binding ClearLogsCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,0"/>

                    <Button Content="保存"
                           Command="{Binding SaveLogsCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"
                           Margin="0,0,8,0"/>

                    <Button Content="导出"
                           Command="{Binding ExportLogsCommand}"
                           Style="{StaticResource MaterialDesignOutlinedButton}"/>
                </StackPanel>
            </Grid>
        </materialDesign:Card>

        <!-- 日志列表 -->
        <Border Grid.Row="1"
               BorderBrush="{DynamicResource MaterialDesignDivider}"
               BorderThickness="1"
               CornerRadius="4">
            <ListView x:Name="LogListView"
                      ItemsSource="{Binding FilteredLogEntries}"
                      ItemTemplate="{StaticResource LogEntryTemplate}"
                      ScrollViewer.VerticalScrollBarVisibility="Auto"
                      ScrollViewer.HorizontalScrollBarVisibility="Auto"
                      ScrollViewer.CanContentScroll="True"
                      VirtualizingPanel.IsVirtualizing="True"
                      VirtualizingPanel.VirtualizationMode="Recycling"
                      VirtualizingPanel.IsContainerVirtualizable="True"
                      VirtualizingPanel.ScrollUnit="Pixel"
                      Background="{DynamicResource MaterialDesignCardBackground}"
                      BorderThickness="0"
                      SelectionMode="Single">
                <ListView.ItemContainerStyle>
                    <Style TargetType="ListViewItem">
                        <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
                        <Setter Property="Padding" Value="0"/>
                        <Setter Property="Margin" Value="0"/>
                        <Setter Property="Background" Value="Transparent"/>
                        <Setter Property="BorderThickness" Value="0"/>
                        <Setter Property="Template">
                            <Setter.Value>
                                <ControlTemplate TargetType="ListViewItem">
                                    <ContentPresenter/>
                                </ControlTemplate>
                            </Setter.Value>
                        </Setter>
                    </Style>
                </ListView.ItemContainerStyle>
            </ListView>
        </Border>

        <!-- 状态栏 -->
        <materialDesign:Card Grid.Row="2" Margin="0,8,0,0" Padding="12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <TextBlock Text="{Binding TotalLogCount, StringFormat=总计: {0}}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Margin="0,0,16,0"/>

                    <TextBlock Text="{Binding FilteredLogCount, StringFormat=显示: {0}}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Margin="0,0,16,0"/>

                    <TextBlock Text="{Binding ErrorCount, StringFormat=错误: {0}}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource ValidationErrorBrush}"
                              Margin="0,0,16,0"/>

                    <TextBlock Text="{Binding WarningCount, StringFormat=警告: {0}}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="#FF9800"/>
                </StackPanel>

                <TextBlock Grid.Column="1"
                          Text="{Binding LastUpdateTime, StringFormat=最后更新: {0:HH:mm:ss}}"
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"/>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>