using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation;

/// <summary>
/// 公式格式化器实现
/// </summary>
public class FormulaFormatter : IFormulaFormatter
{
    private readonly ILogger<FormulaFormatter> _logger;
    private readonly IEnumerable<IFormulaPostProcessor> _postProcessors;

    public FormulaFormatter(
        ILogger<FormulaFormatter> logger,
        IEnumerable<IFormulaPostProcessor> postProcessors)
    {
        _logger = logger;
        _postProcessors = postProcessors.OrderBy(p => p.Priority);
    }

    /// <summary>
    /// 格式化公式输出
    /// </summary>
    public async Task<string> FormatAsync(string rawOutput, FormulaFormattingOptions options)
    {
        try
        {
            _logger.LogDebug("Starting formula formatting");

            var result = rawOutput;

            // 应用所有后处理器
            foreach (var processor in _postProcessors)
            {
                if (processor.CanProcess(options))
                {
                    _logger.LogDebug("Applying post processor: {ProcessorType}", processor.GetType().Name);
                    result = await processor.ProcessAsync(result, options);
                }
            }

            // 应用最终格式化
            result = await ApplyFinalFormattingAsync(result, options);

            _logger.LogDebug("Formula formatting completed");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during formula formatting");
            return rawOutput; // 返回原始输出
        }
    }

    /// <summary>
    /// 应用最终格式化
    /// </summary>
    private async Task<string> ApplyFinalFormattingAsync(string input, FormulaFormattingOptions options)
    {
        try
        {
            var result = input;

            // 应用换行策略
            result = ApplyLineBreakStrategy(result, options);

            // 美化输出
            if (options.BeautifyOutput)
            {
                result = BeautifyOutput(result, options);
            }

            // 应用长度限制
            result = ApplyLengthLimits(result, options);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying final formatting");
            return input;
        }
    }

    /// <summary>
    /// 应用换行策略
    /// </summary>
    private string ApplyLineBreakStrategy(string input, FormulaFormattingOptions options)
    {
        try
        {
            return options.LineBreakStrategy switch
            {
                LineBreakStrategy.None => RemoveLineBreaks(input),
                LineBreakStrategy.AtOperators => AddLineBreaksAtOperators(input, options),
                LineBreakStrategy.AtCommas => AddLineBreaksAtCommas(input, options),
                LineBreakStrategy.Force => ForceLineBreaks(input, options),
                LineBreakStrategy.Auto => ApplyAutoLineBreaks(input, options),
                _ => input
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying line break strategy");
            return input;
        }
    }

    /// <summary>
    /// 移除换行符
    /// </summary>
    private string RemoveLineBreaks(string input)
    {
        return input.Replace("\n", " ").Replace("\r", "");
    }

    /// <summary>
    /// 在运算符处添加换行
    /// </summary>
    private string AddLineBreaksAtOperators(string input, FormulaFormattingOptions options)
    {
        if (input.Length <= options.MaxLineLength)
        {
            return input;
        }

        var operators = new[] { "+", "-", "=", "\\leq", "\\geq", "\\neq" };
        var result = input;

        foreach (var op in operators)
        {
            result = result.Replace($" {op} ", $" {op}\n{options.IndentString}");
        }

        return result;
    }

    /// <summary>
    /// 在逗号处添加换行
    /// </summary>
    private string AddLineBreaksAtCommas(string input, FormulaFormattingOptions options)
    {
        if (input.Length <= options.MaxLineLength)
        {
            return input;
        }

        return input.Replace(", ", $",\n{options.IndentString}");
    }

    /// <summary>
    /// 强制换行
    /// </summary>
    private string ForceLineBreaks(string input, FormulaFormattingOptions options)
    {
        var lines = new List<string>();
        var currentLine = "";

        foreach (var word in input.Split(' '))
        {
            if (currentLine.Length + word.Length + 1 > options.MaxLineLength)
            {
                if (!string.IsNullOrEmpty(currentLine))
                {
                    lines.Add(currentLine);
                    currentLine = options.IndentString + word;
                }
                else
                {
                    lines.Add(word); // 单词太长，单独一行
                }
            }
            else
            {
                currentLine += (string.IsNullOrEmpty(currentLine) ? "" : " ") + word;
            }
        }

        if (!string.IsNullOrEmpty(currentLine))
        {
            lines.Add(currentLine);
        }

        return string.Join("\n", lines);
    }

    /// <summary>
    /// 自动换行
    /// </summary>
    private string ApplyAutoLineBreaks(string input, FormulaFormattingOptions options)
    {
        if (input.Length <= options.MaxLineLength)
        {
            return input;
        }

        // 首先尝试在运算符处换行
        var result = AddLineBreaksAtOperators(input, options);
        
        // 如果还是太长，在逗号处换行
        if (result.Split('\n').Any(line => line.Length > options.MaxLineLength))
        {
            result = AddLineBreaksAtCommas(result, options);
        }

        // 最后强制换行
        if (result.Split('\n').Any(line => line.Length > options.MaxLineLength))
        {
            result = ForceLineBreaks(result, options);
        }

        return result;
    }

    /// <summary>
    /// 美化输出
    /// </summary>
    private string BeautifyOutput(string input, FormulaFormattingOptions options)
    {
        try
        {
            var result = input;

            // 标准化空格
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s+", " ");

            // 在特定位置添加空格以提高可读性
            result = result.Replace("{", "{ ");
            result = result.Replace("}", " }");
            result = result.Replace("\\frac", " \\frac ");
            result = result.Replace("\\sqrt", " \\sqrt ");

            // 清理多余的空格
            result = System.Text.RegularExpressions.Regex.Replace(result, @"\s+", " ");
            result = result.Trim();

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error beautifying output");
            return input;
        }
    }

    /// <summary>
    /// 应用长度限制
    /// </summary>
    private string ApplyLengthLimits(string input, FormulaFormattingOptions options)
    {
        try
        {
            // 检查内联公式长度限制
            if (options.MaxInlineFormulaLength > 0 && 
                input.Length > options.MaxInlineFormulaLength &&
                !input.Contains("\n"))
            {
                _logger.LogWarning("Formula exceeds inline length limit: {Length} > {Limit}", 
                    input.Length, options.MaxInlineFormulaLength);
                
                // 可以选择截断或转换为显示模式
                if (options.SimplifyComplexStructures)
                {
                    return SimplifyForInline(input, options.MaxInlineFormulaLength);
                }
            }

            return input;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error applying length limits");
            return input;
        }
    }

    /// <summary>
    /// 简化公式以适应内联模式
    /// </summary>
    private string SimplifyForInline(string input, int maxLength)
    {
        try
        {
            if (input.Length <= maxLength)
            {
                return input;
            }

            // 简化策略：
            // 1. 移除不必要的空格
            var simplified = System.Text.RegularExpressions.Regex.Replace(input, @"\s+", "");

            // 2. 如果还是太长，截断并添加省略号
            if (simplified.Length > maxLength)
            {
                simplified = simplified.Substring(0, maxLength - 3) + "...";
            }

            return simplified;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error simplifying formula for inline mode");
            return input.Substring(0, Math.Min(input.Length, maxLength));
        }
    }

    /// <summary>
    /// 验证格式化结果
    /// </summary>
    public bool ValidateFormattedOutput(string output, FormulaFormattingOptions options)
    {
        try
        {
            // 基本验证
            if (string.IsNullOrWhiteSpace(output))
            {
                return false;
            }

            // 检查括号匹配
            if (!AreBracketsMatched(output))
            {
                _logger.LogWarning("Formatted output has unmatched brackets");
                return false;
            }

            // 检查LaTeX命令的基本语法
            if (!IsValidLatexSyntax(output))
            {
                _logger.LogWarning("Formatted output has invalid LaTeX syntax");
                return false;
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error validating formatted output");
            return false;
        }
    }

    /// <summary>
    /// 检查括号是否匹配
    /// </summary>
    private bool AreBracketsMatched(string input)
    {
        var braceCount = 0;
        var parenCount = 0;
        var bracketCount = 0;

        foreach (var c in input)
        {
            switch (c)
            {
                case '{': braceCount++; break;
                case '}': braceCount--; break;
                case '(': parenCount++; break;
                case ')': parenCount--; break;
                case '[': bracketCount++; break;
                case ']': bracketCount--; break;
            }

            // 如果任何计数变为负数，说明有不匹配的闭合括号
            if (braceCount < 0 || parenCount < 0 || bracketCount < 0)
            {
                return false;
            }
        }

        // 所有计数都应该为0
        return braceCount == 0 && parenCount == 0 && bracketCount == 0;
    }

    /// <summary>
    /// 检查基本的LaTeX语法
    /// </summary>
    private bool IsValidLatexSyntax(string input)
    {
        try
        {
            // 检查是否有未闭合的LaTeX命令
            var commandPattern = @"\\[a-zA-Z]+\s*\{";
            var commands = System.Text.RegularExpressions.Regex.Matches(input, commandPattern);

            foreach (System.Text.RegularExpressions.Match command in commands)
            {
                var startIndex = command.Index + command.Length - 1; // 开始括号的位置
                var braceCount = 1;
                var index = startIndex + 1;

                while (index < input.Length && braceCount > 0)
                {
                    if (input[index] == '{')
                        braceCount++;
                    else if (input[index] == '}')
                        braceCount--;
                    index++;
                }

                if (braceCount > 0)
                {
                    return false; // 未闭合的命令
                }
            }

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking LaTeX syntax");
            return false;
        }
    }
}
