using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;
using System.Text;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Converters;

/// <summary>
/// LaTeX转换器实现
/// </summary>
public class LaTeXConverter : IFormulaConverter
{
    private readonly ILogger<LaTeXConverter> _logger;
    private readonly Dictionary<string, string> _symbolMap;

    public LaTeXConverter(ILogger<LaTeXConverter> logger)
    {
        _logger = logger;
        _symbolMap = InitializeSymbolMap();
    }

    public FormulaOutputFormat[] SupportedFormats => new[] { FormulaOutputFormat.LaTeX };

    public bool SupportsFormat(FormulaOutputFormat format)
    {
        return format == FormulaOutputFormat.LaTeX;
    }

    public async Task<string> ConvertAsync(FormulaStructure structure, FormulaOutputFormat format)
    {
        if (!SupportsFormat(format))
        {
            throw new ArgumentException($"Unsupported format: {format}");
        }

        try
        {
            _logger.LogDebug("Converting structure to LaTeX: {StructureType}", structure.GetType().Name);

            var result = structure switch
            {
                FractionStructure fraction => ConvertFraction(fraction),
                MatrixStructure matrix => ConvertMatrix(matrix),
                PiecewiseFunctionStructure piecewise => ConvertPiecewiseFunction(piecewise),
                RadicalStructure radical => ConvertRadical(radical),
                ScriptStructure script => ConvertScript(script),
                _ => ConvertGeneric(structure)
            };

            _logger.LogDebug("Successfully converted to LaTeX: {Result}", result);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting structure to LaTeX");
            return $"\\text{{Error: {ex.Message}}}";
        }
    }

    #region Specific Converters

    private string ConvertFraction(FractionStructure fraction)
    {
        try
        {
            var numerator = ConvertComponent(fraction.Numerator);
            var denominator = ConvertComponent(fraction.Denominator);

            return fraction.FractionType switch
            {
                FractionType.Bar => $"\\frac{{{numerator}}}{{{denominator}}}",
                FractionType.Linear => $"{numerator}/{denominator}",
                FractionType.Skewed => $"^{{{numerator}}}\\!/\\!_{{{denominator}}}",
                FractionType.NoBar => $"\\genfrac{{}}{{}}{{}}{{}}{{{numerator}}}{{{denominator}}}",
                _ => $"\\frac{{{numerator}}}{{{denominator}}}"
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting fraction");
            return "\\text{Error: Invalid fraction}";
        }
    }

    private string ConvertMatrix(MatrixStructure matrix)
    {
        try
        {
            var environment = DetermineMatrixEnvironment(matrix);
            var content = new StringBuilder();

            for (int i = 0; i < matrix.Rows.Count; i++)
            {
                var row = matrix.Rows[i];
                var rowContent = new List<string>();

                foreach (var cell in row.Cells)
                {
                    var cellContent = ConvertComponent(cell);
                    rowContent.Add(cellContent);
                }

                content.Append(string.Join(" & ", rowContent));
                
                if (i < matrix.Rows.Count - 1)
                {
                    content.Append(" \\\\ ");
                }
            }

            return $"\\begin{{{environment}}}\n{content}\n\\end{{{environment}}}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting matrix");
            return "\\text{Error: Invalid matrix}";
        }
    }

    private string ConvertPiecewiseFunction(PiecewiseFunctionStructure piecewise)
    {
        try
        {
            var content = new StringBuilder();

            for (int i = 0; i < piecewise.Cases.Count; i++)
            {
                var case_ = piecewise.Cases[i];
                var expression = NormalizeExpression(case_.Expression);
                var condition = NormalizeCondition(case_.Condition);

                content.Append($"{expression}, & \\text{{{condition}}}");
                
                if (i < piecewise.Cases.Count - 1)
                {
                    content.Append(" \\\\ ");
                }
            }

            return $"\\begin{{cases}}\n{content}\n\\end{{cases}}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting piecewise function");
            return "\\text{Error: Invalid piecewise function}";
        }
    }

    private string ConvertRadical(RadicalStructure radical)
    {
        try
        {
            var radicand = ConvertComponent(radical.Radicand);

            if (radical.Index != null)
            {
                var index = ConvertComponent(radical.Index);
                return $"\\sqrt[{index}]{{{radicand}}}";
            }

            return $"\\sqrt{{{radicand}}}";
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting radical");
            return "\\text{Error: Invalid radical}";
        }
    }

    private string ConvertScript(ScriptStructure script)
    {
        try
        {
            var baseText = ConvertComponent(script.Base);
            var result = baseText;

            if (script.Subscript != null)
            {
                var subscript = ConvertComponent(script.Subscript);
                result += $"_{{{subscript}}}";
            }

            if (script.Superscript != null)
            {
                var superscript = ConvertComponent(script.Superscript);
                result += $"^{{{superscript}}}";
            }

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting script");
            return "\\text{Error: Invalid script}";
        }
    }

    private string ConvertGeneric(FormulaStructure structure)
    {
        try
        {
            var text = structure.GetTextRepresentation();
            return NormalizeText(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting generic structure");
            return "\\text{Error: Unknown structure}";
        }
    }

    #endregion

    #region Helper Methods

    private string ConvertComponent(FormulaComponent component)
    {
        try
        {
            if (component.Children.Any())
            {
                var childResults = component.Children.Select(ConvertComponent);
                return string.Join("", childResults);
            }

            var text = component.Data?.ToString() ?? "";
            return NormalizeText(text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting component");
            return "\\text{Error}";
        }
    }

    private string DetermineMatrixEnvironment(MatrixStructure matrix)
    {
        if (matrix.IsPiecewise)
        {
            return "cases";
        }

        // 可以根据矩阵属性选择不同的环境
        return matrix.Alignment switch
        {
            MatrixAlignment.Left => "array",
            MatrixAlignment.Right => "array",
            _ => "pmatrix"
        };
    }

    private string NormalizeExpression(string expression)
    {
        if (string.IsNullOrEmpty(expression))
        {
            return "";
        }

        // 应用符号映射
        var result = expression;
        foreach (var mapping in _symbolMap)
        {
            result = result.Replace(mapping.Key, mapping.Value);
        }

        return result;
    }

    private string NormalizeCondition(string condition)
    {
        if (string.IsNullOrEmpty(condition))
        {
            return "";
        }

        // 标准化条件表达式
        var result = condition;
        
        // 替换常见的条件词
        result = result.Replace("if ", "if ");
        result = result.Replace("when ", "when ");
        result = result.Replace("otherwise", "otherwise");
        result = result.Replace("else", "else");

        // 应用符号映射
        foreach (var mapping in _symbolMap)
        {
            result = result.Replace(mapping.Key, mapping.Value);
        }

        return result;
    }

    private string NormalizeText(string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            return "";
        }

        var result = text;

        // 应用符号映射
        foreach (var mapping in _symbolMap)
        {
            result = result.Replace(mapping.Key, mapping.Value);
        }

        // 转义特殊字符
        result = result.Replace("{", "\\{");
        result = result.Replace("}", "\\}");
        result = result.Replace("$", "\\$");
        result = result.Replace("%", "\\%");
        result = result.Replace("&", "\\&");
        result = result.Replace("#", "\\#");

        return result;
    }

    private Dictionary<string, string> InitializeSymbolMap()
    {
        return new Dictionary<string, string>
        {
            // 希腊字母
            { "α", "\\alpha" },
            { "β", "\\beta" },
            { "γ", "\\gamma" },
            { "δ", "\\delta" },
            { "ε", "\\epsilon" },
            { "ζ", "\\zeta" },
            { "η", "\\eta" },
            { "θ", "\\theta" },
            { "ι", "\\iota" },
            { "κ", "\\kappa" },
            { "λ", "\\lambda" },
            { "μ", "\\mu" },
            { "ν", "\\nu" },
            { "ξ", "\\xi" },
            { "π", "\\pi" },
            { "ρ", "\\rho" },
            { "σ", "\\sigma" },
            { "τ", "\\tau" },
            { "υ", "\\upsilon" },
            { "φ", "\\phi" },
            { "χ", "\\chi" },
            { "ψ", "\\psi" },
            { "ω", "\\omega" },

            // 大写希腊字母
            { "Α", "\\Alpha" },
            { "Β", "\\Beta" },
            { "Γ", "\\Gamma" },
            { "Δ", "\\Delta" },
            { "Ε", "\\Epsilon" },
            { "Ζ", "\\Zeta" },
            { "Η", "\\Eta" },
            { "Θ", "\\Theta" },
            { "Ι", "\\Iota" },
            { "Κ", "\\Kappa" },
            { "Λ", "\\Lambda" },
            { "Μ", "\\Mu" },
            { "Ν", "\\Nu" },
            { "Ξ", "\\Xi" },
            { "Π", "\\Pi" },
            { "Ρ", "\\Rho" },
            { "Σ", "\\Sigma" },
            { "Τ", "\\Tau" },
            { "Υ", "\\Upsilon" },
            { "Φ", "\\Phi" },
            { "Χ", "\\Chi" },
            { "Ψ", "\\Psi" },
            { "Ω", "\\Omega" },

            // 数学符号
            { "∞", "\\infty" },
            { "∂", "\\partial" },
            { "∇", "\\nabla" },
            { "±", "\\pm" },
            { "∓", "\\mp" },
            { "×", "\\times" },
            { "÷", "\\div" },
            { "·", "\\cdot" },
            { "∗", "\\ast" },
            { "∘", "\\circ" },

            // 关系符号
            { "≤", "\\leq" },
            { "≥", "\\geq" },
            { "≠", "\\neq" },
            { "≈", "\\approx" },
            { "≡", "\\equiv" },
            { "∼", "\\sim" },
            { "∝", "\\propto" },
            { "∈", "\\in" },
            { "∉", "\\notin" },
            { "⊂", "\\subset" },
            { "⊃", "\\supset" },
            { "⊆", "\\subseteq" },
            { "⊇", "\\supseteq" },

            // 箭头
            { "→", "\\rightarrow" },
            { "←", "\\leftarrow" },
            { "↔", "\\leftrightarrow" },
            { "⇒", "\\Rightarrow" },
            { "⇐", "\\Leftarrow" },
            { "⇔", "\\Leftrightarrow" },
            { "↑", "\\uparrow" },
            { "↓", "\\downarrow" },

            // 集合符号
            { "∪", "\\cup" },
            { "∩", "\\cap" },
            { "∅", "\\emptyset" },
            { "ℕ", "\\mathbb{N}" },
            { "ℤ", "\\mathbb{Z}" },
            { "ℚ", "\\mathbb{Q}" },
            { "ℝ", "\\mathbb{R}" },
            { "ℂ", "\\mathbb{C}" },

            // 积分和求和
            { "∫", "\\int" },
            { "∮", "\\oint" },
            { "∬", "\\iint" },
            { "∭", "\\iiint" },
            { "∑", "\\sum" },
            { "∏", "\\prod" },
            { "∐", "\\coprod" }
        };
    }

    #endregion
}
