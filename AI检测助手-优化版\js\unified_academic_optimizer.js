/**
 * 统一学术智能优化器
 * 整合智能优化改写和学术专业优化功能
 * 
 * 核心流程：学术规范化 → 表达优化 → AI特征消除
 */

class UnifiedAcademicOptimizer {
    constructor() {
        // 继承原有的学术优化器配置
        this.config = {
            // 创新性指数阈值 (Nature子刊基准)
            innovationDensityThreshold: 3, // 创新点/千字
            // 颠覆性指数阈值 (Science级别)
            disruptiveIndexThreshold: 0.35,
            // 学术性评分权重
            academicScoreWeights: {
                structuralCompliance: 0.25,    // 结构规范性
                innovationIndex: 0.30,         // 创新性指数
                evidenceLevel: 0.25,           // 证据等级
                dataIntegrity: 0.20            // 数据呈现完整性
            },
            // 优化流程配置
            optimizationFlow: {
                enableAcademicNormalization: true,  // 学术规范化
                enableExpressionOptimization: true, // 表达优化
                enableAIFeatureReduction: true,     // AI特征消除
                preserveSemantics: true,            // 保持语义
                maintainRigor: true                 // 维护严谨性
            }
        };

        // 学术同义词库 - 扩展版
        this.academicSynonyms = {
            '研究': ['探索', '调研', '分析', '考察', '探讨', '研判'],
            '方法': ['途径', '手段', '方式', '策略', '技术', '路径'],
            '结果': ['成果', '效果', '产出', '收获', '表现', '成效'],
            '分析': ['剖析', '解析', '研判', '评估', '审视', '考量'],
            '系统': ['体系', '框架', '机制', '平台', '架构', '模式'],
            '问题': ['难题', '挑战', '课题', '议题', '困难', '瓶颈'],
            '重要': ['关键', '核心', '主要', '重点', '关键性', '要害'],
            '提高': ['改善', '增强', '优化', '提升', '强化', '完善'],
            '显示': ['表明', '揭示', '反映', '证实', '展现', '印证'],
            '发现': ['察觉', '观察到', '识别', '注意到', '探明', '洞察'],
            '实验': ['试验', '测试', '验证', '检验', '实证', '测验'],
            '数据': ['资料', '信息', '材料', '统计', '指标', '参数'],
            '算法': ['方法', '策略', '技术', '机制', '模式', '程序'],
            '模型': ['框架', '体系', '结构', '范式', '机制', '样式'],
            '效果': ['成效', '效应', '作用', '影响', '表现', '绩效']
        };

        // 学术表达模板
        this.academicTemplates = {
            '研究表明': [
                '研究结果显示', '分析结果表明', '实证研究证实',
                '调研发现', '数据分析揭示', '实验验证了'
            ],
            '通过分析': [
                '经过深入分析', '基于数据分析', '通过实证研究',
                '借助统计分析', '运用分析方法', '采用分析技术'
            ],
            '实验结果': [
                '测试结果', '验证结果', '实证结果',
                '检验结果', '试验成果', '测试成效'
            ]
        };

        // AI特征消除策略
        this.aiReductionStrategies = {
            // 句式多样化
            sentenceVariation: {
                patterns: [
                    '基于...的研究', '通过...分析', '根据...结果',
                    '从...角度看', '就...而言', '关于...问题'
                ],
                replacements: [
                    '...研究显示', '...分析表明', '...结果证实',
                    '...视角下', '...方面', '...课题'
                ]
            },
            // 个人化表达
            personalization: [
                '我们的研究发现', '本研究认为', '笔者观察到',
                '研究团队注意到', '我们的分析显示', '本文认为'
            ],
            // 不确定性表达
            uncertaintyMarkers: [
                '可能', '或许', '似乎', '大概', '可能性较大',
                '初步显示', '倾向于认为', '有理由相信'
            ]
        };
    }

    /**
     * 统一优化主方法
     * @param {string} text - 原始文本
     * @param {Object} options - 优化选项
     * @returns {Object} 优化结果
     */
    async optimize(text, options = {}) {
        const {
            enableLLM = true,
            optimizationLevel = 'balanced', // 'conservative', 'balanced', 'aggressive'
            preserveSemantics = true,
            maintainAcademicRigor = true,
            targetAIScore = 30
        } = options;

        const startTime = Date.now();
        const optimizationHistory = [];
        let currentText = text;

        try {
            // 第一阶段：学术规范化
            if (this.config.optimizationFlow.enableAcademicNormalization) {
                const academicResult = await this.academicNormalization(currentText, {
                    preserveSemantics,
                    maintainRigor: maintainAcademicRigor
                });
                currentText = academicResult.optimizedText;
                optimizationHistory.push({
                    stage: 'academic_normalization',
                    improvements: academicResult.improvements,
                    qualityScore: academicResult.qualityScore
                });
            }

            // 第二阶段：表达优化
            if (this.config.optimizationFlow.enableExpressionOptimization) {
                const expressionResult = await this.expressionOptimization(currentText, {
                    enableLLM,
                    optimizationLevel,
                    preserveSemantics
                });
                currentText = expressionResult.optimizedText;
                optimizationHistory.push({
                    stage: 'expression_optimization',
                    improvements: expressionResult.improvements,
                    qualityScore: expressionResult.qualityScore
                });
            }

            // 第三阶段：AI特征消除
            if (this.config.optimizationFlow.enableAIFeatureReduction) {
                const aiReductionResult = await this.aiFeatureReduction(currentText, {
                    targetScore: targetAIScore,
                    optimizationLevel
                });
                currentText = aiReductionResult.optimizedText;
                optimizationHistory.push({
                    stage: 'ai_feature_reduction',
                    improvements: aiReductionResult.improvements,
                    qualityScore: aiReductionResult.qualityScore
                });
            }

            const processingTime = Date.now() - startTime;

            return {
                success: true,
                optimizedText: currentText,
                originalText: text,
                optimizationHistory,
                overallImprovements: this.summarizeImprovements(optimizationHistory),
                qualityMetrics: this.calculateQualityMetrics(text, currentText),
                processingTime,
                mode: 'unified_academic'
            };

        } catch (error) {
            console.error('统一学术优化失败:', error);
            return {
                success: false,
                error: error.message,
                optimizedText: text,
                originalText: text,
                mode: 'error'
            };
        }
    }

    /**
     * 第一阶段：学术规范化
     */
    async academicNormalization(text, options = {}) {
        const { preserveSemantics = true, maintainRigor = true } = options;
        let optimizedText = text;
        const improvements = [];

        // 1. 术语标准化
        optimizedText = this.standardizeTerminology(optimizedText);
        improvements.push('术语标准化');

        // 2. 学术表达规范化
        optimizedText = this.normalizeAcademicExpressions(optimizedText);
        improvements.push('学术表达规范化');

        // 3. 结构优化
        if (maintainRigor) {
            optimizedText = this.optimizeStructure(optimizedText);
            improvements.push('结构严谨性优化');
        }

        // 4. 引用格式规范化
        optimizedText = this.normalizeCitations(optimizedText);
        improvements.push('引用格式规范化');

        return {
            optimizedText,
            improvements,
            qualityScore: this.assessAcademicQuality(optimizedText)
        };
    }

    /**
     * 第二阶段：表达优化
     */
    async expressionOptimization(text, options = {}) {
        const { enableLLM = true, optimizationLevel = 'balanced', preserveSemantics = true } = options;
        let optimizedText = text;
        const improvements = [];

        // 优先使用LLM进行智能改写
        if (enableLLM && typeof ollamaManagerV2 !== 'undefined' && ollamaManagerV2.state.isConnected) {
            try {
                const llmResult = await this.llmExpressionOptimization(text, optimizationLevel);
                optimizedText = llmResult.optimizedText;
                improvements.push(...llmResult.improvements);
            } catch (error) {
                console.warn('LLM表达优化失败，使用规则优化:', error);
                const ruleResult = this.ruleBasedExpressionOptimization(text, optimizationLevel);
                optimizedText = ruleResult.optimizedText;
                improvements.push(...ruleResult.improvements);
            }
        } else {
            // 使用规则优化
            const ruleResult = this.ruleBasedExpressionOptimization(text, optimizationLevel);
            optimizedText = ruleResult.optimizedText;
            improvements.push(...ruleResult.improvements);
        }

        return {
            optimizedText,
            improvements,
            qualityScore: this.assessExpressionQuality(optimizedText)
        };
    }

    /**
     * 第三阶段：AI特征消除
     */
    async aiFeatureReduction(text, options = {}) {
        const { targetScore = 30, optimizationLevel = 'balanced' } = options;
        let optimizedText = text;
        const improvements = [];

        // 1. 句式多样化
        optimizedText = this.diversifySentenceStructures(optimizedText);
        improvements.push('句式结构多样化');

        // 2. 添加个人化表达
        optimizedText = this.addPersonalizedExpressions(optimizedText);
        improvements.push('个人化表达增强');

        // 3. 插入不确定性标记
        optimizedText = this.insertUncertaintyMarkers(optimizedText);
        improvements.push('不确定性表达添加');

        // 4. 消除AI生成痕迹
        optimizedText = this.removeAISignatures(optimizedText);
        improvements.push('AI生成痕迹消除');

        // 5. 如果有朱雀优化器，进行深度优化
        if (typeof zhuqueOptimizer !== 'undefined' && optimizationLevel === 'aggressive') {
            try {
                const zhuqueResult = await zhuqueOptimizer.optimizeForZhuque(optimizedText, {
                    aggressiveness: 'medium',
                    targetScore: targetScore
                });
                if (zhuqueResult.success) {
                    optimizedText = zhuqueResult.optimizedText;
                    improvements.push('朱雀深度优化');
                }
            } catch (error) {
                console.warn('朱雀优化失败:', error);
            }
        }

        return {
            optimizedText,
            improvements,
            qualityScore: this.assessAIReductionQuality(optimizedText)
        };
    }

    /**
     * LLM表达优化
     */
    async llmExpressionOptimization(text, level) {
        const useAdvanced = level === 'aggressive';
        const result = await ollamaManagerV2.intelligentRewrite(text, useAdvanced);
        
        return {
            optimizedText: result.data.optimized_text || result.raw_response,
            improvements: result.data.improvements || ['LLM智能改写', '表达自然化', '语言流畅性提升']
        };
    }

    /**
     * 规则表达优化
     */
    ruleBasedExpressionOptimization(text, level) {
        let optimizedText = text;
        const improvements = [];

        // 同义词替换
        optimizedText = this.replaceSynonyms(optimizedText);
        improvements.push('同义词替换');

        // 句式变换
        optimizedText = this.transformSentences(optimizedText);
        improvements.push('句式变换');

        // 表达模板替换
        optimizedText = this.replaceExpressionTemplates(optimizedText);
        improvements.push('表达模板优化');

        return { optimizedText, improvements };
    }

    /**
     * 术语标准化
     */
    standardizeTerminology(text) {
        let optimizedText = text;

        // 使用学术同义词库进行替换
        Object.keys(this.academicSynonyms).forEach(term => {
            const synonyms = this.academicSynonyms[term];
            const regex = new RegExp(term, 'g');
            if (optimizedText.includes(term) && Math.random() < 0.4) {
                const replacement = synonyms[Math.floor(Math.random() * synonyms.length)];
                optimizedText = optimizedText.replace(regex, replacement);
            }
        });

        return optimizedText;
    }

    /**
     * 学术表达规范化
     */
    normalizeAcademicExpressions(text) {
        let optimizedText = text;

        // 替换学术表达模板
        Object.keys(this.academicTemplates).forEach(template => {
            const alternatives = this.academicTemplates[template];
            if (optimizedText.includes(template) && Math.random() < 0.5) {
                const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
                optimizedText = optimizedText.replace(new RegExp(template, 'g'), replacement);
            }
        });

        return optimizedText;
    }

    /**
     * 结构优化
     */
    optimizeStructure(text) {
        let optimizedText = text;

        // 添加逻辑连接词
        const connectors = ['此外', '另外', '同时', '然而', '因此', '综上所述'];
        const sentences = optimizedText.split(/[。！？]/).filter(s => s.trim().length > 0);

        if (sentences.length > 2) {
            const insertIndex = Math.floor(sentences.length / 2);
            const connector = connectors[Math.floor(Math.random() * connectors.length)];
            sentences[insertIndex] = connector + '，' + sentences[insertIndex].trim();
            optimizedText = sentences.join('。') + '。';
        }

        return optimizedText;
    }

    /**
     * 引用格式规范化
     */
    normalizeCitations(text) {
        // 简单的引用格式处理
        return text.replace(/\(\d{4}\)/g, (match) => {
            return Math.random() < 0.3 ? `[${match.slice(1, -1)}]` : match;
        });
    }

    /**
     * 同义词替换
     */
    replaceSynonyms(text) {
        let optimizedText = text;

        Object.keys(this.academicSynonyms).forEach(word => {
            const synonyms = this.academicSynonyms[word];
            const regex = new RegExp(`\\b${word}\\b`, 'g');
            optimizedText = optimizedText.replace(regex, (match) => {
                return Math.random() < 0.3 ? synonyms[Math.floor(Math.random() * synonyms.length)] : match;
            });
        });

        return optimizedText;
    }

    /**
     * 句式变换
     */
    transformSentences(text) {
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);

        return sentences.map(sentence => {
            if (Math.random() < 0.3) {
                const starters = ['基于此', '据此', '由此可见', '综合来看'];
                const starter = starters[Math.floor(Math.random() * starters.length)];
                return starter + '，' + sentence.trim();
            }
            return sentence.trim();
        }).join('。') + '。';
    }

    /**
     * 表达模板替换
     */
    replaceExpressionTemplates(text) {
        let optimizedText = text;

        const templates = {
            '可以看出': ['不难发现', '显而易见', '由此可知'],
            '表明了': ['说明了', '证实了', '反映了'],
            '具有重要意义': ['意义重大', '价值显著', '作用突出']
        };

        Object.keys(templates).forEach(template => {
            const alternatives = templates[template];
            if (optimizedText.includes(template)) {
                const replacement = alternatives[Math.floor(Math.random() * alternatives.length)];
                optimizedText = optimizedText.replace(new RegExp(template, 'g'), replacement);
            }
        });

        return optimizedText;
    }

    /**
     * 句式结构多样化
     */
    diversifySentenceStructures(text) {
        let optimizedText = text;

        // 变换句式结构
        const patterns = this.aiReductionStrategies.sentenceVariation.patterns;
        const replacements = this.aiReductionStrategies.sentenceVariation.replacements;

        patterns.forEach((pattern, index) => {
            if (optimizedText.includes(pattern) && Math.random() < 0.4) {
                const replacement = replacements[index];
                optimizedText = optimizedText.replace(new RegExp(pattern, 'g'), replacement);
            }
        });

        return optimizedText;
    }

    /**
     * 添加个人化表达
     */
    addPersonalizedExpressions(text) {
        let optimizedText = text;
        const personalExpressions = this.aiReductionStrategies.personalization;

        const sentences = optimizedText.split(/[。！？]/).filter(s => s.trim().length > 0);
        if (sentences.length > 1 && Math.random() < 0.4) {
            const insertIndex = Math.floor(sentences.length / 2);
            const expression = personalExpressions[Math.floor(Math.random() * personalExpressions.length)];
            sentences[insertIndex] = expression + '，' + sentences[insertIndex].trim();
            optimizedText = sentences.join('。') + '。';
        }

        return optimizedText;
    }

    /**
     * 插入不确定性标记
     */
    insertUncertaintyMarkers(text) {
        let optimizedText = text;
        const uncertaintyMarkers = this.aiReductionStrategies.uncertaintyMarkers;

        // 在适当位置插入不确定性表达
        const sentences = optimizedText.split(/[。！？]/).filter(s => s.trim().length > 0);
        sentences.forEach((sentence, index) => {
            if (Math.random() < 0.2) {
                const marker = uncertaintyMarkers[Math.floor(Math.random() * uncertaintyMarkers.length)];
                sentences[index] = sentence.replace(/^/, marker + '，');
            }
        });

        return sentences.join('。') + '。';
    }

    /**
     * 消除AI生成痕迹
     */
    removeAISignatures(text) {
        let optimizedText = text;

        // 移除典型的AI生成模式
        const aiPatterns = [
            /总的来说[，,]/g,
            /综上所述[，,]/g,
            /首先[，,].*其次[，,].*最后[，,]/g,
            /第一[，,].*第二[，,].*第三[，,]/g
        ];

        aiPatterns.forEach(pattern => {
            optimizedText = optimizedText.replace(pattern, (match) => {
                // 用更自然的表达替换
                const alternatives = ['另外，', '此外，', '同时，', '然而，'];
                return alternatives[Math.floor(Math.random() * alternatives.length)];
            });
        });

        return optimizedText;
    }

    /**
     * 评估学术质量
     */
    assessAcademicQuality(text) {
        let score = 0.7; // 基础分数

        // 检查学术术语密度
        const academicTerms = Object.keys(this.academicSynonyms);
        const termCount = academicTerms.filter(term => text.includes(term)).length;
        score += Math.min(0.2, termCount * 0.02);

        // 检查句式复杂度
        const avgSentenceLength = text.split(/[。！？]/).reduce((sum, s) => sum + s.length, 0) / text.split(/[。！？]/).length;
        if (avgSentenceLength > 20) score += 0.1;

        return Math.min(1.0, score);
    }

    /**
     * 评估表达质量
     */
    assessExpressionQuality(text) {
        let score = 0.8; // 基础分数

        // 检查句式多样性
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        const uniqueStarters = new Set(sentences.map(s => s.trim().substring(0, 2))).size;
        score += Math.min(0.15, uniqueStarters * 0.02);

        // 检查连接词使用
        const connectors = ['此外', '另外', '同时', '然而', '因此'];
        const connectorCount = connectors.filter(c => text.includes(c)).length;
        score += Math.min(0.05, connectorCount * 0.01);

        return Math.min(1.0, score);
    }

    /**
     * 评估AI特征消除质量
     */
    assessAIReductionQuality(text) {
        let score = 0.9; // 基础分数

        // 检查AI生成痕迹
        const aiPatterns = ['总的来说', '综上所述', '首先.*其次.*最后'];
        const aiSignatures = aiPatterns.filter(pattern => new RegExp(pattern).test(text)).length;
        score -= aiSignatures * 0.1;

        // 检查个人化表达
        const personalExpressions = this.aiReductionStrategies.personalization;
        const personalCount = personalExpressions.filter(expr => text.includes(expr)).length;
        score += Math.min(0.1, personalCount * 0.05);

        return Math.max(0.5, score);
    }

    /**
     * 计算整体质量指标
     */
    calculateQualityMetrics(originalText, optimizedText) {
        return {
            lengthChange: ((optimizedText.length - originalText.length) / originalText.length * 100).toFixed(1),
            academicQuality: this.assessAcademicQuality(optimizedText),
            expressionQuality: this.assessExpressionQuality(optimizedText),
            aiReductionQuality: this.assessAIReductionQuality(optimizedText),
            overallQuality: (this.assessAcademicQuality(optimizedText) +
                           this.assessExpressionQuality(optimizedText) +
                           this.assessAIReductionQuality(optimizedText)) / 3
        };
    }

    /**
     * 总结优化改进
     */
    summarizeImprovements(optimizationHistory) {
        const allImprovements = [];
        const stageNames = {
            'academic_normalization': '学术规范化',
            'expression_optimization': '表达优化',
            'ai_feature_reduction': 'AI特征消除'
        };

        optimizationHistory.forEach(stage => {
            const stageName = stageNames[stage.stage] || stage.stage;
            stage.improvements.forEach(improvement => {
                allImprovements.push(`${stageName}: ${improvement}`);
            });
        });

        return allImprovements;
    }

    /**
     * 获取优化建议
     */
    getOptimizationSuggestions(text) {
        const suggestions = [];

        // 检查文本长度
        if (text.length < 100) {
            suggestions.push('建议增加文本长度以获得更好的优化效果');
        }

        // 检查学术术语
        const academicTerms = Object.keys(this.academicSynonyms);
        const termCount = academicTerms.filter(term => text.includes(term)).length;
        if (termCount < 3) {
            suggestions.push('建议增加学术术语以提升专业性');
        }

        // 检查句式复杂度
        const sentences = text.split(/[。！？]/).filter(s => s.trim().length > 0);
        const avgLength = sentences.reduce((sum, s) => sum + s.length, 0) / sentences.length;
        if (avgLength < 15) {
            suggestions.push('建议增加句式复杂度以提升学术水平');
        }

        return suggestions;
    }

    /**
     * 兼容性方法：保持与原有接口的兼容
     */
    async optimizeAcademic(text, options = {}) {
        return await this.optimize(text, {
            ...options,
            optimizationLevel: 'balanced',
            maintainAcademicRigor: true
        });
    }

    /**
     * 兼容性方法：智能改写
     */
    async intelligentRewrite(text, options = {}) {
        return await this.optimize(text, {
            ...options,
            optimizationLevel: 'conservative',
            preserveSemantics: true
        });
    }
}

// 创建全局实例
const unifiedAcademicOptimizer = new UnifiedAcademicOptimizer();
