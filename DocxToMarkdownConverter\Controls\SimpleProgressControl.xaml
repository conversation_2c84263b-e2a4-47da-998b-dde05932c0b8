<UserControl x:Class="DocxToMarkdownConverter.Controls.SimpleProgressControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d" 
             d:DesignHeight="500" d:DesignWidth="800">
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 进度显示区域 -->
        <materialDesign:Card Grid.Row="0" Margin="16" Padding="20">
            <StackPanel>
                <TextBlock Text="转换进度" 
                          Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                          Margin="0,0,0,16"/>
                
                <Grid Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <TextBlock Grid.Column="0" 
                              Text="{Binding CurrentOperationText}" 
                              Style="{StaticResource MaterialDesignBody1TextBlock}"/>
                    
                    <TextBlock Grid.Column="1" 
                              Text="{Binding OverallProgressText, StringFormat={}{0}%}" 
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                </Grid>
                
                <ProgressBar Value="{Binding OverallProgress}" 
                            Maximum="100"
                            Height="8"
                            Style="{StaticResource MaterialDesignLinearProgressBar}"/>
                
                <TextBlock Text="{Binding EstimatedTimeText}" 
                          Style="{StaticResource MaterialDesignCaptionTextBlock}"
                          Foreground="{DynamicResource MaterialDesignBodyLight}"
                          Margin="0,8,0,0"/>
            </StackPanel>
        </materialDesign:Card>

        <!-- 统计信息区域 -->
        <Grid Grid.Row="1" Margin="16,0,16,16">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <materialDesign:Card Grid.Column="0" Margin="0,0,8,0" Padding="16">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="FileMultiple" 
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding TotalFiles}" 
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              HorizontalAlignment="Center"
                              Margin="0,8,0,4"/>
                    <TextBlock Text="总文件数" 
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Grid.Column="1" Margin="8,0" Padding="16">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="CheckCircle" 
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource SecondaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding CompletedFiles}" 
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              Foreground="{DynamicResource SecondaryHueMidBrush}"
                              HorizontalAlignment="Center"
                              Margin="0,8,0,4"/>
                    <TextBlock Text="已完成" 
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Grid.Column="2" Margin="8,0" Padding="16">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="AlertCircle" 
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource ValidationErrorBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding FailedFiles}" 
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              Foreground="{DynamicResource ValidationErrorBrush}"
                              HorizontalAlignment="Center"
                              Margin="0,8,0,4"/>
                    <TextBlock Text="失败数" 
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>

            <materialDesign:Card Grid.Column="3" Margin="8,0,0,0" Padding="16">
                <StackPanel HorizontalAlignment="Center">
                    <materialDesign:PackIcon Kind="ChartLine" 
                                           Width="24" Height="24"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"
                                           HorizontalAlignment="Center"/>
                    <TextBlock Text="{Binding SuccessRateText}" 
                              Style="{StaticResource MaterialDesignHeadline5TextBlock}"
                              Foreground="{DynamicResource PrimaryHueMidBrush}"
                              HorizontalAlignment="Center"
                              Margin="0,8,0,4"/>
                    <TextBlock Text="成功率" 
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"
                              HorizontalAlignment="Center"/>
                </StackPanel>
            </materialDesign:Card>
        </Grid>

        <!-- 日志区域 -->
        <materialDesign:Card Grid.Row="2" Margin="16,0,16,16" Padding="16">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <Grid Grid.Row="0" Margin="0,0,0,12">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                              Text="转换日志"
                              Style="{StaticResource MaterialDesignSubtitle1TextBlock}"/>

                    <StackPanel Grid.Column="1" Orientation="Horizontal">
                        <CheckBox Content="自动滚动"
                                 IsChecked="{Binding AutoScrollEnabled}"
                                 Style="{StaticResource MaterialDesignCheckBox}"
                                 Margin="0,0,16,0"/>

                        <Button Content="清空"
                               Command="{Binding ClearLogCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"
                               Margin="0,0,8,0"/>

                        <Button Content="保存"
                               Command="{Binding SaveLogCommand}"
                               Style="{StaticResource MaterialDesignOutlinedButton}"/>
                    </StackPanel>
                </Grid>

                <Border Grid.Row="1"
                       BorderBrush="{DynamicResource MaterialDesignDivider}"
                       BorderThickness="1"
                       CornerRadius="4">
                    <ScrollViewer x:Name="LogScrollViewer"
                                 VerticalScrollBarVisibility="Auto"
                                 HorizontalScrollBarVisibility="Auto">
                        <TextBox Text="{Binding LogContent}"
                                IsReadOnly="True"
                                FontFamily="Consolas, Courier New"
                                FontSize="12"
                                Background="Transparent"
                                BorderThickness="0"
                                TextWrapping="NoWrap"
                                MinHeight="200"/>
                    </ScrollViewer>
                </Border>

                <Grid Grid.Row="2" Margin="0,8,0,0">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <TextBlock Grid.Column="0"
                              Text="{Binding LogStatisticsText}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                    <TextBlock Grid.Column="1"
                              Text="{Binding LastUpdateTimeText}"
                              Style="{StaticResource MaterialDesignCaptionTextBlock}"
                              Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </Grid>
            </Grid>
        </materialDesign:Card>

        <!-- 控制按钮区域 -->
        <materialDesign:Card Grid.Row="3" Margin="16" Padding="16">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Command="{Binding StartConversionCommand}"
                       Style="{StaticResource MaterialDesignRaisedButton}"
                       Margin="0,0,12,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Play"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="开始转换" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding PauseConversionCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}"
                       Margin="0,0,12,0">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Pause"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="暂停转换" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>

                <Button Command="{Binding StopConversionCommand}"
                       Style="{StaticResource MaterialDesignOutlinedButton}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Stop"
                                               Width="16" Height="16"
                                               VerticalAlignment="Center"
                                               Margin="0,0,8,0"/>
                        <TextBlock Text="停止转换" VerticalAlignment="Center"/>
                    </StackPanel>
                </Button>
            </StackPanel>
        </materialDesign:Card>
    </Grid>
</UserControl>