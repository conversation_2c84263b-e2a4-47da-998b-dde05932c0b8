using System;

namespace DocxToMarkdownConverter.Exceptions;

/// <summary>
/// 应用程序异常基类
/// </summary>
public abstract class ApplicationException : Exception
{
    public string ErrorCode { get; }
    public ErrorSeverity Severity { get; }
    public DateTime Timestamp { get; }
    public string? Context { get; set; }

    protected ApplicationException(string errorCode, ErrorSeverity severity, string message, Exception? innerException = null)
        : base(message, innerException)
    {
        ErrorCode = errorCode;
        Severity = severity;
        Timestamp = DateTime.UtcNow;
    }
}

/// <summary>
/// 文件转换相关异常
/// </summary>
public class ConversionException : ApplicationException
{
    public string FilePath { get; }
    public ConversionErrorType ErrorType { get; }

    public ConversionException(string filePath, ConversionErrorType errorType, string message, Exception? innerException = null)
        : base($"CONV_{errorType}", GetSeverity(errorType), message, innerException)
    {
        FilePath = filePath;
        ErrorType = errorType;
    }

    private static ErrorSeverity GetSeverity(ConversionErrorType errorType)
    {
        return errorType switch
        {
            ConversionErrorType.FileNotFound => ErrorSeverity.Warning,
            ConversionErrorType.InvalidFormat => ErrorSeverity.Warning,
            ConversionErrorType.PermissionDenied => ErrorSeverity.Error,
            ConversionErrorType.CorruptedFile => ErrorSeverity.Warning,
            ConversionErrorType.ProcessingError => ErrorSeverity.Error,
            ConversionErrorType.OutputError => ErrorSeverity.Error,
            ConversionErrorType.UnsupportedFeature => ErrorSeverity.Warning,
            ConversionErrorType.MemoryError => ErrorSeverity.Critical,
            ConversionErrorType.TimeoutError => ErrorSeverity.Error,
            _ => ErrorSeverity.Error
        };
    }
}

/// <summary>
/// 文件操作相关异常
/// </summary>
public class FileOperationException : ApplicationException
{
    public string FilePath { get; }
    public FileOperationType OperationType { get; }

    public FileOperationException(string filePath, FileOperationType operationType, string message, Exception? innerException = null)
        : base($"FILE_{operationType}", ErrorSeverity.Error, message, innerException)
    {
        FilePath = filePath;
        OperationType = operationType;
    }
}

/// <summary>
/// 配置相关异常
/// </summary>
public class ConfigurationException : ApplicationException
{
    public string ConfigurationKey { get; }

    public ConfigurationException(string configurationKey, string message, Exception? innerException = null)
        : base("CONFIG_ERROR", ErrorSeverity.Error, message, innerException)
    {
        ConfigurationKey = configurationKey;
    }
}

/// <summary>
/// 用户界面相关异常
/// </summary>
public class UIException : ApplicationException
{
    public string ComponentName { get; }

    public UIException(string componentName, string message, Exception? innerException = null)
        : base("UI_ERROR", ErrorSeverity.Warning, message, innerException)
    {
        ComponentName = componentName;
    }
}

/// <summary>
/// 网络相关异常
/// </summary>
public class NetworkException : ApplicationException
{
    public string? Endpoint { get; }

    public NetworkException(string? endpoint, string message, Exception? innerException = null)
        : base("NETWORK_ERROR", ErrorSeverity.Error, message, innerException)
    {
        Endpoint = endpoint;
    }
}

/// <summary>
/// 验证相关异常
/// </summary>
public class ValidationException : ApplicationException
{
    public string PropertyName { get; }
    public object? InvalidValue { get; }

    public ValidationException(string propertyName, object? invalidValue, string message)
        : base("VALIDATION_ERROR", ErrorSeverity.Warning, message)
    {
        PropertyName = propertyName;
        InvalidValue = invalidValue;
    }
}

/// <summary>
/// 错误严重程度
/// </summary>
public enum ErrorSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// 转换错误类型
/// </summary>
public enum ConversionErrorType
{
    FileNotFound,
    InvalidFormat,
    PermissionDenied,
    CorruptedFile,
    ProcessingError,
    OutputError,
    UnsupportedFeature,
    MemoryError,
    TimeoutError,
    UnknownError
}

/// <summary>
/// 文件操作类型
/// </summary>
public enum FileOperationType
{
    Read,
    Write,
    Delete,
    Copy,
    Move,
    Create,
    Access
}
