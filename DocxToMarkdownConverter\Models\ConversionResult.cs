namespace DocxToMarkdownConverter.Models;

/// <summary>
/// 表示单个文件转换操作的结果
/// </summary>
public class ConversionResult
{
    /// <summary>
    /// 转换是否成功
    /// </summary>
    public bool IsSuccess { get; set; }
    
    /// <summary>
    /// 输入文件路径
    /// </summary>
    public string InputPath { get; set; } = string.Empty;
    
    /// <summary>
    /// 输出文件路径
    /// </summary>
    public string OutputPath { get; set; } = string.Empty;
    
    /// <summary>
    /// 转换耗时
    /// </summary>
    public TimeSpan Duration { get; set; }
    
    /// <summary>
    /// 输入文件大小（字节）
    /// </summary>
    public long InputSize { get; set; }
    
    /// <summary>
    /// 输出文件大小（字节）
    /// </summary>
    public long OutputSize { get; set; }
    
    /// <summary>
    /// 处理的图片数量
    /// </summary>
    public int ImageCount { get; set; }
    
    /// <summary>
    /// 处理的表格数量
    /// </summary>
    public int TableCount { get; set; }
    
    /// <summary>
    /// 处理的公式数量
    /// </summary>
    public int FormulaCount { get; set; }
    
    /// <summary>
    /// 错误消息（如果转换失败）
    /// </summary>
    public string? ErrorMessage { get; set; }
    
    /// <summary>
    /// 异常信息（如果转换失败）
    /// </summary>
    public Exception? Exception { get; set; }
    
    /// <summary>
    /// 转换开始时间
    /// </summary>
    public DateTime StartTime { get; set; }
    
    /// <summary>
    /// 转换结束时间
    /// </summary>
    public DateTime EndTime { get; set; }
    
    /// <summary>
    /// 转换成功率（百分比）
    /// </summary>
    public double SuccessRate => IsSuccess ? 100.0 : 0.0;
    
    /// <summary>
    /// 转换速度（字节/秒）
    /// </summary>
    public double ConversionSpeed => Duration.TotalSeconds > 0 ? InputSize / Duration.TotalSeconds : 0;
}