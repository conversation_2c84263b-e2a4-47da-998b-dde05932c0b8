using System.Windows.Controls;
using DocxToMarkdownConverter.ViewModels;

namespace DocxToMarkdownConverter.Controls;

/// <summary>
/// Interaction logic for ThemeControl.xaml
/// </summary>
public partial class ThemeControl : System.Windows.Controls.UserControl
{
    public ThemeControl()
    {
        InitializeComponent();
    }

    public ThemeControl(ThemeControlViewModel viewModel) : this()
    {
        DataContext = viewModel;
    }
}