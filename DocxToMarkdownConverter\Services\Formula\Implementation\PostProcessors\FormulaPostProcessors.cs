using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;
using System.Text.RegularExpressions;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.PostProcessors;

/// <summary>
/// 符号标准化后处理器
/// </summary>
public class SymbolNormalizationProcessor : IFormulaPostProcessor
{
    private readonly ILogger<SymbolNormalizationProcessor> _logger;
    private readonly Dictionary<string, string> _symbolMap;

    public SymbolNormalizationProcessor(ILogger<SymbolNormalizationProcessor> logger)
    {
        _logger = logger;
        _symbolMap = InitializeSymbolMap();
    }

    public int Priority => 100; // 高优先级，最先执行

    public bool CanProcess(FormulaFormattingOptions options)
    {
        return options.NormalizeSymbols;
    }

    public async Task<string> ProcessAsync(string input, FormulaFormattingOptions options)
    {
        try
        {
            _logger.LogDebug("Normalizing symbols in formula");

            var result = input;

            // 应用符号映射
            foreach (var mapping in _symbolMap)
            {
                result = result.Replace(mapping.Key, mapping.Value);
            }

            // 应用自定义符号映射
            if (options.CustomSymbolMap?.Any() == true)
            {
                foreach (var mapping in options.CustomSymbolMap)
                {
                    result = result.Replace(mapping.Key, mapping.Value);
                }
            }

            _logger.LogDebug("Symbol normalization completed");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during symbol normalization");
            return input; // 返回原始输入
        }
    }

    private Dictionary<string, string> InitializeSymbolMap()
    {
        return new Dictionary<string, string>
        {
            // Unicode符号到LaTeX的映射
            { "≤", "\\leq" },
            { "≥", "\\geq" },
            { "≠", "\\neq" },
            { "≈", "\\approx" },
            { "≡", "\\equiv" },
            { "∞", "\\infty" },
            { "±", "\\pm" },
            { "∓", "\\mp" },
            { "×", "\\times" },
            { "÷", "\\div" },
            { "·", "\\cdot" },
            { "∂", "\\partial" },
            { "∇", "\\nabla" },
            { "∑", "\\sum" },
            { "∏", "\\prod" },
            { "∫", "\\int" },
            { "∮", "\\oint" },
            { "√", "\\sqrt" },
            { "∈", "\\in" },
            { "∉", "\\notin" },
            { "⊂", "\\subset" },
            { "⊃", "\\supset" },
            { "∪", "\\cup" },
            { "∩", "\\cap" },
            { "∅", "\\emptyset" }
        };
    }
}

/// <summary>
/// 间距处理器
/// </summary>
public class SpacingProcessor : IFormulaPostProcessor
{
    private readonly ILogger<SpacingProcessor> _logger;

    public SpacingProcessor(ILogger<SpacingProcessor> logger)
    {
        _logger = logger;
    }

    public int Priority => 200; // 中等优先级

    public bool CanProcess(FormulaFormattingOptions options)
    {
        return options.FixSpacing;
    }

    public async Task<string> ProcessAsync(string input, FormulaFormattingOptions options)
    {
        try
        {
            _logger.LogDebug("Processing spacing in formula");

            var result = input;

            // 修复运算符间距
            result = FixOperatorSpacing(result);

            // 修复函数名间距
            result = FixFunctionSpacing(result);

            // 添加运算符周围的空格（如果启用）
            if (options.AddSpacingAroundOperators)
            {
                result = AddOperatorSpacing(result);
            }

            // 清理多余的空格
            result = CleanExtraSpaces(result);

            _logger.LogDebug("Spacing processing completed");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during spacing processing");
            return input;
        }
    }

    private string FixOperatorSpacing(string input)
    {
        // 修复常见的间距问题
        var patterns = new Dictionary<string, string>
        {
            { @"\\times\s*min", @"\\times \min" },
            { @"\\times\s*max", @"\\times \max" },
            { @"\\times\s*log", @"\\times \log" },
            { @"\\times\s*sin", @"\\times \sin" },
            { @"\\times\s*cos", @"\\times \cos" },
            { @"\\times\s*tan", @"\\times \tan" }
        };

        var result = input;
        foreach (var pattern in patterns)
        {
            result = Regex.Replace(result, pattern.Key, pattern.Value);
        }

        return result;
    }

    private string FixFunctionSpacing(string input)
    {
        // 确保函数名后有适当的间距
        var functionPattern = @"\\(sin|cos|tan|log|ln|exp|sqrt|frac|sum|prod|int)\s*";
        return Regex.Replace(input, functionPattern, match =>
        {
            var functionName = match.Groups[1].Value;
            return $"\\{functionName} ";
        });
    }

    private string AddOperatorSpacing(string input)
    {
        // 在二元运算符周围添加空格
        var operators = new[] { "+", "-", "=", "<", ">", "\\leq", "\\geq", "\\neq" };
        var result = input;

        foreach (var op in operators)
        {
            var pattern = $@"(?<!\s){Regex.Escape(op)}(?!\s)";
            result = Regex.Replace(result, pattern, $" {op} ");
        }

        return result;
    }

    private string CleanExtraSpaces(string input)
    {
        // 清理多余的空格
        return Regex.Replace(input, @"\s+", " ").Trim();
    }
}

/// <summary>
/// 分段函数处理器
/// </summary>
public class PiecewiseFunctionProcessor : IFormulaPostProcessor
{
    private readonly ILogger<PiecewiseFunctionProcessor> _logger;

    public PiecewiseFunctionProcessor(ILogger<PiecewiseFunctionProcessor> logger)
    {
        _logger = logger;
    }

    public int Priority => 300; // 较低优先级

    public bool CanProcess(FormulaFormattingOptions options)
    {
        return true; // 总是可以处理
    }

    public async Task<string> ProcessAsync(string input, FormulaFormattingOptions options)
    {
        try
        {
            _logger.LogDebug("Processing piecewise functions");

            var result = input;

            // 修复分段函数格式
            result = FixCasesEnvironment(result);

            // 修复条件表达式
            result = FixConditionExpressions(result);

            // 确保正确的行分隔符
            result = FixLineBreaks(result);

            _logger.LogDebug("Piecewise function processing completed");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during piecewise function processing");
            return input;
        }
    }

    private string FixCasesEnvironment(string input)
    {
        // 确保cases环境的正确格式
        var casesPattern = @"\\begin\{cases\}(.*?)\\end\{cases\}";
        return Regex.Replace(input, casesPattern, match =>
        {
            var content = match.Groups[1].Value.Trim();
            
            // 确保每行都有正确的格式
            var lines = content.Split(new[] { "\\\\" }, StringSplitOptions.RemoveEmptyEntries);
            var formattedLines = lines.Select(line =>
            {
                line = line.Trim();
                if (!line.Contains("&"))
                {
                    // 如果没有对齐符号，尝试添加
                    var parts = line.Split(new[] { "if", "when", "otherwise" }, StringSplitOptions.None);
                    if (parts.Length > 1)
                    {
                        return $"{parts[0].Trim()}, & \\text{{{string.Join("", parts.Skip(1)).Trim()}}}";
                    }
                }
                return line;
            });

            return $"\\begin{{cases}}\n{string.Join(" \\\\ \n", formattedLines)}\n\\end{{cases}}";
        }, RegexOptions.Singleline);
    }

    private string FixConditionExpressions(string input)
    {
        // 确保条件表达式被正确包装在\text{}中
        var conditionPattern = @"&\s*(if|when|otherwise|else)\s+([^\\]+)";
        return Regex.Replace(input, conditionPattern, match =>
        {
            var keyword = match.Groups[1].Value;
            var condition = match.Groups[2].Value.Trim();
            return $"& \\text{{{keyword} {condition}}}";
        });
    }

    private string FixLineBreaks(string input)
    {
        // 确保行分隔符的正确格式
        return Regex.Replace(input, @"\s*\\\\\s*", " \\\\ ");
    }
}

/// <summary>
/// 错误恢复处理器
/// </summary>
public class ErrorRecoveryProcessor : IFormulaPostProcessor
{
    private readonly ILogger<ErrorRecoveryProcessor> _logger;

    public ErrorRecoveryProcessor(ILogger<ErrorRecoveryProcessor> logger)
    {
        _logger = logger;
    }

    public int Priority => 1000; // 最低优先级，最后执行

    public bool CanProcess(FormulaFormattingOptions options)
    {
        return true; // 总是可以处理
    }

    public async Task<string> ProcessAsync(string input, FormulaFormattingOptions options)
    {
        try
        {
            _logger.LogDebug("Processing error recovery");

            var result = input;

            // 修复常见的LaTeX错误
            result = FixCommonLatexErrors(result);

            // 验证括号匹配
            result = FixBracketMatching(result);

            // 处理空的或无效的公式
            result = HandleEmptyFormulas(result);

            _logger.LogDebug("Error recovery processing completed");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during error recovery processing");
            return input;
        }
    }

    private string FixCommonLatexErrors(string input)
    {
        var fixes = new Dictionary<string, string>
        {
            // 修复常见的命令错误
            { @"\\frac\s*\{\s*\}\s*\{", @"\\frac{1}{" },
            { @"\\frac\s*\{([^}]*)\}\s*\{\s*\}", @"\\frac{$1}{1}" },
            
            // 修复空的上下标
            { @"\^\s*\{\s*\}", "^{}" },
            { @"_\s*\{\s*\}", "_{}" },
            
            // 修复多余的空格
            { @"\s+", " " },
            
            // 修复未闭合的命令
            { @"\\text\s*\{([^}]*?)$", @"\\text{$1}" }
        };

        var result = input;
        foreach (var fix in fixes)
        {
            result = Regex.Replace(result, fix.Key, fix.Value);
        }

        return result;
    }

    private string FixBracketMatching(string input)
    {
        try
        {
            var result = input;
            
            // 检查和修复花括号
            result = FixBraceMatching(result);
            
            // 检查和修复圆括号
            result = FixParenthesesMatching(result);

            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error fixing bracket matching");
            return input;
        }
    }

    private string FixBraceMatching(string input)
    {
        var openCount = input.Count(c => c == '{');
        var closeCount = input.Count(c => c == '}');

        if (openCount > closeCount)
        {
            // 添加缺失的闭合括号
            return input + new string('}', openCount - closeCount);
        }
        else if (closeCount > openCount)
        {
            // 移除多余的闭合括号
            var result = input;
            var extraClose = closeCount - openCount;
            for (int i = 0; i < extraClose; i++)
            {
                var lastIndex = result.LastIndexOf('}');
                if (lastIndex >= 0)
                {
                    result = result.Remove(lastIndex, 1);
                }
            }
            return result;
        }

        return input;
    }

    private string FixParenthesesMatching(string input)
    {
        var openCount = input.Count(c => c == '(');
        var closeCount = input.Count(c => c == ')');

        if (openCount > closeCount)
        {
            return input + new string(')', openCount - closeCount);
        }
        else if (closeCount > openCount)
        {
            var result = input;
            var extraClose = closeCount - openCount;
            for (int i = 0; i < extraClose; i++)
            {
                var lastIndex = result.LastIndexOf(')');
                if (lastIndex >= 0)
                {
                    result = result.Remove(lastIndex, 1);
                }
            }
            return result;
        }

        return input;
    }

    private string HandleEmptyFormulas(string input)
    {
        if (string.IsNullOrWhiteSpace(input))
        {
            return "\\text{Empty formula}";
        }

        // 检查是否只包含空白和LaTeX命令
        var cleanInput = Regex.Replace(input, @"\\[a-zA-Z]+\s*\{\s*\}", "").Trim();
        if (string.IsNullOrWhiteSpace(cleanInput))
        {
            return "\\text{Invalid formula}";
        }

        return input;
    }
}
