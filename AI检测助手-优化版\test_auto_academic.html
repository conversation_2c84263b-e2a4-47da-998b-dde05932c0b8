<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自动化学术架构测试</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .test-section { margin: 30px 0; padding: 20px; border: 1px solid #ddd; border-radius: 8px; background: #f9f9f9; }
        .success { color: green; }
        .error { color: red; }
        .warning { color: orange; }
        button { padding: 12px 24px; margin: 10px 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; font-size: 14px; }
        button:hover { background: #0056b3; }
        button.secondary { background: #6c757d; }
        button.secondary:hover { background: #545b62; }
        textarea { width: 100%; height: 150px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; font-family: inherit; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .status { margin: 20px 0; padding: 15px; background: #e9ecef; border-radius: 5px; }
        .result { margin-top: 20px; padding: 20px; background: #f8f9fa; border-radius: 5px; border-left: 4px solid #007bff; }
    </style>
</head>
<body>
    <div class="container">
        <h1>自动化学术架构功能测试</h1>
        
        <div class="test-section">
            <h2>功能测试</h2>
            <div class="grid">
                <div>
                    <label for="testTextA"><strong>学术文本A：</strong></label>
                    <textarea id="testTextA" placeholder="输入第一段学术内容...">本研究提出了一种基于深度学习的文本分析方法，该方法能够有效识别学术文本中的创新点和优势特征。首先，我们构建了一个多层神经网络模型，用于提取文本的语义特征。其次，通过大规模数据集的训练，模型能够准确识别不同类型的学术表达模式。最后，实验结果表明，该方法在多个评估指标上都取得了显著的性能提升，为学术文本分析提供了新的解决方案。</textarea>
                </div>
                <div>
                    <label for="testTextB"><strong>学术文本B：</strong></label>
                    <textarea id="testTextB" placeholder="输入第二段学术内容...">传统的文本分析方法主要依赖于规则和统计特征，但在处理复杂的学术文本时存在明显的局限性。为了解决这一问题，我们开发了一套创新的评估体系，该体系结合了自然语言处理技术和机器学习算法。通过对大量学术论文的分析，我们发现了学术写作中的关键模式和特征。此外，我们还建立了一个综合评分系统，能够从多个维度评估学术文本的质量和创新性。</textarea>
                </div>
            </div>
            
            <div style="margin: 20px 0;">
                <h3>配置选项：</h3>
                <label><input type="checkbox" id="testJacs" checked> JACS式紧凑结构</label>
                <label><input type="checkbox" id="testInnovation" checked> 创新点密度≥3个/千字</label>
                <label><input type="checkbox" id="testEthics" checked> 伦理规范声明</label>
                <label><input type="checkbox" id="testQuality" checked> 自动质量控制</label>
            </div>
            
            <button onclick="testAutoOptimization()">开始自动化优化测试</button>
            <button class="secondary" onclick="clearTest()">清空内容</button>
            
            <div id="testStatus" class="status" style="display: none;"></div>
            <div id="testResult" class="result" style="display: none;"></div>
        </div>
        
        <div class="test-section">
            <h2>模块加载状态</h2>
            <div id="moduleStatus"></div>
        </div>
    </div>

    <script src="js/ai_detector.js"></script>
    <script src="js/academic_optimizer.js"></script>
    <script src="js/hybrid_detector.js"></script>
    <script src="js/multi_round_optimizer.js"></script>
    <script src="js/prompt_templates.js"></script>
    <script src="js/ollama_manager_v2.js"></script>

    <script>
        // 模拟全局变量
        let hybridModeEnabled = false;
        let ollamaManagerV2 = null;

        document.addEventListener('DOMContentLoaded', function() {
            checkModules();
        });

        function checkModules() {
            const statusDiv = document.getElementById('moduleStatus');
            let html = '<h3>模块加载检查：</h3><ul>';

            // 检查academicOptimizer
            if (typeof academicOptimizer !== 'undefined') {
                html += '<li class="success">✅ academicOptimizer 已加载</li>';
                
                // 检查关键方法
                const methods = ['performDynamicComparison', 'extractCoreAdvantages', 'performAdvancedFusion'];
                methods.forEach(method => {
                    if (typeof academicOptimizer[method] === 'function') {
                        html += `<li class="success">✅ ${method}() 方法可用</li>`;
                    } else {
                        html += `<li class="error">❌ ${method}() 方法不可用</li>`;
                    }
                });
            } else {
                html += '<li class="error">❌ academicOptimizer 未加载</li>';
            }

            html += '</ul>';
            statusDiv.innerHTML = html;
        }

        async function testAutoOptimization() {
            const textA = document.getElementById('testTextA').value.trim();
            const textB = document.getElementById('testTextB').value.trim();
            const statusDiv = document.getElementById('testStatus');
            const resultDiv = document.getElementById('testResult');

            if (!textA || !textB) {
                alert('请输入两段测试文本！');
                return;
            }

            const config = {
                jacsStructure: document.getElementById('testJacs').checked,
                innovationDensity: document.getElementById('testInnovation').checked,
                ethicsCompliance: document.getElementById('testEthics').checked,
                qualityControl: document.getElementById('testQuality').checked
            };

            statusDiv.style.display = 'block';
            resultDiv.style.display = 'none';
            statusDiv.innerHTML = '<h3>测试进行中...</h3>';

            try {
                // 第一步：动态对比分析
                statusDiv.innerHTML += '<p>🔍 第一步：执行动态对比分析...</p>';
                const comparison = academicOptimizer.performDynamicComparison(textA, textB);
                statusDiv.innerHTML += '<p class="success">✅ 动态对比分析完成</p>';

                // 第二步：优势结构提取
                statusDiv.innerHTML += '<p>📊 第二步：提取优势结构...</p>';
                const advantagesA = academicOptimizer.extractCoreAdvantages(textA);
                const advantagesB = academicOptimizer.extractCoreAdvantages(textB);
                statusDiv.innerHTML += '<p class="success">✅ 优势结构提取完成</p>';

                // 第三步：智能融合生成
                statusDiv.innerHTML += '<p>🔧 第三步：执行智能融合生成...</p>';
                const fusion = academicOptimizer.performAdvancedFusion(textA, textB, config);
                statusDiv.innerHTML += '<p class="success">✅ 智能融合生成完成</p>';

                // 显示结果
                const resultHtml = `
                    <h3>测试结果：</h3>
                    <div style="margin: 15px 0;">
                        <h4>对比分析结果：</h4>
                        <p>结构规范性评分: ${comparison.structuralCompliance?.score || 'N/A'}</p>
                        <p>创新性指数: ${comparison.innovationIndex?.score || 'N/A'}</p>
                    </div>
                    <div style="margin: 15px 0;">
                        <h4>优势提取结果：</h4>
                        <p>文本A综合评分: ${Math.round(advantagesA.overallScore || 0)}/100</p>
                        <p>文本B综合评分: ${Math.round(advantagesB.overallScore || 0)}/100</p>
                    </div>
                    <div style="margin: 15px 0;">
                        <h4>融合生成结果：</h4>
                        <div style="background: white; padding: 15px; border-radius: 5px; border: 1px solid #ddd; max-height: 200px; overflow-y: auto;">
                            ${fusion.optimizedText || '融合文本生成失败'}
                        </div>
                        <p style="margin-top: 10px;">应用的改进项: ${fusion.improvements?.length || 0} 项</p>
                    </div>
                `;

                resultDiv.innerHTML = resultHtml;
                resultDiv.style.display = 'block';
                statusDiv.innerHTML += '<p class="success">🎉 所有测试步骤完成！</p>';

            } catch (error) {
                statusDiv.innerHTML += `<p class="error">❌ 测试失败: ${error.message}</p>`;
                console.error('测试失败:', error);
            }
        }

        function clearTest() {
            document.getElementById('testTextA').value = '';
            document.getElementById('testTextB').value = '';
            document.getElementById('testStatus').style.display = 'none';
            document.getElementById('testResult').style.display = 'none';
        }
    </script>
</body>
</html>
