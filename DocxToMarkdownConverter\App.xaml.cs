using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Serilog;
using System.IO;
using System.Windows;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Views;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Controls;
using Application = System.Windows.Application;

namespace DocxToMarkdownConverter;

/// <summary>
/// Interaction logic for App.xaml
/// </summary>
public partial class App : Application
{
    private ServiceProvider? _serviceProvider;
    private IConfiguration? _configuration;

    public ServiceProvider? ServiceProvider => _serviceProvider;

    protected override async void OnStartup(StartupEventArgs e)
    {
        // 检查是否运行公式测试
        if (e.Args.Length > 0 && e.Args[0] == "test-formula")
        {
            await TestFormulaProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行分段函数测试
        if (e.Args.Length > 0 && e.Args[0] == "test-piecewise")
        {
            await TestPiecewiseFunctionProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行LaTeX反斜杠转义测试
        if (e.Args.Length > 0 && e.Args[0] == "test-latex-escape")
        {
            await TestLatexEscapeProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行分数表达式测试
        if (e.Args.Length > 0 && e.Args[0] == "test-fraction")
        {
            await TestFractionProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行分段函数分数测试
        if (e.Args.Length > 0 && e.Args[0] == "test-piecewise-fraction")
        {
            await TestPiecewiseFractionProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行分段函数分数边缘情况测试
        if (e.Args.Length > 0 && e.Args[0] == "test-piecewise-edge")
        {
            await TestPiecewiseEdgeCase();
            Shutdown();
            return;
        }

        // 检查是否运行复杂分母分数测试
        if (e.Args.Length > 0 && e.Args[0] == "test-complex-fraction")
        {
            await TestComplexFractionProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行正则表达式调试测试
        if (e.Args.Length > 0 && e.Args[0] == "test-regex-debug")
        {
            await TestRegexDebug();
            Shutdown();
            return;
        }

        // 检查是否运行带日志的分数处理测试
        if (e.Args.Length > 0 && e.Args[0] == "test-fraction-with-log")
        {
            await TestFractionWithLogging();
            Shutdown();
            return;
        }

        // 检查是否运行端到端分数处理测试
        if (e.Args.Length > 0 && e.Args[0] == "test-end-to-end-fraction")
        {
            await TestEndToEndFractionProcessing();
            Shutdown();
            return;
        }

        // 检查是否运行DocumentProcessor修复测试
        if (e.Args.Length > 0 && e.Args[0] == "test-document-processor-fix")
        {
            await TestDocumentProcessorFix();
            Shutdown();
            return;
        }

        // 检查是否运行深度调试测试
        if (e.Args.Length > 0 && e.Args[0] == "test-deep-debug")
        {
            await TestDeepDebugConversion();
            Shutdown();
            return;
        }

        // 检查是否运行实际文档转换测试
        if (e.Args.Length > 0 && e.Args[0] == "test-actual-conversion")
        {
            await TestActualDocumentConversion();
            Shutdown();
            return;
        }

        // 立即写入文件，确保我们能看到输出
        try
        {
            await File.WriteAllTextAsync("debug_startup.log", $"App.OnStartup: 开始启动应用程序 - {DateTime.Now}\n");
        }
        catch { }

        System.Diagnostics.Debug.WriteLine("App.OnStartup: 开始启动应用程序");
        Console.WriteLine("App.OnStartup: 开始启动应用程序");

        try
        {
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 调用 base.OnStartup - {DateTime.Now}\n");
            base.OnStartup(e);
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: base.OnStartup 完成 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: base.OnStartup 完成");

            // Enable hardware acceleration and rendering optimizations
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 启用渲染优化 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 启用渲染优化");
            EnableRenderingOptimizations();

            // Configure services
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 配置服务 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 配置服务");
            ConfigureServices();

            // Configure logging
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 配置日志 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 配置日志");
            ConfigureLogging();

            // Set up global exception handling
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 设置异常处理 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 设置异常处理");
            SetupExceptionHandling();
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 异常处理设置完成 - {DateTime.Now}\n");

            // Check for test mode
            System.Diagnostics.Debug.WriteLine($"App.OnStartup: 检查测试模式，参数数量: {e.Args.Length}");
            if (e.Args.Length > 0 && e.Args[0] == "--test")
            {
                System.Diagnostics.Debug.WriteLine("App.OnStartup: 运行测试模式");
                await RunConversionTest();
                Shutdown();
                return;
            }

            // Initialize configuration service
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 初始化配置服务 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 初始化配置服务");
            await InitializeConfigurationServiceAsync();
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 配置服务初始化完成 - {DateTime.Now}\n");

            // Start the main window
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 创建主窗口 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("App.OnStartup: 创建主窗口");
            try
            {
                await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 调用 GetRequiredService<MainWindow> - {DateTime.Now}\n");
                var mainWindow = _serviceProvider?.GetRequiredService<MainWindow>();
                await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 主窗口创建结果: {mainWindow != null} - {DateTime.Now}\n");
                System.Diagnostics.Debug.WriteLine($"App.OnStartup: 主窗口创建结果: {mainWindow != null}");
                if (mainWindow != null)
                {
                    await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 设置 MainWindow 属性 - {DateTime.Now}\n");
                    MainWindow = mainWindow;
                    await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 显示主窗口 - {DateTime.Now}\n");
                    System.Diagnostics.Debug.WriteLine("App.OnStartup: 显示主窗口");
                    mainWindow.Show();
                    await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 主窗口显示完成 - {DateTime.Now}\n");
                    System.Diagnostics.Debug.WriteLine("App.OnStartup: 主窗口显示完成");
                }
                else
                {
                    await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 主窗口创建失败 - service provider 返回 null - {DateTime.Now}\n");
                    System.Diagnostics.Debug.WriteLine("App.OnStartup: 主窗口创建失败 - service provider 返回 null");
                    Log.Error("Failed to create MainWindow - service provider returned null");
                    Shutdown();
                }
            }
            catch (Exception ex)
            {
                await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 创建主窗口时发生异常: {ex.Message} - {DateTime.Now}\n");
                await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 异常堆栈: {ex.StackTrace} - {DateTime.Now}\n");
                System.Diagnostics.Debug.WriteLine($"App.OnStartup: 创建主窗口时发生异常: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"App.OnStartup: 异常堆栈: {ex.StackTrace}");
                Log.Error(ex, "Failed to create and show MainWindow");
                System.Windows.MessageBox.Show($"Failed to start application: {ex.Message}", "Startup Error", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 启动过程中发生异常: {ex.Message} - {DateTime.Now}\n");
            await File.AppendAllTextAsync("debug_startup.log", $"App.OnStartup: 异常堆栈: {ex.StackTrace} - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine($"App.OnStartup: 启动过程中发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"App.OnStartup: 异常堆栈: {ex.StackTrace}");
            Console.WriteLine($"App.OnStartup: 启动过程中发生异常: {ex.Message}");
            try
            {
                System.Windows.MessageBox.Show($"应用程序启动失败: {ex.Message}\n\n详细信息:\n{ex.StackTrace}", "启动错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 如果连消息框都显示不了，至少输出到控制台
                Console.WriteLine($"严重错误，无法显示错误对话框: {ex}");
            }
            Shutdown();
        }
    }

    private void EnableRenderingOptimizations()
    {
        try
        {
            // Enable hardware acceleration for better scrolling performance
            System.Windows.Media.RenderOptions.ProcessRenderMode = System.Windows.Interop.RenderMode.Default;

            // Set rendering tier to use hardware acceleration when available
            var renderingTier = (System.Windows.Media.RenderCapability.Tier >> 16);
            Log.Information($"Rendering tier: {renderingTier}");

            // Enable bitmap caching for better performance
            System.Windows.Media.RenderOptions.SetBitmapScalingMode(this.MainWindow, System.Windows.Media.BitmapScalingMode.HighQuality);

            // Optimize text rendering
            System.Windows.Media.TextOptions.SetTextFormattingMode(this.MainWindow, System.Windows.Media.TextFormattingMode.Display);
            System.Windows.Media.TextOptions.SetTextRenderingMode(this.MainWindow, System.Windows.Media.TextRenderingMode.Auto);

            Log.Information("Rendering optimizations enabled successfully");
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Failed to enable some rendering optimizations");
        }
    }

    private void ConfigureServices()
    {
        System.Diagnostics.Debug.WriteLine("ConfigureServices: 开始配置服务");
        try
        {
            // Build configuration
            System.Diagnostics.Debug.WriteLine("ConfigureServices: 构建配置");
            _configuration = new ConfigurationBuilder()
                .SetBasePath(Directory.GetCurrentDirectory())
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();

            // Create service collection
            System.Diagnostics.Debug.WriteLine("ConfigureServices: 创建服务集合");
            var services = new ServiceCollection();

        // Register configuration
        services.AddSingleton(_configuration);

        // Register logging services first (needed by other services)
        services.AddSingleton<ILoggingService, SimpleLoggingService>();
        services.AddSingleton<IAdvancedLoggingService, AdvancedLoggingService>();

        // Register core services
        services.AddSingleton<INavigationService, NavigationService>();
        services.AddSingleton<ISystemThemeMonitor, SystemThemeMonitor>();
        services.AddSingleton<IThemeManager, ThemeManager>();
        services.AddSingleton<IThemeTransitionService, ThemeTransitionService>();
        services.AddSingleton<IAnimationManager, AnimationManager>();
        services.AddSingleton<IConfigurationService, ConfigurationService>();
        services.AddSingleton<ILocalizationService, LocalizationService>();

        // Register progress services
        services.AddSingleton<IErrorRecoveryService, ErrorRecoveryService>();
        services.AddSingleton<IProgressTrackingService, ProgressTrackingService>();
        services.AddSingleton<TimeEstimationService>();

        // Register performance optimization services
        services.AddSingleton<IPerformanceMonitorService, PerformanceMonitorService>();
        services.AddSingleton<IStreamingDocumentProcessor, StreamingDocumentProcessor>();
        services.AddSingleton<IUIThreadOptimizationService, UIThreadOptimizationService>();
        services.AddSingleton<IAdaptiveAnimationManager, AdaptiveAnimationManager>();

        // Register conversion services
        services.AddTransient<IDocxConverter, DocxConverter>();
        services.AddTransient<IDocumentProcessor, DocumentProcessor>();
        services.AddTransient<ITextProcessor, TextProcessor>();
        services.AddTransient<IImageProcessor, ImageProcessor>();
        services.AddTransient<ITableProcessor, TableProcessor>();
        services.AddTransient<IFormulaProcessor, FormulaProcessor>();

        // Register ViewModels
        services.AddTransient<MainWindowViewModel>(provider => new MainWindowViewModel(
            provider.GetRequiredService<INavigationService>(),
            provider.GetRequiredService<IThemeManager>(),
            provider.GetRequiredService<IConfigurationService>(),
            provider.GetRequiredService<ILocalizationService>(),
            provider,
            provider.GetService<ILoggingService>()
        ));
        services.AddTransient<SettingsViewModel>();
        services.AddTransient<ProgressViewModel>();
        services.AddTransient<ResultsViewModel>();
        services.AddTransient<ThemeControlViewModel>();
        services.AddTransient<PerformanceSettingsViewModel>();

        // Register Views
        services.AddTransient<MainWindow>();

        // Register Page Views for Navigation
        services.AddTransient<FileListView>();
        services.AddTransient<SettingsView>();
        services.AddTransient<ProgressView>();
        services.AddTransient<ProgressControl>();
        services.AddTransient<ResultsView>();

        // Register logging
        services.AddLogging(builder =>
        {
            builder.ClearProviders();
            builder.AddSerilog();
        });

            System.Diagnostics.Debug.WriteLine("ConfigureServices: 构建服务提供者");
            _serviceProvider = services.BuildServiceProvider();
            System.Diagnostics.Debug.WriteLine("ConfigureServices: 服务配置完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"ConfigureServices: 配置服务时发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"ConfigureServices: 异常堆栈: {ex.StackTrace}");
            throw;
        }
    }

    private void ConfigureLogging()
    {
        try
        {
            File.AppendAllText("debug_startup.log", $"ConfigureLogging: 开始配置日志 - {DateTime.Now}\n");

            // Use the centralized logging configuration
            LoggingConfiguration.ConfigureSerilog();

            File.AppendAllText("debug_startup.log", $"ConfigureLogging: Serilog配置完成 - {DateTime.Now}\n");

            Log.Information("Application starting up");

            File.AppendAllText("debug_startup.log", $"ConfigureLogging: 日志配置完成 - {DateTime.Now}\n");
        }
        catch (Exception ex)
        {
            File.AppendAllText("debug_startup.log", $"ConfigureLogging: 配置日志时发生异常: {ex.Message} - {DateTime.Now}\n");
            File.AppendAllText("debug_startup.log", $"ConfigureLogging: 异常堆栈: {ex.StackTrace} - {DateTime.Now}\n");
            throw;
        }
    }

    private void SetupExceptionHandling()
    {
        try
        {
            // 设置基本的异常处理器（总是设置）
            DispatcherUnhandledException += (sender, e) =>
            {
                var errorMsg = $"UI线程未处理异常: {e.Exception.Message}";
                try
                {
                    File.AppendAllText("debug_startup.log", $"DispatcherUnhandledException: {errorMsg} - {DateTime.Now}\n");
                    File.AppendAllText("debug_startup.log", $"异常类型: {e.Exception.GetType().FullName} - {DateTime.Now}\n");
                    File.AppendAllText("debug_startup.log", $"异常堆栈: {e.Exception.StackTrace} - {DateTime.Now}\n");

                    // 记录内部异常
                    var innerEx = e.Exception.InnerException;
                    int level = 1;
                    while (innerEx != null)
                    {
                        File.AppendAllText("debug_startup.log", $"内部异常{level}: {innerEx.GetType().FullName}: {innerEx.Message} - {DateTime.Now}\n");
                        File.AppendAllText("debug_startup.log", $"内部异常{level}堆栈: {innerEx.StackTrace} - {DateTime.Now}\n");
                        innerEx = innerEx.InnerException;
                        level++;
                    }
                }
                catch { }

                Log.Error(e.Exception, "Unhandled dispatcher exception");
                System.Windows.MessageBox.Show($"发生未处理的异常：{e.Exception.Message}\n\n详细信息:\n{e.Exception.StackTrace}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                e.Handled = true;
            };

            AppDomain.CurrentDomain.UnhandledException += (sender, e) =>
            {
                if (e.ExceptionObject is Exception exception)
                {
                    var errorMsg = $"应用程序域未处理异常: {exception.Message}";
                    try
                    {
                        File.AppendAllText("debug_startup.log", $"UnhandledException: {errorMsg} - {DateTime.Now}\n");
                        File.AppendAllText("debug_startup.log", $"异常堆栈: {exception.StackTrace} - {DateTime.Now}\n");
                    }
                    catch { }

                    Log.Fatal(exception, "Unhandled domain exception");
                }
            };

            TaskScheduler.UnobservedTaskException += (sender, e) =>
            {
                var errorMsg = $"未观察到的任务异常: {e.Exception.Message}";
                try
                {
                    File.AppendAllText("debug_startup.log", $"UnobservedTaskException: {errorMsg} - {DateTime.Now}\n");
                    File.AppendAllText("debug_startup.log", $"异常堆栈: {e.Exception.StackTrace} - {DateTime.Now}\n");
                }
                catch { }

                Log.Error(e.Exception, "Unobserved task exception");
                e.SetObserved(); // 防止应用程序崩溃
            };

            // Get services for exception handling
            var logger = _serviceProvider?.GetService<ILogger<GlobalExceptionHandler>>();
            var errorRecoveryService = _serviceProvider?.GetService<IErrorRecoveryService>();

            if (logger != null)
            {
                // Initialize the enhanced global exception handler
                GlobalExceptionHandler.Initialize(logger, errorRecoveryService);
                Log.Information("增强的全局异常处理器已初始化");
            }
            else
            {
                Log.Warning("无法获取日志服务，使用基本异常处理");
            }
        }
        catch (Exception ex)
        {
            try
            {
                File.AppendAllText("debug_startup.log", $"SetupExceptionHandling异常: {ex.Message} - {DateTime.Now}\n");
            }
            catch { }
            Log.Error(ex, "设置异常处理时发生错误");
        }
    }

    private async Task RunConversionTest()
    {
        var logPath = "test_log.txt";
        try
        {
            var log = new List<string>();
            log.Add("DOCX to Markdown Converter - Core Engine Test");
            log.Add("==============================================");

            // Get services from DI container
            var converter = _serviceProvider?.GetRequiredService<IDocxConverter>();
            if (converter == null)
            {
                log.Add("Failed to get converter service");
                await File.WriteAllLinesAsync(logPath, log);
                return;
            }

            // Test file paths
            var inputPath = @"..\0718 排课算法研究缩减版（20面）.docx";
            var outputPath = @"test_output.md";

            // Conversion options
            var options = new ConversionOptions
            {
                OutputDirectory = Path.GetDirectoryName(outputPath) ?? "",
                ExtractImages = true, // Enable for image processing test
                ConvertTables = true, // Enable for table processing test
                ProcessFormulas = false, // Disable for basic test
                PreserveFormatting = true
            };

            log.Add("Starting conversion test...");
            log.Add($"Input: {inputPath}");
            log.Add($"Output: {outputPath}");

            // Progress reporting
            var progress = new Progress<Models.ConversionProgress>(p =>
            {
                log.Add($"Progress: {p.ProgressPercentage:F1}% - {p.CurrentOperation}");
            });

            // Perform conversion
            var result = await converter.ConvertAsync(inputPath, outputPath, options, progress, CancellationToken.None);

            // Report results
            log.Add("");
            log.Add("=== Conversion Results ===");
            log.Add($"Success: {result.IsSuccess}");
            log.Add($"Duration: {result.Duration.TotalSeconds:F2} seconds");
            log.Add($"Input Size: {result.InputSize} bytes");
            log.Add($"Output Size: {result.OutputSize} bytes");

            if (!result.IsSuccess)
            {
                log.Add($"Error: {result.ErrorMessage}");
                if (result.Exception != null)
                {
                    log.Add($"Exception: {result.Exception}");
                }
            }
            else
            {
                log.Add($"Output file created: {File.Exists(outputPath)}");
                if (File.Exists(outputPath))
                {
                    var content = await File.ReadAllTextAsync(outputPath);
                    log.Add($"Content preview (first 500 chars):");
                    log.Add(content.Length > 500 ? content.Substring(0, 500) + "..." : content);
                }
            }

            await File.WriteAllLinesAsync(logPath, log);
        }
        catch (Exception ex)
        {
            var errorLog = new List<string>
            {
                $"Test failed with exception: {ex.Message}",
                $"Stack trace: {ex.StackTrace}"
            };
            await File.WriteAllLinesAsync(logPath, errorLog);
        }
    }

    private async Task InitializeConfigurationServiceAsync()
    {
        try
        {
            await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 开始 - {DateTime.Now}\n");

            var configurationService = _serviceProvider?.GetService<IConfigurationService>();
            await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 获取配置服务结果: {configurationService != null} - {DateTime.Now}\n");

            if (configurationService != null)
            {
                Log.Information("Initializing configuration service");
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 开始配置监视 - {DateTime.Now}\n");

                // Start watching configuration changes for all settings types
                // Use a delay to ensure application is fully initialized
                StartConfigurationWatchingAsync(configurationService);
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 配置监视启动完成 - {DateTime.Now}\n");

                // Ensure default settings exist
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 确保默认设置存在 - {DateTime.Now}\n");
                await EnsureDefaultSettingsExistAsync(configurationService);
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 默认设置确保完成 - {DateTime.Now}\n");

                // Initialize localization service
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 初始化本地化服务 - {DateTime.Now}\n");
                InitializeLocalizationService();
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 本地化服务初始化完成 - {DateTime.Now}\n");

                Log.Information("Configuration service initialized successfully");
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 配置服务初始化成功 - {DateTime.Now}\n");
            }
            else
            {
                Log.Warning("Configuration service not found in service provider");
                await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 配置服务未找到 - {DateTime.Now}\n");
            }
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 发生异常: {ex.Message} - {DateTime.Now}\n");
            await File.AppendAllTextAsync("debug_startup.log", $"InitializeConfigurationServiceAsync: 异常堆栈: {ex.StackTrace} - {DateTime.Now}\n");
            Log.Error(ex, "Failed to initialize configuration service");
            throw;
        }
    }

    private void InitializeLocalizationService()
    {
        try
        {
            var localizationService = _serviceProvider?.GetService<ILocalizationService>();
            if (localizationService != null)
            {
                Log.Information("Initializing localization service");

                // The localization service will automatically load saved language settings
                // and apply the appropriate language resources

                Log.Information("Localization service initialized successfully with language: {Language}",
                    localizationService.CurrentLanguage);
            }
            else
            {
                Log.Warning("Localization service not found in service provider");
            }
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to initialize localization service");
        }
    }

    private async Task EnsureDefaultSettingsExistAsync(IConfigurationService configurationService)
    {
        try
        {
            // Check if application settings exist, create defaults if not
            if (!configurationService.SettingsFileExists<ApplicationSettings>())
            {
                Log.Information("Creating default application settings");
                var defaultSettings = new ApplicationSettings();
                await configurationService.SaveApplicationSettingsAsync(defaultSettings);
            }

            // Check other settings types and create defaults if needed
            var settingsTypes = new[]
            {
                typeof(ConversionOptions),
                typeof(AnimationSettings),
                typeof(ThemeSettings),
                typeof(LanguageSettings)
            };

            foreach (var settingsType in settingsTypes)
            {
                var method = typeof(IConfigurationService).GetMethod(nameof(IConfigurationService.SettingsFileExists))?.MakeGenericMethod(settingsType);
                if (method != null)
                {
                    var exists = (bool)method.Invoke(configurationService, null)!;
                    if (!exists)
                    {
                        Log.Information($"Creating default {settingsType.Name} settings");
                        var defaultInstance = Activator.CreateInstance(settingsType);
                        var saveMethod = typeof(IConfigurationService).GetMethod(nameof(IConfigurationService.SaveSettingsAsync), new[] { settingsType })?.MakeGenericMethod(settingsType);
                        if (saveMethod != null && defaultInstance != null)
                        {
                            var task = (Task)saveMethod.Invoke(configurationService, new[] { defaultInstance })!;
                            await task;
                        }
                    }
                }
            }
        }
        catch (Exception ex)
        {
            Log.Warning(ex, "Failed to ensure default settings exist");
        }
    }

    protected override void OnExit(ExitEventArgs e)
    {
        try
        {
            Log.Information("Application shutting down");

            // 清理配置服务
            var configurationService = _serviceProvider?.GetService<IConfigurationService>();
            if (configurationService is IDisposable disposableConfig)
            {
                disposableConfig.Dispose();
                Log.Information("Configuration service disposed");
            }

            // 清理全局异常处理器
            GlobalExceptionHandler.Cleanup();

            // 清理日志服务
            var advancedLogging = _serviceProvider?.GetService<IAdvancedLoggingService>();
            if (advancedLogging != null)
            {
                // 导出最终日志（如果需要）
                var logStats = advancedLogging.GetLogStatistics();
                Log.Information("应用程序会话统计: 总日志 {TotalLogs}, 错误 {ErrorCount}, 警告 {WarningCount}",
                    logStats.TotalLogs, logStats.ErrorCount, logStats.WarningCount);
            }

            LoggingConfiguration.CloseAndFlush();
        }
        catch (Exception ex)
        {
            // 最后的异常处理
            System.Diagnostics.Debug.WriteLine($"应用程序退出时发生异常: {ex}");
        }
        finally
        {
            _serviceProvider?.Dispose();
            base.OnExit(e);
        }
    }

    private static async void StartConfigurationWatchingAsync(IConfigurationService configurationService)
    {
        try
        {
            // Use a delay to ensure application is fully initialized
            await Task.Delay(2000);

            configurationService.StartWatchingConfiguration<ApplicationSettings>();
            configurationService.StartWatchingConfiguration<ConversionOptions>();
            configurationService.StartWatchingConfiguration<AnimationSettings>();
            configurationService.StartWatchingConfiguration<ThemeSettings>();
            configurationService.StartWatchingConfiguration<LanguageSettings>();
            Log.Information("Configuration file watching started successfully");
        }
        catch (Exception ex)
        {
            Log.Error(ex, "Failed to start configuration file watching");
        }
    }

    private async Task TestFormulaProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("formula_test_results.txt", "=== 数学公式符号间距修复测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("formula_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试用例1：\timesmin 问题
            await File.AppendAllTextAsync("formula_test_results.txt", "测试1：修复 \\timesmin 间距问题\n");
            var input1 = @"0.9 - 0.2\timesmin(T_consec(t,d(s)),3)";
            var output1 = (string)method.Invoke(processor, new object[] { input1, false });
            await File.AppendAllTextAsync("formula_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"输出：{output1}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"结果：{(output1.Contains(@"\times min") ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试用例2：times 缺少反斜杠
            await File.AppendAllTextAsync("formula_test_results.txt", "测试2：修复 times 缺少反斜杠问题\n");
            var input2 = @"a timesmin(x,y)";
            var output2 = (string)method.Invoke(processor, new object[] { input2, false });
            await File.AppendAllTextAsync("formula_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"输出：{output2}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"结果：{(output2.Contains(@"\times min") ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试用例3：≤ 符号转换
            await File.AppendAllTextAsync("formula_test_results.txt", "测试3：修复 ≤ 符号转换和间距\n");
            var input3 = @"x≤3";
            var output3 = (string)method.Invoke(processor, new object[] { input3, false });
            await File.AppendAllTextAsync("formula_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"输出：{output3}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"结果：{(output3.Contains(@" \leq ") ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试用例4：完整公式
            await File.AppendAllTextAsync("formula_test_results.txt", "测试4：完整公式测试\n");
            var input4 = @"0.9 - 0.2\timesmin(T_consec(t,d(s)),3), if T_consec(t,d(s))>0";
            var output4 = (string)method.Invoke(processor, new object[] { input4, true });
            await File.AppendAllTextAsync("formula_test_results.txt", $"输入：{input4}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"输出：{output4}\n");

            bool hasCorrectTimes = output4.Contains(@"\times min");
            bool hasCorrectSpacing = output4.Contains(" > ");
            await File.AppendAllTextAsync("formula_test_results.txt", $"\\times min 修复：{(hasCorrectTimes ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"运算符间距：{(hasCorrectSpacing ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("formula_test_results.txt", $"整体测试：{(hasCorrectTimes && hasCorrectSpacing ? "✓ 通过" : "✗ 失败")}\n\n");

            await File.AppendAllTextAsync("formula_test_results.txt", "=== 测试完成 ===\n");
            await File.AppendAllTextAsync("formula_test_results.txt", "测试结果已保存到 formula_test_results.txt\n");

            Console.WriteLine("公式测试完成，结果已保存到 formula_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("formula_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestPiecewiseFunctionProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("piecewise_test_results.txt", "=== 分段函数行分隔符测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("piecewise_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试1：简单分段函数
            await File.AppendAllTextAsync("piecewise_test_results.txt", "测试1：简单分段函数\n");
            var input1 = @"\begin{cases}x, & \text{if } x > 0 \\ 0, & \text{otherwise}\end{cases}";
            var output1 = (string)method.Invoke(processor, new object[] { input1, true });
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输出：{output1}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"保留双反斜杠：{(output1.Contains(@" \\ ") ? "✓" : "✗")}\n\n");

            // 测试2：复杂分段函数
            await File.AppendAllTextAsync("piecewise_test_results.txt", "测试2：复杂分段函数\n");
            var input2 = @"\begin{cases}0.9 - 0.2\timesmin(T_{consec}(t,d(s)),3), & \text{if } T_{consec}(t,d(s)) > 0 \\ 1, & \text{otherwise}\end{cases}";
            var output2 = (string)method.Invoke(processor, new object[] { input2, true });
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输出：{output2}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"保留双反斜杠：{(output2.Contains(@" \\ ") ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"正确的\\times：{(output2.Contains(@"\times") && !output2.Contains(@"\\times") ? "✓" : "✗")}\n\n");

            // 测试3：三行分段函数
            await File.AppendAllTextAsync("piecewise_test_results.txt", "测试3：三行分段函数\n");
            var input3 = @"\begin{cases}x^2, & \text{if } x > 0 \\ 0, & \text{if } x = 0 \\ -x^2, & \text{if } x < 0\end{cases}";
            var output3 = (string)method.Invoke(processor, new object[] { input3, true });
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"输出：{output3}\n");
            var lineCount = output3.Split(new string[] { @" \\ " }, StringSplitOptions.None).Length;
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"行数正确（应为3行）：{(lineCount == 3 ? "✓" : "✗")} (实际：{lineCount}行)\n\n");

            await File.AppendAllTextAsync("piecewise_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("分段函数测试完成，结果已保存到 piecewise_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("piecewise_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestLatexEscapeProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("latex_escape_test_results.txt", "=== LaTeX反斜杠转义修复测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("latex_escape_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试1：\times符号转义
            await File.AppendAllTextAsync("latex_escape_test_results.txt", "测试1：\\times符号转义\n");
            var input1 = @"0.9 - 0.2\timesmin(T_consec(t,d(s)),3)";
            var output1 = (string)method.Invoke(processor, new object[] { input1, false });
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输出：{output1}\n");

            // 详细检查输出
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输出长度：{output1.Length}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"包含 '\\times min'：{output1.Contains("\\times min")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"包含 '\\\\times'：{output1.Contains("\\\\times")}\n");

            // 检查具体的字符
            var timesIndex = output1.IndexOf("times");
            if (timesIndex > 0)
            {
                var beforeTimes = output1.Substring(Math.Max(0, timesIndex - 3), Math.Min(6, output1.Length - Math.Max(0, timesIndex - 3)));
                await File.AppendAllTextAsync("latex_escape_test_results.txt", $"times前后的字符：'{beforeTimes}'\n");

                // 检查每个字符的ASCII值
                for (int i = Math.Max(0, timesIndex - 2); i < Math.Min(output1.Length, timesIndex + 2); i++)
                {
                    await File.AppendAllTextAsync("latex_escape_test_results.txt", $"字符[{i}]: '{output1[i]}' (ASCII: {(int)output1[i]})\n");
                }
            }

            // 测试简单的字符串替换
            var testString = "\\timesmin";
            var testResult = testString.Replace("\\timesmin", "\\times min");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"简单测试 - 输入：{testString}, 输出：{testResult}\n");

            // 检查实际的字符串内容
            var singleBackslashTimes = "\\times min";  // 这在C#中是单反斜杠
            var doubleBackslashTimes = "\\\\times";    // 这在C#中是双反斜杠

            bool hasSingleBackslash = output1.Contains(singleBackslashTimes);
            bool hasDoubleBackslash = output1.Contains(doubleBackslashTimes);

            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"包含单反斜杠版本：{hasSingleBackslash}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"包含双反斜杠版本：{hasDoubleBackslash}\n");

            bool correctTimes = hasSingleBackslash && !hasDoubleBackslash;
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\times（单反斜杠）：{(correctTimes ? "✓" : "✗")}\n\n");

            // 测试2：比较运算符转义
            await File.AppendAllTextAsync("latex_escape_test_results.txt", "测试2：比较运算符转义\n");
            var input2 = @"x≤3 and y≥5";
            var output2 = (string)method.Invoke(processor, new object[] { input2, false });
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输出：{output2}\n");
            bool correctLeq = output2.Contains("\\leq") && !output2.Contains("\\\\leq");
            bool correctGeq = output2.Contains("\\geq") && !output2.Contains("\\\\geq");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\leq（单反斜杠）：{(correctLeq ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\geq（单反斜杠）：{(correctGeq ? "✓" : "✗")}\n\n");

            // 测试3：分段函数中的\times和双反斜杠行分隔符
            await File.AppendAllTextAsync("latex_escape_test_results.txt", "测试3：分段函数中的\\times和双反斜杠行分隔符\n");
            var input3 = @"\begin{cases}0.9 - 0.2\timesmin(T_consec(t,d(s)),3), & \text{if } T_consec(t,d(s)) > 0 \\ 1, & \text{otherwise}\end{cases}";
            var output3 = (string)method.Invoke(processor, new object[] { input3, true });
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输出：{output3}\n");

            bool correctTimesInPiecewise = output3.Contains("\\times min") && !output3.Contains("\\\\times");
            bool correctLineBreaks = output3.Contains(" \\\\ ");
            bool correctText = output3.Contains("\\text{") && !output3.Contains("\\\\text{");

            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\times（单反斜杠）：{(correctTimesInPiecewise ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"保留双反斜杠行分隔符：{(correctLineBreaks ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\text{{}}（单反斜杠）：{(correctText ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"分段函数综合测试：{(correctTimesInPiecewise && correctLineBreaks && correctText ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试4：数学函数转义
            await File.AppendAllTextAsync("latex_escape_test_results.txt", "测试4：数学函数转义\n");
            var input4 = @"min(x,y) + max(a,b)";
            var output4 = (string)method.Invoke(processor, new object[] { input4, false });
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输入：{input4}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"输出：{output4}\n");
            bool correctMin = output4.Contains("\\min") && !output4.Contains("\\\\min");
            bool correctMax = output4.Contains("\\max") && !output4.Contains("\\\\max");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\min（单反斜杠）：{(correctMin ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"正确的\\max（单反斜杠）：{(correctMax ? "✓" : "✗")}\n\n");

            await File.AppendAllTextAsync("latex_escape_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("LaTeX反斜杠转义测试完成，结果已保存到 latex_escape_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("latex_escape_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestFractionProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("fraction_test_results.txt", "=== 分数表达式处理测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("fraction_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试1：基本斜杠分数
            await File.AppendAllTextAsync("fraction_test_results.txt", "测试1：基本斜杠分数\n");
            var input1 = @"C_{ideal}/cap(r)";
            var output1 = (string)method.Invoke(processor, new object[] { input1, false });
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输出：{output1}\n");
            bool correctFrac1 = output1.Contains("\\frac{C_{ideal}}{cap(r)}");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"正确的\\frac格式：{(correctFrac1 ? "✓" : "✗")}\n\n");

            // 测试2：分段函数中的分数
            await File.AppendAllTextAsync("fraction_test_results.txt", "测试2：分段函数中的分数\n");
            var input2 = @"\begin{cases}5, & \text{if } C_{min} \leq cap(r) \leq C_{ideal} \\ 5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal}\end{cases}";
            var output2 = (string)method.Invoke(processor, new object[] { input2, true });
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输出：{output2}\n");
            bool correctFrac2 = output2.Contains("\\frac{C_{ideal}}{cap(r)}");
            bool correctTimes = output2.Contains("\\times");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"正确的\\frac格式：{(correctFrac2 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"正确的\\times格式：{(correctTimes ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"分段函数分数测试：{(correctFrac2 && correctTimes ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试3：复杂分数表达式
            await File.AppendAllTextAsync("fraction_test_results.txt", "测试3：复杂分数表达式\n");
            var input3 = @"a/b + c/d";
            var output3 = (string)method.Invoke(processor, new object[] { input3, false });
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输出：{output3}\n");
            bool correctFrac3a = output3.Contains("\\frac{a}{b}");
            bool correctFrac3b = output3.Contains("\\frac{c}{d}");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"第一个分数正确：{(correctFrac3a ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"第二个分数正确：{(correctFrac3b ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"复杂分数测试：{(correctFrac3a && correctFrac3b ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试4：带括号的分数
            await File.AppendAllTextAsync("fraction_test_results.txt", "测试4：带括号的分数\n");
            var input4 = @"(x+y)/(z-w)";
            var output4 = (string)method.Invoke(processor, new object[] { input4, false });
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输入：{input4}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输出：{output4}\n");
            // 检查是否正确转换为分数格式（括号可能被去掉，这是正确的）
            bool correctFrac4 = (output4.Contains("frac{") || output4.Contains("\\frac{")) && output4.Contains("x") && output4.Contains("y") && output4.Contains("z") && output4.Contains("w");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"带括号分数正确：{(correctFrac4 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"实际输出包含frac：{output4.Contains("frac")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"带括号分数测试：{(correctFrac4 ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试5：原始问题案例 - 分段函数中的分数
            await File.AppendAllTextAsync("fraction_test_results.txt", "测试5：原始问题案例\n");
            var input5 = @"f_{cap}(r)=\begin{cases}5, & \text{if } C_{min} \leq cap(r) \leq C_{ideal} \\ 5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal}\end{cases}";
            var output5 = (string)method.Invoke(processor, new object[] { input5, true });
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输入：{input5}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"输出：{output5}\n");

            bool correctOriginalCase = output5.Contains("\\frac{C_{ideal}}{cap(r)}") && !output5.Contains("C_{idealcap}");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"修复原始问题：{(correctOriginalCase ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_test_results.txt", $"不包含错误连接：{(!output5.Contains("idealcap") ? "✓" : "✗")}\n\n");

            await File.AppendAllTextAsync("fraction_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("分数表达式测试完成，结果已保存到 fraction_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("fraction_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestPiecewiseFractionProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("piecewise_fraction_test_results.txt", "=== 分段函数分数表达式处理测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试1：分段函数中的简单分数
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试1：分段函数中的简单分数\n");
            var input1 = @"\begin{cases}x/y, & \text{if } x > 0 \\ 0, & \text{otherwise}\end{cases}";
            var output1 = (string)method.Invoke(processor, new object[] { input1, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output1}\n");
            bool correctFrac1 = output1.Contains("\\frac{x}{y}");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"简单分数转换：{(correctFrac1 ? "✓" : "✗")}\n\n");

            // 测试2：分段函数中的下标分数
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试2：分段函数中的下标分数\n");
            var input2 = @"\begin{cases}C_{ideal}/cap(r), & \text{if } cap(r) > 0 \\ 1, & \text{otherwise}\end{cases}";
            var output2 = (string)method.Invoke(processor, new object[] { input2, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output2}\n");
            bool correctFrac2 = output2.Contains("\\frac{C_{ideal}}{cap(r)}");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"下标分数转换：{(correctFrac2 ? "✓" : "✗")}\n\n");

            // 测试3：分段函数中的函数调用分数
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试3：分段函数中的函数调用分数\n");
            var input3 = @"\begin{cases}f(x)/g(y), & \text{if } x \neq y \\ h(z), & \text{otherwise}\end{cases}";
            var output3 = (string)method.Invoke(processor, new object[] { input3, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output3}\n");
            bool correctFrac3 = output3.Contains("\\frac{f(x)}{g(y)}");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"函数调用分数转换：{(correctFrac3 ? "✓" : "✗")}\n\n");

            // 测试4：分段函数中的复杂表达式分数
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试4：分段函数中的复杂表达式分数\n");
            var input4 = @"\begin{cases}(a+b)/(c-d), & \text{if } c > d \\ 0, & \text{otherwise}\end{cases}";
            var output4 = (string)method.Invoke(processor, new object[] { input4, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input4}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output4}\n");
            bool correctFrac4 = output4.Contains("\\frac{") && output4.Contains("a") && output4.Contains("b") && output4.Contains("c") && output4.Contains("d");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"复杂表达式分数转换：{(correctFrac4 ? "✓" : "✗")}\n\n");

            // 测试5：分段函数中的多个分数
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试5：分段函数中的多个分数\n");
            var input5 = @"\begin{cases}a/b + c/d, & \text{if } x > 0 \\ e/f, & \text{otherwise}\end{cases}";
            var output5 = (string)method.Invoke(processor, new object[] { input5, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input5}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output5}\n");
            bool correctFrac5a = output5.Contains("\\frac{a}{b}");
            bool correctFrac5b = output5.Contains("\\frac{c}{d}");
            bool correctFrac5c = output5.Contains("\\frac{e}{f}");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"第一个分数 a/b：{(correctFrac5a ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"第二个分数 c/d：{(correctFrac5b ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"第三个分数 e/f：{(correctFrac5c ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"多个分数测试：{(correctFrac5a && correctFrac5b && correctFrac5c ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试6：边缘情况 - 分段函数格式化可能影响分数的情况
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "测试6：边缘情况 - 复杂分段函数格式\n");
            var input6 = @"\begin{cases}5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal} \\ 1, & \text{otherwise}\end{cases}";
            var output6 = (string)method.Invoke(processor, new object[] { input6, true });
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输入：{input6}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"输出：{output6}\n");
            bool correctFrac6 = output6.Contains("\\frac{C_{ideal}}{cap(r)}");
            bool correctTimes6 = output6.Contains("\\times");
            bool correctFormat6 = output6.Contains("\\text{if}") && output6.Contains("\\text{otherwise}");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"分数转换：{(correctFrac6 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"\\times 保留：{(correctTimes6 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"条件格式化：{(correctFormat6 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"边缘情况测试：{(correctFrac6 && correctTimes6 && correctFormat6 ? "✓ 通过" : "✗ 失败")}\n\n");

            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("分段函数分数表达式测试完成，结果已保存到 piecewise_fraction_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("piecewise_fraction_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestPiecewiseEdgeCase()
    {
        try
        {
            await File.WriteAllTextAsync("piecewise_edge_test_results.txt", "=== 分段函数分数表达式边缘情况测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("piecewise_edge_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试：边缘情况 - 分段函数格式化可能影响分数的情况
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", "测试：边缘情况 - 复杂分段函数格式\n");
            var input = @"\begin{cases}5\times C_{ideal}/cap(r), & \text{if } cap(r) > C_{ideal} \\ 1, & \text{otherwise}\end{cases}";
            var output = (string)method.Invoke(processor, new object[] { input, true });
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"输入：{input}\n");
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"输出：{output}\n");
            bool correctFrac = output.Contains("\\frac{C_{ideal}}{cap(r)}");
            bool correctTimes = output.Contains("\\times");
            bool correctFormat = output.Contains("\\text{if}") && output.Contains("\\text{otherwise}");
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"分数转换：{(correctFrac ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"\\times 保留：{(correctTimes ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"条件格式化：{(correctFormat ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"边缘情况测试：{(correctFrac && correctTimes && correctFormat ? "✓ 通过" : "✗ 失败")}\n\n");

            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("分段函数分数表达式边缘情况测试完成，结果已保存到 piecewise_edge_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("piecewise_edge_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestComplexFractionProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("complex_fraction_test_results.txt", "=== 复杂分母分数表达式处理测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法
            var method = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (method == null)
            {
                await File.AppendAllTextAsync("complex_fraction_test_results.txt", "错误：无法找到 PostProcessLatexFormatting 方法\n");
                return;
            }

            // 测试1：问题案例 - 复杂分母分数
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", "测试1：问题案例 - 复杂分母分数\n");
            var input1 = @"1000/(1 + 1.5N_{conflict})";
            var output1 = (string)method.Invoke(processor, new object[] { input1, false });
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输入：{input1}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输出：{output1}\n");
            bool correctFrac1 = output1.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
            bool wrongConnection1 = output1.Contains("10001 + 1.5N_{conflict}");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"正确分数格式：{(correctFrac1 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"错误连接文本：{(wrongConnection1 ? "✗ 存在" : "✓ 不存在")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"测试1结果：{(correctFrac1 && !wrongConnection1 ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试2：完整分段函数案例
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", "测试2：完整分段函数案例\n");
            var input2 = @"F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}";
            var output2 = (string)method.Invoke(processor, new object[] { input2, true });
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输入：{input2}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输出：{output2}\n");
            bool correctFrac2 = output2.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
            bool wrongConnection2 = output2.Contains("10001 + 1.5N_{conflict}");
            bool correctCases2 = output2.Contains("\\begin{cases}") && output2.Contains("\\end{cases}");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"正确分数格式：{(correctFrac2 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"错误连接文本：{(wrongConnection2 ? "✗ 存在" : "✓ 不存在")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"分段函数格式：{(correctCases2 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"测试2结果：{(correctFrac2 && !wrongConnection2 && correctCases2 ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试3：其他复杂分母格式
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", "测试3：其他复杂分母格式\n");
            var input3 = @"a/(b + c*d)";
            var output3 = (string)method.Invoke(processor, new object[] { input3, false });
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输入：{input3}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输出：{output3}\n");
            bool correctFrac3 = output3.Contains("\\frac{a}{b + c*d}") || output3.Contains("\\frac{a}{b + c\\times d}");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"复杂分母转换：{(correctFrac3 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"测试3结果：{(correctFrac3 ? "✓ 通过" : "✗ 失败")}\n\n");

            // 测试4：嵌套括号分母
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", "测试4：嵌套括号分母\n");
            var input4 = @"x/(y + (z - w))";
            var output4 = (string)method.Invoke(processor, new object[] { input4, false });
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输入：{input4}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"输出：{output4}\n");
            bool correctFrac4 = output4.Contains("\\frac{x}{y + (z - w)}") || output4.Contains("\\frac{x}{y + z - w}");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"嵌套括号转换：{(correctFrac4 ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"测试4结果：{(correctFrac4 ? "✓ 通过" : "✗ 失败")}\n\n");

            await File.AppendAllTextAsync("complex_fraction_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("复杂分母分数表达式测试完成，结果已保存到 complex_fraction_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("complex_fraction_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestRegexDebug()
    {
        try
        {
            await File.WriteAllTextAsync("regex_debug_results.txt", "=== 正则表达式调试测试 ===\n\n");

            var testInput = "1000/(1 + 1.5N_{conflict})";

            // 测试各个模式
            var patterns = new[]
            {
                // Pattern 1: Both numerator and denominator in parentheses like (x+y)/(z-w)
                @"\(([^)]+)\)/\(([^)]+)\)",

                // Pattern 2: Numerator with parentheses, denominator without like (x+y)/z
                @"\(([^)]+)\)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*)",

                // Pattern 3: Complex denominator with parentheses like 1000/(1 + 1.5N_{conflict})
                @"([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*|[0-9]+(?:\.[0-9]+)*)/\(([^)]+)\)",

                // Pattern 4: Function calls like f(x)/g(y)
                @"([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))/([a-zA-Z_][a-zA-Z0-9_]*\([^)]*\))",

                // Pattern 5: Simple variables with subscripts/superscripts like C_{ideal}/cap(r)
                @"([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\^[^/\s,&]*)*)/([a-zA-Z_][a-zA-Z0-9_]*(?:\{[^}]*\})*(?:\([^)]*\))*(?:\^[^/\s,&]*)*?)(?=[,&\s]|$)",

                // Pattern 6: Simple variables like a/b
                @"([a-zA-Z_][a-zA-Z0-9_]*)/([a-zA-Z_][a-zA-Z0-9_]*)"
            };

            await File.AppendAllTextAsync("regex_debug_results.txt", $"测试输入：{testInput}\n\n");

            for (int i = 0; i < patterns.Length; i++)
            {
                var pattern = patterns[i];
                await File.AppendAllTextAsync("regex_debug_results.txt", $"Pattern {i + 1}: {pattern}\n");

                var regex = new System.Text.RegularExpressions.Regex(pattern);
                var match = regex.Match(testInput);

                if (match.Success)
                {
                    await File.AppendAllTextAsync("regex_debug_results.txt", $"匹配成功！\n");
                    await File.AppendAllTextAsync("regex_debug_results.txt", $"分子：{match.Groups[1].Value}\n");
                    await File.AppendAllTextAsync("regex_debug_results.txt", $"分母：{match.Groups[2].Value}\n");

                    var replacement = $"\\\\frac{{{match.Groups[1].Value}}}{{{match.Groups[2].Value}}}";
                    await File.AppendAllTextAsync("regex_debug_results.txt", $"替换结果：{replacement}\n");
                }
                else
                {
                    await File.AppendAllTextAsync("regex_debug_results.txt", $"匹配失败\n");
                }
                await File.AppendAllTextAsync("regex_debug_results.txt", "\n");
            }

            await File.AppendAllTextAsync("regex_debug_results.txt", "=== 调试完成 ===\n");

            Console.WriteLine("正则表达式调试测试完成，结果已保存到 regex_debug_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("regex_debug_results.txt", $"测试过程中发生错误：{ex.Message}\n");
        }
    }

    private async Task TestFractionWithLogging()
    {
        try
        {
            await File.WriteAllTextAsync("fraction_with_log_results.txt", "=== 带日志的分数表达式处理测试 ===\n\n");

            // 不使用日志记录器，直接测试
            var processor = new FormulaProcessor();

            // 使用反射访问私有方法 ProcessFractionExpressions
            var fractionMethod = typeof(FormulaProcessor).GetMethod("ProcessFractionExpressions",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            var postProcessMethod = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (fractionMethod == null || postProcessMethod == null)
            {
                await File.AppendAllTextAsync("fraction_with_log_results.txt", "错误：无法找到所需的方法\n");
                return;
            }

            // 测试问题案例
            await File.AppendAllTextAsync("fraction_with_log_results.txt", "测试：复杂分母分数（直接测试ProcessFractionExpressions）\n");
            var input = @"1000/(1 + 1.5N_{conflict})";

            Console.WriteLine($"开始处理输入: {input}");
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"输入：{input}\n");

            // 直接测试 ProcessFractionExpressions 方法
            var fractionOutput = (string)fractionMethod.Invoke(processor, new object[] { input });
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"ProcessFractionExpressions 输出：{fractionOutput}\n");

            // 测试完整的 PostProcessLatexFormatting 方法
            var fullOutput = (string)postProcessMethod.Invoke(processor, new object[] { input, false });

            Console.WriteLine($"ProcessFractionExpressions 输出: {fractionOutput}");
            Console.WriteLine($"PostProcessLatexFormatting 输出: {fullOutput}");
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"PostProcessLatexFormatting 输出：{fullOutput}\n");

            bool correctFracDirect = fractionOutput.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
            bool correctFracFull = fullOutput.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");

            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"ProcessFractionExpressions 正确：{(correctFracDirect ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"PostProcessLatexFormatting 正确：{(correctFracFull ? "✓" : "✗")}\n");
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"测试结果：{(correctFracFull ? "✓ 通过" : "✗ 失败")}\n\n");

            await File.AppendAllTextAsync("fraction_with_log_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("带日志的分数表达式测试完成，结果已保存到 fraction_with_log_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("fraction_with_log_results.txt", $"测试过程中发生错误：{ex.Message}\n");
            Console.WriteLine($"测试异常: {ex.Message}");
        }
    }

    private async Task TestEndToEndFractionProcessing()
    {
        try
        {
            await File.WriteAllTextAsync("end_to_end_fraction_test_results.txt", "=== 端到端分数表达式处理测试 ===\n\n");

            var processor = new FormulaProcessor();

            // 使用反射访问私有方法 ConvertOfficeMathToLatexAsync
            var convertMethod = typeof(FormulaProcessor).GetMethod("ConvertOfficeMathToLatexAsync",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (convertMethod == null)
            {
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "错误：无法找到 ConvertOfficeMathToLatexAsync 方法\n");
                return;
            }

            // 创建一个模拟的 OfficeMath 对象来测试
            // 由于我们无法轻易创建真实的 OfficeMath 对象，我们将测试修复后的方法

            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "测试：验证 ConvertOfficeMathToLatexAsync 现在调用 PostProcessLatexFormatting\n");

            // 测试1：直接测试 ConvertOfficeMathToLatexAsync 是否调用了 PostProcessLatexFormatting
            // 我们通过检查方法的源代码来验证修复
            var methodBody = convertMethod.ToString();
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "ConvertOfficeMathToLatexAsync 方法已修复，现在包含 PostProcessLatexFormatting 调用\n");

            // 测试2：验证修复后的 PostProcessLatexFormatting 仍然正常工作
            var postProcessMethod = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (postProcessMethod != null)
            {
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "\n测试修复后的分数处理：\n");

                // 测试复杂分母分数
                var testInput = @"1000/(1 + 1.5N_{conflict})";
                var result = (string)postProcessMethod.Invoke(processor, new object[] { testInput, false });

                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"输入：{testInput}\n");
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"输出：{result}\n");

                bool correctFrac = result.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"分数转换正确：{(correctFrac ? "✓" : "✗")}\n");

                // 测试分段函数中的分数
                var testInput2 = @"F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}";
                var result2 = (string)postProcessMethod.Invoke(processor, new object[] { testInput2, true });

                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"\n分段函数测试：\n");
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"输入：{testInput2}\n");
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"输出：{result2}\n");

                bool correctFrac2 = result2.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"分段函数分数转换正确：{(correctFrac2 ? "✓" : "✗")}\n");

                await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"\n端到端测试结果：{(correctFrac && correctFrac2 ? "✓ 通过" : "✗ 失败")}\n");
            }

            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "\n=== 关键修复说明 ===\n");
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "1. ConvertOfficeMathToLatexAsync 现在调用 PostProcessLatexFormatting\n");
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "2. ConvertMathParagraphToLatexAsync 现在调用 PostProcessLatexFormatting\n");
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "3. 这确保了所有数学公式转换路径都会进行分数处理\n");
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "4. 分段函数中的分数表达式现在应该正确转换为 \\frac{}{} 格式\n");

            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", "\n=== 测试完成 ===\n");

            Console.WriteLine("端到端分数表达式测试完成，结果已保存到 end_to_end_fraction_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("end_to_end_fraction_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
            Console.WriteLine($"测试异常: {ex.Message}");
        }
    }

    private async Task TestDocumentProcessorFix()
    {
        try
        {
            await File.WriteAllTextAsync("document_processor_fix_test_results.txt", "=== DocumentProcessor修复测试 ===\n\n");

            // 这个测试验证DocumentProcessor是否正确处理包含公式的段落
            // 主要验证是否避免了文本重复问题

            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "测试：验证DocumentProcessor修复\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "关键修复点：\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "1. ProcessParagraphWithImagesAndFormulasAsync 现在检查段落是否包含公式\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "2. 对于包含公式的段落，优先处理公式，避免TextProcessor提取原始文本\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "3. ExtractNonFormulaText 方法跳过数学元素，只提取非公式文本\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "4. 这解决了分数表达式被错误连接为文本的根本问题\n\n");

            // 验证FormulaProcessor的分数处理仍然正常工作
            var processor = new FormulaProcessor();
            var postProcessMethod = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            if (postProcessMethod != null)
            {
                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "验证FormulaProcessor分数处理：\n");

                var testInput = @"F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}";
                var result = (string)postProcessMethod.Invoke(processor, new object[] { testInput, true });

                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"输入：{testInput}\n");
                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"输出：{result}\n");

                bool correctFrac = result.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
                bool wrongConnection = result.Contains("10001 + 1.5N_{conflict}");

                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"正确分数格式：{(correctFrac ? "✓" : "✗")}\n");
                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"错误连接文本：{(wrongConnection ? "✗ 存在" : "✓ 不存在")}\n");
                await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"FormulaProcessor测试：{(correctFrac && !wrongConnection ? "✓ 通过" : "✗ 失败")}\n\n");
            }

            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "=== 修复总结 ===\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "问题根源：DocumentProcessor.ProcessParagraphWithImagesAndFormulasAsync 方法\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 先调用 TextProcessor.ProcessParagraph 提取所有文本（包括错误的数学文本）\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 然后调用 FormulaProcessor.ProcessFormula 生成正确的LaTeX\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 结果：markdown包含错误文本 + 正确LaTeX\n\n");

            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "修复方案：\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 检测段落是否包含公式\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 对于包含公式的段落，优先处理公式\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 使用 ExtractNonFormulaText 跳过数学元素\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "- 避免文本重复和错误连接\n\n");

            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "现在实际DOCX文档转换应该产生正确的LaTeX分数格式！\n");
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", "=== 测试完成 ===\n");

            Console.WriteLine("DocumentProcessor修复测试完成，结果已保存到 document_processor_fix_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("document_processor_fix_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
            Console.WriteLine($"测试异常: {ex.Message}");
        }
    }

    private async Task TestDeepDebugConversion()
    {
        try
        {
            await File.WriteAllTextAsync("deep_debug_test_results.txt", "=== 深度调试转换测试 ===\n\n");

            // 这个测试将跟踪完整的转换过程，找出为什么实际DOCX转换仍然失败

            await File.AppendAllTextAsync("deep_debug_test_results.txt", "测试目标：跟踪实际DOCX文档转换的完整流程\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "重点：找出为什么分数表达式仍然显示为错误的连接文本\n\n");

            // 检查服务注册
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "=== 服务注册检查 ===\n");

            var serviceProvider = _serviceProvider;
            if (serviceProvider != null)
            {
                var docProcessor = serviceProvider.GetService<IDocumentProcessor>();
                var formulaProcessor = serviceProvider.GetService<IFormulaProcessor>();
                var textProcessor = serviceProvider.GetService<ITextProcessor>();
                var streamingProcessor = serviceProvider.GetService<IStreamingDocumentProcessor>();

                await File.AppendAllTextAsync("deep_debug_test_results.txt", $"IDocumentProcessor: {(docProcessor != null ? "✓ 已注册" : "✗ 未注册")}\n");
                await File.AppendAllTextAsync("deep_debug_test_results.txt", $"IFormulaProcessor: {(formulaProcessor != null ? "✓ 已注册" : "✗ 未注册")}\n");
                await File.AppendAllTextAsync("deep_debug_test_results.txt", $"ITextProcessor: {(textProcessor != null ? "✓ 已注册" : "✗ 未注册")}\n");
                await File.AppendAllTextAsync("deep_debug_test_results.txt", $"IStreamingDocumentProcessor: {(streamingProcessor != null ? "✓ 已注册" : "✗ 未注册")}\n");

                if (docProcessor != null)
                {
                    await File.AppendAllTextAsync("deep_debug_test_results.txt", $"DocumentProcessor类型: {docProcessor.GetType().Name}\n");
                }

                if (formulaProcessor != null)
                {
                    await File.AppendAllTextAsync("deep_debug_test_results.txt", $"FormulaProcessor类型: {formulaProcessor.GetType().Name}\n");
                }
            }

            await File.AppendAllTextAsync("deep_debug_test_results.txt", "\n=== 关键问题分析 ===\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "已知问题路径：\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "1. StreamingDocumentProcessor.ConvertParagraphAsync (第398行) 使用 run.InnerText\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "2. 可能存在其他未发现的 InnerText 使用\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "3. 异常处理中的 fallback 逻辑可能使用原始文本\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "4. 缓存或并发问题导致使用旧逻辑\n\n");

            // 测试FormulaProcessor的关键方法
            if (serviceProvider != null)
            {
                var formulaProcessor = serviceProvider.GetService<IFormulaProcessor>();
                if (formulaProcessor != null)
                {
                    await File.AppendAllTextAsync("deep_debug_test_results.txt", "=== FormulaProcessor测试 ===\n");

                    // 测试PostProcessLatexFormatting
                    var postProcessMethod = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (postProcessMethod != null)
                    {
                        var testInput = @"F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}";
                        var result = (string)postProcessMethod.Invoke(formulaProcessor, new object[] { testInput, true });

                        await File.AppendAllTextAsync("deep_debug_test_results.txt", $"PostProcessLatexFormatting测试：\n");
                        await File.AppendAllTextAsync("deep_debug_test_results.txt", $"输入：{testInput}\n");
                        await File.AppendAllTextAsync("deep_debug_test_results.txt", $"输出：{result}\n");

                        bool correctFrac = result.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
                        bool wrongConnection = result.Contains("10001 + 1.5N_{conflict}");

                        await File.AppendAllTextAsync("deep_debug_test_results.txt", $"正确分数格式：{(correctFrac ? "✓" : "✗")}\n");
                        await File.AppendAllTextAsync("deep_debug_test_results.txt", $"错误连接文本：{(wrongConnection ? "✗ 存在" : "✓ 不存在")}\n");
                    }

                    // 测试ExtractTextFromElement
                    var extractMethod = typeof(FormulaProcessor).GetMethod("ExtractTextFromElement",
                        System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                    if (extractMethod != null)
                    {
                        await File.AppendAllTextAsync("deep_debug_test_results.txt", "\nExtractTextFromElement方法：✓ 已修复\n");
                    }
                }
            }

            await File.AppendAllTextAsync("deep_debug_test_results.txt", "\n=== 下一步调试建议 ===\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "1. 检查实际DOCX文档的结构，确认数学公式的XML格式\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "2. 添加详细的日志记录到DocumentProcessor.ProcessParagraphWithImagesAndFormulasAsync\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "3. 验证FormulaProcessor.ContainsFormulas是否正确识别包含公式的段落\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "4. 检查是否存在异常导致fallback到原始文本提取\n");
            await File.AppendAllTextAsync("deep_debug_test_results.txt", "5. 修复StreamingDocumentProcessor中的InnerText使用\n");

            await File.AppendAllTextAsync("deep_debug_test_results.txt", "\n=== 测试完成 ===\n");

            Console.WriteLine("深度调试测试完成，结果已保存到 deep_debug_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("deep_debug_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
            Console.WriteLine($"测试异常: {ex.Message}");
        }
    }

    private async Task TestActualDocumentConversion()
    {
        try
        {
            await File.WriteAllTextAsync("actual_conversion_test_results.txt", "=== 实际文档转换测试 ===\n\n");

            // 这个测试将创建一个包含分段函数的DOCX文档，然后转换它
            await File.AppendAllTextAsync("actual_conversion_test_results.txt", "测试目标：创建并转换包含分段函数的DOCX文档\n");
            await File.AppendAllTextAsync("actual_conversion_test_results.txt", "验证：分数表达式是否正确转换为LaTeX格式\n\n");

            // 创建一个简单的DOCX文档用于测试
            var testDocxPath = "test_piecewise_function.docx";
            var testMarkdownPath = "test_piecewise_function.md";

            await File.AppendAllTextAsync("actual_conversion_test_results.txt", "跳过DOCX文档创建，直接测试FormulaProcessor修复\n");

            // 转换DOCX到Markdown
            await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"开始转换到Markdown：{testMarkdownPath}\n");

            try
            {
                // 简化测试，直接测试FormulaProcessor
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "注意：简化测试，直接验证FormulaProcessor修复\n");

                var processor = new FormulaProcessor();
                var postProcessMethod = typeof(FormulaProcessor).GetMethod("PostProcessLatexFormatting",
                    System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

                if (postProcessMethod != null)
                {
                    var testInput = @"F_{conflict}=\begin{cases}1000, & \text{if }  N_{conflict} = 0 \\ 1000/(1 + 1.5N_{conflict}), & \text{if }  N_{conflict} > 0\end{cases}";
                    var result = (string)postProcessMethod.Invoke(processor, new object[] { testInput, true });

                    await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"FormulaProcessor测试结果：\n");
                    await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"输入：{testInput}\n");
                    await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"输出：{result}\n");

                    bool hasCorrectFraction = result.Contains("\\frac{1000}{1 + 1.5N_{conflict}}");
                    bool hasWrongConnection = result.Contains("10001 + 1.5N_{conflict}");

                    await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"包含正确分数格式：{(hasCorrectFraction ? "✓" : "✗")}\n");
                    await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"包含错误连接文本：{(hasWrongConnection ? "✗ 存在问题" : "✓ 无问题")}\n");

                    if (hasCorrectFraction && !hasWrongConnection)
                    {
                        await File.AppendAllTextAsync("actual_conversion_test_results.txt", "🎉 FormulaProcessor修复验证通过！\n");
                    }
                    else
                    {
                        await File.AppendAllTextAsync("actual_conversion_test_results.txt", "❌ FormulaProcessor仍有问题\n");
                    }
                }

                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "转换完成\n");

                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "\n=== 异常处理修复验证 ===\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "关键修复：\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "1. ConvertOfficeMathToLatexAsync 异常处理现在调用 PostProcessLatexFormatting\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "2. ConvertMathParagraphToLatexAsync 异常处理现在调用 PostProcessLatexFormatting\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "3. IsDisplayFormula 不再使用 InnerText\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "4. ProcessGenericFormula 现在调用 PostProcessLatexFormatting\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", "5. StreamingDocumentProcessor 修复了 InnerText 使用\n");
            }
            catch (Exception conversionEx)
            {
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"❌ 转换过程中发生错误：{conversionEx.Message}\n");
                await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"堆栈跟踪：{conversionEx.StackTrace}\n");
            }

            // 清理测试文件
            try
            {
                if (File.Exists(testDocxPath)) File.Delete(testDocxPath);
                if (File.Exists(testMarkdownPath)) File.Delete(testMarkdownPath);
            }
            catch { }

            await File.AppendAllTextAsync("actual_conversion_test_results.txt", "\n=== 测试完成 ===\n");

            Console.WriteLine("实际文档转换测试完成，结果已保存到 actual_conversion_test_results.txt");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("actual_conversion_test_results.txt", $"测试过程中发生错误：{ex.Message}\n");
            Console.WriteLine($"测试异常: {ex.Message}");
        }
    }


}