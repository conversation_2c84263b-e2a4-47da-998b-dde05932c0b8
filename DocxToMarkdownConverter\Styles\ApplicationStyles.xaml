<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes">

    <!-- Enhanced Navigation Button Styles -->
    <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource SmartNavigationButtonStyle}">
        <Setter Property="HorizontalAlignment" Value="Stretch"/>
        <Setter Property="HorizontalContentAlignment" Value="Left"/>
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="4,2"/>
        <Setter Property="Height" Value="48"/>
        <Setter Property="Background" Value="{DynamicResource NavigationItemDefaultBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource NavigationTextDefaultBrush}"/>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource NavigationItemHoverBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource NavigationTextHoverBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Enhanced Active Navigation Button Style -->
    <Style x:Key="ActiveNavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource NavigationButtonStyle}">
        <Setter Property="Background" Value="{DynamicResource NavigationItemSelectedBrush}"/>
        <Setter Property="Foreground" Value="{DynamicResource NavigationTextSelectedBrush}"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{DynamicResource NavigationItemActiveBrush}"/>
                <Setter Property="Foreground" Value="{DynamicResource NavigationTextSelectedBrush}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- 优化的动画主按钮样式 - 性能优化版 -->
    <Style x:Key="AnimatedPrimaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <!-- 移除阴影效果动画以提升性能 -->
        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <!-- 减少缩放幅度和动画持续时间 -->
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1.02" Duration="0:0:0.1">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1.02" Duration="0:0:0.1">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <!-- 快速恢复动画 -->
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1" Duration="0:0:0.12">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1" Duration="0:0:0.12">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- 优化的动画次要按钮样式 - 性能优化版 -->
    <Style x:Key="AnimatedSecondaryButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignOutlinedButton}">
        <Setter Property="RenderTransformOrigin" Value="0.5,0.5"/>
        <Setter Property="RenderTransform">
            <Setter.Value>
                <ScaleTransform ScaleX="1" ScaleY="1"/>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <EventTrigger RoutedEvent="MouseEnter">
                <BeginStoryboard>
                    <Storyboard>
                        <!-- 减少缩放幅度和动画持续时间 -->
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1.01" Duration="0:0:0.1">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1.01" Duration="0:0:0.1">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
            <EventTrigger RoutedEvent="MouseLeave">
                <BeginStoryboard>
                    <Storyboard>
                        <!-- 快速恢复动画 -->
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                                         To="1" Duration="0:0:0.12">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                                         To="1" Duration="0:0:0.12">
                            <DoubleAnimation.EasingFunction>
                                <QuadraticEase EasingMode="EaseOut"/>
                            </DoubleAnimation.EasingFunction>
                        </DoubleAnimation>
                    </Storyboard>
                </BeginStoryboard>
            </EventTrigger>
        </Style.Triggers>
    </Style>

    <!-- Custom Card Style -->
    <Style x:Key="ContentCardStyle" TargetType="Border">
        <Setter Property="Background" Value="{DynamicResource MaterialDesignCardBackground}"/>
        <Setter Property="CornerRadius" Value="8"/>
        <Setter Property="Padding" Value="24"/>
        <Setter Property="Margin" Value="8"/>
        <Setter Property="Effect">
            <Setter.Value>
                <DropShadowEffect Color="Black" Opacity="0.1" ShadowDepth="2" BlurRadius="8"/>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Unified Settings Card Style -->
    <Style x:Key="SettingsCardStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource AppCardBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp2"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth2"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="materialDesign:Card">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="{DynamicResource MaterialDesignShadow}"
                                              Opacity="0.15"
                                              ShadowDepth="4"
                                              BlurRadius="12"
                                              Direction="270"/>
                        </Border.Effect>
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <!-- Hover effect for better interactivity -->
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Settings Card Header Text Style -->
    <Style x:Key="SettingsCardHeaderStyle" TargetType="TextBlock" BasedOn="{StaticResource MaterialDesignHeadline6TextBlock}">
        <Setter Property="Foreground" Value="{DynamicResource AppTextPrimaryBrush}"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="FontWeight" Value="SemiBold"/>
    </Style>

    <!-- Setting Group Card Style -->
    <Style x:Key="SettingGroupStyle" TargetType="materialDesign:Card">
        <Setter Property="Background" Value="{DynamicResource AppCardBrush}"/>
        <Setter Property="BorderBrush" Value="{DynamicResource AppBorderBrush}"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Margin" Value="0,0,0,16"/>
        <Setter Property="Padding" Value="20"/>
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
        <Setter Property="materialDesign:ShadowAssist.ShadowDepth" Value="Depth1"/>
        <Setter Property="ScrollViewer.CanContentScroll" Value="False"/>
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality"/>
        <Setter Property="RenderOptions.EdgeMode" Value="Aliased"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="materialDesign:Card">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12"
                            Padding="{TemplateBinding Padding}">
                        <Border.Effect>
                            <DropShadowEffect Color="{DynamicResource MaterialDesignShadow}"
                                              Opacity="0.1"
                                              ShadowDepth="2"
                                              BlurRadius="8"
                                              Direction="270"/>
                        </Border.Effect>
                        <ContentPresenter/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="BorderBrush" Value="{DynamicResource PrimaryHueMidBrush}"/>
                <Setter Property="BorderThickness" Value="2"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Optimized ScrollViewer Style for Settings -->
    <Style x:Key="SmoothScrollViewerStyle" TargetType="ScrollViewer">
        <Setter Property="CanContentScroll" Value="False"/>
        <Setter Property="PanningMode" Value="VerticalOnly"/>
        <Setter Property="ScrollViewer.IsDeferredScrollingEnabled" Value="False"/>
        <Setter Property="HorizontalScrollBarVisibility" Value="Disabled"/>
        <Setter Property="VerticalScrollBarVisibility" Value="Auto"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ScrollViewer">
                    <Grid x:Name="Grid" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <Rectangle x:Name="Corner"
                                   Grid.Column="1"
                                   Grid.Row="1"
                                   Fill="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"/>

                        <ScrollContentPresenter x:Name="PART_ScrollContentPresenter"
                                                Grid.Column="0"
                                                Grid.Row="0"
                                                CanContentScroll="{TemplateBinding CanContentScroll}"
                                                CanHorizontallyScroll="False"
                                                CanVerticallyScroll="False"
                                                ContentTemplate="{TemplateBinding ContentTemplate}"
                                                Content="{TemplateBinding Content}"
                                                Margin="{TemplateBinding Padding}"/>

                        <ScrollBar x:Name="PART_VerticalScrollBar"
                                   Grid.Column="1"
                                   Grid.Row="0"
                                   AutomationProperties.AutomationId="VerticalScrollBar"
                                   Cursor="Arrow"
                                   Maximum="{TemplateBinding ScrollableHeight}"
                                   Minimum="0"
                                   Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}"
                                   Value="{Binding VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                   ViewportSize="{TemplateBinding ViewportHeight}"
                                   Style="{DynamicResource MaterialDesignScrollBarStyle}"/>

                        <ScrollBar x:Name="PART_HorizontalScrollBar"
                                   Grid.Column="0"
                                   Grid.Row="1"
                                   AutomationProperties.AutomationId="HorizontalScrollBar"
                                   Cursor="Arrow"
                                   Maximum="{TemplateBinding ScrollableWidth}"
                                   Minimum="0"
                                   Orientation="Horizontal"
                                   Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}"
                                   Value="{Binding HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}"
                                   ViewportSize="{TemplateBinding ViewportWidth}"
                                   Style="{DynamicResource MaterialDesignScrollBarStyle}"/>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Style.Triggers>
            <!-- Smooth scrolling behavior -->
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="ScrollViewer.PanningRatio" Value="1"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Performance-optimized StackPanel for Settings -->
    <Style x:Key="OptimizedSettingsStackPanel" TargetType="StackPanel">
        <Setter Property="VirtualizingPanel.IsVirtualizing" Value="False"/>
        <Setter Property="VirtualizingPanel.VirtualizationMode" Value="Standard"/>
        <Setter Property="VirtualizingPanel.ScrollUnit" Value="Pixel"/>
        <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality"/>
        <Setter Property="RenderOptions.EdgeMode" Value="Aliased"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
    </Style>

    <!-- Optimized Card Style for better scrolling -->
    <Style x:Key="OptimizedSettingsCardStyle" TargetType="materialDesign:Card" BasedOn="{StaticResource SettingsCardStyle}">
        <Setter Property="RenderOptions.BitmapScalingMode" Value="HighQuality"/>
        <Setter Property="RenderOptions.EdgeMode" Value="Aliased"/>
        <Setter Property="UseLayoutRounding" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="RenderOptions.ClearTypeHint" Value="Enabled"/>
        <!-- Reduce shadow complexity for better performance -->
        <Setter Property="materialDesign:ElevationAssist.Elevation" Value="Dp1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="materialDesign:Card">
                    <Border Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="12"
                            Padding="{TemplateBinding Padding}"
                            UseLayoutRounding="True"
                            SnapsToDevicePixels="True">
                        <Border.Effect>
                            <!-- Simplified shadow for better performance -->
                            <DropShadowEffect Color="{DynamicResource MaterialDesignShadow}"
                                              Opacity="0.12"
                                              ShadowDepth="2"
                                              BlurRadius="8"
                                              Direction="270"
                                              RenderingBias="Performance"/>
                        </Border.Effect>
                        <ContentPresenter UseLayoutRounding="True" SnapsToDevicePixels="True"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!-- Progress Bar Style -->
    <Style x:Key="ModernProgressBarStyle" TargetType="ProgressBar" BasedOn="{StaticResource MaterialDesignLinearProgressBar}">
        <Setter Property="Height" Value="8"/>
        <Setter Property="Margin" Value="0,8"/>
    </Style>

    <!-- File List Item Style -->
    <Style x:Key="FileListItemStyle" TargetType="ListBoxItem" BasedOn="{StaticResource MaterialDesignListBoxItem}">
        <Setter Property="Padding" Value="16,12"/>
        <Setter Property="Margin" Value="0,2"/>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!-- Animation Resources -->
    <Storyboard x:Key="FadeInAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="FadeOutAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="1" To="0" Duration="0:0:0.25">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseIn"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="SlideInFromRightAnimation">
        <ThicknessAnimation Storyboard.TargetProperty="Margin"
                            From="50,0,0,0" To="0,0,0,0" Duration="0:0:0.4">
            <ThicknessAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </ThicknessAnimation.EasingFunction>
        </ThicknessAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="SlideInFromLeftAnimation">
        <ThicknessAnimation Storyboard.TargetProperty="Margin"
                            From="-50,0,0,0" To="0,0,0,0" Duration="0:0:0.4">
            <ThicknessAnimation.EasingFunction>
                <BackEase EasingMode="EaseOut" Amplitude="0.3"/>
            </ThicknessAnimation.EasingFunction>
        </ThicknessAnimation>
        <DoubleAnimation Storyboard.TargetProperty="Opacity"
                         From="0" To="1" Duration="0:0:0.4">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="PulseAnimation">
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleX)"
                         From="1" To="1.1" Duration="0:0:0.2" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
        <DoubleAnimation Storyboard.TargetProperty="(UIElement.RenderTransform).(ScaleTransform.ScaleY)"
                         From="1" To="1.1" Duration="0:0:0.2" AutoReverse="True">
            <DoubleAnimation.EasingFunction>
                <SineEase EasingMode="EaseInOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

    <Storyboard x:Key="ProgressBarAnimation">
        <DoubleAnimation Storyboard.TargetProperty="Value"
                         Duration="0:0:0.6">
            <DoubleAnimation.EasingFunction>
                <CubicEase EasingMode="EaseOut"/>
            </DoubleAnimation.EasingFunction>
        </DoubleAnimation>
    </Storyboard>

</ResourceDictionary>