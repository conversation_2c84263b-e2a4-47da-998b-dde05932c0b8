# Product Overview

This repository contains multiple document processing and AI-assisted tools focused on academic and professional document workflows:

## Core Products

### 1. DOCX to Markdown Converter (C# WPF)
- **Purpose**: Modern Windows desktop application for converting DOCX files to Markdown format
- **Target Users**: Researchers, writers, and content creators who need to convert Word documents to Markdown
- **Key Features**: Batch conversion, drag & drop support, Material Design UI, real-time progress tracking

### 2. DOCX to Markdown Converter (Python GUI)
- **Purpose**: Cross-platform Python-based converter with advanced GUI and animation system
- **Target Users**: Technical users who prefer Python-based tools with customizable interfaces
- **Key Features**: 60FPS animation system, theme switching, batch processing, formula conversion

### 3. AI Detection Assistant
- **Purpose**: Web-based AI content detection and optimization tool for academic writing
- **Target Users**: Academic researchers, students, and professional writers
- **Key Features**: Local LLM integration (Ollama), academic text optimization, multi-round improvement

## Academic Focus
All tools are specifically designed for academic and professional document processing, with emphasis on:
- Maintaining document structure and formatting integrity
- Supporting academic citation formats
- Preserving mathematical formulas and technical content
- Ensuring high-quality output suitable for publication

## Technology Stack
- **Desktop**: C# WPF (.NET 8), Python with Tkinter
- **Web**: Vanilla JavaScript, HTML5/CSS3
- **AI Integration**: Ollama local models (Qwen, Llama series)
- **Document Processing**: OpenXML, python-docx libraries