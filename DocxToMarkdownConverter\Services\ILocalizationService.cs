using System.ComponentModel;
using System.Globalization;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 本地化服务接口
/// </summary>
public interface ILocalizationService : INotifyPropertyChanged
{
    /// <summary>
    /// 当前语言文化信息
    /// </summary>
    CultureInfo CurrentCulture { get; }

    /// <summary>
    /// 当前语言代码
    /// </summary>
    string CurrentLanguage { get; }

    /// <summary>
    /// 支持的语言列表
    /// </summary>
    IReadOnlyList<LanguageInfo> SupportedLanguages { get; }

    /// <summary>
    /// 是否跟随系统语言
    /// </summary>
    bool FollowSystemLanguage { get; set; }

    /// <summary>
    /// 语言变更事件
    /// </summary>
    event EventHandler<LanguageChangedEventArgs>? LanguageChanged;

    /// <summary>
    /// 获取本地化字符串
    /// </summary>
    /// <param name="key">资源键</param>
    /// <param name="defaultValue">默认值</param>
    /// <returns>本地化字符串</returns>
    string GetString(string key, string? defaultValue = null);

    /// <summary>
    /// 获取格式化的本地化字符串
    /// </summary>
    /// <param name="key">资源键</param>
    /// <param name="args">格式化参数</param>
    /// <returns>格式化的本地化字符串</returns>
    string GetFormattedString(string key, params object[] args);

    /// <summary>
    /// 设置当前语言
    /// </summary>
    /// <param name="languageCode">语言代码</param>
    /// <returns>是否设置成功</returns>
    Task<bool> SetLanguageAsync(string languageCode);

    /// <summary>
    /// 重新加载资源
    /// </summary>
    Task ReloadResourcesAsync();

    /// <summary>
    /// 获取系统语言
    /// </summary>
    /// <returns>系统语言代码</returns>
    string GetSystemLanguage();
}

/// <summary>
/// 语言信息
/// </summary>
public class LanguageInfo
{
    public string Code { get; set; } = string.Empty;
    public string Name { get; set; } = string.Empty;
    public string NativeName { get; set; } = string.Empty;
    public CultureInfo Culture { get; set; } = CultureInfo.InvariantCulture;
}

/// <summary>
/// 语言变更事件参数
/// </summary>
public class LanguageChangedEventArgs : EventArgs
{
    public string OldLanguage { get; }
    public string NewLanguage { get; }
    public CultureInfo OldCulture { get; }
    public CultureInfo NewCulture { get; }

    public LanguageChangedEventArgs(string oldLanguage, string newLanguage, 
        CultureInfo oldCulture, CultureInfo newCulture)
    {
        OldLanguage = oldLanguage;
        NewLanguage = newLanguage;
        OldCulture = oldCulture;
        NewCulture = newCulture;
    }
}
