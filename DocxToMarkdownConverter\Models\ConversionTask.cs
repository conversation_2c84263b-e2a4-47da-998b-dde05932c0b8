namespace DocxToMarkdownConverter.Models;

public class ConversionTask
{
    public string InputPath { get; set; } = string.Empty;
    public string OutputPath { get; set; } = string.Empty;
    public ConversionOptions Options { get; set; } = new();
    public int Priority { get; set; } = 0;
    public DateTime CreatedAt { get; set; } = DateTime.Now;
    
    public ConversionTask()
    {
    }
    
    public ConversionTask(string inputPath, string outputPath, ConversionOptions options)
    {
        InputPath = inputPath ?? throw new ArgumentNullException(nameof(inputPath));
        OutputPath = outputPath ?? throw new ArgumentNullException(nameof(outputPath));
        Options = options ?? throw new ArgumentNullException(nameof(options));
    }
}