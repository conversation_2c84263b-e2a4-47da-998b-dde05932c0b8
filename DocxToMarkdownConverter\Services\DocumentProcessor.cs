using DocumentFormat.OpenXml.Packaging;
using DocxToMarkdownConverter.Models;
using System.Text;
using DocumentFormat.OpenXml.Wordprocessing;
using Microsoft.Extensions.Logging;
using System.IO;

namespace DocxToMarkdownConverter.Services;

public class DocumentProcessor : IDocumentProcessor
{
    private readonly ITextProcessor _textProcessor;
    private readonly IImageProcessor _imageProcessor;
    private readonly ITableProcessor _tableProcessor;
    private readonly IFormulaProcessor _formulaProcessor;
    private readonly ILogger<DocumentProcessor> _logger;

    public DocumentProcessor(
        ITextProcessor textProcessor, 
        IImageProcessor imageProcessor,
        ITableProcessor tableProcessor,
        IFormulaProcessor formulaProcessor,
        ILogger<DocumentProcessor> logger)
    {
        _textProcessor = textProcessor ?? throw new ArgumentNullException(nameof(textProcessor));
        _imageProcessor = imageProcessor ?? throw new ArgumentNullException(nameof(imageProcessor));
        _tableProcessor = tableProcessor ?? throw new ArgumentNullException(nameof(tableProcessor));
        _formulaProcessor = formulaProcessor ?? throw new ArgumentNullException(nameof(formulaProcessor));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task<string> ProcessDocumentAsync(WordprocessingDocument document, ConversionOptions options)
    {
        var processingId = Guid.NewGuid().ToString("N")[..8];
        await LogProcessingStep(processingId, "DOCUMENT_PROCESSING_START", "Starting document processing");

        if (document.MainDocumentPart?.Document?.Body == null)
        {
            await LogProcessingStep(processingId, "DOCUMENT_PROCESSING_FAILED", "Document body is null or empty");
            return string.Empty;
        }

        var markdownBuilder = new StringBuilder();
        var body = document.MainDocumentPart.Document.Body;
        var elementCount = body.Elements().Count();

        await LogProcessingStep(processingId, "DOCUMENT_ANALYSIS", $"Document body contains {elementCount} elements");

        // Extract images first if enabled
        if (options.ExtractImages)
        {
            await LogProcessingStep(processingId, "IMAGE_EXTRACTION_START", "Starting image extraction");
            try
            {
                var imageDirectory = Path.Combine(options.OutputDirectory, options.ImageDirectory);
                await _imageProcessor.ExtractImagesAsync(document, options.OutputDirectory);
                await LogProcessingStep(processingId, "IMAGE_EXTRACTION_SUCCESS", $"Images extracted to: {imageDirectory}");
                _logger.LogDebug("Images extracted to: {ImageDirectory}", imageDirectory);
            }
            catch (Exception ex)
            {
                await LogProcessingStep(processingId, "IMAGE_EXTRACTION_FAILED", $"Failed to extract images: {ex.Message}");
                _logger.LogError(ex, "Failed to extract images during document processing");
            }
        }
        else
        {
            await LogProcessingStep(processingId, "IMAGE_EXTRACTION_SKIPPED", "Image extraction disabled");
        }

        // Process each element in the document body
        var processedElements = 0;
        var skippedElements = 0;
        var errorElements = 0;

        await LogProcessingStep(processingId, "ELEMENT_PROCESSING_START", $"Processing {elementCount} document elements");

        var elementIndex = 0;
        foreach (var element in body.Elements())
        {
            elementIndex++;
            try
            {
                var elementType = element.GetType().Name;
                await LogProcessingStep(processingId, "ELEMENT_START", $"Processing element {elementIndex}/{elementCount}: {elementType}");

                var processedContent = await ProcessElementAsync(element, options, document);

                if (!string.IsNullOrEmpty(processedContent))
                {
                    // Add proper spacing for different element types
                    if (elementType == "Paragraph")
                    {
                        // Check if this is a heading or special paragraph
                        if (processedContent.StartsWith("#"))
                        {
                            // Heading - add extra line before and after
                            if (markdownBuilder.Length > 0)
                                markdownBuilder.AppendLine();
                            markdownBuilder.AppendLine(processedContent);
                            markdownBuilder.AppendLine();
                        }
                        else
                        {
                            // Regular paragraph - add single line
                            markdownBuilder.AppendLine(processedContent);
                            markdownBuilder.AppendLine(); // Add blank line after paragraph
                        }
                    }
                    else if (elementType == "Table")
                    {
                        // Table - add extra spacing
                        if (markdownBuilder.Length > 0)
                            markdownBuilder.AppendLine();
                        markdownBuilder.AppendLine(processedContent);
                        markdownBuilder.AppendLine();
                    }
                    else
                    {
                        // Other elements
                        markdownBuilder.AppendLine(processedContent);
                    }

                    processedElements++;
                    await LogProcessingStep(processingId, "ELEMENT_PROCESSED", $"Processed {elementType}: {processedContent.Length} chars");
                }
                else
                {
                    skippedElements++;
                    await LogProcessingStep(processingId, "ELEMENT_SKIPPED", $"Skipped {elementType} (empty result)");
                }
            }
            catch (Exception ex)
            {
                errorElements++;
                var elementType = element?.GetType().Name ?? "Unknown";
                await LogProcessingStep(processingId, "ELEMENT_ERROR", $"Error processing element {elementIndex}/{elementCount} ({elementType}): {ex.GetType().Name}: {ex.Message}");
                await LogProcessingStep(processingId, "ELEMENT_ERROR_STACK", $"Stack trace: {ex.StackTrace}");

                if (ex.InnerException != null)
                {
                    await LogProcessingStep(processingId, "ELEMENT_ERROR_INNER", $"Inner exception: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
                }

                _logger.LogError(ex, "Error processing document element {ElementType} at index {ElementIndex}", elementType, elementIndex);

                // Continue processing other elements instead of stopping
                continue;
            }
        }

        var result = markdownBuilder.ToString().Trim();
        await LogProcessingStep(processingId, "DOCUMENT_PROCESSING_COMPLETE",
            $"Processing complete. Processed: {processedElements}, Skipped: {skippedElements}, Errors: {errorElements}, Output length: {result.Length} chars");

        if (errorElements > 0)
        {
            await LogProcessingStep(processingId, "PROCESSING_WARNING", $"Completed with {errorElements} errors out of {elementCount} elements");
        }

        return result;
    }

    private async Task<string> ProcessElementAsync(object element, ConversionOptions options, WordprocessingDocument document)
    {
        var result = element switch
        {
            Paragraph paragraph => await ProcessParagraphWithImagesAndFormulasAsync(paragraph, options, document),
            DocumentFormat.OpenXml.Wordprocessing.Table table when options.ConvertTables => ProcessTable(table, options),
            DocumentFormat.OpenXml.Math.OfficeMath officeMath when options.ProcessFormulas => ProcessFormula(officeMath, options),
            _ => string.Empty
        };

        return result;
    }

    private string ProcessTable(DocumentFormat.OpenXml.Wordprocessing.Table table, ConversionOptions options)
    {
        try
        {
            var tableMarkdown = _tableProcessor.ProcessTable(table, options);
            _logger.LogDebug("Successfully processed table");
            return tableMarkdown;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process table in document");
            return "<!-- Error processing table -->";
        }
    }

    private string ProcessFormula(object formula, ConversionOptions options)
    {
        try
        {
            var formulaMarkdown = _formulaProcessor.ProcessFormula(formula, options);
            _logger.LogDebug("Successfully processed formula");
            return formulaMarkdown;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process formula in document");
            return "<!-- Error processing formula -->";
        }
    }

    private async Task<string> ProcessParagraphWithImagesAndFormulasAsync(Paragraph paragraph, ConversionOptions options, WordprocessingDocument document)
    {
        var resultBuilder = new StringBuilder();

        // Check if this paragraph contains formulas
        bool containsFormulas = options.ProcessFormulas && _formulaProcessor.ContainsFormulas(paragraph);

        if (containsFormulas)
        {
            // For paragraphs with formulas, we need to process them separately to avoid text duplication
            _logger.LogDebug("Processing paragraph with formulas - using formula-aware processing");

            // Process formulas first
            var formulas = _formulaProcessor.ExtractFormulas(paragraph);
            _logger.LogDebug($"Extracted {formulas.Count()} formulas from paragraph");

            foreach (var formula in formulas)
            {
                try
                {
                    var formulaMarkdown = _formulaProcessor.ProcessFormula(formula, options);
                    _logger.LogDebug($"Formula processed: '{formulaMarkdown?.Substring(0, Math.Min(100, formulaMarkdown?.Length ?? 0))}...'");

                    if (!string.IsNullOrEmpty(formulaMarkdown))
                    {
                        if (resultBuilder.Length > 0)
                            resultBuilder.AppendLine();
                        resultBuilder.AppendLine(formulaMarkdown);
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to process formula in paragraph");
                    resultBuilder.AppendLine("<!-- Failed to process formula -->");
                }
            }

            // Process any non-formula text content
            var nonFormulaText = ExtractNonFormulaText(paragraph);
            _logger.LogDebug($"Non-formula text extracted: '{nonFormulaText}'");

            if (!string.IsNullOrWhiteSpace(nonFormulaText))
            {
                if (resultBuilder.Length > 0)
                    resultBuilder.AppendLine();
                resultBuilder.Append(nonFormulaText);
            }
        }
        else
        {
            // For paragraphs without formulas, process normally
            var paragraphText = _textProcessor.ProcessParagraph(paragraph, options);
            resultBuilder.Append(paragraphText);
        }

        // Process images if enabled
        if (options.ExtractImages)
        {
            var drawings = paragraph.Descendants<DocumentFormat.OpenXml.Wordprocessing.Drawing>().ToList();
            if (drawings.Any())
            {
                // Process each drawing/image in the paragraph
                foreach (var drawing in drawings)
                {
                    try
                    {
                        var imageReference = await ProcessDrawingAsync(drawing, options, document);
                        if (!string.IsNullOrEmpty(imageReference))
                        {
                            // Add the image reference after the paragraph text
                            resultBuilder.AppendLine();
                            resultBuilder.AppendLine(imageReference);
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to process drawing in paragraph");
                        resultBuilder.AppendLine("<!-- Failed to process image -->");
                    }
                }
            }
        }

        return resultBuilder.ToString().Trim();
    }

    private async Task<string> ProcessDrawingAsync(DocumentFormat.OpenXml.Wordprocessing.Drawing drawing, ConversionOptions options, WordprocessingDocument document)
    {
        try
        {
            // Look for embedded images in the drawing
            var blips = drawing.Descendants<DocumentFormat.OpenXml.Drawing.Blip>().ToList();
            
            foreach (var blip in blips)
            {
                if (blip.Embed?.Value != null)
                {
                    // Find the image part by relationship ID
                    var imagePart = document.MainDocumentPart?.GetPartById(blip.Embed.Value) as ImagePart;
                    if (imagePart != null)
                    {
                        var imageDirectory = Path.Combine(options.OutputDirectory, options.ImageDirectory);
                        return await _imageProcessor.ProcessImageAsync(imagePart, imageDirectory);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to process drawing element");
        }

        return string.Empty;
    }

    private string ExtractNonFormulaText(Paragraph paragraph)
    {
        var textBuilder = new StringBuilder();

        try
        {
            // Get all OfficeMath elements in this paragraph
            var mathElements = paragraph.Descendants<DocumentFormat.OpenXml.Math.OfficeMath>().ToList();

            // Process each element in the paragraph
            foreach (var element in paragraph.Elements())
            {
                // Skip if this element contains math
                if (element.Descendants<DocumentFormat.OpenXml.Math.OfficeMath>().Any())
                {
                    _logger.LogDebug("Skipping element containing math formula");
                    continue;
                }

                // Process non-math elements normally
                if (element is Run run)
                {
                    var runText = _textProcessor.ProcessRun(run, new ConversionOptions { PreserveFormatting = true });
                    textBuilder.Append(runText);
                }
                else if (element is Hyperlink hyperlink)
                {
                    // Process hyperlink text (but skip if it contains math)
                    var hyperlinkText = hyperlink.InnerText;
                    textBuilder.Append(hyperlinkText);
                }
                // Add other element types as needed
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting non-formula text from paragraph");
            // Fallback: return empty string to avoid including corrupted text
            return string.Empty;
        }

        return textBuilder.ToString().Trim();
    }

    #region 日志记录方法

    private async Task LogProcessingStep(string processingId, string step, string message)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"{timestamp} - [{processingId}] {step}: {message}\n";
            await File.AppendAllTextAsync("document_processor_debug.log", logMessage);
        }
        catch
        {
            // 静默处理日志写入失败，避免影响主流程
        }
    }

    #endregion
}