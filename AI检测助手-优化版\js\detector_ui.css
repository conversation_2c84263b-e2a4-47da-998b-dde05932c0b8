/* 
 * AI检测结果展示界面样式优化
 * 基于用户提供的界面参考
 */

/* 主要结果区域样式 */
.ai-detection-result {
    background: white;
    border-radius: 15px;
    padding: 25px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.08);
    margin-bottom: 20px;
    animation: fadeInUp 0.5s ease;
}

/* 结果头部区域 */
.detection-header {
    text-align: center;
    margin-bottom: 25px;
    position: relative;
    padding-bottom: 15px;
    border-bottom: 1px solid #f1f1f1;
}

/* 检测分数主显示 */
.detection-score-main {
    font-size: 3rem;
    font-weight: bold;
    margin: 5px 0;
    display: block;
    position: relative;
    z-index: 2;
    transition: all 0.3s ease;
}

/* 警告提示样式 */
.ai-detection-warning {
    display: flex;
    align-items: center;
    background: #ffebee;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
    border-left: 4px solid #ef5350;
}

.warning-icon {
    font-size: 24px;
    color: #ef5350;
    margin-right: 15px;
}

/* 颜色分级 */
.detection-score-main.low { color: #28a745; }
.detection-score-main.medium { color: #ffc107; }
.detection-score-main.high { color: #dc3545; }

/* 饼图样式优化 */
.pie-chart-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    margin: 30px 0;
    gap: 30px;
}

.pie-chart {
    position: relative;
    width: 220px;
    height: 220px;
    filter: drop-shadow(0 5px 10px rgba(0,0,0,0.1));
}

.pie-chart svg {
    width: 100%;
    height: 100%;
    transform: rotate(-90deg);
}

.pie-chart-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    background: white;
    border-radius: 50%;
    width: 65%;
    height: 65%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    box-shadow: 0 0 20px rgba(0,0,0,0.05);
}

.pie-chart-score {
    font-size: 2rem;
    font-weight: bold;
    color: #dc3545;
    line-height: 1;
}

.pie-chart-label {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

/* 分类显示增强 */
.detection-breakdown {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(130px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.breakdown-item {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    text-align: center;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
    transition: all 0.3s ease;
}

.breakdown-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.08);
}

.breakdown-item.human-written {
    border-left: 4px solid #28a745;
}

.breakdown-item.ai-generated {
    border-left: 4px solid #dc3545;
}

.breakdown-item.suspicious {
    border-left: 4px solid #ffc107;
}

.breakdown-percentage {
    font-size: 1.5rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.breakdown-percentage.human-written { color: #28a745; }
.breakdown-percentage.ai-generated { color: #dc3545; }
.breakdown-percentage.suspicious { color: #ffc107; }

.breakdown-label {
    font-size: 0.9rem;
    color: #666;
}

/* 图例样式优化 */
.chart-legend {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin: 15px 0;
    flex-wrap: wrap;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 0.9rem;
    padding: 5px 10px;
    background: white;
    border-radius: 20px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.05);
}

.legend-color {
    width: 16px;
    height: 16px;
    border-radius: 50%;
}

.legend-color.human { background: #28a745; }
.legend-color.ai { background: #dc3545; }
.legend-color.suspicious { background: #ffc107; }

/* 进度条区域优化 */
.progress-bars {
    margin: 25px 0;
}

.progress-item {
    margin-bottom: 15px;
}

.progress-label {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 0.95rem;
}

.progress-bar-container {
    height: 10px;
    background: #e9ecef;
    border-radius: 10px;
    overflow: hidden;
}

.progress-bar-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 1s ease-in-out;
}

.progress-bar-fill.ai-score {
    background: linear-gradient(90deg, #28a745, #dc3545);
}

.progress-bar-fill.confidence {
    background: linear-gradient(90deg, #007bff, #0056b3);
}

.progress-bar-fill.complexity {
    background: linear-gradient(90deg, #6f42c1, #495057);
}

/* 结果详情区域优化 */
.result-details {
    background: #f9f9f9;
    padding: 20px;
    border-radius: 12px;
    margin-top: 25px;
    box-shadow: 0 3px 10px rgba(0,0,0,0.05);
}

.result-details h4 {
    color: #555;
    font-size: 1.2rem;
    margin-bottom: 15px;
    border-bottom: 2px solid #eaeaea;
    padding-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.result-details ul {
    list-style: none;
    padding-left: 10px;
}

.result-details li {
    position: relative;
    padding: 8px 0 8px 20px;
    border-bottom: 1px solid #f0f0f0;
}

.result-details li:before {
    content: '•';
    color: #dc3545;
    font-weight: bold;
    position: absolute;
    left: 0;
}

/* 自适应样式 */
@media (max-width: 768px) {
    .pie-chart-container {
        flex-direction: column;
    }
    
    .pie-chart {
        width: 180px;
        height: 180px;
    }
    
    .detection-score-main {
        font-size: 2.5rem;
    }
}

/* 按钮样式 */
.action-buttons {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    flex-wrap: wrap;
    justify-content: center;
}

.action-button {
    padding: 10px 20px;
    border-radius: 8px;
    border: none;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 8px;
    transition: all 0.3s ease;
}

.action-button.optimize {
    background: #dc3545;
    color: white;
}

.action-button.optimize:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.action-button.copy {
    background: #f8f9fa;
    color: #495057;
}

.action-button.copy:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

/* 动画效果 */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 结果卡片样式 */
.result-card {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    border-left: 4px solid #007bff;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.result-card h5 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 8px;
}

/* 结果状态显示 */
.detection-status {
    font-size: 1.2rem;
    font-weight: 500;
    padding: 8px 15px;
    border-radius: 30px;
    display: inline-block;
    margin: 10px 0;
}

.detection-status.low {
    background: #d4edda;
    color: #155724;
}

.detection-status.medium {
    background: #fff3cd;
    color: #856404;
}

.detection-status.high {
    background: #f8d7da;
    color: #721c24;
} 