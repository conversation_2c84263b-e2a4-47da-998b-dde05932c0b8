<Window x:Class="DocxToMarkdownConverter.Views.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        xmlns:behaviors="clr-namespace:DocxToMarkdownConverter.Behaviors"
        Title="DOCX to Markdown Converter" 
        Height="700" Width="1200"
        MinHeight="600" MinWidth="800"
        WindowStartupLocation="CenterScreen"
        TextElement.Foreground="{DynamicResource MaterialDesignBody}"
        TextElement.FontWeight="Regular"
        TextElement.FontSize="13"
        TextOptions.TextFormattingMode="Display"
        TextOptions.TextRenderingMode="ClearType"
        RenderOptions.BitmapScalingMode="Linear"
        RenderOptions.EdgeMode="Aliased"
        Background="{DynamicResource AppBackgroundBrush}"
        FontFamily="{DynamicResource MaterialDesignFont}"
        Style="{StaticResource ThemedWindowStyle}">

    <Window.InputBindings>
        <!-- Ctrl+O: 打开文件选择对话框 -->
        <KeyBinding Key="O" Modifiers="Ctrl" Command="{Binding OpenFileCommand}"/>
        <!-- F5: 开始转换操作 -->
        <KeyBinding Key="F5" Command="{Binding StartConversionCommand}"/>
        <!-- Ctrl****: 切换功能页面 -->
        <KeyBinding Key="D1" Modifiers="Ctrl" Command="{Binding NavigateToPageCommand}" CommandParameter="0"/>
        <KeyBinding Key="D2" Modifiers="Ctrl" Command="{Binding NavigateToPageCommand}" CommandParameter="1"/>
        <KeyBinding Key="D3" Modifiers="Ctrl" Command="{Binding NavigateToPageCommand}" CommandParameter="2"/>
        <KeyBinding Key="D4" Modifiers="Ctrl" Command="{Binding NavigateToPageCommand}" CommandParameter="3"/>
        <!-- Esc: 取消操作或关闭对话框 -->
        <KeyBinding Key="Escape" Command="{Binding CancelOperationCommand}"/>
    </Window.InputBindings>

    <Window.Resources>
        <!-- Enhanced Navigation Button Style -->
        <Style x:Key="NavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignFlatButton}">
            <Setter Property="HorizontalAlignment" Value="Stretch"/>
            <Setter Property="HorizontalContentAlignment" Value="Left"/>
            <Setter Property="Height" Value="48"/>
            <Setter Property="Margin" Value="4,2"/>
            <Setter Property="Padding" Value="16,0"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
            <Setter Property="Background" Value="{DynamicResource NavigationItemDefaultBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource NavigationTextDefaultBrush}"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="materialDesign:ButtonAssist.CornerRadius" Value="8"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource NavigationItemHoverBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource NavigationTextHoverBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Enhanced Active Navigation Button Style -->
        <Style x:Key="ActiveNavigationButtonStyle" TargetType="Button" BasedOn="{StaticResource NavigationButtonStyle}">
            <Setter Property="Background" Value="{DynamicResource NavigationItemSelectedBrush}"/>
            <Setter Property="Foreground" Value="{DynamicResource NavigationTextSelectedBrush}"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="{DynamicResource NavigationItemActiveBrush}"/>
                    <Setter Property="Foreground" Value="{DynamicResource NavigationTextSelectedBrush}"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Navigation Icon Style for Dynamic Color Changes -->
        <Style x:Key="NavigationIconStyle" TargetType="materialDesign:PackIcon">
            <Setter Property="Foreground" Value="{DynamicResource NavigationIconDefaultBrush}"/>
            <Style.Triggers>
                <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=Button}, Path=IsMouseOver}" Value="True">
                    <Setter Property="Foreground" Value="{DynamicResource NavigationIconHoverBrush}"/>
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <materialDesign:DialogHost Identifier="RootDialog">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="280" MinWidth="200" MaxWidth="350"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 玻璃背景层 -->
            <Border Style="{StaticResource GlassWindowBackgroundStyle}"
                    Grid.ColumnSpan="3"/>

            <!-- Sidebar Navigation -->
            <materialDesign:Card Grid.Column="0"
                                 Style="{StaticResource GlassSidebarStyle}"
                                 Margin="0,0,8,0">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Header Section - Clickable to return to Welcome -->
                    <Button x:Name="HeaderButton"
                            Grid.Row="0"
                            Background="{DynamicResource NavigationHeaderBrush}"
                            BorderThickness="0,0,0,1"
                            BorderBrush="{DynamicResource AppDividerBrush}"
                            Padding="24,32"
                            Click="NavigateToWelcome_Click"
                            ToolTip="返回欢迎界面"
                            Cursor="Hand"
                            HorizontalContentAlignment="Center"
                            VerticalContentAlignment="Center">
                        <Button.Style>
                            <Style TargetType="Button">
                                <Setter Property="Template">
                                    <Setter.Value>
                                        <ControlTemplate TargetType="Button">
                                            <Border Background="{TemplateBinding Background}"
                                                    BorderBrush="{TemplateBinding BorderBrush}"
                                                    BorderThickness="{TemplateBinding BorderThickness}"
                                                    CornerRadius="8,8,0,0"
                                                    Padding="{TemplateBinding Padding}">
                                                <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                  VerticalAlignment="{TemplateBinding VerticalContentAlignment}"/>
                                            </Border>
                                            <ControlTemplate.Triggers>
                                                <Trigger Property="IsMouseOver" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource NavigationItemHoverBrush}"/>
                                                </Trigger>
                                                <Trigger Property="IsPressed" Value="True">
                                                    <Setter Property="Background" Value="{DynamicResource NavigationItemActiveBrush}"/>
                                                </Trigger>
                                            </ControlTemplate.Triggers>
                                        </ControlTemplate>
                                    </Setter.Value>
                                </Setter>
                            </Style>
                        </Button.Style>
                        <StackPanel>
                            <materialDesign:PackIcon Kind="FileDocument"
                                                     Width="32" Height="32"
                                                     Foreground="{DynamicResource NavigationIconDefaultBrush}"
                                                     HorizontalAlignment="Center"
                                                     Margin="0,0,0,12"/>
                            <TextBlock Text="{DynamicResource App.Title}"
                                       Style="{StaticResource MaterialDesignHeadline6TextBlock}"
                                       Foreground="{DynamicResource NavigationTextDefaultBrush}"
                                       HorizontalAlignment="Center"
                                       FontWeight="Medium"/>
                        </StackPanel>
                    </Button>

                    <!-- Navigation Menu -->
                    <ScrollViewer Grid.Row="1"
                                  VerticalScrollBarVisibility="Auto"
                                  Padding="8,16"
                                  Background="{DynamicResource NavigationBackgroundBrush}">
                        <StackPanel behaviors:AnimationBehaviors.AutoAttachHoverAnimations="True">
                            <!-- Navigation Buttons -->
                            <Button x:Name="FilesButton"
                                    Style="{StaticResource NavigationButtonStyle}"
                                    Click="NavigateToFiles_Click"
                                    ToolTip="Files (Ctrl+1)">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="FolderMultiple"
                                                             Width="20" Height="20"
                                                             Margin="0,0,16,0"
                                                             VerticalAlignment="Center"
                                                             Style="{StaticResource NavigationIconStyle}"/>
                                    <TextBlock Text="{DynamicResource Navigation.Files}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="SettingsButton"
                                    Style="{StaticResource NavigationButtonStyle}"
                                    Click="NavigateToSettings_Click"
                                    ToolTip="Settings (Ctrl+2)">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="Settings"
                                                             Width="20" Height="20"
                                                             Margin="0,0,16,0"
                                                             VerticalAlignment="Center"
                                                             Style="{StaticResource NavigationIconStyle}"/>
                                    <TextBlock Text="{DynamicResource Navigation.Settings}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ProgressButton"
                                    Style="{StaticResource NavigationButtonStyle}"
                                    Click="NavigateToProgress_Click"
                                    ToolTip="Progress (Ctrl+3)">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="ProgressClock"
                                                             Width="20" Height="20"
                                                             Margin="0,0,16,0"
                                                             VerticalAlignment="Center"
                                                             Style="{StaticResource NavigationIconStyle}"/>
                                    <TextBlock Text="{DynamicResource Navigation.Progress}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <Button x:Name="ResultsButton"
                                    Style="{StaticResource NavigationButtonStyle}"
                                    Click="NavigateToResults_Click"
                                    ToolTip="Results (Ctrl+4)">
                                <StackPanel Orientation="Horizontal">
                                    <materialDesign:PackIcon Kind="CheckboxMarkedCircle"
                                                             Width="20" Height="20"
                                                             Margin="0,0,16,0"
                                                             VerticalAlignment="Center"
                                                             Style="{StaticResource NavigationIconStyle}"/>
                                    <TextBlock Text="{DynamicResource Navigation.Results}" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Button>

                            <!-- Separator -->
                            <Separator Margin="16,24,16,16" 
                                       Background="{DynamicResource MaterialDesignDivider}"/>

                            <!-- Statistics Section -->
                            <TextBlock Text="{DynamicResource Statistics.Title}"
                                       Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                       Margin="16,0,16,8"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>

                            <StackPanel Margin="16,0">
                                <TextBlock Text="{Binding Statistics.FilesProcessedText}"
                                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                                           Margin="0,2"/>
                                <TextBlock Text="{Binding Statistics.SuccessfulText}"
                                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                                           Margin="0,2"/>
                                <TextBlock Text="{Binding Statistics.FailedText}"
                                           Style="{StaticResource MaterialDesignBody2TextBlock}"
                                           Margin="0,2"/>
                            </StackPanel>
                        </StackPanel>
                    </ScrollViewer>

                    <!-- Footer Section -->
                    <Border Grid.Row="2" 
                            Padding="16"
                            BorderBrush="{DynamicResource MaterialDesignDivider}"
                            BorderThickness="0,1,0,0">
                        <StackPanel>
                            <!-- Theme Toggle -->
                            <Button x:Name="ThemeToggleButton"
                                    Command="{Binding ToggleThemeCommand}"
                                    Style="{StaticResource MaterialDesignIconButton}"
                                    HorizontalAlignment="Center"
                                    ToolTip="{DynamicResource Tooltip.ToggleTheme}"
                                    Margin="0,0,0,8">
                                <materialDesign:PackIcon Kind="Brightness6" Width="24" Height="24"/>
                            </Button>
                            
                            <!-- Version Info -->
                            <TextBlock Text="{DynamicResource App.Version}"
                                       Style="{StaticResource MaterialDesignCaptionTextBlock}"
                                       HorizontalAlignment="Center"
                                       Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                        </StackPanel>
                    </Border>
                </Grid>
            </materialDesign:Card>

            <!-- GridSplitter for resizable sidebar -->
            <GridSplitter Grid.Column="1"
                          Width="4"
                          HorizontalAlignment="Center"
                          VerticalAlignment="Stretch"
                          Background="Transparent"
                          ShowsPreview="False"
                          ResizeBehavior="PreviousAndNext"/>

            <!-- Main Content Area -->
            <Grid Grid.Column="2" Margin="8,0,0,0">
                <!-- Content Container with Material Design Card -->
                <materialDesign:Card materialDesign:ShadowAssist.ShadowDepth="Depth1"
                                     Margin="0">
                    <Grid>
                        <!-- Page Content Presenter -->
                        <ContentPresenter x:Name="MainContentPresenter" 
                                          Margin="0"/>

                        <!-- Default Welcome Content -->
                        <Grid x:Name="WelcomeContent">
                            <StackPanel HorizontalAlignment="Center" 
                                        VerticalAlignment="Center"
                                        MaxWidth="400"
                                        behaviors:AnimationBehaviors.AutoAttachHoverAnimations="True"
                                        behaviors:AnimationBehaviors.EnableEntranceAnimation="True">
                                <!-- Welcome Icon -->
                                <Border Background="{DynamicResource PrimaryHueLightBrush}"
                                        CornerRadius="50"
                                        Width="100" Height="100"
                                        HorizontalAlignment="Center"
                                        Margin="0,0,0,24">
                                    <materialDesign:PackIcon Kind="FileDocument" 
                                                             Width="48" Height="48"
                                                             Foreground="White"
                                                             HorizontalAlignment="Center"
                                                             VerticalAlignment="Center"/>
                                </Border>
                                
                                <!-- Welcome Text -->
                                <TextBlock Text="{DynamicResource App.Welcome}"
                                           Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           Margin="0,0,0,16"/>

                                <TextBlock Text="{DynamicResource App.Description}"
                                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           HorizontalAlignment="Center"
                                           TextAlignment="Center"
                                           TextWrapping="Wrap"
                                           Margin="0,0,0,32"/>
                            </StackPanel>
                        </Grid>

                        <!-- Loading Overlay -->
                        <Grid x:Name="LoadingOverlay" 
                              Background="{DynamicResource MaterialDesignPaper}"
                              Opacity="0.9"
                              Visibility="Collapsed">
                            <StackPanel HorizontalAlignment="Center" 
                                        VerticalAlignment="Center">
                                <ProgressBar Style="{StaticResource MaterialDesignCircularProgressBar}"
                                             IsIndeterminate="True"
                                             Width="48" Height="48"
                                             Margin="0,0,0,16"/>
                                <TextBlock Text="{Binding BusyMessage}"
                                           Style="{StaticResource MaterialDesignBody1TextBlock}"
                                           HorizontalAlignment="Center"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </materialDesign:Card>
            </Grid>
        </Grid>
    </materialDesign:DialogHost>
</Window>