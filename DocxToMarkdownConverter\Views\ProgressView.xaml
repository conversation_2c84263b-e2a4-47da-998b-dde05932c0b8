<UserControl x:Class="DocxToMarkdownConverter.Views.ProgressView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:DocxToMarkdownConverter.Controls"
             xmlns:behaviors="clr-namespace:DocxToMarkdownConverter.Behaviors"
             mc:Ignorable="d" 
             d:DesignHeight="600" d:DesignWidth="900">

    <Grid>
        <!-- 主要内容 -->
        <materialDesign:Card Style="{StaticResource PerformanceGlassCardStyle}"
                             Margin="16"
                             Padding="0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>
                
                <!-- 标题栏 -->
                <Border Grid.Row="0" 
                        Background="{DynamicResource PrimaryHueMidBrush}"
                        CornerRadius="8,8,0,0"
                        Padding="24,16">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <materialDesign:PackIcon Grid.Column="0"
                                                 Kind="ProgressClock" 
                                                 Width="24" Height="24"
                                                 Foreground="White"
                                                 VerticalAlignment="Center"
                                                 Margin="0,0,12,0"/>
                        
                        <StackPanel Grid.Column="1" VerticalAlignment="Center">
                            <TextBlock Text="转换进度" 
                                       FontSize="18" 
                                       FontWeight="Medium"
                                       Foreground="White"/>
                            <TextBlock Text="实时监控文件转换进度和日志信息" 
                                       FontSize="12"
                                       Foreground="#E0FFFFFF"
                                       Margin="0,2,0,0"/>
                        </StackPanel>
                    </Grid>
                </Border>
                
                <!-- 进度控件内容 -->
                <Border Grid.Row="1" 
                        Background="{DynamicResource MaterialDesignCardBackground}"
                        CornerRadius="0,0,8,8">
                    <controls:ProgressControl x:Name="ProgressControlInstance"
                                              Margin="0"/>
                </Border>
            </Grid>
        </materialDesign:Card>
    </Grid>
</UserControl>
