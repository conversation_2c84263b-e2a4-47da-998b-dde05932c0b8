/**
 * Ollama本地模型管理器 v2.0
 * 基于Web前端与本地LLM集成最佳实践重构
 * 
 * 主要改进：
 * 1. 基于官方ollama-js库的API设计
 * 2. 改进的CORS处理和错误恢复
 * 3. 指数退避重试机制
 * 4. 更好的连接状态管理
 * 5. 增强的安全性和稳定性
 */

class OllamaManagerV2 {
    constructor(options = {}) {
        // 基础配置
        this.config = {
            baseUrl: options.baseUrl || 'http://localhost:11434',
            timeout: options.timeout || 30000,
            maxRetries: options.maxRetries || 3,
            retryDelay: options.retryDelay || 1000,
            healthCheckInterval: options.healthCheckInterval || 30000,
            ...options
        };

        // 连接状态
        this.state = {
            isConnected: false,
            currentModel: null,
            models: [],
            lastHealthCheck: null,
            connectionAttempts: 0,
            lastError: null
        };

        // 性能监控
        this.metrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            totalResponseTime: 0,
            averageResponseTime: 0,
            lastRequestTime: null,
            requestHistory: []
        };

        // 事件监听器
        this.listeners = new Map();

        // 推荐模型配置
        this.recommendedModels = [
            {
                name: 'qwen3:30b-a3b',
                description: 'Qwen3 MoE模型，最新架构，高性能',
                size: '约20GB',
                category: 'flagship',
                recommended: true,
                minMemory: '20GB'
            },
            {
                name: 'qwen2.5:32b-instruct',
                description: '高性能中文大模型，适合学术写作',
                size: '约18GB',
                category: 'high-performance',
                recommended: true,
                minMemory: '16GB'
            },
            {
                name: 'qwen2.5:14b-instruct',
                description: '平衡性能与资源的中文模型',
                size: '约8GB',
                category: 'balanced',
                recommended: true,
                minMemory: '8GB'
            },
            {
                name: 'qwen2.5:7b-instruct',
                description: '轻量级中文模型，快速响应',
                size: '约4GB',
                category: 'lightweight',
                recommended: true,
                minMemory: '4GB'
            }
        ];

        // 初始化
        this.init();
    }

    /**
     * 初始化管理器
     */
    async init() {
        console.log('🚀 初始化Ollama管理器v2.0...');
        
        // 检测环境
        this.detectEnvironment();
        
        // 设置错误处理
        this.setupErrorHandling();
        
        // 启动健康检查
        this.startHealthCheck();
        
        // 初始连接检查
        await this.checkConnection();
    }

    /**
     * 检测运行环境
     */
    detectEnvironment() {
        const isLocalFile = window.location.protocol === 'file:';
        const isLocalhost = ['localhost', '127.0.0.1'].includes(window.location.hostname);
        const isCustomDomain = window.location.hostname.endsWith('.local') ||
                              window.location.hostname.includes('ai-detector');
        const isSecure = window.location.protocol === 'https:';

        this.environment = {
            isLocalFile,
            isLocalhost,
            isCustomDomain,
            isSecure,
            hostname: window.location.hostname,
            protocol: window.location.protocol,
            port: window.location.port,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };

        // 根据环境调整配置
        this.adjustConfigForEnvironment();

        console.log('🔍 环境检测:', this.environment);
    }

    /**
     * 根据环境调整配置
     */
    adjustConfigForEnvironment() {
        // 如果是自定义域名环境，确保CORS配置正确
        if (this.environment.isCustomDomain) {
            console.log('🌐 检测到自定义域名环境，调整CORS配置');

            // 更新推荐的CORS配置提示
            this.corsConfig = {
                recommended: `export OLLAMA_ORIGINS="http://${this.environment.hostname}:${this.environment.port || '3000'},http://localhost,http://127.0.0.1,file://"`,
                compatible: 'export OLLAMA_ORIGINS="*"'
            };
        } else if (this.environment.isLocalhost) {
            this.corsConfig = {
                recommended: 'export OLLAMA_ORIGINS="http://localhost,http://127.0.0.1,file://"',
                compatible: 'export OLLAMA_ORIGINS="*"'
            };
        } else if (this.environment.isLocalFile) {
            this.corsConfig = {
                recommended: 'export OLLAMA_ORIGINS="file://"',
                compatible: 'export OLLAMA_ORIGINS="*"'
            };
        }
    }

    /**
     * 设置全局错误处理
     */
    setupErrorHandling() {
        // 处理未捕获的Promise错误
        window.addEventListener('unhandledrejection', (event) => {
            if (event.reason && event.reason.message && 
                event.reason.message.toLowerCase().includes('ollama')) {
                console.error('🚨 Ollama相关未处理错误:', event.reason);
                this.handleError(event.reason);
                event.preventDefault();
            }
        });
    }

    /**
     * 启动健康检查
     */
    startHealthCheck() {
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
        }
        
        this.healthCheckTimer = setInterval(async () => {
            if (this.state.isConnected) {
                await this.checkConnection();
            }
        }, this.config.healthCheckInterval);
    }

    /**
     * 停止健康检查
     */
    stopHealthCheck() {
        if (this.healthCheckTimer) {
            clearInterval(this.healthCheckTimer);
            this.healthCheckTimer = null;
        }
    }

    /**
     * 检查连接状态
     */
    async checkConnection() {
        const startTime = Date.now();
        
        try {
            console.log('🔍 检查Ollama连接状态...');
            
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);
            
            const response = await fetch(`${this.config.baseUrl}/api/tags`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                signal: controller.signal,
                mode: 'cors',
                credentials: 'omit'
            });
            
            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;
            
            if (response.ok) {
                const data = await response.json();
                this.handleConnectionSuccess(data, responseTime);
                return true;
            } else {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }
            
        } catch (error) {
            this.handleConnectionError(error, Date.now() - startTime);
            return false;
        }
    }

    /**
     * 处理连接成功
     */
    handleConnectionSuccess(data, responseTime) {
        this.state.isConnected = true;
        this.state.models = data.models || [];
        this.state.lastHealthCheck = new Date();
        this.state.connectionAttempts = 0;
        this.state.lastError = null;
        
        // 更新性能指标
        this.updateMetrics(responseTime, true);
        
        // 自动选择模型
        this.autoSelectModel();
        
        // 更新UI
        this.updateUI('connected', `连接成功 (${responseTime}ms)`);
        
        // 触发事件
        this.emit('connected', {
            models: this.state.models,
            responseTime,
            timestamp: new Date()
        });
        
        console.log('✅ Ollama连接成功，发现', this.state.models.length, '个模型');
    }

    /**
     * 处理连接错误
     */
    handleConnectionError(error, responseTime = 0) {
        this.state.isConnected = false;
        this.state.connectionAttempts++;
        this.state.lastError = {
            message: error.message,
            timestamp: new Date(),
            responseTime
        };
        
        // 更新性能指标
        this.updateMetrics(responseTime, false);
        
        // 分析错误类型
        const errorType = this.analyzeError(error);
        
        // 更新UI
        this.updateUI('error', this.getErrorMessage(errorType));
        
        // 触发事件
        this.emit('error', {
            error,
            errorType,
            attempts: this.state.connectionAttempts,
            timestamp: new Date()
        });
        
        console.error('❌ Ollama连接失败:', error.message);
        
        // 自动重试（带指数退避）
        this.scheduleRetry();
    }

    /**
     * 分析错误类型
     */
    analyzeError(error) {
        const message = error.message.toLowerCase();
        
        if (error.name === 'AbortError') {
            return 'timeout';
        } else if (message.includes('failed to fetch') || message.includes('network error')) {
            return 'network';
        } else if (message.includes('cors')) {
            return 'cors';
        } else if (message.includes('refused') || message.includes('econnrefused')) {
            return 'service_down';
        } else if (message.includes('404')) {
            return 'not_found';
        } else {
            return 'unknown';
        }
    }

    /**
     * 获取错误消息
     */
    getErrorMessage(errorType) {
        const messages = {
            timeout: '连接超时，请检查Ollama服务状态',
            network: '网络连接失败，请检查网络设置',
            cors: this.getCorsErrorMessage(),
            service_down: 'Ollama服务未启动，请运行 ollama serve',
            not_found: 'API端点不存在，请检查Ollama版本',
            unknown: '未知连接错误，请查看控制台详情'
        };

        return messages[errorType] || messages.unknown;
    }

    /**
     * 获取CORS错误消息（根据环境定制）
     */
    getCorsErrorMessage() {
        if (this.corsConfig) {
            return `CORS配置错误，请设置环境变量：\n${this.corsConfig.recommended}`;
        }
        return 'CORS配置错误，请设置OLLAMA_ORIGINS环境变量';
    }

    /**
     * 安排重试（指数退避）
     */
    scheduleRetry() {
        if (this.state.connectionAttempts >= this.config.maxRetries) {
            console.log('🛑 达到最大重试次数，停止重试');
            return;
        }
        
        const delay = this.config.retryDelay * Math.pow(2, this.state.connectionAttempts - 1);
        
        console.log(`🔄 ${delay}ms后进行第${this.state.connectionAttempts}次重试...`);
        
        setTimeout(() => {
            this.checkConnection();
        }, delay);
    }

    /**
     * 自动选择模型
     */
    autoSelectModel() {
        if (this.state.currentModel && 
            this.state.models.find(m => m.name === this.state.currentModel)) {
            return; // 当前模型仍然可用
        }
        
        // 优先选择推荐模型
        for (const recommended of this.recommendedModels) {
            const found = this.state.models.find(m => 
                m.name === recommended.name || 
                m.name.includes(recommended.name.split(':')[0])
            );
            
            if (found) {
                this.setCurrentModel(found.name);
                console.log('🎯 自动选择模型:', found.name);
                return;
            }
        }
        
        // 如果没有推荐模型，选择第一个可用模型
        if (this.state.models.length > 0) {
            this.setCurrentModel(this.state.models[0].name);
            console.log('🎯 选择第一个可用模型:', this.state.models[0].name);
        }
    }

    /**
     * 设置当前模型
     */
    setCurrentModel(modelName) {
        this.state.currentModel = modelName;
        this.emit('modelChanged', { model: modelName });
        this.updateCurrentModelUI(modelName);
    }

    /**
     * 更新性能指标
     */
    updateMetrics(responseTime, success) {
        this.metrics.totalRequests++;
        this.metrics.lastRequestTime = new Date();
        
        if (success) {
            this.metrics.successfulRequests++;
            this.metrics.totalResponseTime += responseTime;
            this.metrics.averageResponseTime = 
                this.metrics.totalResponseTime / this.metrics.successfulRequests;
        } else {
            this.metrics.failedRequests++;
        }
        
        // 保持请求历史（最多100条）
        this.metrics.requestHistory.push({
            timestamp: new Date(),
            responseTime,
            success
        });
        
        if (this.metrics.requestHistory.length > 100) {
            this.metrics.requestHistory.shift();
        }
        
        // 更新性能UI
        this.updatePerformanceUI();
    }

    /**
     * 生成文本 - 核心API方法
     */
    async generateText(prompt, options = {}) {
        if (!this.state.isConnected) {
            throw new Error('Ollama服务未连接');
        }

        if (!this.state.currentModel) {
            throw new Error('未选择模型');
        }

        const startTime = Date.now();

        try {
            const controller = new AbortController();
            const timeoutId = setTimeout(() => controller.abort(), this.config.timeout);

            const requestBody = {
                model: this.state.currentModel,
                prompt: prompt,
                stream: false,
                options: {
                    temperature: options.temperature || 0.7,
                    top_p: options.top_p || 0.9,
                    max_tokens: options.max_tokens || 2000,
                    ...options
                }
            };

            console.log('📤 发送生成请求:', {
                model: this.state.currentModel,
                promptLength: prompt.length,
                options: requestBody.options
            });

            const response = await fetch(`${this.config.baseUrl}/api/generate`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                body: JSON.stringify(requestBody),
                signal: controller.signal,
                mode: 'cors',
                credentials: 'omit'
            });

            clearTimeout(timeoutId);
            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const data = await response.json();
                this.updateMetrics(responseTime, true);

                console.log('📥 生成成功:', {
                    responseTime: responseTime + 'ms',
                    responseLength: data.response?.length || 0
                });

                return data.response;
            } else {
                const errorText = await response.text();
                throw new Error(`HTTP ${response.status}: ${errorText}`);
            }

        } catch (error) {
            const responseTime = Date.now() - startTime;
            this.updateMetrics(responseTime, false);

            console.error('❌ 生成失败:', error.message);
            throw error;
        }
    }

    /**
     * 使用专业提示词进行AI检测
     */
    async detectAIContent(text, useAdvanced = true) {
        try {
            const template = promptManager.getTemplate('aiDetection', useAdvanced ? 'primary' : 'fallback');
            const config = promptManager.getConfig('aiDetection');

            const formattedPrompt = promptManager.formatPrompt(template, { text });
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            // 尝试解析JSON响应
            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                // 如果JSON解析失败，返回原始响应
                return {
                    success: true,
                    data: { analysis: response, ai_probability: 0.5 },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('AI检测失败:', error);
            throw error;
        }
    }

    /**
     * 使用专业提示词进行智能改写
     */
    async intelligentRewrite(text, useAdvanced = true) {
        try {
            const template = promptManager.getTemplate('intelligentRewrite', useAdvanced ? 'primary' : 'fallback');
            const config = promptManager.getConfig('intelligentRewrite');

            const formattedPrompt = promptManager.formatPrompt(template, { text });
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                return {
                    success: true,
                    data: { optimized_text: response, quality_score: 0.8 },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('智能改写失败:', error);
            throw error;
        }
    }

    /**
     * 使用专业提示词进行学术优化
     */
    async academicOptimization(text, useAdvanced = true) {
        try {
            const template = promptManager.getTemplate('academicOptimization', useAdvanced ? 'primary' : 'fallback');
            const config = promptManager.getConfig('academicOptimization');

            const formattedPrompt = promptManager.formatPrompt(template, { text });
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                return {
                    success: true,
                    data: { academic_text: response, academic_level: "优化完成" },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('学术优化失败:', error);
            throw error;
        }
    }

    /**
     * 测试连接
     */
    async testConnection() {
        if (!this.state.currentModel) {
            throw new Error('请先选择一个模型');
        }

        try {
            const testPrompt = '你好，请简单回复确认连接正常。';
            const response = await this.generateText(testPrompt, {
                max_tokens: 50,
                temperature: 0.7
            });

            return {
                success: true,
                model: this.state.currentModel,
                response: response,
                timestamp: new Date()
            };

        } catch (error) {
            return {
                success: false,
                error: error.message,
                timestamp: new Date()
            };
        }
    }

    /**
     * 获取模型信息
     */
    async getModelInfo(modelName) {
        try {
            const response = await fetch(`${this.config.baseUrl}/api/show`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({ name: modelName }),
                mode: 'cors',
                credentials: 'omit'
            });

            if (response.ok) {
                return await response.json();
            } else {
                throw new Error(`HTTP ${response.status}`);
            }

        } catch (error) {
            console.error('获取模型信息失败:', error);
            throw error;
        }
    }

    /**
     * 诊断连接问题
     */
    async diagnoseConnection() {
        console.log('🔧 开始连接诊断...');

        const diagnostics = {
            timestamp: new Date().toISOString(),
            environment: this.environment,
            config: {
                baseUrl: this.config.baseUrl,
                timeout: this.config.timeout
            },
            tests: []
        };

        // 测试1: 基础连接
        try {
            const response = await fetch(this.config.baseUrl, {
                method: 'GET',
                mode: 'cors',
                credentials: 'omit'
            });

            diagnostics.tests.push({
                name: '基础连接测试',
                status: response.ok ? 'PASS' : 'FAIL',
                details: `HTTP ${response.status}`,
                responseTime: 0
            });
        } catch (error) {
            diagnostics.tests.push({
                name: '基础连接测试',
                status: 'FAIL',
                details: error.message,
                responseTime: 0
            });
        }

        // 测试2: API端点测试
        try {
            const startTime = Date.now();
            const response = await fetch(`${this.config.baseUrl}/api/tags`, {
                mode: 'cors',
                credentials: 'omit'
            });
            const responseTime = Date.now() - startTime;

            if (response.ok) {
                const data = await response.json();
                diagnostics.tests.push({
                    name: 'API端点测试',
                    status: 'PASS',
                    details: `发现 ${data.models?.length || 0} 个模型`,
                    responseTime
                });
            } else {
                diagnostics.tests.push({
                    name: 'API端点测试',
                    status: 'FAIL',
                    details: `HTTP ${response.status}`,
                    responseTime
                });
            }
        } catch (error) {
            diagnostics.tests.push({
                name: 'API端点测试',
                status: 'FAIL',
                details: error.message,
                responseTime: 0
            });
        }

        // 测试3: 模型可用性
        if (this.state.currentModel) {
            try {
                const startTime = Date.now();
                const modelInfo = await this.getModelInfo(this.state.currentModel);
                const responseTime = Date.now() - startTime;

                diagnostics.tests.push({
                    name: '模型可用性测试',
                    status: 'PASS',
                    details: `模型 ${this.state.currentModel} 可用`,
                    responseTime
                });
            } catch (error) {
                diagnostics.tests.push({
                    name: '模型可用性测试',
                    status: 'FAIL',
                    details: error.message,
                    responseTime: 0
                });
            }
        }

        // 测试4: 生成测试
        if (this.state.currentModel && this.state.isConnected) {
            try {
                const startTime = Date.now();
                await this.generateText('测试', { max_tokens: 10 });
                const responseTime = Date.now() - startTime;

                diagnostics.tests.push({
                    name: '生成功能测试',
                    status: 'PASS',
                    details: '生成功能正常',
                    responseTime
                });
            } catch (error) {
                diagnostics.tests.push({
                    name: '生成功能测试',
                    status: 'FAIL',
                    details: error.message,
                    responseTime: 0
                });
            }
        }

        console.log('📋 诊断完成:', diagnostics);
        return diagnostics;
    }

    /**
     * 高级学术架构分析
     */
    async advancedAcademicAnalysis(type, data) {
        try {
            let template, variables;

            switch (type) {
                case 'comparison':
                    template = promptManager.getTemplate('advancedAcademicAnalysis', 'primary', 'comparison');
                    variables = { text_a: data.textA, text_b: data.textB };
                    break;
                case 'extraction':
                    template = promptManager.getTemplate('advancedAcademicAnalysis', 'primary', 'extraction');
                    variables = { text: data.text };
                    break;
                case 'fusion':
                    template = promptManager.getTemplate('advancedAcademicAnalysis', 'primary', 'fusion');
                    variables = { text_1: data.text1, text_2: data.text2 };
                    break;
                default:
                    throw new Error('不支持的分析类型');
            }

            const config = promptManager.getConfig('advancedAnalysis');
            const formattedPrompt = promptManager.formatPrompt(template, variables);
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                return {
                    success: true,
                    data: { analysis: response },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('高级学术分析失败:', error);
            throw error;
        }
    }

    /**
     * 多轮优化处理
     */
    async multiRoundOptimization(text, round, feedback = '', focus = '') {
        try {
            const template = promptManager.getTemplate('multiRoundOptimization', 'primary', 'iteration');
            const config = promptManager.getConfig('multiRound');

            const variables = {
                text,
                round: round.toString(),
                feedback: feedback || '首轮优化',
                focus: focus || '整体质量提升'
            };

            const formattedPrompt = promptManager.formatPrompt(template, variables);
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                return {
                    success: true,
                    data: {
                        optimized_text: response,
                        round_focus: focus,
                        quality_improvement: 0.1
                    },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('多轮优化失败:', error);
            throw error;
        }
    }

    /**
     * 评估优化质量
     */
    async evaluateOptimization(originalText, optimizedText, goal) {
        try {
            const template = promptManager.getTemplate('multiRoundOptimization', 'primary', 'evaluation');
            const config = promptManager.getConfig('multiRound');

            const variables = {
                original_text: originalText,
                optimized_text: optimizedText,
                goal: goal || '提升文本质量'
            };

            const formattedPrompt = promptManager.formatPrompt(template, variables);
            const fullPrompt = `${formattedPrompt.system}\n\n${formattedPrompt.user}`;

            const response = await this.generateText(fullPrompt, config);

            try {
                const result = JSON.parse(response);
                return {
                    success: true,
                    data: result,
                    raw_response: response
                };
            } catch (parseError) {
                return {
                    success: true,
                    data: {
                        overall_score: 0.8,
                        continue_optimization: false,
                        feedback: response
                    },
                    raw_response: response
                };
            }
        } catch (error) {
            console.error('质量评估失败:', error);
            throw error;
        }
    }

    /**
     * 事件系统
     */
    on(event, callback) {
        if (!this.listeners.has(event)) {
            this.listeners.set(event, []);
        }
        this.listeners.get(event).push(callback);
    }

    off(event, callback) {
        if (this.listeners.has(event)) {
            const callbacks = this.listeners.get(event);
            const index = callbacks.indexOf(callback);
            if (index > -1) {
                callbacks.splice(index, 1);
            }
        }
    }

    emit(event, data) {
        if (this.listeners.has(event)) {
            this.listeners.get(event).forEach(callback => {
                try {
                    callback(data);
                } catch (error) {
                    console.error('事件回调错误:', error);
                }
            });
        }
    }

    /**
     * UI更新方法
     */
    updateUI(status, message) {
        // 更新状态指示器
        const statusDot = document.getElementById('ollamaStatusDot');
        const statusText = document.getElementById('ollamaStatusText');
        const statusBadge = document.getElementById('ollamaStatusBadge');

        if (statusDot) {
            statusDot.className = `status-dot ${status}`;
        }

        if (statusText) {
            statusText.textContent = message;
        }

        if (statusBadge) {
            const badgeText = status === 'connected' ? '在线' :
                             status === 'error' ? '离线' : '连接中';
            statusBadge.textContent = badgeText;
            statusBadge.style.background = status === 'connected' ?
                'rgba(40, 167, 69, 0.8)' :
                status === 'error' ? 'rgba(220, 53, 69, 0.8)' :
                'rgba(255, 193, 7, 0.8)';
        }

        // 更新模型列表
        this.updateModelsUI();

        // 更新按钮状态
        this.updateButtonStates();
    }

    updateModelsUI() {
        const modelSelect = document.getElementById('modelSelect');
        if (!modelSelect) return;

        modelSelect.innerHTML = '<option value="">请选择模型...</option>';

        this.state.models.forEach(model => {
            const option = document.createElement('option');
            option.value = model.name;
            option.textContent = `${model.name} (${this.formatSize(model.size)})`;
            if (model.name === this.state.currentModel) {
                option.selected = true;
            }
            modelSelect.appendChild(option);
        });
    }

    updateCurrentModelUI(modelName) {
        const currentModelElement = document.getElementById('currentModel');
        if (currentModelElement) {
            currentModelElement.textContent = modelName || '未选择';
        }
    }

    updateButtonStates() {
        const connectBtn = document.getElementById('connectOllamaBtn');
        const testBtn = document.getElementById('testConnectionBtn');
        const diagnoseBtn = document.getElementById('diagnoseBtn');

        if (connectBtn) {
            connectBtn.disabled = this.state.isConnected;
            connectBtn.innerHTML = this.state.isConnected ?
                '<i class="fas fa-check"></i> 已连接' :
                '<i class="fas fa-plug"></i> 连接Ollama';
        }

        if (testBtn) {
            testBtn.disabled = !this.state.isConnected || !this.state.currentModel;
        }

        if (diagnoseBtn) {
            diagnoseBtn.disabled = false; // 诊断功能始终可用
        }
    }

    updatePerformanceUI() {
        const avgResponseTime = document.getElementById('avgResponseTime');
        const totalRequests = document.getElementById('totalRequests');
        const successRate = document.getElementById('successRate');

        if (avgResponseTime) {
            avgResponseTime.textContent = `${Math.round(this.metrics.averageResponseTime)}ms`;
        }

        if (totalRequests) {
            totalRequests.textContent = this.metrics.totalRequests;
        }

        if (successRate) {
            const rate = this.metrics.totalRequests > 0 ?
                ((this.metrics.successfulRequests / this.metrics.totalRequests) * 100).toFixed(1) : 0;
            successRate.textContent = `${rate}%`;
        }
    }

    /**
     * 工具方法
     */
    formatSize(bytes) {
        if (bytes === 0) return '0 B';
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i];
    }

    /**
     * 销毁管理器
     */
    destroy() {
        this.stopHealthCheck();
        this.listeners.clear();
        console.log('🗑️ Ollama管理器已销毁');
    }
}

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { OllamaManagerV2 };
}

// 创建全局实例 - 延迟初始化
let ollamaManagerV2;
window.addEventListener('DOMContentLoaded', function() {
    try {
        if (typeof window.ollamaManagerV2 === 'undefined') {
            window.ollamaManagerV2 = new OllamaManagerV2();
            ollamaManagerV2 = window.ollamaManagerV2; // 向后兼容
            console.log('✅ OllamaManagerV2 实例已创建');
        }
    } catch (error) {
        console.error('创建Ollama管理器实例时出错:', error);
    }
});

// 全局函数 - 兼容现有代码
async function connectOllama() {
    if (window.ollamaManagerV2) {
        await window.ollamaManagerV2.checkConnection();
    }
}

async function testOllamaConnection() {
    try {
        if (!window.ollamaManagerV2) {
            alert('❌ Ollama管理器未初始化');
            return;
        }

        const result = await window.ollamaManagerV2.testConnection();
        if (result.success) {
            alert(`🎉 连接测试成功！\n\n📋 模型: ${result.model}\n💬 回复: ${result.response}`);
        } else {
            alert(`❌ 连接测试失败\n\n🔍 错误: ${result.error}`);
        }
    } catch (error) {
        alert(`❌ 测试过程出错: ${error.message}`);
    }
}

async function diagnoseOllamaConnection() {
    try {
        if (!window.ollamaManagerV2) {
            alert('❌ Ollama管理器未初始化');
            return;
        }

        const diagnostics = await window.ollamaManagerV2.diagnoseConnection();

        let message = '🔧 Ollama连接诊断报告\n\n';
        message += `📡 连接地址: ${diagnostics.config.baseUrl}\n`;
        message += `⏰ 检测时间: ${new Date(diagnostics.timestamp).toLocaleString()}\n\n`;

        diagnostics.tests.forEach((test, index) => {
            const status = test.status === 'PASS' ? '✅' : '❌';
            message += `${index + 1}. ${test.name}: ${status}\n`;
            message += `   详情: ${test.details}\n`;
            if (test.responseTime > 0) {
                message += `   响应时间: ${test.responseTime}ms\n`;
            }
            message += '\n';
        });

        const failedTests = diagnostics.tests.filter(test => test.status === 'FAIL');
        if (failedTests.length > 0) {
            message += '💡 建议解决方案:\n';
            message += '1. 确认Ollama服务正在运行: ollama serve\n';

            // 根据环境提供CORS配置建议
            if (this.corsConfig) {
                message += `2. 配置CORS (推荐): ${this.corsConfig.recommended}\n`;
                message += `   或兼容模式: ${this.corsConfig.compatible}\n`;
            } else {
                message += '2. 检查CORS配置: export OLLAMA_ORIGINS="*"\n';
            }

            message += '3. 确认端口11434未被占用\n';
            message += '4. 重启Ollama服务后重试\n';

            // 自定义域名特殊提示
            if (this.environment.isCustomDomain) {
                message += `5. 确认hosts文件配置: 127.0.0.1 ${this.environment.hostname}\n`;
            }
        } else {
            message += '🎉 所有测试通过！连接正常。';
        }

        alert(message);

    } catch (error) {
        alert(`诊断过程出错: ${error.message}`);
    }
}

async function refreshOllamaStatus() {
    if (window.ollamaManagerV2) {
        await window.ollamaManagerV2.checkConnection();
    }
}

async function switchModel() {
    const modelSelect = document.getElementById('modelSelect');
    if (modelSelect && modelSelect.value && window.ollamaManagerV2) {
        window.ollamaManagerV2.setCurrentModel(modelSelect.value);
    }
}

// 页面加载时自动初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 页面加载完成，Ollama管理器v2.0已就绪');
});
