using System.Globalization;
using System.Windows;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// 本地化状态文本转换器
/// </summary>
public class LocalizedStatusConverter : IMultiValueConverter
{
    public object Convert(object[] values, Type targetType, object parameter, CultureInfo culture)
    {
        if (values == null || values.Length < 4)
            return "Total: 0 files";

        try
        {
            var total = values[0] as int? ?? 0;
            var ready = values[1] as int? ?? 0;
            var completed = values[2] as int? ?? 0;
            var failed = values[3] as int? ?? 0;

            // 尝试从应用程序资源获取本地化格式字符串
            var format = System.Windows.Application.Current?.FindResource("Files.StatusSummary") as string
                ?? "Total: {0} files • Ready: {1} • Completed: {2} • Failed: {3}";

            return string.Format(format, total, ready, completed, failed);
        }
        catch
        {
            return "Total: 0 files";
        }
    }

    public object[] ConvertBack(object value, Type[] targetTypes, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
