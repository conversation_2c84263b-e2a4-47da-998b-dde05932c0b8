using System;
using System.Collections.Generic;
using System.Threading.Tasks;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 性能监控服务接口
/// </summary>
public interface IPerformanceMonitorService
{
    /// <summary>
    /// 开始性能监控
    /// </summary>
    void StartMonitoring();

    /// <summary>
    /// 停止性能监控
    /// </summary>
    void StopMonitoring();

    /// <summary>
    /// 获取当前内存使用情况
    /// </summary>
    MemoryUsageInfo GetMemoryUsage();

    /// <summary>
    /// 获取CPU使用情况
    /// </summary>
    CpuUsageInfo GetCpuUsage();

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    PerformanceStatistics GetPerformanceStatistics();

    /// <summary>
    /// 强制垃圾回收
    /// </summary>
    Task ForceGarbageCollectionAsync();

    /// <summary>
    /// 检查内存压力
    /// </summary>
    MemoryPressureLevel CheckMemoryPressure();

    /// <summary>
    /// 性能监控事件
    /// </summary>
    event EventHandler<PerformanceEventArgs>? PerformanceAlert;

    /// <summary>
    /// 内存使用变化事件
    /// </summary>
    event EventHandler<MemoryUsageEventArgs>? MemoryUsageChanged;

    /// <summary>
    /// 添加性能计数器
    /// </summary>
    void AddPerformanceCounter(string name, Func<double> valueProvider);

    /// <summary>
    /// 移除性能计数器
    /// </summary>
    void RemovePerformanceCounter(string name);

    /// <summary>
    /// 设置内存警告阈值
    /// </summary>
    void SetMemoryWarningThreshold(long thresholdBytes);

    /// <summary>
    /// 获取内存使用历史
    /// </summary>
    IEnumerable<MemoryUsageSnapshot> GetMemoryUsageHistory();
}

/// <summary>
/// 内存使用信息
/// </summary>
public class MemoryUsageInfo
{
    public long WorkingSetBytes { get; set; }
    public long PrivateMemoryBytes { get; set; }
    public long VirtualMemoryBytes { get; set; }
    public long ManagedMemoryBytes { get; set; }
    public long Gen0Collections { get; set; }
    public long Gen1Collections { get; set; }
    public long Gen2Collections { get; set; }
    public DateTime Timestamp { get; set; }
    public double MemoryUsagePercentage { get; set; }
}

/// <summary>
/// CPU使用信息
/// </summary>
public class CpuUsageInfo
{
    public double ProcessorTime { get; set; }
    public double UserTime { get; set; }
    public double SystemTime { get; set; }
    public int ThreadCount { get; set; }
    public DateTime Timestamp { get; set; }
}

/// <summary>
/// 性能统计信息
/// </summary>
public class PerformanceStatistics
{
    public MemoryUsageInfo CurrentMemoryUsage { get; set; } = new();
    public CpuUsageInfo CurrentCpuUsage { get; set; } = new();
    public TimeSpan MonitoringDuration { get; set; }
    public long PeakMemoryUsage { get; set; }
    public double AverageMemoryUsage { get; set; }
    public double AverageCpuUsage { get; set; }
    public int GarbageCollectionCount { get; set; }
    public Dictionary<string, double> CustomCounters { get; set; } = new();
    public List<PerformanceAlert> Alerts { get; set; } = new();
}

/// <summary>
/// 内存压力级别
/// </summary>
public enum MemoryPressureLevel
{
    Low,
    Medium,
    High,
    Critical
}

/// <summary>
/// 性能事件参数
/// </summary>
public class PerformanceEventArgs : EventArgs
{
    public PerformanceAlert Alert { get; }
    public DateTime Timestamp { get; }

    public PerformanceEventArgs(PerformanceAlert alert)
    {
        Alert = alert;
        Timestamp = DateTime.Now;
    }
}

/// <summary>
/// 内存使用事件参数
/// </summary>
public class MemoryUsageEventArgs : EventArgs
{
    public MemoryUsageInfo MemoryUsage { get; }
    public MemoryPressureLevel PressureLevel { get; }

    public MemoryUsageEventArgs(MemoryUsageInfo memoryUsage, MemoryPressureLevel pressureLevel)
    {
        MemoryUsage = memoryUsage;
        PressureLevel = pressureLevel;
    }
}

/// <summary>
/// 性能警告
/// </summary>
public class PerformanceAlert
{
    public string Type { get; set; } = string.Empty;
    public string Message { get; set; } = string.Empty;
    public AlertSeverity Severity { get; set; }
    public DateTime Timestamp { get; set; }
    public Dictionary<string, object> Data { get; set; } = new();
}

/// <summary>
/// 警告严重程度
/// </summary>
public enum AlertSeverity
{
    Info,
    Warning,
    Error,
    Critical
}

/// <summary>
/// 内存使用快照
/// </summary>
public class MemoryUsageSnapshot
{
    public DateTime Timestamp { get; set; }
    public long MemoryUsageBytes { get; set; }
    public double MemoryUsagePercentage { get; set; }
    public int ActiveObjects { get; set; }
}
