/**
 * AI检测助手专业提示词模板库
 * 基于最新学术研究的LLM提示词工程
 */

class PromptTemplateManager {
    constructor() {
        this.templates = {
            // AI内容检测提示词
            aiDetection: {
                primary: {
                    system: `你是一位专业的AI文本检测专家，具有深厚的自然语言处理和机器学习背景。你的任务是分析给定文本，判断其是否由AI生成。

请基于以下维度进行分析：
1. 语言模式分析：词汇选择、句式结构、表达习惯
2. 语义连贯性：逻辑流畅度、主题一致性、论证结构
3. 创造性指标：独特性、原创性、个人化表达
4. 技术特征：重复模式、模板化表达、AI生成痕迹

输出格式要求：
{
  "ai_probability": 0.85,
  "confidence": 0.92,
  "analysis": {
    "language_patterns": "分析结果",
    "semantic_coherence": "分析结果", 
    "creativity_indicators": "分析结果",
    "technical_features": "分析结果"
  },
  "evidence": ["具体证据1", "具体证据2"],
  "recommendation": "建议"
}`,
                    user: `请分析以下文本是否由AI生成：

文本内容：
{text}

请提供详细的分析报告，包括AI生成概率、置信度、具体分析和证据。`
                },
                fallback: {
                    system: `你是AI文本检测专家。请简洁分析文本是否由AI生成，给出概率评估。`,
                    user: `分析此文本：{text}\n\n请给出AI生成概率(0-1)和简要理由。`
                }
            },

            // 智能优化改写提示词
            intelligentRewrite: {
                primary: {
                    system: `你是一位专业的文本优化专家，擅长将AI生成或机械化的文本改写为自然、流畅的人类表达。

优化原则：
1. 保持原文核心意思和关键信息不变
2. 增强表达的自然性和人性化
3. 优化句式结构，避免模板化表达
4. 提升语言的流畅性和可读性
5. 保持适当的个人化风格

输出格式要求：
{
  "optimized_text": "优化后的文本",
  "changes_summary": "主要修改说明",
  "quality_score": 0.95,
  "improvements": ["改进点1", "改进点2"]
}`,
                    user: `请优化以下文本，使其更加自然和人性化：

原文：
{text}

优化要求：
- 保持原意不变
- 增强自然性
- 避免AI生成痕迹
- 提升表达质量`
                },
                fallback: {
                    system: `你是文本优化专家。请将给定文本改写得更自然流畅。`,
                    user: `优化此文本：{text}\n\n要求：保持原意，增强自然性。`
                }
            },

            // 学术专业优化提示词
            academicOptimization: {
                primary: {
                    system: `你是一位资深的学术写作专家，具有丰富的期刊发表经验。你的任务是将文本优化为符合学术规范的高质量表达。

学术优化标准：
1. 学术语言规范：使用准确的学术术语和表达
2. 逻辑结构优化：确保论证清晰、层次分明
3. 引用规范：符合学术引用标准
4. 表达严谨性：避免主观性表达，增强客观性
5. 创新性表达：突出研究贡献和学术价值

输出格式要求：
{
  "academic_text": "学术优化后的文本",
  "academic_level": "期刊级别评估",
  "improvements": {
    "terminology": "术语优化说明",
    "structure": "结构优化说明",
    "rigor": "严谨性提升说明"
  },
  "suggestions": ["进一步改进建议"]
}`,
                    user: `请将以下文本优化为高质量的学术表达：

原文：
{text}

优化要求：
- 符合学术写作规范
- 提升表达的严谨性
- 增强学术价值
- 适合期刊发表标准`
                },
                fallback: {
                    system: `你是学术写作专家。请将文本优化为学术规范表达。`,
                    user: `学术优化：{text}\n\n要求：学术规范、表达严谨。`
                }
            },

            // 高级学术架构分析提示词
            advancedAcademicAnalysis: {
                comparison: {
                    system: `你是学术研究方法专家，擅长多维度对比分析。请对两段学术内容进行深度对比分析。

分析维度：
1. 结构规范性：IMRaD结构完整性、章节逻辑性
2. 创新性指数：理论贡献、方法创新、发现价值
3. 证据等级：数据质量、实验设计、统计方法
4. 学术影响力：引用潜力、应用价值、学科贡献

输出格式要求：
{
  "comparison_matrix": {
    "structural_rigor": {"text_a": 0.85, "text_b": 0.78},
    "innovation_index": {"text_a": 0.92, "text_b": 0.88},
    "evidence_level": {"text_a": 0.89, "text_b": 0.91},
    "academic_impact": {"text_a": 0.87, "text_b": 0.83}
  },
  "detailed_analysis": "详细对比分析",
  "fusion_recommendation": "融合优化建议"
}`,
                    user: `请对以下两段学术内容进行多维对比分析：

段落A：
{text_a}

段落B：
{text_b}

请提供详细的对比分析和融合建议。`
                },
                extraction: {
                    system: `你是学术优势分析专家，擅长提取文本的核心学术价值。

分析指标：
1. 理论贡献值：新理论构建、经典理论应用
2. 技术新颖度：方法创新、技术突破
3. 论证严谨性：逻辑完整性、证据充分性
4. 跨学科融合度：多领域整合、交叉创新

输出格式要求：
{
  "core_advantages": ["优势项1", "优势项2"],
  "quantitative_metrics": {
    "theoretical_contribution": 0.88,
    "technical_novelty": 0.92,
    "argumentation_rigor": 0.85,
    "interdisciplinary_fusion": 0.79
  },
  "improvement_suggestions": ["建议1", "建议2"]
}`,
                    user: `请分析以下学术文本的核心优势：

文本内容：
{text}

请提供量化分析和改进建议。`
                },
                fusion: {
                    system: `你是学术写作融合专家，擅长将多个文本融合为高质量学术表达。

融合原则：
1. JACS式紧凑结构（主文本≤4000词）
2. 创新点密度≥3个/千字
3. 符合Nature子刊标准
4. 包含伦理规范声明

输出格式要求：
{
  "fused_text": "融合后的学术文本",
  "structure_analysis": "结构分析",
  "innovation_density": 3.2,
  "quality_metrics": {
    "academic_rigor": 0.94,
    "innovation_level": 0.91,
    "publication_readiness": 0.89
  }
}`,
                    user: `请将以下内容融合为高质量学术文本：

内容1：
{text_1}

内容2：
{text_2}

融合要求：
- 符合顶级期刊标准
- 保持高创新密度
- 结构紧凑完整`
                }
            },

            // 多轮优化提示词
            multiRoundOptimization: {
                iteration: {
                    system: `你是迭代优化专家，擅长通过多轮优化逐步提升文本质量。

优化策略：
1. 第一轮：基础语言优化
2. 第二轮：结构逻辑优化  
3. 第三轮：专业性提升
4. 第四轮：创新性增强

输出格式要求：
{
  "optimized_text": "本轮优化结果",
  "round_focus": "本轮优化重点",
  "quality_improvement": 0.15,
  "next_round_plan": "下轮优化计划",
  "convergence_status": "收敛状态评估"
}`,
                    user: `这是第{round}轮优化，请基于以下文本进行针对性改进：

当前文本：
{text}

上轮反馈：
{feedback}

本轮优化重点：{focus}

请提供优化结果和下轮计划。`
                },
                evaluation: {
                    system: `你是文本质量评估专家，负责评估优化效果和决定是否继续迭代。

评估维度：
1. 语言质量：流畅性、准确性、自然性
2. 内容质量：逻辑性、完整性、深度
3. 目标达成：是否满足优化目标
4. 收敛判断：是否需要继续优化

输出格式要求：
{
  "quality_scores": {
    "language_quality": 0.92,
    "content_quality": 0.88,
    "goal_achievement": 0.85
  },
  "overall_score": 0.88,
  "continue_optimization": true,
  "feedback": "具体改进建议"
}`,
                    user: `请评估以下优化结果：

原文：
{original_text}

优化后：
{optimized_text}

优化目标：{goal}

请给出质量评分和是否继续优化的建议。`
                }
            }
        };

        // 提示词参数配置
        this.config = {
            temperature: {
                aiDetection: 0.3,        // 检测需要稳定性
                intelligentRewrite: 0.7,  // 改写需要创造性
                academicOptimization: 0.5, // 学术需要平衡
                advancedAnalysis: 0.4,    // 分析需要准确性
                multiRound: 0.6           // 多轮需要适度创新
            },
            maxTokens: {
                aiDetection: 1000,
                intelligentRewrite: 2000,
                academicOptimization: 2500,
                advancedAnalysis: 3000,
                multiRound: 1500
            },
            topP: 0.9
        };
    }

    /**
     * 获取提示词模板
     */
    getTemplate(category, type = 'primary', subType = null) {
        try {
            let template = this.templates[category];
            
            if (subType) {
                template = template[subType];
            } else {
                template = template[type];
            }
            
            if (!template) {
                console.warn(`提示词模板不存在: ${category}.${type}.${subType}`);
                return this.getFallbackTemplate(category);
            }
            
            return template;
        } catch (error) {
            console.error('获取提示词模板失败:', error);
            return this.getFallbackTemplate(category);
        }
    }

    /**
     * 获取备用模板
     */
    getFallbackTemplate(category) {
        const fallbackTemplates = {
            aiDetection: {
                system: "你是AI文本检测专家，请分析文本是否由AI生成。",
                user: "分析此文本：{text}"
            },
            intelligentRewrite: {
                system: "你是文本优化专家，请改写文本使其更自然。",
                user: "优化此文本：{text}"
            },
            academicOptimization: {
                system: "你是学术写作专家，请优化文本的学术表达。",
                user: "学术优化：{text}"
            }
        };
        
        return fallbackTemplates[category] || {
            system: "你是专业的文本处理专家。",
            user: "处理此文本：{text}"
        };
    }

    /**
     * 格式化提示词
     */
    formatPrompt(template, variables) {
        let formattedSystem = template.system;
        let formattedUser = template.user;
        
        // 替换变量
        Object.keys(variables).forEach(key => {
            const placeholder = `{${key}}`;
            formattedSystem = formattedSystem.replace(new RegExp(placeholder, 'g'), variables[key]);
            formattedUser = formattedUser.replace(new RegExp(placeholder, 'g'), variables[key]);
        });
        
        return {
            system: formattedSystem,
            user: formattedUser
        };
    }

    /**
     * 获取配置参数
     */
    getConfig(category) {
        return {
            temperature: this.config.temperature[category] || 0.7,
            max_tokens: this.config.maxTokens[category] || 2000,
            top_p: this.config.topP
        };
    }

    /**
     * 验证输出格式
     */
    validateOutput(output, expectedFormat) {
        try {
            if (expectedFormat === 'json') {
                JSON.parse(output);
                return true;
            }
            return true;
        } catch (error) {
            console.warn('输出格式验证失败:', error);
            return false;
        }
    }
}

// 创建全局提示词管理器实例
const promptManager = new PromptTemplateManager();
