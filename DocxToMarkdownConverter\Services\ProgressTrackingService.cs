using DocxToMarkdownConverter.Models;
using System.Collections.Concurrent;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 进度跟踪服务实现
/// </summary>
public class ProgressTrackingService : IProgressTrackingService
{
    private readonly ILoggingService _loggingService;
    private readonly object _lockObject = new object();
    
    // 当前转换状态
    private ConversionProgress? _currentProgress;
    private BatchConversionProgress? _batchProgress;
    private ConversionStatistics _statistics;
    
    // 文件转换跟踪
    private readonly ConcurrentDictionary<string, FileConversionInfo> _fileConversions;
    private DateTime _batchStartTime;
    private DateTime _currentFileStartTime;

    public ProgressTrackingService(ILoggingService loggingService)
    {
        _loggingService = loggingService ?? throw new ArgumentNullException(nameof(loggingService));
        _fileConversions = new ConcurrentDictionary<string, FileConversionInfo>();
        _statistics = new ConversionStatistics();
    }

    public event EventHandler<ConversionProgress>? ProgressUpdated;
    public event EventHandler<BatchConversionProgress>? BatchProgressUpdated;
    public event EventHandler<ConversionStatistics>? StatisticsUpdated;

    public void StartFileConversion(string fileName)
    {
        lock (_lockObject)
        {
            _currentFileStartTime = DateTime.Now;
            
            var fileInfo = new FileConversionInfo
            {
                FileName = fileName,
                StartTime = _currentFileStartTime,
                Status = ConversionStatus.InProgress
            };
            
            _fileConversions.AddOrUpdate(fileName, fileInfo, (key, existing) => fileInfo);

            _currentProgress = new ConversionProgress
            {
                CurrentFile = fileName,
                CurrentOperation = "准备转换",
                ProgressPercentage = 0,
                ProcessedItems = 0,
                TotalItems = 100, // 默认100个步骤
                ElapsedTime = TimeSpan.Zero,
                EstimatedTimeRemaining = null
            };

            _loggingService.LogConversionStart(fileName, "文件转换");
            ProgressUpdated?.Invoke(this, _currentProgress);
        }
    }

    public void UpdateFileProgress(string fileName, string operation, double progress)
    {
        lock (_lockObject)
        {
            if (_currentProgress?.CurrentFile == fileName)
            {
                var elapsed = DateTime.Now - _currentFileStartTime;
                TimeSpan? estimated = null;
                
                if (progress > 0 && progress < 100)
                {
                    var totalEstimated = TimeSpan.FromTicks((long)(elapsed.Ticks / (progress / 100.0)));
                    estimated = totalEstimated - elapsed;
                }

                _currentProgress = new ConversionProgress
                {
                    CurrentFile = fileName,
                    CurrentOperation = operation,
                    ProgressPercentage = progress,
                    ProcessedItems = (int)progress,
                    TotalItems = 100,
                    ElapsedTime = elapsed,
                    EstimatedTimeRemaining = estimated
                };

                // 更新文件转换信息
                if (_fileConversions.TryGetValue(fileName, out var fileInfo))
                {
                    fileInfo.Progress = progress;
                    fileInfo.CurrentOperation = operation;
                }

                _loggingService.LogConversionProgress(fileName, operation, progress);
                ProgressUpdated?.Invoke(this, _currentProgress);
                
                // 同时更新批量进度
                UpdateBatchProgressInternal();
            }
        }
    }

    public void CompleteFileConversion(string fileName, bool success, string? errorMessage = null)
    {
        lock (_lockObject)
        {
            var endTime = DateTime.Now;
            var duration = endTime - _currentFileStartTime;

            if (_fileConversions.TryGetValue(fileName, out var fileInfo))
            {
                fileInfo.EndTime = endTime;
                fileInfo.Duration = duration;
                fileInfo.Status = success ? ConversionStatus.Completed : ConversionStatus.Failed;
                fileInfo.ErrorMessage = errorMessage;
            }

            // 更新统计信息
            _statistics.TotalFilesProcessed++;
            if (success)
            {
                _statistics.SuccessfulConversions++;
                _statistics.TotalProcessingTime += duration;
            }
            else
            {
                _statistics.FailedConversions++;
            }

            _statistics.AverageProcessingTime = _statistics.SuccessfulFiles > 0 
                ? TimeSpan.FromTicks(_statistics.TotalProcessingTime.Ticks / _statistics.SuccessfulFiles)
                : TimeSpan.Zero;

            _loggingService.LogConversionComplete(fileName, success, duration, errorMessage);
            StatisticsUpdated?.Invoke(this, _statistics);
            
            // 更新批量进度
            UpdateBatchProgressInternal();
        }
    }

    public void StartBatchConversion(int totalFiles)
    {
        lock (_lockObject)
        {
            _batchStartTime = DateTime.Now;
            _batchProgress = new BatchConversionProgress
            {
                TotalFiles = totalFiles,
                CompletedFiles = 0,
                SuccessfulFiles = 0,
                FailedFiles = 0,
                ElapsedTime = TimeSpan.Zero,
                EstimatedTimeRemaining = null
            };

            // 重置统计信息
            _statistics = new ConversionStatistics
            {
                StartTime = _batchStartTime
            };

            _loggingService.LogInfo("开始批量转换，总文件数: {0}", totalFiles);
            BatchProgressUpdated?.Invoke(this, _batchProgress);
            StatisticsUpdated?.Invoke(this, _statistics);
        }
    }

    public void UpdateBatchProgress()
    {
        lock (_lockObject)
        {
            UpdateBatchProgressInternal();
        }
    }

    public void CompleteBatchConversion()
    {
        lock (_lockObject)
        {
            if (_batchProgress != null)
            {
                var endTime = DateTime.Now;
                var totalDuration = endTime - _batchStartTime;
                
                _batchProgress.ElapsedTime = totalDuration;
                _batchProgress.EstimatedTimeRemaining = TimeSpan.Zero;

                _statistics.EndTime = endTime;
                _statistics.TotalBatchTime = totalDuration;

                _loggingService.LogBatchStatistics(
                    _batchProgress.TotalFiles,
                    _batchProgress.CompletedFiles,
                    _batchProgress.FailedFiles,
                    totalDuration);

                BatchProgressUpdated?.Invoke(this, _batchProgress);
                StatisticsUpdated?.Invoke(this, _statistics);
            }
        }
    }

    public void Reset()
    {
        lock (_lockObject)
        {
            _currentProgress = null;
            _batchProgress = null;
            _statistics = new ConversionStatistics();
            _fileConversions.Clear();

            _loggingService.LogInfo("进度跟踪已重置");
            
            ProgressUpdated?.Invoke(this, new ConversionProgress
            {
                CurrentFile = "",
                CurrentOperation = "等待开始转换...",
                ProgressPercentage = 0,
                ProcessedItems = 0,
                TotalItems = 0,
                ElapsedTime = TimeSpan.Zero
            });
            
            StatisticsUpdated?.Invoke(this, _statistics);
        }
    }

    public ConversionProgress? GetCurrentProgress()
    {
        lock (_lockObject)
        {
            return _currentProgress;
        }
    }

    public BatchConversionProgress? GetBatchProgress()
    {
        lock (_lockObject)
        {
            return _batchProgress;
        }
    }

    public ConversionStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return _statistics;
        }
    }

    private void UpdateBatchProgressInternal()
    {
        if (_batchProgress == null) return;

        var completedFiles = _fileConversions.Values.Count(f => f.Status != ConversionStatus.InProgress);
        var successfulFiles = _fileConversions.Values.Count(f => f.Status == ConversionStatus.Completed);
        var failedFiles = _fileConversions.Values.Count(f => f.Status == ConversionStatus.Failed);

        _batchProgress.CompletedFiles = completedFiles;
        _batchProgress.SuccessfulFiles = successfulFiles;
        _batchProgress.FailedFiles = failedFiles;
        _batchProgress.ElapsedTime = DateTime.Now - _batchStartTime;
        _batchProgress.CurrentFileProgress = _currentProgress;

        // 估算剩余时间
        if (completedFiles > 0 && completedFiles < _batchProgress.TotalFiles)
        {
            var avgTimePerFile = _batchProgress.ElapsedTime.TotalMilliseconds / completedFiles;
            var remainingFiles = _batchProgress.TotalFiles - completedFiles;
            _batchProgress.EstimatedTimeRemaining = TimeSpan.FromMilliseconds(avgTimePerFile * remainingFiles);
        }

        BatchProgressUpdated?.Invoke(this, _batchProgress);
    }

    /// <summary>
    /// 文件转换信息
    /// </summary>
    private class FileConversionInfo
    {
        public string FileName { get; set; } = string.Empty;
        public DateTime StartTime { get; set; }
        public DateTime? EndTime { get; set; }
        public TimeSpan Duration { get; set; }
        public ConversionStatus Status { get; set; }
        public double Progress { get; set; }
        public string CurrentOperation { get; set; } = string.Empty;
        public string? ErrorMessage { get; set; }
    }
}