// 主要交互逻辑

// 全局变量
let currentModal = null;
let isDetecting = false;
let isOptimizing = false;
let hybridModeEnabled = false;
let multiRoundInProgress = false;
let currentPanel = 'ai-detection'; // 当前激活的面板

// AI检测器实例将由ai_detector.js文件自动创建

// 导航栏功能
function showPanel(panelId) {
    // 隐藏所有面板
    const panels = document.querySelectorAll('.content-panel');
    panels.forEach(panel => {
        panel.classList.remove('active');
    });

    // 移除所有导航项的激活状态
    const navItems = document.querySelectorAll('.nav-item');
    navItems.forEach(item => {
        item.classList.remove('active');
    });

    // 显示目标面板
    const targetPanel = document.getElementById(panelId + '-panel');
    if (targetPanel) {
        targetPanel.classList.add('active');
    }

    // 激活对应的导航项
    const targetNavItem = document.querySelector(`[onclick="showPanel('${panelId}')"]`);
    if (targetNavItem) {
        targetNavItem.classList.add('active');
    }

    // 更新主标题和描述
    updateMainHeader(panelId);

    // 记录当前面板
    currentPanel = panelId;

    // 在移动端关闭侧边栏
    if (window.innerWidth <= 768) {
        closeSidebar();
    }
}

// 更新主标题和描述
function updateMainHeader(panelId) {
    const titles = {
        'ai-detection': {
            title: 'AI内容检测',
            description: '专业的学术论文AI检测功能，支持多种检测算法'
        },
        'unified-academic-optimization': {
            title: '学术智能优化',
            description: '整合智能改写与学术优化的统一功能，三阶段优化：学术规范化 → 表达优化 → AI特征消除'
        },
        'advanced-academic': {
            title: '高级学术架构',
            description: '基于多篇文献的学术写作规范和创新性评价系统'
        },
        'multi-round': {
            title: '多轮优化',
            description: '自动多轮优化直到达到目标分数，智能迭代提升文本质量'
        },
        'llm-control': {
            title: 'LLM服务控制中心',
            description: '零配置一键启动LLM服务，支持混合检测和高级优化功能'
        }
    };

    const config = titles[panelId] || titles['ai-detection'];
    document.getElementById('main-title').textContent = config.title;
    document.getElementById('main-description').textContent = config.description;
}

// 移动端侧边栏控制
function toggleSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    sidebar.classList.toggle('open');
    overlay.classList.toggle('show');
}

function closeSidebar() {
    const sidebar = document.getElementById('sidebar');
    const overlay = document.querySelector('.sidebar-overlay');

    sidebar.classList.remove('open');
    overlay.classList.remove('show');
}

/**
 * 检查必需的依赖模块
 */
function checkDependencies() {
    if (window.moduleInitializer) {
        const check = window.moduleInitializer.checkDependencies();
        return check.allRequired;
    }

    // 备用检查方法
    const dependencies = [
        { name: 'aiDetector', obj: window.aiDetector },
        { name: 'zhuqueDetector', obj: window.zhuqueDetector },
        { name: 'hybridDetector', obj: window.hybridDetector },
        { name: 'ollamaManagerV2', obj: window.ollamaManagerV2 }
    ];

    const missing = dependencies.filter(dep => !dep.obj);
    if (missing.length > 0) {
        console.warn('缺少依赖模块:', missing.map(dep => dep.name));
        return false;
    }
    return true;
}

// AI检测功能
function detectAI() {
    const text = document.getElementById('detectText').value.trim();

    if (!text) {
        alert('请输入要检测的文本内容！');
        return;
    }

    if (text.length < 50) {
        alert('文本长度至少需要50个字符才能进行准确检测！');
        return;
    }

    // 检查依赖模块
    if (!checkDependencies()) {
        showErrorResult('检测模块未正确加载，请刷新页面重试');
        return;
    }
    
    // 显示加载状态
    document.getElementById('detectLoading').style.display = 'block';
    document.getElementById('detectResult').style.display = 'none';
    
    // 更新加载提示
    const loadingElement = document.getElementById('detectLoading');
    if (hybridModeEnabled) {
        loadingElement.innerHTML = `
            <div class="spinner"></div>
            <p>正在使用混合检测模式分析文本...</p>
            <small>结合规则算法和LLM模型进行深度检测</small>
            <div class="loading-progress">
                <div class="loading-progress-bar"></div>
            </div>
        `;
    } else {
        loadingElement.innerHTML = `
            <div class="spinner"></div>
            <p>正在分析文本特征...</p>
            <small>使用朱雀算法进行AI内容检测</small>
            <div class="loading-progress">
                <div class="loading-progress-bar"></div>
            </div>
        `;
    }

    // 执行检测
    setTimeout(async () => {
        try {
            let result;

            // 更新检测进度
            updateDetectionProgress('正在初始化检测器...', 10);

            // 检查核心检测器是否可用
            if (typeof aiDetector === 'undefined') {
                throw new Error('AI检测器模块未正确加载');
            }

            if (typeof zhuqueDetector === 'undefined') {
                throw new Error('朱雀检测器模块未正确加载');
            }

            if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
                // 使用专业LLM检测模式
                console.log('🤖 使用专业LLM检测模式');
                updateDetectionProgress('正在使用专业LLM模型分析...', 30);

                try {
                    // 使用专业提示词进行AI检测
                    const llmResult = await ollamaManagerV2.detectAIContent(text, true);
                    updateDetectionProgress('LLM分析完成，正在运行朱雀算法...', 60);

                    // 同时使用朱雀算法和规则算法作为参考
                    const zhuqueResult = zhuqueDetector.detectWithZhuque(text);
                    updateDetectionProgress('正在运行规则检测算法...', 80);
                    const ruleResult = aiDetector.detectAI(text);
                    updateDetectionProgress('正在合并检测结果...', 90);

                    // 合并结果，以LLM为主，朱雀为辅
                    result = {
                        aiProbability: llmResult.data.ai_probability || zhuqueResult.aiProbability,
                        confidence: Math.max(llmResult.data.confidence || 0, zhuqueResult.confidence),
                        analysis: {
                            llm: llmResult.data.analysis || {},
                            zhuque: zhuqueResult.analysis,
                            rule: ruleResult.analysis || {}
                        },
                        evidence: [
                            ...(llmResult.data.evidence || []),
                            ...zhuqueResult.evidence,
                            ...ruleResult.evidence
                        ],
                        recommendation: llmResult.data.recommendation || zhuqueResult.recommendation,
                        llmAnalysis: llmResult.raw_response,
                        zhuqueAnalysis: zhuqueResult,
                        ruleAnalysis: ruleResult,
                        mode: 'professional_llm_zhuque'
                    };

                } catch (llmError) {
                    console.warn('专业LLM检测失败，使用朱雀+规则备用模式:', llmError);

                    // 备用：朱雀算法 + 简化LLM检测
                    try {
                        const simpleLLM = await ollamaManagerV2.detectAIContent(text, false);
                        const zhuqueResult = zhuqueDetector.detectWithZhuque(text);
                        const ruleResult = aiDetector.detectAI(text);

                        // 朱雀算法权重更高
                        result = {
                            aiProbability: Math.round((zhuqueResult.aiProbability * 0.6) + (ruleResult.aiProbability * 0.4)),
                            confidence: Math.max(zhuqueResult.confidence, ruleResult.confidence),
                            analysis: {
                                zhuque: zhuqueResult.analysis,
                                rule: ruleResult.analysis || {}
                            },
                            evidence: [...zhuqueResult.evidence, ...ruleResult.evidence],
                            recommendation: zhuqueResult.recommendation,
                            llmAnalysis: simpleLLM.raw_response,
                            zhuqueAnalysis: zhuqueResult,
                            ruleAnalysis: ruleResult,
                            mode: 'zhuque_enhanced'
                        };
                    } catch (fallbackError) {
                        // 最终备用：朱雀算法优先
                        const zhuqueResult = zhuqueDetector.detectWithZhuque(text);
                        const ruleResult = aiDetector.detectAI(text);

                        result = {
                            aiProbability: zhuqueResult.aiProbability,
                            confidence: zhuqueResult.confidence,
                            analysis: zhuqueResult.analysis,
                            evidence: zhuqueResult.evidence,
                            recommendation: zhuqueResult.recommendation,
                            zhuqueAnalysis: zhuqueResult,
                            ruleAnalysis: ruleResult,
                            mode: 'zhuque_only'
                        };
                    }
                }
            } else if (hybridModeEnabled && typeof hybridDetector !== 'undefined') {
                // 使用传统混合检测器 + 朱雀增强
                const hybridResult = await hybridDetector.detect(text);
                const zhuqueResult = zhuqueDetector.detectWithZhuque(text);

                result = {
                    aiProbability: Math.round((zhuqueResult.aiProbability * 0.7) + (hybridResult.aiProbability * 0.3)),
                    confidence: Math.max(zhuqueResult.confidence, hybridResult.confidence),
                    analysis: {
                        zhuque: zhuqueResult.analysis,
                        hybrid: hybridResult.analysis || {}
                    },
                    evidence: [...zhuqueResult.evidence, ...(hybridResult.evidence || [])],
                    recommendation: zhuqueResult.recommendation,
                    zhuqueAnalysis: zhuqueResult,
                    hybridAnalysis: hybridResult,
                    mode: 'hybrid_zhuque'
                };
            } else {
                // 使用朱雀增强的规则检测器
                const zhuqueResult = zhuqueDetector.detectWithZhuque(text);
                const ruleResult = aiDetector.detectAI(text);

                result = {
                    aiProbability: Math.round((zhuqueResult.aiProbability * 0.8) + (ruleResult.aiProbability * 0.2)),
                    confidence: Math.max(zhuqueResult.confidence, ruleResult.confidence),
                    analysis: {
                        zhuque: zhuqueResult.analysis,
                        rule: ruleResult.analysis || {}
                    },
                    evidence: [...zhuqueResult.evidence, ...ruleResult.evidence],
                    recommendation: zhuqueResult.recommendation,
                    zhuqueAnalysis: zhuqueResult,
                    ruleAnalysis: ruleResult,
                    mode: 'zhuque_rule'
                };
            }

            // 验证结果数据
            if (!result || typeof result.aiProbability !== 'number') {
                throw new Error('检测结果数据格式无效');
            }

            console.log('检测完成，准备显示结果:', result);
            updateDetectionProgress('检测完成，正在生成结果...', 100);

            // 短暂延迟后显示结果，让用户看到100%完成
            setTimeout(() => {
                displayDetectionResult(result);
                showNotification('AI检测完成！', 'success');
            }, 500);

        } catch (error) {
            console.error('检测过程中出现错误:', error);
            showErrorResult(`检测失败: ${error.message}`);
        } finally {
            // 确保加载状态被隐藏
            const loadingElement = document.getElementById('detectLoading');
            if (loadingElement) {
                loadingElement.style.display = 'none';
                loadingElement.innerHTML = `
                    <div class="spinner"></div>
                    <p>正在分析文本内容...</p>
                `;
            }
        }
    }, 1500);
}

// 显示检测结果
function displayDetectionResult(result) {
    try {
        console.log('开始显示检测结果:', result);

        const resultArea = document.getElementById('detectResult');
        const resultDetails = document.getElementById('resultDetails');
        const loadingElement = document.getElementById('detectLoading');

        if (!resultArea || !resultDetails) {
            console.error('detectResult或resultDetails元素不存在');
            return;
        }

        // 确保隐藏加载状态
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        // 验证结果数据
        if (!result || typeof result !== 'object') {
            console.error('检测结果数据无效:', result);
            showErrorResult('检测结果数据无效');
            return;
        }

    // 兼容新旧结果格式
    const aiScore = result.aiProbability || result.score || 0;
    const humanScore = Math.max(0, 100 - aiScore);
    const suspiciousScore = Math.min(aiScore, 20); // 疑似AI分数，最多20%

    // 添加渐入动画效果
    resultArea.style.opacity = '0';
    resultArea.style.transform = 'translateY(20px)';
    resultArea.style.transition = 'opacity 0.5s ease-in-out, transform 0.5s ease-in-out';

    // 延迟显示结果，创建更好的用户体验
    setTimeout(() => {
        // 更新主要分数显示
        updateMainScore(aiScore);

        // 更新检测状态样式
        updateDetectionStatus(aiScore);

        // 更新检测模式
        updateDetectionMode(result.mode);

        // 延迟显示饼图和进度条，创建层次感
        setTimeout(() => {
            updatePieChart(aiScore, humanScore);
        }, 200);

        setTimeout(() => {
            updateBreakdown(humanScore, aiScore, suspiciousScore);
        }, 400);

        setTimeout(() => {
            updateProgressBars(aiScore, result);
        }, 600);

        // 显示结果区域
        resultArea.style.opacity = '1';
        resultArea.style.transform = 'translateY(0)';
    }, 100);

    // 根据分数设置建议文本
    let recommendation;
    if (aiScore < 25) {
        recommendation = result.recommendation || '当前文本AI特征很少，可以放心使用。';
    } else if (aiScore < 60) {
        recommendation = result.recommendation || '建议使用智能优化功能进行改进，降低AI检测率。';
    } else {
        recommendation = result.recommendation || '强烈建议使用学术专业优化功能进行深度改写。';
    }

    // 生成检测模式标签
    const modeLabels = {
        'professional_llm_zhuque': '🔬 专业LLM+朱雀',
        'zhuque_enhanced': '⚡ 朱雀增强',
        'zhuque_only': '🏮 朱雀算法',
        'hybrid_zhuque': '🔄 混合+朱雀',
        'zhuque_rule': '📊 朱雀+规则',
        'rule_only': '📝 规则检测'
    };
    const modeLabel = modeLabels[result.mode] || '📝 基础检测';

    // 生成朱雀分析详情
    let zhuqueDetails = '';
    if (result.zhuqueAnalysis && result.zhuqueAnalysis.analysis) {
        const analysis = result.zhuqueAnalysis.analysis;
        zhuqueDetails = `
            <div class="result-card">
                <h5><i class="fas fa-microscope"></i> 朱雀算法分析</h5>
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                    <div style="background: white; padding: 10px; border-radius: 5px;">
                        <strong>困惑度分析:</strong> ${analysis.perplexity?.score || 'N/A'}/100
                        ${analysis.perplexity?.details ? `<br><small>调整困惑度: ${Math.round(analysis.perplexity.details.adjustedPerplexity * 100) / 100}</small>` : ''}
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 5px;">
                        <strong>结构化特征:</strong> ${analysis.structural?.score || 'N/A'}/100
                        ${analysis.structural?.details ? `<br><small>句子一致性: ${analysis.structural.details.consistencyRatio}</small>` : ''}
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 5px;">
                        <strong>语义一致性:</strong> ${analysis.semantic?.score || 'N/A'}/100
                        ${analysis.semantic?.details ? `<br><small>词汇多样性: ${analysis.semantic.details.lexicalDiversity}</small>` : ''}
                    </div>
                    <div style="background: white; padding: 10px; border-radius: 5px;">
                        <strong>频域特征:</strong> ${analysis.frequency?.score || 'N/A'}/100
                        ${analysis.frequency?.details ? `<br><small>字符熵: ${analysis.frequency.details.entropy}</small>` : ''}
                    </div>
                </div>
                
                ${result.zhuqueAnalysis.technicalDetails ? `
                    <div style="margin-top: 10px; padding: 10px; background: white; border-radius: 5px;">
                        <strong>技术细节:</strong> ${result.zhuqueAnalysis.technicalDetails.method}<br>
                        <strong>特征维度:</strong> ${result.zhuqueAnalysis.technicalDetails.features.join(', ')}<br>
                        <strong>置信区间:</strong> [${result.zhuqueAnalysis.technicalDetails.confidence_interval.map(x => Math.round(x * 100) / 100).join(', ')}]
                    </div>
                ` : ''}
            </div>
        `;
    }

    // 显示详细信息
    resultDetails.innerHTML = `
        <h4><i class="fas fa-chart-line"></i> 检测详情</h4>
        <div class="result-card">
            <p><strong>🎯 检测建议：</strong>${recommendation}</p>
            <p><strong>📊 置信度：</strong>${typeof result.confidence === 'number' ?
                Math.round(result.confidence * 100) + '%' :
                (result.confidence === 'high' ? '高' : result.confidence === 'medium' ? '中' : '低')}</p>
            <p><strong>🔍 检测模式：</strong>${modeLabel}</p>
        </div>

        ${zhuqueDetails}

        ${result.evidence && result.evidence.length > 0 ? `
            <div class="result-card">
                <h5>📋 检测证据：</h5>
                <ul>
                    ${result.evidence.map(evidence => `<li>${evidence}</li>`).join('')}
                </ul>
            </div>
        ` : ''}

        ${result.details && result.details.length > 0 ? `
            <div class="result-card">
                <h5>📋 分析明细：</h5>
                <ul>
                    ${result.details.map(detail => `<li>${detail}</li>`).join('')}
                </ul>
            </div>
        ` : ''}

        ${result.llmAnalysis ? `
            <div class="result-card">
                <h5><i class="fas fa-robot"></i> LLM分析</h5>
                <div style="max-height: 150px; overflow-y: auto; background: white; padding: 10px; border-radius: 5px;">
                    ${result.llmAnalysis.substring(0, 500)}${result.llmAnalysis.length > 500 ? '...' : ''}
                </div>
            </div>
        ` : ''}
    `;
    
        resultArea.style.display = 'block';
        console.log('检测结果显示完成');

    } catch (error) {
        console.error('显示检测结果时出错:', error);
        showErrorResult('显示检测结果时出现错误: ' + error.message);
    }
}

/**
 * 显示错误结果
 */
function showErrorResult(errorMessage) {
    try {
        const resultArea = document.getElementById('detectResult');
        const loadingElement = document.getElementById('detectLoading');

        // 隐藏加载状态
        if (loadingElement) {
            loadingElement.style.display = 'none';
        }

        if (resultArea) {
            resultArea.innerHTML = `
                <div class="ai-detection-result">
                    <div class="error-message" style="text-align: center; padding: 40px; background: #fff5f5; border: 2px solid #dc3545; border-radius: 15px;">
                        <div style="font-size: 3rem; color: #dc3545; margin-bottom: 20px;">
                            <i class="fas fa-exclamation-triangle"></i>
                        </div>
                        <h3 style="color: #dc3545; margin-bottom: 15px;">检测失败</h3>
                        <p style="color: #666; margin-bottom: 20px;">${errorMessage}</p>
                        <div style="margin-top: 20px;">
                            <button class="btn btn-primary" onclick="location.reload()">
                                <i class="fas fa-refresh"></i> 刷新页面
                            </button>
                            <button class="btn btn-secondary" onclick="clearDetection()" style="margin-left: 10px;">
                                <i class="fas fa-eraser"></i> 清空重试
                            </button>
                        </div>
                    </div>
                </div>
            `;
            resultArea.style.display = 'block';
        }
    } catch (error) {
        console.error('显示错误结果时也出错了:', error);
        alert('检测功能出现严重错误，请刷新页面重试');
    }
}

/**
 * 更新主要分数显示
 */
function updateMainScore(aiScore) {
    try {
        const scoreElement = document.getElementById('detectionScoreMain');
        const pieScoreElement = document.getElementById('pieChartScore');
        const warningElement = document.getElementById('aiDetectionWarning');

        // 验证aiScore是有效数字
        const validScore = typeof aiScore === 'number' && !isNaN(aiScore) ? Math.round(aiScore) : 0;

        if (scoreElement) {
            // 数字计数动画
            animateNumber(scoreElement, 0, validScore, 1000, '%');

            // 设置颜色类
            scoreElement.className = 'detection-score-main';
            if (validScore < 25) {
                scoreElement.classList.add('low');
            } else if (validScore < 60) {
                scoreElement.classList.add('medium');
            } else {
                scoreElement.classList.add('high');
            }
        } else {
            console.warn('detectionScoreMain 元素未找到');
        }

        if (pieScoreElement) {
            // 饼图中心的分数也使用动画
            animateNumber(pieScoreElement, 0, validScore, 1200, '%');
        } else {
            console.warn('pieChartScore 元素未找到');
        }

        // 显示或隐藏警告
        if (warningElement) {
            if (validScore >= 75) {
                warningElement.style.display = 'flex';
            } else {
                warningElement.style.display = 'none';
            }
        } else {
            console.warn('aiDetectionWarning 元素未找到');
        }
    } catch (error) {
        console.error('updateMainScore 函数执行错误:', error);
    }
}

/**
 * 更新检测进度
 */
function updateDetectionProgress(message, progress) {
    try {
        const loadingElement = document.getElementById('detectLoading');
        if (!loadingElement) return;

        const messageElement = loadingElement.querySelector('p');
        const smallElement = loadingElement.querySelector('small');

        if (messageElement) {
            messageElement.textContent = message;
        }

        if (smallElement && progress) {
            smallElement.textContent = `检测进度: ${progress}%`;
        }

        console.log(`检测进度: ${progress}% - ${message}`);
    } catch (error) {
        console.warn('更新检测进度失败:', error);
    }
}

/**
 * 数字动画函数
 */
function animateNumber(element, start, end, duration, suffix = '') {
    if (!element) return;

    const startTime = performance.now();
    const range = end - start;

    function updateNumber(currentTime) {
        const elapsed = currentTime - startTime;
        const progress = Math.min(elapsed / duration, 1);

        // 使用缓动函数
        const easeOutQuart = 1 - Math.pow(1 - progress, 4);
        const current = Math.round(start + range * easeOutQuart);

        element.textContent = current + suffix;

        if (progress < 1) {
            requestAnimationFrame(updateNumber);
        }
    }

    requestAnimationFrame(updateNumber);
}

/**
 * 更新检测状态样式
 */
function updateDetectionStatus(aiScore) {
    try {
        const detectionStatus = document.getElementById('detectionStatus');
        if (!detectionStatus) {
            console.warn('detectionStatus 元素未找到');
            return;
        }

        // 验证输入参数
        const validScore = typeof aiScore === 'number' && !isNaN(aiScore) ? Math.max(0, Math.min(100, aiScore)) : 0;

        // 移除所有状态类
        detectionStatus.classList.remove('low', 'medium', 'high');

        // 根据分数设置状态和描述
        let description, statusClass;
        if (validScore < 25) {
            description = '✅ AI特征较少，内容较为自然';
            statusClass = 'low';
        } else if (validScore < 60) {
            description = '⚠️ 存在一定AI特征，建议适当优化';
            statusClass = 'medium';
        } else {
            description = '🚨 AI特征明显，需要重点优化';
            statusClass = 'high';
        }

        // 更新状态描述和样式
        detectionStatus.textContent = description;
        detectionStatus.classList.add(statusClass);

        // 添加淡入动画效果
        detectionStatus.style.transition = 'all 0.5s ease-in-out';
        detectionStatus.style.opacity = '0';
        setTimeout(() => {
            detectionStatus.style.opacity = '1';
        }, 100);
    } catch (error) {
        console.error('updateDetectionStatus 函数执行错误:', error);
    }
}

/**
 * 复制检测结果报告
 */
function copyDetectionResult() {
    const scoreElement = document.getElementById('detectionScoreMain');
    const statusElement = document.getElementById('detectionStatus');
    const modeElement = document.getElementById('detectionMode');
    const detailsElement = document.getElementById('resultDetails');
    
    if (!scoreElement || !statusElement || !detailsElement) {
        alert('未找到检测结果');
        return;
    }
    
    // 创建文本报告
    const score = scoreElement.textContent;
    const status = statusElement.textContent;
    const mode = modeElement.textContent;
    
    // 简单提取详情内容的文本，移除HTML标签
    const detailsText = detailsElement.textContent.replace(/\s+/g, ' ').trim();
    
    // 生成报告文本
    const report = `
==== AI检测报告 ====
AI检测度: ${score}
检测状态: ${status}
检测模式: ${mode}

检测详情:
${detailsText.substring(0, 500)}${detailsText.length > 500 ? '...' : ''}

（报告生成时间: ${new Date().toLocaleString()}）
    `.trim();
    
    // 复制到剪贴板
    navigator.clipboard.writeText(report)
        .then(() => {
            // 显示成功提示
            showNotification('检测报告已复制到剪贴板', 'success');
        })
        .catch(err => {
            console.error('无法复制报告:', err);
            alert('复制失败，请手动复制');
        });
}

/**
 * 更新饼图显示
 */
function updatePieChart(aiScore, humanScore) {
    try {
        const aiCircle = document.getElementById('aiScoreCircle');
        const humanCircle = document.getElementById('humanScoreCircle');

        // 验证输入参数
        const validAiScore = typeof aiScore === 'number' && !isNaN(aiScore) ? Math.max(0, Math.min(100, aiScore)) : 0;
        const validHumanScore = typeof humanScore === 'number' && !isNaN(humanScore) ? Math.max(0, Math.min(100, humanScore)) : 100 - validAiScore;

        if (aiCircle && humanCircle) {
            const circumference = 2 * Math.PI * 40; // r=40

            // 重置初始状态
            aiCircle.style.strokeDasharray = `0 ${circumference}`;
            humanCircle.style.strokeDasharray = `0 ${circumference}`;

            // 使用requestAnimationFrame确保动画流畅
            requestAnimationFrame(() => {
                // 计算AI部分的弧长
                const aiArcLength = (validAiScore / 100) * circumference;
                const humanArcLength = (validHumanScore / 100) * circumference;

                // 设置AI部分（红色）
                aiCircle.style.strokeDasharray = `${aiArcLength} ${circumference}`;
                aiCircle.style.strokeDashoffset = '0';

                // 设置人工部分（绿色），从AI部分结束的地方开始
                humanCircle.style.strokeDasharray = `${humanArcLength} ${circumference}`;
                humanCircle.style.strokeDashoffset = `-${aiArcLength}`;
            });

            // 添加动画效果
            aiCircle.style.transition = 'stroke-dasharray 1.5s cubic-bezier(0.4, 0, 0.2, 1)';
            humanCircle.style.transition = 'stroke-dasharray 1.5s cubic-bezier(0.4, 0, 0.2, 1), stroke-dashoffset 1.5s cubic-bezier(0.4, 0, 0.2, 1)';
        } else {
            console.warn('饼图元素未找到:', {
                aiCircle: !!aiCircle,
                humanCircle: !!humanCircle
            });
        }
    } catch (error) {
        console.error('updatePieChart 函数执行错误:', error);
    }
}

/**
 * 更新分类展示
 */
function updateBreakdown(humanScore, aiScore, suspiciousScore) {
    try {
        const humanElement = document.getElementById('humanPercentage');
        const aiElement = document.getElementById('aiPercentage');
        const suspiciousElement = document.getElementById('suspiciousPercentage');

        // 验证输入参数
        const validHumanScore = typeof humanScore === 'number' && !isNaN(humanScore) ? Math.max(0, Math.min(100, humanScore)) : 0;
        const validAiScore = typeof aiScore === 'number' && !isNaN(aiScore) ? Math.max(0, Math.min(100, aiScore)) : 0;
        const validSuspiciousScore = typeof suspiciousScore === 'number' && !isNaN(suspiciousScore) ? Math.max(0, Math.min(100, suspiciousScore)) : 0;

        if (humanElement) {
            // 使用数字动画
            animateNumber(humanElement, 0, Math.round(validHumanScore), 800, '%');
            // 添加动画效果
            humanElement.style.transition = 'all 0.5s ease-in-out';
        } else {
            console.warn('humanPercentage 元素未找到');
        }

        if (aiElement) {
            // 使用数字动画
            animateNumber(aiElement, 0, Math.round(validAiScore), 900, '%');
            // 添加动画效果
            aiElement.style.transition = 'all 0.5s ease-in-out';
        } else {
            console.warn('aiPercentage 元素未找到');
        }

        if (suspiciousElement) {
            // 使用数字动画
            animateNumber(suspiciousElement, 0, Math.round(validSuspiciousScore), 700, '%');
            // 添加动画效果
            suspiciousElement.style.transition = 'all 0.5s ease-in-out';
        } else {
            console.warn('suspiciousPercentage 元素未找到');
        }
    } catch (error) {
        console.error('updateBreakdown 函数执行错误:', error);
    }
}

/**
 * 更新进度条
 */
function updateProgressBars(aiScore, result) {
    try {
        // 验证输入参数
        const validAiScore = typeof aiScore === 'number' && !isNaN(aiScore) ? Math.max(0, Math.min(100, aiScore)) : 0;
        const safeResult = result || {};

        // AI检测分数进度条
        const aiScoreProgress = document.getElementById('aiScoreProgress');
        const aiScoreLabel = document.getElementById('aiScoreLabel');
        if (aiScoreProgress && aiScoreLabel) {
            // 添加平滑过渡效果
            aiScoreProgress.style.transition = 'width 0.8s ease-in-out';
            setTimeout(() => {
                aiScoreProgress.style.width = validAiScore + '%';
            }, 100);
            // 使用数字动画
            animateNumber(aiScoreLabel, 0, Math.round(validAiScore), 800, '%');
        } else {
            console.warn('AI分数进度条元素未找到:', {
                aiScoreProgress: !!aiScoreProgress,
                aiScoreLabel: !!aiScoreLabel
            });
        }

        // 置信度进度条
        const confidenceProgress = document.getElementById('confidenceProgress');
        const confidenceLabel = document.getElementById('confidenceLabel');
        if (confidenceProgress && confidenceLabel) {
            let confidenceValue = 0;
            if (typeof safeResult.confidence === 'number') {
                confidenceValue = Math.round(safeResult.confidence * 100);
            } else if (safeResult.confidence === 'high') {
                confidenceValue = 85;
            } else if (safeResult.confidence === 'medium') {
                confidenceValue = 65;
            } else {
                confidenceValue = 45;
            }

            // 添加平滑过渡效果
            confidenceProgress.style.transition = 'width 0.8s ease-in-out';
            setTimeout(() => {
                confidenceProgress.style.width = confidenceValue + '%';
            }, 200);
            // 使用数字动画
            animateNumber(confidenceLabel, 0, confidenceValue, 900, '%');
        } else {
            console.warn('置信度进度条元素未找到:', {
                confidenceProgress: !!confidenceProgress,
                confidenceLabel: !!confidenceLabel
            });
        }

        // 文本复杂度进度条
        const complexityProgress = document.getElementById('complexityProgress');
        const complexityLabel = document.getElementById('complexityLabel');
        if (complexityProgress && complexityLabel) {
            // 基于文本特征计算复杂度
            let complexityValue = 50; // 默认值
            if (safeResult.zhuqueAnalysis && safeResult.zhuqueAnalysis.analysis) {
                const analysis = safeResult.zhuqueAnalysis.analysis;
                complexityValue = Math.round((
                    (analysis.perplexity?.score || 50) +
                    (analysis.structural?.score || 50) +
                    (analysis.semantic?.score || 50)
                ) / 3);
            }

            // 添加平滑过渡效果
            complexityProgress.style.transition = 'width 0.8s ease-in-out';
            setTimeout(() => {
                complexityProgress.style.width = complexityValue + '%';
            }, 300);
            // 使用数字动画
            animateNumber(complexityLabel, 0, complexityValue, 1000, '%');
        } else {
            console.warn('复杂度进度条元素未找到:', {
                complexityProgress: !!complexityProgress,
                complexityLabel: !!complexityLabel
            });
        }
    } catch (error) {
        console.error('updateProgressBars 函数执行错误:', error);
    }
}

/**
 * 更新检测模式显示
 */
function updateDetectionMode(mode) {
    try {
        const modeElement = document.getElementById('detectionMode');
        if (modeElement) {
            const modeLabels = {
                'professional_llm_zhuque': '🔬 专业LLM+朱雀',
                'zhuque_enhanced': '⚡ 朱雀增强',
                'zhuque_only': '🏮 朱雀算法',
                'hybrid_zhuque': '🔄 混合+朱雀',
                'zhuque_rule': '📊 朱雀+规则',
                'rule_only': '📝 规则检测'
            };

            const modeText = modeLabels[mode] || '📝 基础检测';
            modeElement.textContent = modeText;

            // 添加淡入效果
            modeElement.style.transition = 'opacity 0.3s ease-in-out';
            modeElement.style.opacity = '0';
            setTimeout(() => {
                modeElement.style.opacity = '1';
            }, 100);
        } else {
            console.warn('detectionMode 元素未找到');
        }
    } catch (error) {
        console.error('updateDetectionMode 函数执行错误:', error);
    }
}

// 复制到优化器
function copyToOptimizer() {
    const detectText = document.getElementById('detectText').value;
    document.getElementById('optimizeText').value = detectText;
    
    // 滚动到优化区域
    document.getElementById('optimizeText').scrollIntoView({ behavior: 'smooth' });
    document.getElementById('optimizeText').focus();
}

// 清空检测内容
function clearDetection() {
    document.getElementById('detectText').value = '';
    document.getElementById('detectResult').style.display = 'none';
}

// 智能优化功能 - 深度LLM集成版本
async function optimizeText() {
    const text = document.getElementById('optimizeText').value.trim();

    if (!text) {
        alert('请输入要优化的文本内容！');
        return;
    }

    // 显示加载状态
    const loadingElement = document.getElementById('optimizeLoading');
    loadingElement.style.display = 'block';
    document.getElementById('optimizeResult').style.display = 'none';

    // 更新加载提示
    if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
        loadingElement.innerHTML = `
            <div class="spinner"></div>
            <p>正在使用专业LLM进行智能优化...</p>
            <small>基于最新学术研究的提示词工程</small>
        `;
    }

    try {
        let result;

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            // 使用专业LLM优化模式
            console.log('🤖 使用专业LLM优化模式');

            try {
                // 使用专业提示词进行智能改写
                const llmResult = await ollamaManagerV2.intelligentRewrite(text, true);

                // 同时使用传统优化器作为参考
                const traditionalResult = textOptimizer.optimize(text);

                // 合并结果，以LLM为主
                result = {
                    optimizedText: llmResult.data.optimized_text || llmResult.raw_response,
                    improvements: llmResult.data.improvements || traditionalResult.improvements,
                    qualityScore: llmResult.data.quality_score || 0.9,
                    changesSummary: llmResult.data.changes_summary || '使用专业LLM进行智能优化',
                    llmAnalysis: llmResult.raw_response,
                    traditionalResult: traditionalResult,
                    mode: 'professional_llm'
                };

            } catch (llmError) {
                console.warn('专业LLM优化失败，使用备用模式:', llmError);

                // 备用：简化LLM优化
                try {
                    const simpleLLM = await ollamaManagerV2.intelligentRewrite(text, false);
                    const traditionalResult = textOptimizer.optimize(text);

                    result = {
                        optimizedText: simpleLLM.data.optimized_text || simpleLLM.raw_response,
                        improvements: traditionalResult.improvements,
                        qualityScore: 0.8,
                        llmAnalysis: simpleLLM.raw_response,
                        mode: 'simple_llm'
                    };
                } catch (fallbackError) {
                    // 最终备用：传统优化器
                    result = textOptimizer.optimize(text);
                    result.mode = 'traditional';
                }
            }
        } else {
            // 使用传统优化器
            console.log('📊 使用传统优化器');
            result = textOptimizer.optimize(text);
            result.mode = 'traditional';
        }

        displayOptimizationResult(result);

    } catch (error) {
        console.error('优化过程中出现错误:', error);
        alert('优化过程中出现错误，请重试！');
    } finally {
        loadingElement.style.display = 'none';
        loadingElement.innerHTML = `
            <div class="spinner"></div>
            <p>正在优化文本内容...</p>
        `;
    }
}

// 显示优化结果
function displayOptimizationResult(result) {
    const resultArea = document.getElementById('optimizeResult');
    const optimizedText = document.getElementById('optimizedText');
    const optimizationSummary = document.getElementById('optimizationSummary');
    
    optimizedText.textContent = result.optimizedText;
    
    optimizationSummary.innerHTML = `
        <h5><i class="fas fa-list-check"></i> 优化摘要</h5>
        <ul style="margin: 10px 0; padding-left: 20px;">
            ${result.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
        </ul>
        <p style="margin-top: 10px; color: #28a745; font-weight: bold;">
            <i class="fas fa-check-circle"></i> 优化完成！建议重新检测以验证效果。
        </p>
    `;
    
    resultArea.style.display = 'block';
}

// 复制优化结果
function copyOptimizedText() {
    const optimizedText = document.getElementById('optimizedText').textContent;
    navigator.clipboard.writeText(optimizedText).then(() => {
        // 临时改变按钮文本
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
        btn.style.background = '#28a745';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(() => {
        alert('复制失败，请手动选择文本复制');
    });
}

// 清空优化内容
function clearOptimization() {
    document.getElementById('optimizeText').value = '';
    document.getElementById('optimizeResult').style.display = 'none';
}

// 清空多轮优化内容
function clearMultiRound() {
    const textArea = document.getElementById('multiRoundText');
    if (textArea) {
        textArea.value = '';
    }
    const resultArea = document.getElementById('multiRoundResult');
    if (resultArea) {
        resultArea.style.display = 'none';
    }
}

// 学术专业优化相关功能
function openAcademicOptimizer() {
    // 切换到学术专业优化面板
    showPanel('academic-optimization');

    // 如果有优化文本，复制到学术优化面板
    const text = document.getElementById('optimizeText').value.trim();
    if (text) {
        document.getElementById('academicText').value = text;
    }
}

// 高级学术架构标签页切换
function showAdvancedTab(tabId) {
    // 隐藏所有标签内容
    const tabContents = document.querySelectorAll('.advanced-tab-content');
    tabContents.forEach(content => content.classList.remove('active'));

    // 移除所有标签按钮的激活状态
    const tabBtns = document.querySelectorAll('.tab-btn');
    tabBtns.forEach(btn => btn.classList.remove('active'));

    // 显示目标标签内容
    const targetContent = document.getElementById(tabId + '-tab');
    if (targetContent) {
        targetContent.classList.add('active');
    }

    // 激活对应的标签按钮
    const targetBtn = document.querySelector(`[onclick="showAdvancedTab('${tabId}')"]`);
    if (targetBtn) {
        targetBtn.classList.add('active');
    }
}

// 动态对比引擎功能
async function performDynamicComparison() {
    const textA = document.getElementById('comparisonTextA').value.trim();
    const textB = document.getElementById('comparisonTextB').value.trim();

    if (!textA || !textB) {
        alert('请输入两段需要对比的学术内容！');
        return;
    }

    if (textA.length < 100 || textB.length < 100) {
        alert('每段文本长度至少需要100个字符才能进行准确对比分析！');
        return;
    }

    const loadingElement = document.getElementById('comparisonLoading');
    const resultElement = document.getElementById('comparisonResult');

    loadingElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        let result;

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            // 使用专业LLM进行学术对比分析
            console.log('🤖 使用专业LLM学术对比分析');

            try {
                const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('comparison', {
                    textA: textA,
                    textB: textB
                });

                result = llmResult.data;
                result.mode = 'professional_llm';

            } catch (llmError) {
                console.warn('LLM学术分析失败，使用传统方法:', llmError);
                result = academicOptimizer.performDynamicComparison(textA, textB);
                result.mode = 'traditional';
            }
        } else {
            // 使用传统学术优化器
            result = academicOptimizer.performDynamicComparison(textA, textB);
            result.mode = 'traditional';
        }

        // 显示对比结果
        displayComparisonResult(result);

    } catch (error) {
        console.error('对比分析失败:', error);
        alert('对比分析过程中出现错误，请重试！');
    } finally {
        loadingElement.style.display = 'none';
    }
}

// 优势解构模块功能
async function extractCoreAdvantages() {
    const text = document.getElementById('advantagesText').value.trim();

    if (!text) {
        alert('请输入需要分析的学术文本内容！');
        return;
    }

    if (text.length < 200) {
        alert('文本长度至少需要200个字符才能进行准确的优势解构分析！');
        return;
    }

    const loadingElement = document.getElementById('advantagesLoading');
    const resultElement = document.getElementById('advantagesResult');

    loadingElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        let result;

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            // 使用专业LLM进行优势解构分析
            console.log('🤖 使用专业LLM优势解构分析');

            try {
                const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('extraction', {
                    text: text
                });

                result = llmResult.data;
                result.mode = 'professional_llm';

            } catch (llmError) {
                console.warn('LLM优势分析失败，使用传统方法:', llmError);
                result = academicOptimizer.extractCoreAdvantages(text);
                result.mode = 'traditional';
            }
        } else {
            // 使用传统学术优化器
            result = academicOptimizer.extractCoreAdvantages(text);
            result.mode = 'traditional';
        }

        // 显示分析结果
        displayAdvantagesResult(result);

    } catch (error) {
        console.error('优势解构分析失败:', error);
        alert('优势解构分析过程中出现错误，请重试！');
    } finally {
        loadingElement.style.display = 'none';
    }
}

// 融合生成协议功能
async function generateFusedOptimization() {
    const textA = document.getElementById('fusionTextA').value.trim();
    const textB = document.getElementById('fusionTextB').value.trim();

    if (!textA || !textB) {
        alert('请输入两段需要融合的学术内容！');
        return;
    }

    // 获取优化选项
    const options = {
        targetStructure: document.getElementById('jacsStructure').checked ? 'JACS' : 'standard',
        maxWords: 4000,
        innovationDensity: document.getElementById('innovationDensity').checked ? 3 : 0,
        includeEthicsStatement: document.getElementById('ethicsCompliance').checked,
        enableVisualization: document.getElementById('visualComponents').checked
    };

    const loadingElement = document.getElementById('fusionLoading');
    const resultElement = document.getElementById('fusionResult');

    loadingElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        let result;

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            // 使用专业LLM进行融合生成
            console.log('🤖 使用专业LLM融合生成');

            try {
                const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('fusion', {
                    text1: textA,
                    text2: textB
                });

                result = llmResult.data;
                result.mode = 'professional_llm';

            } catch (llmError) {
                console.warn('LLM融合生成失败，使用传统方法:', llmError);
                result = academicOptimizer.generateFusedOptimization(textA, textB, options);
                result.mode = 'traditional';
            }
        } else {
            // 使用传统学术优化器
            result = academicOptimizer.generateFusedOptimization(textA, textB, options);
            result.mode = 'traditional';
        }

        // 显示融合结果
        displayFusionResult(result);

    } catch (error) {
        console.error('融合生成失败:', error);
        alert('融合生成过程中出现错误，请重试！');
    } finally {
        loadingElement.style.display = 'none';
    }
}

// 质量控制验证
async function performQualityControl() {
    const textA = document.getElementById('fusionTextA').value.trim();
    const textB = document.getElementById('fusionTextB').value.trim();

    if (!textA && !textB) {
        alert('请输入需要进行质量控制的文本内容！');
        return;
    }

    const text = textA + ' ' + textB;

    try {
        const result = academicOptimizer.performQualityControl(text);

        // 显示质量控制结果
        displayQualityControlResult(result);

    } catch (error) {
        console.error('质量控制验证失败:', error);
        alert('质量控制验证过程中出现错误，请重试！');
    }
}

// 清空功能
function clearComparison() {
    document.getElementById('comparisonTextA').value = '';
    document.getElementById('comparisonTextB').value = '';
    document.getElementById('comparisonResult').style.display = 'none';
}

function clearAdvantages() {
    document.getElementById('advantagesText').value = '';
    document.getElementById('advantagesResult').style.display = 'none';
}

function clearFusion() {
    document.getElementById('fusionTextA').value = '';
    document.getElementById('fusionTextB').value = '';
    document.getElementById('fusionResult').style.display = 'none';
}

// 显示对比分析结果
function displayComparisonResult(result) {
    const resultArea = document.getElementById('comparisonResult');

    // 生成对比矩阵HTML
    const matrixHTML = generateComparisonMatrixHTML(result.comparisonMatrix);

    // 生成融合推荐HTML
    const recommendationsHTML = generateRecommendationsHTML(result.fusionRecommendations);

    resultArea.innerHTML = `
        <h4><i class="fas fa-chart-line"></i> 多维对比分析结果</h4>

        <!-- 对比矩阵 -->
        <div style="margin: 20px 0;">
            <h5>📊 对比矩阵</h5>
            ${matrixHTML}
        </div>

        <!-- 各维度详细分析 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">${result.structuralCompliance.textA.score}% vs ${result.structuralCompliance.textB.score}%</div>
                <div class="metric-label">结构规范性</div>
                <div class="metric-description">${result.structuralCompliance.comparison}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${result.innovationIndex.textA.density.toFixed(1)} vs ${result.innovationIndex.textB.density.toFixed(1)}</div>
                <div class="metric-label">创新点密度 (点/千字)</div>
                <div class="metric-description">${result.innovationIndex.comparison}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${result.evidenceLevel.textA.evidenceQuality} vs ${result.evidenceLevel.textB.evidenceQuality}</div>
                <div class="metric-label">证据质量等级</div>
                <div class="metric-description">${result.evidenceLevel.comparison}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${result.dataIntegrity.textA.integrityLevel} vs ${result.dataIntegrity.textB.integrityLevel}</div>
                <div class="metric-label">数据完整性</div>
                <div class="metric-description">${result.dataIntegrity.comparison}</div>
            </div>
        </div>

        <!-- 融合推荐 -->
        <div style="margin: 20px 0;">
            <h5>🔗 智能融合推荐</h5>
            ${recommendationsHTML}
        </div>

        <div style="margin-top: 20px;">
            <button class="btn btn-success" onclick="copyComparisonResult()">
                <i class="fas fa-copy"></i> 复制分析报告
            </button>
        </div>
    `;

    resultArea.style.display = 'block';
}

// 显示优势解构结果
function displayAdvantagesResult(result) {
    const resultArea = document.getElementById('advantagesResult');

    resultArea.innerHTML = `
        <h4><i class="fas fa-microscope"></i> 核心优势解构报告</h4>

        <!-- 综合评分 -->
        <div style="text-align: center; margin: 20px 0;">
            <div style="font-size: 2rem; font-weight: bold; color: ${getScoreColor(result.overallScore)};">
                ${result.overallScore.toFixed(1)}分
            </div>
            <div style="color: #666;">综合优势评分</div>
        </div>

        <!-- 各维度详细分析 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value" style="color: ${getScoreColor(result.theoreticalContribution.score)};">
                    ${result.theoreticalContribution.score}分
                </div>
                <div class="metric-label">理论贡献值</div>
                <div class="metric-description">${result.theoreticalContribution.level} (创新比例: ${result.theoreticalContribution.ratio.toFixed(1)}%)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" style="color: ${getScoreColor(result.technicalNovelty.score)};">
                    ${result.technicalNovelty.score}分
                </div>
                <div class="metric-label">技术新颖度</div>
                <div class="metric-description">${result.technicalNovelty.level} (新技术比例: ${result.technicalNovelty.ratio.toFixed(1)}%)</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" style="color: ${getScoreColor(result.argumentRigor.score)};">
                    ${result.argumentRigor.score}分
                </div>
                <div class="metric-label">论证严谨性</div>
                <div class="metric-description">${result.argumentRigor.level}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value" style="color: ${getScoreColor(result.interdisciplinaryFusion.score)};">
                    ${result.interdisciplinaryFusion.score}分
                </div>
                <div class="metric-label">跨学科融合度</div>
                <div class="metric-description">${result.interdisciplinaryFusion.level}</div>
            </div>
        </div>

        <!-- 改进建议 -->
        ${result.recommendations.length > 0 ? `
        <div style="margin: 20px 0;">
            <h5>💡 改进建议</h5>
            <ul class="recommendations-list">
                ${result.recommendations.map(rec => `
                    <li class="${rec.priority === 'high' ? 'high-priority' : ''}">
                        <strong>${rec.type}:</strong> ${rec.suggestion}
                        <span style="float: right; font-size: 0.8em; color: #666;">优先级: ${rec.priority}</span>
                    </li>
                `).join('')}
            </ul>
        </div>
        ` : ''}

        <div style="margin-top: 20px;">
            <button class="btn btn-success" onclick="copyAdvantagesReport()">
                <i class="fas fa-copy"></i> 复制分析报告
            </button>
        </div>
    `;

    resultArea.style.display = 'block';
}

// 显示融合生成结果
function displayFusionResult(result) {
    const resultArea = document.getElementById('fusionResult');

    resultArea.innerHTML = `
        <h4><i class="fas fa-magic"></i> 融合优化结果</h4>

        <!-- 融合文本 -->
        <div style="margin: 20px 0;">
            <h5>📝 融合优化文本</h5>
            <div style="background: white; padding: 20px; border-radius: 8px; border: 2px solid #28a745; line-height: 1.6;">
                ${result.fusedText}
            </div>
        </div>

        <!-- 质量指标 -->
        <div class="metrics-grid">
            <div class="metric-card">
                <div class="metric-value">${result.qualityMetrics.disruptiveIndex}</div>
                <div class="metric-label">颠覆性指数</div>
                <div class="metric-description">${result.qualityMetrics.disruptiveIndex >= 0.35 ? '✅ 达到Science级别' : '⚠️ 需要提升'}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${result.qualityMetrics.innovationDensity}点/千字</div>
                <div class="metric-label">创新点密度</div>
                <div class="metric-description">${result.qualityMetrics.innovationDensity >= 3 ? '✅ 达到Nature基准' : '⚠️ 需要提升'}</div>
            </div>
            <div class="metric-card">
                <div class="metric-value">${result.qualityMetrics.overallQuality}分</div>
                <div class="metric-label">综合质量评分</div>
                <div class="metric-description">${getQualityLevel(result.qualityMetrics.overallQuality)}</div>
            </div>
        </div>

        <div style="margin-top: 20px;">
            <button class="btn btn-success" onclick="copyFusionResult()">
                <i class="fas fa-copy"></i> 复制融合文本
            </button>
        </div>
    `;

    resultArea.style.display = 'block';
}

// 显示质量控制结果
function displayQualityControlResult(result) {
    // 可以在融合结果中显示质量控制信息
    console.log('质量控制结果:', result);
}

// 辅助函数
function generateComparisonMatrixHTML(matrix) {
    if (!matrix || !matrix.rows) return '<p>暂无对比数据</p>';

    let html = '<table class="comparison-matrix">';

    // 表头
    html += '<thead><tr>';
    matrix.headers.forEach(header => {
        html += `<th>${header}</th>`;
    });
    html += '</tr></thead>';

    // 表格内容
    html += '<tbody>';
    matrix.rows.forEach(row => {
        html += '<tr>';
        row.forEach((cell, index) => {
            let cellClass = '';
            if (index === 3) { // 优势方列
                if (cell === 'A') cellClass = 'winner-a';
                else if (cell === 'B') cellClass = 'winner-b';
                else cellClass = 'tie';
            }
            html += `<td class="${cellClass}">${cell}</td>`;
        });
        html += '</tr>';
    });
    html += '</tbody></table>';

    return html;
}

function generateRecommendationsHTML(recommendations) {
    if (!recommendations || recommendations.length === 0) {
        return '<p>暂无融合推荐</p>';
    }

    let html = '<ul class="recommendations-list">';
    recommendations.forEach(rec => {
        const priorityClass = rec.priority === 'high' ? 'high-priority' : '';
        html += `<li class="${priorityClass}">
            <strong>${rec.type}:</strong> ${rec.description}
            <span style="float: right; font-size: 0.8em; color: #666;">优先级: ${rec.priority}</span>
        </li>`;
    });
    html += '</ul>';

    return html;
}

function getScoreColor(score) {
    if (score >= 80) return '#28a745';
    if (score >= 60) return '#ffc107';
    if (score >= 40) return '#fd7e14';
    return '#dc3545';
}

function getQualityLevel(score) {
    if (score >= 90) return '🏆 顶级期刊水准';
    if (score >= 75) return '🥇 高影响因子期刊';
    if (score >= 60) return '🥈 SCI一区期刊';
    return '🥉 需要进一步完善';
}

// 复制功能
function copyComparisonResult() {
    const resultText = document.getElementById('comparisonResult').innerText;
    navigator.clipboard.writeText(resultText).then(() => {
        showNotification('对比分析报告已复制到剪贴板', 'success');
    });
}

function copyAdvantagesReport() {
    const resultText = document.getElementById('advantagesResult').innerText;
    navigator.clipboard.writeText(resultText).then(() => {
        showNotification('优势解构报告已复制到剪贴板', 'success');
    });
}

function copyFusionResult() {
    const fusionText = document.querySelector('#fusionResult .border-2').innerText;
    navigator.clipboard.writeText(fusionText).then(() => {
        showNotification('融合优化文本已复制到剪贴板', 'success');
    });
}

function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.style.cssText = `
        position: fixed; top: 20px; right: 20px; z-index: 10000;
        background: ${type === 'success' ? '#28a745' : '#007bff'};
        color: white; padding: 12px 20px; border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    `;
    notification.textContent = message;
    document.body.appendChild(notification);

    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

// 执行学术专业优化
function performAcademicOptimization() {
    const text = document.getElementById('academicText').value.trim();
    
    if (!text) {
        alert('请输入要优化的学术文本内容！');
        return;
    }
    
    // 显示加载状态
    document.getElementById('academicLoading').style.display = 'block';
    document.getElementById('academicResult').style.display = 'none';
    
    setTimeout(() => {
        try {
            const result = academicOptimizer.optimize(text);
            displayAcademicResult(result, text);
        } catch (error) {
            console.error('学术优化过程中出现错误:', error);
            alert('学术优化过程中出现错误，请重试！');
        } finally {
            document.getElementById('academicLoading').style.display = 'none';
        }
    }, 2500);
}

// 显示学术优化结果
function displayAcademicResult(result, originalText) {
    const resultArea = document.getElementById('academicResult');
    const summaryArea = document.getElementById('academicSummary');
    const optimizedTextArea = document.getElementById('academicOptimizedText');
    const detailsArea = document.getElementById('academicDetails');
    
    // 显示优化摘要统计
    summaryArea.innerHTML = `
        <div style="background: #e7f3ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h5>📊 优化统计</h5>
            <p><strong>原文词数：</strong>${result.summary.originalWordCount}</p>
        </div>
        <div style="background: #f0f9ff; padding: 15px; border-radius: 8px; text-align: center;">
            <h5>🎯 优化词数</h5>
            <p><strong>${result.summary.optimizedWordCount}</strong></p>
        </div>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; text-align: center;">
            <h5>📈 学术性评分</h5>
            <p><strong>${result.summary.academicScore.toFixed(1)}分</strong></p>
        </div>
        <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; text-align: center;">
            <h5>✨ 改进项目</h5>
            <p><strong>${result.summary.improvementCount}项</strong></p>
        </div>
    `;
    
    // 显示优化后的文本
    optimizedTextArea.textContent = result.optimizedText;
    
    // 保存原文用于对比
    document.getElementById('originalText').textContent = originalText;
    document.getElementById('optimizedComparisonText').textContent = result.optimizedText;
    
    // 显示详细改进信息
    detailsArea.innerHTML = `
        <h5><i class="fas fa-list-check"></i> 优化详情</h5>
        <ul style="margin: 10px 0; padding-left: 20px;">
            ${result.improvements.map(improvement => `<li>${improvement}</li>`).join('')}
        </ul>
        <div style="background: #f8f9fa; padding: 15px; border-radius: 8px; margin-top: 15px;">
            <h6>🔧 技术统计</h6>
            <p>• 同义词替换：${result.statistics.synonymReplacements}处</p>
            <p>• 模板优化：${result.statistics.templateReplacements}处</p>
            <p>• 术语增强：${result.statistics.technicalEnhancements}处</p>
            <p>• 结构优化：${result.statistics.structureOptimizations}处</p>
            <p>• 置信度：${result.confidence === 'high' ? '高' : result.confidence === 'medium' ? '中' : '低'}</p>
        </div>
    `;
    
    resultArea.style.display = 'block';
}

// 切换对比视图
function toggleComparisonView() {
    const comparisonView = document.getElementById('comparisonView');
    const toggleText = document.getElementById('comparisonToggleText');
    
    if (comparisonView.style.display === 'none' || !comparisonView.style.display) {
        comparisonView.style.display = 'block';
        toggleText.textContent = '隐藏对比视图';
    } else {
        comparisonView.style.display = 'none';
        toggleText.textContent = '显示对比视图';
    }
}

// 复制学术优化结果
function copyAcademicResult() {
    const optimizedText = document.getElementById('academicOptimizedText').textContent;
    navigator.clipboard.writeText(optimizedText).then(() => {
        const btn = event.target;
        const originalText = btn.innerHTML;
        btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
        btn.style.background = '#28a745';
        
        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = '';
        }, 2000);
    }).catch(() => {
        alert('复制失败，请手动选择文本复制');
    });
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化显示AI检测面板
    showPanel('ai-detection');

    // 响应式处理
    window.addEventListener('resize', function() {
        if (window.innerWidth > 768) {
            closeSidebar();
        }
    });
    
    // 添加键盘快捷键
    document.addEventListener('keydown', function(event) {
        // Ctrl+Enter 快速检测
        if (event.ctrlKey && event.key === 'Enter') {
            const activeElement = document.activeElement;
            if (activeElement.id === 'detectText') {
                detectAI();
            } else if (activeElement.id === 'optimizeText') {
                optimizeText();
            } else if (activeElement.id === 'academicText') {
                performAcademicOptimization();
            }
        }
        
        // ESC 关闭模态框
        if (event.key === 'Escape') {
            closeAcademicOptimizer();
        }
    });
    
    console.log('🤖 AI检测助手已加载完成！');
    console.log('💡 提示：使用 Ctrl+Enter 快速执行检测或优化');

    // 检查核心模块加载状态
    if (typeof aiDetector !== 'undefined') {
        console.log('✅ AI检测器模块已加载');
    } else {
        console.error('❌ AI检测器模块未加载');
    }

    if (typeof zhuqueDetector !== 'undefined') {
        console.log('✅ 朱雀检测器模块已加载');
    } else {
        console.error('❌ 朱雀检测器模块未加载');
    }

    if (typeof zhuqueOptimizer !== 'undefined') {
        console.log('✅ 朱雀优化器模块已加载');
    } else {
        console.error('❌ 朱雀优化器模块未加载');
    }

    // 检查新模块加载状态
    if (typeof llmService !== 'undefined') {
        console.log('✅ LLM服务模块已加载');
        // 初始检查模型可用性
        setTimeout(() => {
            llmService.checkModelAvailability();
        }, 1000);
    } else {
        console.warn('⚠️ LLM服务模块未加载');
    }

    if (typeof hybridDetector !== 'undefined') {
        console.log('✅ 混合检测器模块已加载');
    } else {
        console.warn('⚠️ 混合检测器模块未加载');
    }

    if (typeof multiRoundOptimizer !== 'undefined') {
        console.log('✅ 多轮优化器模块已加载');
    } else {
        console.warn('⚠️ 多轮优化器模块未加载');
    }

    if (typeof llmManager !== 'undefined') {
        console.log('✅ LLM管理器模块已加载');
        // 初始化LLM状态显示
        updateLLMStatusUI(llmManager.getStatus());

        // 开始定期状态检查
        llmManager.startStatusMonitoring();
    } else {
        console.warn('⚠️ LLM管理器模块未加载');
    }
});

// 自动化学术架构优化功能
async function startAutoAcademicOptimization() {
    const textA = document.getElementById('autoTextA').value.trim();
    const textB = document.getElementById('autoTextB').value.trim();

    if (!textA || !textB) {
        alert('请输入两段需要优化的学术内容！');
        return;
    }

    if (textA.length < 100 || textB.length < 100) {
        alert('每段文本长度至少需要100个字符才能进行准确分析！');
        return;
    }

    // 获取配置选项
    const config = {
        jacsStructure: document.getElementById('autoJacsStructure').checked,
        innovationDensity: document.getElementById('autoInnovationDensity').checked,
        ethicsCompliance: document.getElementById('autoEthicsCompliance').checked,
        qualityControl: document.getElementById('autoQualityControl').checked
    };

    // 显示处理状态
    const statusElement = document.getElementById('autoProcessStatus');
    const resultElement = document.getElementById('autoAcademicResult');

    statusElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        // 第一步：动态对比分析
        await updateProcessStep('step1', 'active', '正在进行动态对比分析...', 0);
        const comparisonResult = await performAutoComparison(textA, textB);
        await updateProcessStep('step1', 'completed', '动态对比分析完成', 100);

        // 第二步：优势结构提取
        await updateProcessStep('step2', 'active', '正在提取优势结构...', 0);
        const advantagesA = await performAutoAdvantageExtraction(textA);
        const advantagesB = await performAutoAdvantageExtraction(textB);
        await updateProcessStep('step2', 'completed', '优势结构提取完成', 100);

        // 第三步：智能融合生成
        await updateProcessStep('step3', 'active', '正在进行智能融合生成...', 0);
        const fusionResult = await performAutoFusion(textA, textB, comparisonResult, advantagesA, advantagesB, config);
        await updateProcessStep('step3', 'completed', '智能融合生成完成', 100);

        // 显示综合结果
        displayAutoAcademicResult({
            comparison: comparisonResult,
            advantagesA: advantagesA,
            advantagesB: advantagesB,
            fusion: fusionResult,
            config: config
        });

    } catch (error) {
        console.error('自动化学术架构优化失败:', error);
        alert('优化过程中出现错误，请重试！');
        statusElement.style.display = 'none';
    }
}

// 更新处理步骤状态
async function updateProcessStep(stepId, status, message, progress) {
    const stepElement = document.getElementById(stepId + '-status');
    const statusText = stepElement.querySelector('.status-text');
    const progressFill = stepElement.querySelector('.progress-fill');

    // 移除所有状态类
    stepElement.classList.remove('active', 'completed');

    // 添加新状态
    if (status !== 'waiting') {
        stepElement.classList.add(status);
    }

    // 更新文本和进度
    statusText.textContent = message;
    progressFill.style.width = progress + '%';

    // 添加延迟以显示动画效果
    await new Promise(resolve => setTimeout(resolve, 500));
}

// 执行自动对比分析
async function performAutoComparison(textA, textB) {
    try {
        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('comparison', {
                textA: textA,
                textB: textB
            });
            return llmResult.data;
        } else {
            return academicOptimizer.performDynamicComparison(textA, textB);
        }
    } catch (error) {
        console.warn('LLM对比分析失败，使用传统方法:', error);
        return academicOptimizer.performDynamicComparison(textA, textB);
    }
}

// 执行自动优势提取
async function performAutoAdvantageExtraction(text) {
    try {
        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('extraction', {
                text: text
            });
            return llmResult.data;
        } else {
            return academicOptimizer.extractCoreAdvantages(text);
        }
    } catch (error) {
        console.warn('LLM优势提取失败，使用传统方法:', error);
        return academicOptimizer.extractCoreAdvantages(text);
    }
}

// 执行自动融合生成
async function performAutoFusion(textA, textB, comparison, advantagesA, advantagesB, config) {
    try {
        const fusionOptions = {
            jacsStructure: config.jacsStructure,
            innovationDensity: config.innovationDensity ? 3 : 1,
            ethicsCompliance: config.ethicsCompliance,
            qualityControl: config.qualityControl
        };

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            const llmResult = await ollamaManagerV2.advancedAcademicAnalysis('fusion', {
                textA: textA,
                textB: textB,
                comparison: comparison,
                advantagesA: advantagesA,
                advantagesB: advantagesB,
                options: fusionOptions
            });
            return llmResult.data;
        } else {
            return academicOptimizer.performAdvancedFusion(textA, textB, fusionOptions);
        }
    } catch (error) {
        console.warn('LLM融合生成失败，使用传统方法:', error);
        return academicOptimizer.performAdvancedFusion(textA, textB, {
            jacsStructure: config.jacsStructure,
            innovationDensity: config.innovationDensity ? 3 : 1,
            ethicsCompliance: config.ethicsCompliance
        });
    }
}

// 清空自动化学术架构内容
function clearAutoAcademic() {
    document.getElementById('autoTextA').value = '';
    document.getElementById('autoTextB').value = '';
    document.getElementById('autoProcessStatus').style.display = 'none';
    document.getElementById('autoAcademicResult').style.display = 'none';

    // 重置所有步骤状态
    ['step1', 'step2', 'step3'].forEach(stepId => {
        const stepElement = document.getElementById(stepId + '-status');
        stepElement.classList.remove('active', 'completed');
        stepElement.querySelector('.status-text').textContent = stepId === 'step1' ? '准备中...' : '等待中...';
        stepElement.querySelector('.progress-fill').style.width = '0%';
    });
}

// 显示自动化学术架构结果
function displayAutoAcademicResult(results) {
    const resultElement = document.getElementById('autoAcademicResult');

    const html = `
        <div class="auto-result-container">
            <div class="result-header">
                <h3><i class="fas fa-check-circle"></i> 自动化学术架构优化完成</h3>
                <p class="result-summary">已完成三步自动化流程：动态对比 → 优势结构 → 智能融合</p>
            </div>

            <!-- 第一步结果：动态对比分析 -->
            <div class="result-section">
                <h4><i class="fas fa-balance-scale"></i> 第一步：动态对比分析结果</h4>
                <div class="comparison-summary">
                    <div class="metrics-grid">
                        <div class="metric-item">
                            <span class="metric-label">结构规范性</span>
                            <span class="metric-value">${results.comparison.structuralCompliance?.score || 'N/A'}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">创新性指数</span>
                            <span class="metric-value">${results.comparison.innovationIndex?.score || 'N/A'}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">证据等级</span>
                            <span class="metric-value">${results.comparison.evidenceLevel?.score || 'N/A'}</span>
                        </div>
                        <div class="metric-item">
                            <span class="metric-label">数据完整性</span>
                            <span class="metric-value">${results.comparison.dataIntegrity?.score || 'N/A'}</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第二步结果：优势结构提取 -->
            <div class="result-section">
                <h4><i class="fas fa-search-plus"></i> 第二步：优势结构提取结果</h4>
                <div class="advantages-summary">
                    <div class="text-advantages">
                        <div class="advantage-column">
                            <h5>文本A优势特征</h5>
                            <div class="advantage-score">综合评分: ${Math.round(results.advantagesA.overallScore || 0)}/100</div>
                            <ul class="advantage-list">
                                <li>理论贡献: ${Math.round(results.advantagesA.theoreticalContribution?.score || 0)}/100</li>
                                <li>技术新颖度: ${Math.round(results.advantagesA.technicalNovelty?.score || 0)}/100</li>
                                <li>论证严谨性: ${Math.round(results.advantagesA.argumentRigor?.score || 0)}/100</li>
                            </ul>
                        </div>
                        <div class="advantage-column">
                            <h5>文本B优势特征</h5>
                            <div class="advantage-score">综合评分: ${Math.round(results.advantagesB.overallScore || 0)}/100</div>
                            <ul class="advantage-list">
                                <li>理论贡献: ${Math.round(results.advantagesB.theoreticalContribution?.score || 0)}/100</li>
                                <li>技术新颖度: ${Math.round(results.advantagesB.technicalNovelty?.score || 0)}/100</li>
                                <li>论证严谨性: ${Math.round(results.advantagesB.argumentRigor?.score || 0)}/100</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 第三步结果：智能融合生成 -->
            <div class="result-section">
                <h4><i class="fas fa-magic"></i> 第三步：智能融合生成结果</h4>
                <div class="fusion-result">
                    <div class="fusion-text">
                        <h5>优化后的融合文本</h5>
                        <div class="optimized-content" id="autoOptimizedText">
                            ${results.fusion.optimizedText || '融合文本生成中...'}
                        </div>
                    </div>

                    <div class="fusion-metrics">
                        <h5>优化指标</h5>
                        <div class="metrics-grid">
                            <div class="metric-item">
                                <span class="metric-label">JACS结构</span>
                                <span class="metric-value ${results.config.jacsStructure ? 'enabled' : 'disabled'}">
                                    ${results.config.jacsStructure ? '✓ 已应用' : '✗ 未应用'}
                                </span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">创新点密度</span>
                                <span class="metric-value ${results.config.innovationDensity ? 'enabled' : 'disabled'}">
                                    ${results.config.innovationDensity ? '✓ ≥3个/千字' : '✗ 标准密度'}
                                </span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">伦理规范</span>
                                <span class="metric-value ${results.config.ethicsCompliance ? 'enabled' : 'disabled'}">
                                    ${results.config.ethicsCompliance ? '✓ 已包含' : '✗ 未包含'}
                                </span>
                            </div>
                            <div class="metric-item">
                                <span class="metric-label">质量控制</span>
                                <span class="metric-value ${results.config.qualityControl ? 'enabled' : 'disabled'}">
                                    ${results.config.qualityControl ? '✓ 已验证' : '✗ 未验证'}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 操作按钮 -->
            <div class="result-actions">
                <button class="btn btn-success" onclick="copyAutoResult()">
                    <i class="fas fa-copy"></i> 复制优化结果
                </button>
                <button class="btn btn-info" onclick="downloadAutoResult()">
                    <i class="fas fa-download"></i> 下载完整报告
                </button>
                <button class="btn btn-warning" onclick="startAutoAcademicOptimization()">
                    <i class="fas fa-redo"></i> 重新优化
                </button>
            </div>
        </div>
    `;

    resultElement.innerHTML = html;
    resultElement.style.display = 'block';

    // 隐藏处理状态
    document.getElementById('autoProcessStatus').style.display = 'none';
}

// 复制自动化优化结果
function copyAutoResult() {
    const optimizedText = document.getElementById('autoOptimizedText').textContent;
    navigator.clipboard.writeText(optimizedText).then(() => {
        alert('优化结果已复制到剪贴板！');
    }).catch(() => {
        alert('复制失败，请手动选择文本复制');
    });
}

// 下载自动化优化完整报告
function downloadAutoResult() {
    const resultElement = document.getElementById('autoAcademicResult');
    const content = resultElement.textContent;

    const blob = new Blob([content], { type: 'text/plain;charset=utf-8' });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = `自动化学术架构优化报告_${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

// 切换LLM模式（使用Ollama v2）
async function toggleLLMMode() {
    const btn = document.getElementById('llmModeBtn');
    const text = document.getElementById('llmModeText');

    if (!hybridModeEnabled) {
        // 启用Ollama模式
        await enableOllamaMode(btn, text);
    } else {
        // 禁用Ollama模式
        await disableOllamaMode(btn, text);
    }
}

/**
 * 启用Ollama模式 - 使用v2管理器
 */
async function enableOllamaMode(btn, text) {
    // 显示启动状态
    btn.disabled = true;
    btn.className = 'btn btn-warning';
    text.textContent = '连接中...';

    try {
        console.log('🚀 开始连接Ollama服务v2...');

        // 检查Ollama服务状态
        const isConnected = await ollamaManagerV2.checkConnection();

        if (isConnected && ollamaManagerV2.state.currentModel) {
            // 连接成功
            hybridModeEnabled = true;
            btn.className = 'btn btn-success';
            text.textContent = 'Ollama已启用';
            btn.disabled = false;

            console.log('✅ Ollama连接成功');
            showNotification(`🎉 Ollama服务连接成功！当前模型：${ollamaManagerV2.state.currentModel}`, 'success');
        } else if (isConnected && !ollamaManagerV2.state.currentModel) {
            // 服务在线但未选择模型
            btn.className = 'btn btn-warning';
            text.textContent = '请选择模型';
            btn.disabled = false;

            showNotification('⚠️ Ollama服务已连接，请在LLM控制面板中选择模型', 'warning');
            showPanel('llm-control');
        } else {
            // 连接失败
            btn.className = 'btn btn-danger';
            text.textContent = '连接失败';
            btn.disabled = false;

            showNotification('❌ 无法连接到Ollama服务，请确保Ollama已启动', 'error');
            showPanel('llm-control');
        }

    } catch (error) {
        console.error('❌ Ollama连接过程中发生错误:', error);

        // 回退到初始状态
        hybridModeEnabled = false;
        btn.className = 'btn btn-info';
        text.textContent = '启用Ollama';
        btn.disabled = false;

        showNotification(`连接失败: ${error.message}`, 'error');
    }
}

/**
 * 禁用Ollama模式
 */
async function disableOllamaMode(btn, text) {
    btn.disabled = true;
    btn.className = 'btn btn-warning';
    text.textContent = '断开中...';

    try {
        hybridModeEnabled = false;
        console.log('🛑 正在关闭Ollama混合检测模式...');

        btn.className = 'btn btn-info';
        text.textContent = '启用Ollama';
        btn.disabled = false;

        showNotification('Ollama混合检测模式已关闭', 'info');

    } catch (error) {
        console.error('断开Ollama连接失败:', error);

        btn.className = 'btn btn-info';
        text.textContent = '启用Ollama';
        btn.disabled = false;

        showNotification('断开连接时发生错误', 'error');
    }
}

// 已删除不再需要的LLM启动失败处理函数

// 已删除不再需要的LLM进度显示函数

// 已删除不再需要的LLM样式函数

// 专业LLM多轮优化函数
async function performLLMMultiRoundOptimization(text, options) {
    const maxRounds = options.maxRounds || 5;
    const targetScore = options.targetScore || 15;

    let currentText = text;
    let currentRound = 1;
    let optimizationHistory = [];
    let finalScore = 100;

    try {
        while (currentRound <= maxRounds) {
            // 更新进度显示
            updateOptimizationProgress(currentRound, finalScore, `第${currentRound}轮优化`);

            // 执行当前轮优化
            const roundResult = await ollamaManagerV2.multiRoundOptimization(
                currentText,
                currentRound,
                currentRound > 1 ? optimizationHistory[currentRound - 2].feedback : '',
                getRoundFocus(currentRound)
            );

            if (roundResult.success) {
                currentText = roundResult.data.optimized_text || currentText;

                // 评估优化质量
                const evaluation = await ollamaManagerV2.evaluateOptimization(
                    text,
                    currentText,
                    '降低AI检测率并提升文本质量'
                );

                // 使用AI检测评估当前分数
                const detectionResult = await ollamaManagerV2.detectAIContent(currentText, true);
                finalScore = Math.round((detectionResult.data.ai_probability || 0.5) * 100);

                // 记录优化历史
                optimizationHistory.push({
                    round: currentRound,
                    text: currentText,
                    score: finalScore,
                    improvements: roundResult.data.improvements || [],
                    feedback: evaluation.data.feedback || '',
                    qualityImprovement: roundResult.data.quality_improvement || 0
                });

                // 检查是否达到目标
                if (finalScore <= targetScore || !evaluation.data.continue_optimization) {
                    break;
                }
            } else {
                console.warn(`第${currentRound}轮优化失败`);
                break;
            }

            currentRound++;
        }

        return {
            success: true,
            finalText: currentText,
            finalScore: finalScore,
            totalRounds: currentRound - 1,
            optimizationHistory: optimizationHistory,
            mode: 'professional_llm'
        };

    } catch (error) {
        console.error('LLM多轮优化失败:', error);

        // 备用：使用传统多轮优化器
        return await multiRoundOptimizer.optimize(text, options);
    }
}

// 获取轮次优化重点
function getRoundFocus(round) {
    const focuses = [
        '基础语言优化和流畅性提升',
        '结构逻辑优化和连贯性增强',
        '专业性提升和术语优化',
        '创新性增强和个性化表达',
        '最终质量检查和细节完善'
    ];

    return focuses[round - 1] || '综合质量提升';
}

// 更新优化进度显示
function updateOptimizationProgress(round, score, strategy) {
    const roundElement = document.getElementById('currentRound');
    const scoreElement = document.getElementById('currentScore');
    const strategyElement = document.getElementById('currentStrategy');

    if (roundElement) roundElement.textContent = `第${round}轮`;
    if (scoreElement) scoreElement.textContent = `${score}%`;
    if (strategyElement) strategyElement.textContent = strategy;
}

// 多轮优化功能
async function startMultiRoundOptimization() {
    if (multiRoundInProgress) {
        alert('多轮优化正在进行中，请等待完成！');
        return;
    }

    // 根据当前面板获取文本
    let text = '';
    if (currentPanel === 'multi-round') {
        text = document.getElementById('multiRoundText').value.trim();
    } else {
        text = document.getElementById('optimizeText').value.trim();
    }

    if (!text) {
        alert('请输入要优化的文本内容！');
        return;
    }

    multiRoundInProgress = true;

    // 根据当前面板选择正确的加载和结果元素
    let loadingElement, resultElement;
    if (currentPanel === 'multi-round') {
        loadingElement = document.getElementById('multiRoundLoading');
        resultElement = document.getElementById('multiRoundResult');
    } else {
        loadingElement = document.getElementById('optimizeLoading');
        resultElement = document.getElementById('optimizeResult');
        // 为非多轮面板更新加载内容
        loadingElement.innerHTML = `
            <div class="spinner"></div>
            <p>正在进行多轮优化...</p>
            <div class="progress-info">
                <span id="currentRound">第1轮</span> |
                <span id="currentScore">检测中...</span> |
                <span id="currentStrategy">初始化...</span>
            </div>
        `;
    }

    // 显示加载状态
    loadingElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        // 配置优化选项
        const options = {
            targetScore: 15,
            maxRounds: 5,
            preserveSemantics: true,
            enableLLM: hybridModeEnabled
        };

        // 执行多轮优化
        let result;

        if (hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected) {
            // 使用专业LLM多轮优化
            console.log('🤖 使用专业LLM多轮优化');
            result = await performLLMMultiRoundOptimization(text, options);
        } else {
            // 使用传统多轮优化器
            result = await multiRoundOptimizer.optimize(text, options);
        }

        // 显示结果
        displayMultiRoundResult(result);

    } catch (error) {
        console.error('多轮优化失败:', error);
        resultElement.innerHTML = `
            <div class="error-message">
                <h4><i class="fas fa-exclamation-triangle"></i> 优化失败</h4>
                <p>错误信息: ${error.message}</p>
                <p>请检查网络连接和模型服务状态</p>
            </div>
        `;
        resultElement.style.display = 'block';
    } finally {
        loadingElement.style.display = 'none';
        multiRoundInProgress = false;
    }
}

// 显示多轮优化结果
function displayMultiRoundResult(result) {
    const resultElement = document.getElementById('optimizeResult');

    let statusIcon = result.success ? 'fas fa-check-circle' : 'fas fa-exclamation-circle';
    let statusClass = result.success ? 'success' : 'warning';

    let historyHtml = '';
    if (result.history && result.history.length > 0) {
        historyHtml = result.history.map((round, index) => {
            if (round.error) {
                return `
                    <div class="round-item error">
                        <strong>第${round.round}轮</strong> - 失败
                        <span class="strategy">${round.strategy}</span>
                        <div class="error">错误: ${round.error}</div>
                    </div>
                `;
            }

            const improvement = round.improvement ? `(${round.improvement > 0 ? '-' : '+'}${Math.abs(round.improvement).toFixed(1)})` : '';
            return `
                <div class="round-item ${index === result.history.length - 1 ? 'final' : ''}">
                    <strong>第${round.round}轮</strong> - 分数: ${round.score}% ${improvement}
                    <span class="strategy">${round.strategy}</span>
                    <div class="improvements">${round.improvements ? round.improvements.join(', ') : ''}</div>
                </div>
            `;
        }).join('');
    }

    resultElement.innerHTML = `
        <div class="result-details ${statusClass}">
            <h4><i class="${statusIcon}"></i> 多轮优化结果</h4>
            <div class="result-summary">
                <div class="score-display">
                    <span class="label">最终分数:</span>
                    <span class="score ${result.finalScore <= 15 ? 'good' : result.finalScore <= 30 ? 'medium' : 'high'}">${result.finalScore}%</span>
                </div>
                <div class="rounds-info">
                    <span class="label">优化轮数:</span> ${result.rounds}轮
                </div>
                <div class="time-info">
                    <span class="label">总耗时:</span> ${(result.totalProcessingTime / 1000).toFixed(1)}秒
                </div>
            </div>
            <div class="status-message">
                <p><strong>状态:</strong> ${result.message}</p>
                ${result.averageImprovement ? `<p><strong>平均改进:</strong> ${result.averageImprovement.toFixed(1)}分/轮</p>` : ''}
            </div>
            ${historyHtml ? `
                <div class="optimization-history">
                    <h5><i class="fas fa-history"></i> 优化历程</h5>
                    ${historyHtml}
                </div>
            ` : ''}
            <div class="optimized-text">
                <h5><i class="fas fa-file-text"></i> 优化后文本</h5>
                <div class="text-content">${result.finalText}</div>
                <button class="btn btn-sm btn-primary" onclick="copyToClipboard('${result.finalText.replace(/'/g, "\\'")}')">
                    <i class="fas fa-copy"></i> 复制文本
                </button>
            </div>
        </div>
    `;

    resultElement.style.display = 'block';
}

// 复制文本到剪贴板
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('文本已复制到剪贴板', 'success');
    }).catch(() => {
        showNotification('复制失败，请手动选择文本复制', 'error');
    });
}

// 显示通知
function showNotification(message, type = 'info') {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        z-index: 10000;
        animation: slideInRight 0.3s ease;
    `;

    document.body.appendChild(notification);

    // 3秒后自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

// LLM服务控制功能
async function startLLMService() {
    const startBtn = document.getElementById('startServiceBtn');
    const originalText = startBtn.innerHTML;

    try {
        startBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 启动中...';
        startBtn.disabled = true;

        const result = await llmManager.startService();

        if (result.success) {
            showNotification('LLM服务启动成功！', 'success');
            if (result.autoStarted) {
                showNotification('已自动配置LLM服务，可以使用混合检测功能', 'info');
            }
        } else {
            if (result.needsInstallation) {
                llmManager.showInstallGuide(result.installGuide);
            } else if (result.needsModelDownload) {
                showModelDownloadGuide(result);
            } else {
                showNotification(`启动失败: ${result.message}`, 'error');
            }
        }
    } catch (error) {
        console.error('启动LLM服务失败:', error);
        showNotification('启动LLM服务时发生错误', 'error');
    } finally {
        startBtn.innerHTML = originalText;
        startBtn.disabled = false;
    }
}

async function stopLLMService() {
    const stopBtn = document.getElementById('stopServiceBtn');
    const originalText = stopBtn.innerHTML;

    try {
        stopBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 停止中...';
        stopBtn.disabled = true;

        const result = await llmManager.stopService();

        if (result.success) {
            showNotification('LLM服务已停止', 'success');
            if (result.note) {
                showNotification(result.note, 'info');
            }
        } else {
            showNotification(`停止失败: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('停止LLM服务失败:', error);
        showNotification('停止LLM服务时发生错误', 'error');
    } finally {
        stopBtn.innerHTML = originalText;
        stopBtn.disabled = false;
    }
}

async function restartLLMService() {
    const restartBtn = document.getElementById('restartServiceBtn');
    const originalText = restartBtn.innerHTML;

    try {
        restartBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 重启中...';
        restartBtn.disabled = true;

        const result = await llmManager.restartService();

        if (result.success) {
            showNotification('LLM服务重启成功！', 'success');
        } else {
            showNotification(`重启失败: ${result.message}`, 'error');
        }
    } catch (error) {
        console.error('重启LLM服务失败:', error);
        showNotification('重启LLM服务时发生错误', 'error');
    } finally {
        restartBtn.innerHTML = originalText;
        restartBtn.disabled = false;
    }
}

async function refreshLLMStatus() {
    const refreshBtn = document.getElementById('refreshStatusBtn');
    const originalText = refreshBtn.innerHTML;

    try {
        refreshBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        refreshBtn.disabled = true;

        await llmManager.checkServiceStatus();
        showNotification('状态已刷新', 'info');
    } catch (error) {
        console.error('刷新状态失败:', error);
        showNotification('刷新状态时发生错误', 'error');
    } finally {
        refreshBtn.innerHTML = originalText;
        refreshBtn.disabled = false;
    }
}

function showLLMGuide() {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            <h2><i class="fas fa-book"></i> LLM服务设置指南</h2>

            <div style="margin: 20px 0;">
                <h3>🚀 快速开始</h3>
                <ol>
                    <li>点击"一键启动"按钮，系统会自动检测和配置</li>
                    <li>如果提示需要安装，请按照弹出的指南操作</li>
                    <li>启动成功后，即可使用"启用LLM"功能</li>
                </ol>
            </div>

            <div style="margin: 20px 0;">
                <h3>🔧 手动配置</h3>
                <h4>1. 安装Ollama</h4>
                <p>访问 <a href="https://ollama.ai/download" target="_blank">https://ollama.ai/download</a> 下载安装</p>

                <h4>2. 启动服务</h4>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
                    ollama serve
                </div>

                <h4>3. 下载模型</h4>
                <div style="background: #f8f9fa; padding: 10px; border-radius: 5px; font-family: monospace;">
                    ollama pull qwen3:30b-a3b
                </div>
            </div>

            <div style="margin: 20px 0;">
                <h3>💡 使用提示</h3>
                <ul>
                    <li>LLM服务启动后，检测精度会显著提升</li>
                    <li>混合检测模式结合规则算法和AI模型</li>
                    <li>多轮优化功能可自动迭代到目标分数</li>
                    <li>服务会在页面关闭时自动停止</li>
                </ul>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

function showModelDownloadGuide(result) {
    const modal = document.createElement('div');
    modal.className = 'modal';
    modal.style.display = 'block';
    modal.innerHTML = `
        <div class="modal-content">
            <span class="close" onclick="this.closest('.modal').remove()">&times;</span>
            <h2><i class="fas fa-download"></i> 模型下载指南</h2>

            <div style="margin: 20px 0;">
                <p><strong>检测到Ollama服务正在运行，但缺少所需模型。</strong></p>

                <h3>📥 下载命令</h3>
                <div style="background: #f8f9fa; padding: 15px; border-radius: 5px; font-family: monospace; display: flex; justify-content: space-between; align-items: center;">
                    <code>${result.downloadCommand}</code>
                    <button onclick="navigator.clipboard.writeText('${result.downloadCommand}')" style="background: #007bff; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">
                        <i class="fas fa-copy"></i> 复制
                    </button>
                </div>

                <h3>📋 操作步骤</h3>
                <ol>
                    ${result.instructions.map(instruction => `<li>${instruction}</li>`).join('')}
                </ol>

                <div style="background: #fff3cd; padding: 15px; border-radius: 5px; margin-top: 15px;">
                    <i class="fas fa-exclamation-triangle" style="color: #856404;"></i>
                    <strong>注意:</strong> 模型文件较大（约15GB），请确保网络稳定和足够的存储空间。
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);
}

// 监听LLM状态变化
document.addEventListener('llmStatusChanged', function(event) {
    const status = event.detail;
    updateLLMStatusUI(status);
});

// 监听LLM启动进度
document.addEventListener('llmStartupProgress', function(event) {
    const { message, progress } = event.detail;
    updateLLMStartupProgress(message, progress);
});

/**
 * 更新LLM启动进度显示
 */
function updateLLMStartupProgress(message, progress) {
    const progressContainer = document.getElementById('llmStartupProgress');
    if (!progressContainer) return;

    const progressMessage = progressContainer.querySelector('.progress-message');
    const progressFill = progressContainer.querySelector('.progress-fill');
    const progressPercentage = progressContainer.querySelector('.progress-percentage');

    if (progressMessage) {
        progressMessage.textContent = message;
    }

    if (progressFill) {
        progressFill.style.width = `${progress}%`;
    }

    if (progressPercentage) {
        progressPercentage.textContent = `${progress}%`;
    }

    // 如果进度达到100%，延迟隐藏进度条
    if (progress >= 100) {
        setTimeout(() => {
            hideLLMStartupProgress();
        }, 1500);
    }
}

function updateLLMStatusUI(status) {
    const statusBadge = document.getElementById('llmStatusBadge');
    const statusDot = document.getElementById('statusDot');
    const statusText = document.getElementById('statusText');
    const serviceInfo = document.getElementById('serviceInfo');
    const serviceDetails = document.getElementById('serviceDetails');

    const startBtn = document.getElementById('startServiceBtn');
    const stopBtn = document.getElementById('stopServiceBtn');
    const restartBtn = document.getElementById('restartServiceBtn');

    if (status.running) {
        statusBadge.textContent = '运行中';
        statusBadge.style.background = '#28a745';
        statusDot.style.background = '#28a745';
        statusText.textContent = 'LLM服务正在运行';

        startBtn.disabled = true;
        stopBtn.disabled = false;
        restartBtn.disabled = false;

        // 显示服务详情
        if (status.lastCheck) {
            serviceInfo.style.display = 'block';
            serviceDetails.textContent = `最后检查: ${status.lastCheck}`;
        }

    } else if (status.starting) {
        statusBadge.textContent = '启动中';
        statusBadge.style.background = '#ffc107';
        statusDot.style.background = '#ffc107';
        statusText.textContent = 'LLM服务正在启动...';

        startBtn.disabled = true;
        stopBtn.disabled = true;
        restartBtn.disabled = true;

    } else if (status.stopping) {
        statusBadge.textContent = '停止中';
        statusBadge.style.background = '#ffc107';
        statusDot.style.background = '#ffc107';
        statusText.textContent = 'LLM服务正在停止...';

        startBtn.disabled = true;
        stopBtn.disabled = true;
        restartBtn.disabled = true;

    } else {
        statusBadge.textContent = '未运行';
        statusBadge.style.background = '#6c757d';
        statusDot.style.background = '#6c757d';
        statusText.textContent = 'LLM服务未运行';

        startBtn.disabled = false;
        stopBtn.disabled = true;
        restartBtn.disabled = true;

        serviceInfo.style.display = 'none';
    }

    if (status.error) {
        statusText.textContent += ` (${status.error})`;
        statusDot.style.background = '#dc3545';
    }
}

// ==================== 朱雀AI检测对抗优化功能 ====================

/**
 * 执行朱雀对抗优化
 */
async function performZhuqueOptimization() {
    const text = document.getElementById('academicText').value.trim();

    if (!text) {
        alert('请输入要优化的学术文本内容！');
        return;
    }

    // 获取选择的优化模式
    const selectedMode = document.querySelector('input[name="optimizationMode"]:checked').value;

    // 显示加载状态
    const loadingElement = document.getElementById('academicLoading');
    const resultElement = document.getElementById('academicResult');

    loadingElement.style.display = 'block';
    loadingElement.innerHTML = `
        <div class="spinner"></div>
        <p>🏮 正在执行朱雀对抗优化...</p>
        <div style="font-size: 0.9em; color: #666; margin-top: 10px;">
            模式: ${getOptimizationModeLabel(selectedMode)}
        </div>
    `;
    resultElement.style.display = 'none';

    try {
        console.log('🏮 开始朱雀对抗优化，模式:', selectedMode);

        // 检查朱雀优化器是否可用
        if (typeof zhuqueOptimizer === 'undefined') {
            throw new Error('朱雀优化器模块未加载');
        }

        // 配置优化选项
        const optimizationOptions = getZhuqueOptimizationOptions(selectedMode);

        // 执行朱雀优化
        const result = await zhuqueOptimizer.optimizeForZhuque(text, optimizationOptions);

        if (result.success) {
            // 显示优化结果
            displayZhuqueOptimizationResult(result);
            console.log('✅ 朱雀优化完成:', result);
        } else {
            throw new Error(result.error || '朱雀优化失败');
        }

    } catch (error) {
        console.error('❌ 朱雀优化失败:', error);

        // 显示错误信息
        resultElement.innerHTML = `
            <div class="error-message" style="background: #f8d7da; color: #721c24; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb;">
                <h4><i class="fas fa-exclamation-triangle"></i> 朱雀优化失败</h4>
                <p>${error.message}</p>
                <div style="margin-top: 10px;">
                    <button class="btn btn-secondary" onclick="performAcademicOptimization()">
                        <i class="fas fa-redo"></i> 使用标准优化
                    </button>
                </div>
            </div>
        `;
        resultElement.style.display = 'block';
    } finally {
        loadingElement.style.display = 'none';
    }
}

/**
 * 预览朱雀优化效果
 */
async function previewZhuqueEffect() {
    const text = document.getElementById('academicText').value.trim();

    if (!text) {
        alert('请输入要预览的文本内容！');
        return;
    }

    try {
        // 执行基线检测
        let baselineResult;
        if (typeof zhuqueDetector !== 'undefined') {
            baselineResult = zhuqueDetector.detectWithZhuque(text);
        } else if (typeof aiDetector !== 'undefined') {
            const result = aiDetector.detectAI(text);
            baselineResult = {
                aiProbability: result.score || result.aiProbability || 50,
                confidence: result.confidence || 0.5
            };
        } else {
            baselineResult = { aiProbability: 75, confidence: 0.3 };
        }

        // 分析四维度
        const dimensionAnalysis = zhuqueOptimizer.analyzeFourDimensions(text);

        // 显示预览结果
        const previewModal = createZhuquePreviewModal(baselineResult, dimensionAnalysis);
        document.body.appendChild(previewModal);

        // 显示模态框
        setTimeout(() => {
            previewModal.style.opacity = '1';
            previewModal.querySelector('.modal-content').style.transform = 'scale(1)';
        }, 10);

    } catch (error) {
        console.error('预览失败:', error);
        alert('预览功能暂时不可用：' + error.message);
    }
}

/**
 * 获取优化模式标签
 */
function getOptimizationModeLabel(mode) {
    const labels = {
        'standard': '📊 标准优化',
        'zhuque-light': '🏮 朱雀轻度',
        'zhuque-medium': '🔥 朱雀中度',
        'zhuque-aggressive': '⚡ 朱雀强力'
    };
    return labels[mode] || '📊 标准优化';
}

/**
 * 获取朱雀优化选项
 */
function getZhuqueOptimizationOptions(mode) {
    const baseOptions = {
        preserveSemantics: false, // 默认不保持语义，更激进
        enableRealTimeCheck: true
    };

    switch (mode) {
        case 'zhuque-light':
            return {
                ...baseOptions,
                aggressiveness: 'medium', // 提升轻度模式强度
                targetScore: 20,          // 降低目标分数
                preserveSemantics: true   // 轻度模式保持语义
            };
        case 'zhuque-medium':
            return {
                ...baseOptions,
                aggressiveness: 'high',   // 提升中度模式强度
                targetScore: 10,          // 大幅降低目标分数
                preserveSemantics: false  // 不保持语义
            };
        case 'zhuque-aggressive':
            return {
                ...baseOptions,
                aggressiveness: 'high',
                targetScore: 5,           // 极低目标分数
                preserveSemantics: false,
                enableRealTimeCheck: false // 禁用实时检查，全力优化
            };
        default:
            return {
                ...baseOptions,
                aggressiveness: 'medium',
                targetScore: 15           // 默认也更激进
            };
    }
}

/**
 * 显示朱雀优化结果
 */
function displayZhuqueOptimizationResult(result) {
    const resultElement = document.getElementById('academicResult');
    const summaryElement = document.getElementById('academicSummary');
    const optimizedTextElement = document.getElementById('academicOptimizedText');
    const detailsElement = document.getElementById('academicDetails');

    // 显示优化摘要
    summaryElement.innerHTML = `
        <div class="summary-card">
            <h4>🏮 朱雀优化</h4>
            <div class="summary-value">${result.metadata.aggressiveness}</div>
            <div class="summary-label">优化强度</div>
        </div>
        <div class="summary-card ${result.improvement > 0 ? 'positive' : 'neutral'}">
            <h4>📈 AI概率变化</h4>
            <div class="summary-value">${result.improvement > 0 ? '-' : ''}${Math.abs(result.improvement)}%</div>
            <div class="summary-label">${result.baseline.aiProbability}% → ${result.finalResult.aiProbability}%</div>
        </div>
        <div class="summary-card ${result.metadata.targetAchieved ? 'positive' : 'warning'}">
            <h4>🎯 目标达成</h4>
            <div class="summary-value">${result.metadata.targetAchieved ? '✅' : '⚠️'}</div>
            <div class="summary-label">目标: ${result.metadata.targetScore}%</div>
        </div>
        <div class="summary-card">
            <h4>🔧 优化步骤</h4>
            <div class="summary-value">${result.optimizationSteps.length}</div>
            <div class="summary-label">执行步骤</div>
        </div>
    `;

    // 显示优化后文本
    optimizedTextElement.innerHTML = result.optimizedText;

    // 显示详细分析
    detailsElement.innerHTML = generateZhuqueDetailsHTML(result);

    // 更新对比视图
    document.getElementById('originalText').innerHTML = result.originalText;
    document.getElementById('optimizedComparisonText').innerHTML = result.optimizedText;

    resultElement.style.display = 'block';
}

/**
 * 生成朱雀优化详细信息HTML
 */
function generateZhuqueDetailsHTML(result) {
    let html = `
        <div class="zhuque-analysis-section">
            <h4><i class="fas fa-microscope"></i> 朱雀四维度分析</h4>
            <div class="dimension-comparison">
                <div class="dimension-grid">
    `;

    // 四维度对比
    const dimensions = ['perplexity', 'structural', 'semantic', 'frequency'];
    const dimensionLabels = {
        perplexity: '困惑度',
        structural: '结构化特征',
        semantic: '语义一致性',
        frequency: '频域特征'
    };

    for (const dim of dimensions) {
        const baseline = result.dimensionAnalysis[dim];
        const final = result.finalResult.finalDimensionAnalysis?.[dim] || baseline;

        html += `
            <div class="dimension-item">
                <h5>${dimensionLabels[dim]}</h5>
                <div class="dimension-scores">
                    <div class="score-before">
                        <span class="label">优化前:</span>
                        <span class="value ${baseline.isProblematic ? 'problematic' : 'normal'}">
                            ${getDimensionDisplayValue(dim, baseline)}
                        </span>
                    </div>
                    <div class="score-after">
                        <span class="label">优化后:</span>
                        <span class="value ${final.isProblematic ? 'problematic' : 'normal'}">
                            ${getDimensionDisplayValue(dim, final)}
                        </span>
                    </div>
                </div>
                ${baseline.isProblematic ? '<div class="improvement-indicator">✅ 已优化</div>' : '<div class="status-indicator">✓ 正常</div>'}
            </div>
        `;
    }

    html += `
                </div>
            </div>
        </div>

        <div class="optimization-steps-section">
            <h4><i class="fas fa-list-ol"></i> 优化步骤详情</h4>
            <div class="steps-timeline">
    `;

    // 优化步骤
    for (let i = 0; i < result.optimizationSteps.length; i++) {
        const step = result.optimizationSteps[i];
        html += `
            <div class="step-item ${step.success ? 'success' : 'failed'}">
                <div class="step-number">${i + 1}</div>
                <div class="step-content">
                    <h5>${step.step.description}</h5>
                    <div class="step-details">
                        <p><strong>类型:</strong> ${step.step.type}</p>
                        <p><strong>方法:</strong> ${step.appliedMethods?.join(', ') || '无'}</p>
                        ${step.changes && step.changes.length > 0 ? `
                            <div class="changes-list">
                                <strong>具体变更:</strong>
                                <ul>
                                    ${step.changes.slice(0, 3).map(change => `<li>${change.description}</li>`).join('')}
                                    ${step.changes.length > 3 ? `<li>...还有${step.changes.length - 3}项变更</li>` : ''}
                                </ul>
                            </div>
                        ` : ''}
                        ${step.intermediateScore ? `<p><strong>中间检测分数:</strong> ${step.intermediateScore}%</p>` : ''}
                    </div>
                </div>
            </div>
        `;
    }

    html += `
            </div>
        </div>

        <div class="validation-section">
            <h4><i class="fas fa-check-circle"></i> 验证结果</h4>
            <div class="validation-grid">
                <div class="validation-item">
                    <span class="label">朱雀阈值检查:</span>
                    <span class="value ${result.finalResult.passesZhuqueThresholds?.overallPass ? 'pass' : 'fail'}">
                        ${result.finalResult.passesZhuqueThresholds?.overallPass ? '✅ 通过' : '❌ 未通过'}
                        (${result.finalResult.passesZhuqueThresholds?.passedCount || 0}/${result.finalResult.passesZhuqueThresholds?.totalChecks || 4})
                    </span>
                </div>
                <div class="validation-item">
                    <span class="label">可读性评分:</span>
                    <span class="value">${result.finalResult.readabilityScore || 'N/A'}/100</span>
                </div>
                <div class="validation-item">
                    <span class="label">语义完整性:</span>
                    <span class="value">${Math.round((result.finalResult.semanticIntegrity?.score || 0) * 100)}%</span>
                </div>
            </div>
        </div>
    `;

    return html;
}

/**
 * 获取维度显示值
 */
function getDimensionDisplayValue(dimension, analysis) {
    switch (dimension) {
        case 'perplexity':
            return `${Math.round(analysis.estimatedPerplexity || 0)}`;
        case 'structural':
            return `${analysis.patternCount || 0}个模式`;
        case 'semantic':
            return `${Math.round((analysis.lexicalDiversity || 0) * 100)}%`;
        case 'frequency':
            return `${Math.round((analysis.entropy || 0) * 100) / 100}`;
        default:
            return 'N/A';
    }
}

/**
 * 创建朱雀预览模态框
 */
function createZhuquePreviewModal(baselineResult, dimensionAnalysis) {
    const modal = document.createElement('div');
    modal.className = 'zhuque-preview-modal';
    modal.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.5);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        opacity: 0;
        transition: opacity 0.3s ease;
    `;

    modal.innerHTML = `
        <div class="modal-content" style="
            background: white;
            border-radius: 12px;
            padding: 30px;
            max-width: 800px;
            max-height: 80vh;
            overflow-y: auto;
            transform: scale(0.9);
            transition: transform 0.3s ease;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        ">
            <div class="modal-header" style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 20px;">
                <h3 style="margin: 0; color: #ff6b6b;">
                    <i class="fas fa-eye"></i> 朱雀检测预览
                </h3>
                <button onclick="this.closest('.zhuque-preview-modal').remove()" style="
                    background: none;
                    border: none;
                    font-size: 24px;
                    cursor: pointer;
                    color: #999;
                ">×</button>
            </div>

            <div class="preview-content">
                <div class="baseline-section" style="margin-bottom: 25px; padding: 20px; background: #f8f9fa; border-radius: 8px;">
                    <h4 style="color: #333; margin-bottom: 15px;">
                        <i class="fas fa-chart-line"></i> 当前检测结果
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                            <div style="font-size: 2em; font-weight: bold; color: ${baselineResult.aiProbability > 70 ? '#dc3545' : baselineResult.aiProbability > 40 ? '#ffc107' : '#28a745'};">
                                ${baselineResult.aiProbability}%
                            </div>
                            <div style="color: #666;">AI生成概率</div>
                        </div>
                        <div style="text-align: center; padding: 15px; background: white; border-radius: 8px;">
                            <div style="font-size: 2em; font-weight: bold; color: #007bff;">
                                ${Math.round((baselineResult.confidence || 0) * 100)}%
                            </div>
                            <div style="color: #666;">检测置信度</div>
                        </div>
                    </div>
                </div>

                <div class="dimensions-section">
                    <h4 style="color: #333; margin-bottom: 15px;">
                        <i class="fas fa-microscope"></i> 四维度分析
                    </h4>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 15px;">
                        ${generateDimensionPreviewCards(dimensionAnalysis)}
                    </div>
                </div>

                <div class="recommendation-section" style="margin-top: 25px; padding: 20px; background: #e8f5e8; border-radius: 8px; border-left: 4px solid #28a745;">
                    <h4 style="color: #155724; margin-bottom: 10px;">
                        <i class="fas fa-lightbulb"></i> 优化建议
                    </h4>
                    <ul style="margin: 0; padding-left: 20px; color: #155724;">
                        ${generateOptimizationRecommendations(dimensionAnalysis)}
                    </ul>
                </div>
            </div>

            <div class="modal-footer" style="margin-top: 25px; text-align: center;">
                <button onclick="this.closest('.zhuque-preview-modal').remove(); performZhuqueOptimization();" style="
                    background: linear-gradient(135deg, #ff6b6b, #ee5a24);
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    font-weight: bold;
                    cursor: pointer;
                    margin-right: 10px;
                ">
                    <i class="fas fa-fire"></i> 开始朱雀优化
                </button>
                <button onclick="this.closest('.zhuque-preview-modal').remove()" style="
                    background: #6c757d;
                    color: white;
                    border: none;
                    padding: 12px 24px;
                    border-radius: 6px;
                    cursor: pointer;
                ">
                    关闭预览
                </button>
            </div>
        </div>
    `;

    // 点击背景关闭
    modal.addEventListener('click', (e) => {
        if (e.target === modal) {
            modal.remove();
        }
    });

    return modal;
}

/**
 * 生成维度预览卡片
 */
function generateDimensionPreviewCards(dimensionAnalysis) {
    const dimensions = [
        { key: 'perplexity', label: '困惑度', icon: 'fas fa-brain' },
        { key: 'structural', label: '结构化', icon: 'fas fa-sitemap' },
        { key: 'semantic', label: '语义性', icon: 'fas fa-language' },
        { key: 'frequency', label: '频域', icon: 'fas fa-wave-square' }
    ];

    return dimensions.map(dim => {
        const analysis = dimensionAnalysis[dim];
        const isProblematic = analysis?.isProblematic || false;
        const value = getDimensionDisplayValue(dim.key, analysis || {});

        return `
            <div style="
                padding: 15px;
                background: white;
                border-radius: 8px;
                border-left: 4px solid ${isProblematic ? '#dc3545' : '#28a745'};
                text-align: center;
            ">
                <div style="font-size: 1.5em; margin-bottom: 8px; color: ${isProblematic ? '#dc3545' : '#28a745'};">
                    <i class="${dim.icon}"></i>
                </div>
                <div style="font-weight: bold; margin-bottom: 5px;">${dim.label}</div>
                <div style="font-size: 1.1em; color: #333;">${value}</div>
                <div style="font-size: 0.8em; color: ${isProblematic ? '#dc3545' : '#28a745'}; margin-top: 5px;">
                    ${isProblematic ? '需要优化' : '状态正常'}
                </div>
            </div>
        `;
    }).join('');
}

/**
 * 生成优化建议
 */
function generateOptimizationRecommendations(dimensionAnalysis) {
    const recommendations = [];

    if (dimensionAnalysis.perplexity?.needsOptimization) {
        recommendations.push('增加文本复杂度和句式变化，提升困惑度');
    }

    if (dimensionAnalysis.structural?.needsOptimization) {
        recommendations.push('消除"首先-其次-最后"等AI典型结构模式');
    }

    if (dimensionAnalysis.semantic?.needsOptimization) {
        recommendations.push('增加词汇多样性，降低主题过度一致性');
    }

    if (dimensionAnalysis.frequency?.needsOptimization) {
        recommendations.push('调整字符频率分布，模拟人类写作特征');
    }

    if (recommendations.length === 0) {
        recommendations.push('当前文本各项指标良好，可选择轻度优化模式');
    }

    return recommendations.map(rec => `<li>${rec}</li>`).join('');
}

// ==================== 统一学术智能优化功能 ====================

/**
 * 执行统一学术智能优化
 */
async function performUnifiedOptimization() {
    const text = document.getElementById('unifiedAcademicText').value.trim();

    if (!text) {
        alert('请输入要优化的学术文本内容！');
        return;
    }

    // 获取选择的优化模式
    const selectedMode = document.querySelector('input[name="unifiedOptimizationMode"]:checked').value;

    // 显示加载状态
    const loadingElement = document.getElementById('unifiedOptimizationLoading');
    const resultElement = document.getElementById('unifiedOptimizationResult');
    const progressElement = document.getElementById('optimizationProgress');

    loadingElement.style.display = 'block';
    resultElement.style.display = 'none';

    try {
        // 更新进度显示
        progressElement.textContent = '正在初始化优化流程...';

        // 配置优化选项
        const options = {
            enableLLM: hybridModeEnabled && ollamaManagerV2 && ollamaManagerV2.state.isConnected,
            optimizationLevel: selectedMode,
            preserveSemantics: true,
            maintainAcademicRigor: true,
            targetAIScore: selectedMode === 'aggressive' ? 20 : selectedMode === 'balanced' ? 30 : 40
        };

        progressElement.textContent = '正在执行三阶段优化...';

        // 调用统一优化器
        const result = await unifiedAcademicOptimizer.optimize(text, options);

        if (result.success) {
            progressElement.textContent = '优化完成，正在生成结果...';
            displayUnifiedOptimizationResult(result, text);
        } else {
            throw new Error(result.error || '优化过程中发生未知错误');
        }

    } catch (error) {
        console.error('统一优化失败:', error);
        progressElement.textContent = '优化失败: ' + error.message;

        // 显示错误信息
        setTimeout(() => {
            loadingElement.style.display = 'none';
            alert('优化失败：' + error.message);
        }, 1000);
    }
}

/**
 * 显示统一优化结果
 */
function displayUnifiedOptimizationResult(result, originalText) {
    const loadingElement = document.getElementById('unifiedOptimizationLoading');
    const resultElement = document.getElementById('unifiedOptimizationResult');
    const summaryElement = document.getElementById('unifiedOptimizationSummary');
    const qualityElement = document.getElementById('qualityMetrics');
    const optimizedTextElement = document.getElementById('unifiedOptimizedText');
    const detailsElement = document.getElementById('unifiedOptimizationDetails');

    // 隐藏加载状态，显示结果
    loadingElement.style.display = 'none';
    resultElement.style.display = 'block';

    // 显示优化摘要
    const improvements = result.overallImprovements || [];
    summaryElement.innerHTML = `
        <div class="metric-card">
            <div class="metric-value">${improvements.length}</div>
            <div class="metric-label">优化项目</div>
            <div class="metric-description">应用的优化技术</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${result.processingTime || 0}ms</div>
            <div class="metric-label">处理时间</div>
            <div class="metric-description">总优化耗时</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${result.optimizationHistory?.length || 0}</div>
            <div class="metric-label">优化阶段</div>
            <div class="metric-description">完成的处理阶段</div>
        </div>
    `;

    // 显示质量指标
    const metrics = result.qualityMetrics || {};
    qualityElement.innerHTML = `
        <div class="metric-card">
            <div class="metric-value">${(metrics.academicQuality * 100).toFixed(0)}%</div>
            <div class="metric-label">学术质量</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${(metrics.expressionQuality * 100).toFixed(0)}%</div>
            <div class="metric-label">表达质量</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${(metrics.aiReductionQuality * 100).toFixed(0)}%</div>
            <div class="metric-label">AI消除度</div>
        </div>
        <div class="metric-card">
            <div class="metric-value">${metrics.lengthChange}%</div>
            <div class="metric-label">长度变化</div>
        </div>
    `;

    // 显示优化后的文本
    optimizedTextElement.textContent = result.optimizedText;

    // 显示详细信息
    const improvementsList = improvements.map(imp => `<li>${imp}</li>`).join('');
    detailsElement.innerHTML = `
        <h4><i class="fas fa-list"></i> 优化详情</h4>
        <div style="background: white; padding: 15px; border-radius: 8px; border: 1px solid #dee2e6;">
            <h5>应用的优化技术：</h5>
            <ul style="margin: 10px 0; padding-left: 20px;">
                ${improvementsList}
            </ul>
            <h5>质量评估：</h5>
            <p>整体质量评分: <strong>${(metrics.overallQuality * 100).toFixed(1)}%</strong></p>
            <p>优化模式: <strong>${result.mode || 'unified_academic'}</strong></p>
        </div>
    `;

    // 设置对比视图的原始文本
    document.getElementById('unifiedOriginalText').textContent = originalText;
    document.getElementById('unifiedOptimizedComparisonText').textContent = result.optimizedText;
}

/**
 * 清空统一优化内容
 */
function clearUnifiedOptimization() {
    document.getElementById('unifiedAcademicText').value = '';
    document.getElementById('unifiedOptimizationLoading').style.display = 'none';
    document.getElementById('unifiedOptimizationResult').style.display = 'none';
    document.getElementById('unifiedComparisonView').style.display = 'none';
    document.getElementById('optimizationHistory').style.display = 'none';

    // 重置模式选择
    document.querySelector('input[name="unifiedOptimizationMode"][value="conservative"]').checked = true;
}

/**
 * 切换统一优化对比视图
 */
function toggleUnifiedComparisonView() {
    const comparisonView = document.getElementById('unifiedComparisonView');
    const toggleText = document.getElementById('unifiedComparisonToggleText');

    if (comparisonView.style.display === 'none' || !comparisonView.style.display) {
        comparisonView.style.display = 'block';
        toggleText.textContent = '隐藏对比视图';
    } else {
        comparisonView.style.display = 'none';
        toggleText.textContent = '显示对比视图';
    }
}

/**
 * 复制统一优化结果
 */
function copyUnifiedOptimizationResult(event) {
    const optimizedText = document.getElementById('unifiedOptimizedText').textContent;

    if (!optimizedText || optimizedText.trim() === '') {
        alert('没有可复制的优化结果！');
        return;
    }

    // 获取按钮元素
    const btn = event ? event.target : document.querySelector('[onclick="copyUnifiedOptimizationResult()"]');
    if (!btn) {
        console.error('无法找到复制按钮元素');
        return;
    }

    const originalText = btn.innerHTML;
    const originalBackground = btn.style.background;

    // 显示复制中状态
    btn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> 复制中...';
    btn.disabled = true;

    navigator.clipboard.writeText(optimizedText).then(() => {
        // 复制成功
        btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
        btn.style.background = '#28a745';
        btn.style.color = 'white';

        // 显示成功通知
        showNotification('优化结果已复制到剪贴板', 'success');

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = originalBackground;
            btn.style.color = '';
            btn.disabled = false;
        }, 2000);
    }).catch(err => {
        console.error('复制失败:', err);

        // 复制失败处理
        btn.innerHTML = '<i class="fas fa-times"></i> 复制失败';
        btn.style.background = '#dc3545';
        btn.style.color = 'white';

        // 显示错误通知
        showNotification('复制失败，请手动选择文本复制', 'error');

        // 尝试使用备用方法
        try {
            // 创建临时文本区域
            const textArea = document.createElement('textarea');
            textArea.value = optimizedText;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            if (document.execCommand('copy')) {
                showNotification('已使用备用方法复制到剪贴板', 'success');
                btn.innerHTML = '<i class="fas fa-check"></i> 已复制';
                btn.style.background = '#28a745';
            }

            document.body.removeChild(textArea);
        } catch (fallbackErr) {
            console.error('备用复制方法也失败:', fallbackErr);
        }

        setTimeout(() => {
            btn.innerHTML = originalText;
            btn.style.background = originalBackground;
            btn.style.color = '';
            btn.disabled = false;
        }, 3000);
    });
}

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    // 移除现有通知
    const existingNotification = document.querySelector('.notification');
    if (existingNotification) {
        existingNotification.remove();
    }

    // 创建新通知
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <i class="fas ${type === 'success' ? 'fa-check-circle' : type === 'error' ? 'fa-exclamation-circle' : 'fa-info-circle'}"></i>
            <span>${message}</span>
        </div>
    `;

    // 添加到页面
    document.body.appendChild(notification);

    // 添加动画
    notification.style.animation = 'slideInRight 0.3s ease';

    // 自动移除
    setTimeout(() => {
        notification.style.animation = 'slideOutRight 0.3s ease';
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 300);
    }, 3000);
}

/**
 * 显示优化历程
 */
function showOptimizationHistory() {
    const historyElement = document.getElementById('optimizationHistory');
    const stagesElement = document.getElementById('optimizationStages');

    if (historyElement.style.display === 'none' || !historyElement.style.display) {
        historyElement.style.display = 'block';

        // 这里可以显示详细的优化历程
        // 暂时显示一个示例
        stagesElement.innerHTML = `
            <div class="timeline-item completed">
                <div class="timeline-icon"><i class="fas fa-check"></i></div>
                <div class="timeline-content">
                    <h5>学术规范化</h5>
                    <p class="status-text">术语标准化、表达规范化、结构优化</p>
                    <div class="progress-bar"><div class="progress-fill" style="width: 100%;"></div></div>
                </div>
            </div>
            <div class="timeline-item completed">
                <div class="timeline-icon"><i class="fas fa-check"></i></div>
                <div class="timeline-content">
                    <h5>表达优化</h5>
                    <p class="status-text">语言流畅性提升、句式多样化、自然性增强</p>
                    <div class="progress-bar"><div class="progress-fill" style="width: 100%;"></div></div>
                </div>
            </div>
            <div class="timeline-item completed">
                <div class="timeline-icon"><i class="fas fa-check"></i></div>
                <div class="timeline-content">
                    <h5>AI特征消除</h5>
                    <p class="status-text">个人化表达、不确定性标记、生成痕迹清除</p>
                    <div class="progress-bar"><div class="progress-fill" style="width: 100%;"></div></div>
                </div>
            </div>
        `;
    } else {
        historyElement.style.display = 'none';
    }
}

/**
 * 获取优化建议
 */
function getOptimizationSuggestions() {
    const text = document.getElementById('unifiedAcademicText').value.trim();

    if (!text) {
        alert('请先输入文本内容！');
        return;
    }

    try {
        const suggestions = unifiedAcademicOptimizer.getOptimizationSuggestions(text);

        if (suggestions.length > 0) {
            const suggestionsList = suggestions.map(s => `• ${s}`).join('\n');
            alert(`优化建议：\n\n${suggestionsList}`);
        } else {
            alert('当前文本质量良好，无特殊优化建议。');
        }
    } catch (error) {
        console.error('获取建议失败:', error);
        alert('获取优化建议失败，请稍后重试。');
    }
}
