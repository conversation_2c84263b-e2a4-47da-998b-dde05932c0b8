using DocxToMarkdownConverter.Models;
using System.Text.Json;
using System.IO;

namespace DocxToMarkdownConverter.Services;

public interface ISettingsService
{
    Task<T> LoadSettingsAsync<T>() where T : new();
    Task SaveSettingsAsync<T>(T settings);
    Task<ConversionOptions> LoadConversionOptionsAsync();
    Task SaveConversionOptionsAsync(ConversionOptions options);
}

public class SettingsService : ISettingsService
{
    private readonly string _settingsDirectory;

    public SettingsService()
    {
        _settingsDirectory = Path.Combine(
            Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData),
            "DocxToMarkdownConverter");

        if (!Directory.Exists(_settingsDirectory))
        {
            Directory.CreateDirectory(_settingsDirectory);
        }
    }

    public async Task<T> LoadSettingsAsync<T>() where T : new()
    {
        var fileName = $"{typeof(T).Name}.json";
        var filePath = Path.Combine(_settingsDirectory, fileName);

        if (!File.Exists(filePath))
        {
            return new T();
        }

        try
        {
            var json = await File.ReadAllTextAsync(filePath);
            return JsonSerializer.Deserialize<T>(json) ?? new T();
        }
        catch
        {
            return new T();
        }
    }

    public async Task SaveSettingsAsync<T>(T settings)
    {
        var fileName = $"{typeof(T).Name}.json";
        var filePath = Path.Combine(_settingsDirectory, fileName);

        var json = JsonSerializer.Serialize(settings, new JsonSerializerOptions
        {
            WriteIndented = true
        });

        await File.WriteAllTextAsync(filePath, json);
    }

    public async Task<ConversionOptions> LoadConversionOptionsAsync()
    {
        return await LoadSettingsAsync<ConversionOptions>();
    }

    public async Task SaveConversionOptionsAsync(ConversionOptions options)
    {
        await SaveSettingsAsync(options);
    }
}