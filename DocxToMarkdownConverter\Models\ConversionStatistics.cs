using System.ComponentModel;
using System.Runtime.CompilerServices;
using DocxToMarkdownConverter.Services;
using System.Windows;

namespace DocxToMarkdownConverter.Models;

public class ConversionStatistics : INotifyPropertyChanged
{
    private int _totalFilesProcessed;
    private int _successfulConversions;
    private int _failedConversions;
    private int _pendingConversions;
    private TimeSpan _totalTime;
    private long _totalInputSize;
    private long _totalOutputSize;
    private DateTime? _startTime;
    private DateTime? _endTime;
    private TimeSpan _totalProcessingTime;
    private TimeSpan _averageProcessingTime;
    private TimeSpan _totalBatchTime;

    public int TotalFilesProcessed
    {
        get => _totalFilesProcessed;
        set => SetProperty(ref _totalFilesProcessed, value);
    }

    public int SuccessfulConversions
    {
        get => _successfulConversions;
        set => SetProperty(ref _successfulConversions, value);
    }

    public int FailedConversions
    {
        get => _failedConversions;
        set => SetProperty(ref _failedConversions, value);
    }

    public int PendingConversions
    {
        get => _pendingConversions;
        set => SetProperty(ref _pendingConversions, value);
    }

    public TimeSpan TotalTime
    {
        get => _totalTime;
        set => SetProperty(ref _totalTime, value);
    }

    public long TotalInputSize
    {
        get => _totalInputSize;
        set => SetProperty(ref _totalInputSize, value);
    }

    public long TotalOutputSize
    {
        get => _totalOutputSize;
        set => SetProperty(ref _totalOutputSize, value);
    }

    public DateTime? StartTime
    {
        get => _startTime;
        set => SetProperty(ref _startTime, value);
    }

    public DateTime? EndTime
    {
        get => _endTime;
        set => SetProperty(ref _endTime, value);
    }

    public TimeSpan TotalProcessingTime
    {
        get => _totalProcessingTime;
        set => SetProperty(ref _totalProcessingTime, value);
    }

    public TimeSpan AverageProcessingTime
    {
        get => _averageProcessingTime;
        set => SetProperty(ref _averageProcessingTime, value);
    }

    public TimeSpan TotalBatchTime
    {
        get => _totalBatchTime;
        set => SetProperty(ref _totalBatchTime, value);
    }

    // Legacy properties for backward compatibility
    public int TotalFiles
    {
        get => TotalFilesProcessed;
        set => TotalFilesProcessed = value;
    }

    public int CompletedFiles => SuccessfulConversions;
    public int FailedFiles => FailedConversions;
    public int SuccessfulFiles => SuccessfulConversions;

    public double SuccessRate => TotalFilesProcessed > 0 ? (double)SuccessfulConversions / TotalFilesProcessed * 100 : 0;

    public double CompressionRatio => TotalInputSize > 0 ? (double)TotalOutputSize / TotalInputSize : 0;

    // 本地化文本属性
    public string FilesProcessedText
    {
        get
        {
            try
            {
                // 尝试从应用程序资源获取格式字符串
                var format = GetLocalizedString("Statistics.FilesProcessed") ?? "已处理文件: {0}";
                return string.Format(format, TotalFilesProcessed);
            }
            catch
            {
                return $"已处理文件: {TotalFilesProcessed}";
            }
        }
    }

    public string SuccessfulText
    {
        get
        {
            try
            {
                var format = GetLocalizedString("Statistics.Successful") ?? "成功: {0}";
                return string.Format(format, SuccessfulConversions);
            }
            catch
            {
                return $"成功: {SuccessfulConversions}";
            }
        }
    }

    public string FailedText
    {
        get
        {
            try
            {
                var format = GetLocalizedString("Statistics.Failed") ?? "失败: {0}";
                return string.Format(format, FailedConversions);
            }
            catch
            {
                return $"失败: {FailedConversions}";
            }
        }
    }

    /// <summary>
    /// 获取本地化字符串的辅助方法
    /// </summary>
    private static string? GetLocalizedString(string key)
    {
        try
        {
            // 首先尝试从当前应用程序资源获取
            if (System.Windows.Application.Current?.Resources.Contains(key) == true)
            {
                return System.Windows.Application.Current.Resources[key] as string;
            }

            // 然后尝试从FindResource获取（包括合并的资源字典）
            var resource = System.Windows.Application.Current?.TryFindResource(key) as string;
            if (!string.IsNullOrEmpty(resource))
            {
                return resource;
            }

            // 最后尝试直接访问资源字典
            var mergedDictionaries = System.Windows.Application.Current?.Resources.MergedDictionaries;
            if (mergedDictionaries != null)
            {
                foreach (var dict in mergedDictionaries)
                {
                    if (dict.Contains(key))
                    {
                        return dict[key] as string;
                    }
                }
            }

            return null;
        }
        catch
        {
            return null;
        }
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        
        // Notify dependent properties
        if (propertyName == nameof(SuccessfulConversions) || propertyName == nameof(TotalFilesProcessed))
        {
            OnPropertyChanged(nameof(SuccessRate));
            OnPropertyChanged(nameof(CompletedFiles)); // Legacy property
            OnPropertyChanged(nameof(TotalFiles)); // Legacy property
            OnPropertyChanged(nameof(SuccessfulFiles)); // Legacy property
            OnPropertyChanged(nameof(FilesProcessedText)); // Localized text
            OnPropertyChanged(nameof(SuccessfulText)); // Localized text
        }
        if (propertyName == nameof(FailedConversions))
        {
            OnPropertyChanged(nameof(FailedFiles)); // Legacy property
            OnPropertyChanged(nameof(FailedText)); // Localized text
        }
        if (propertyName == nameof(TotalInputSize) || propertyName == nameof(TotalOutputSize))
        {
            OnPropertyChanged(nameof(CompressionRatio));
        }
        
        return true;
    }

    /// <summary>
    /// 刷新本地化文本，在语言改变时调用
    /// </summary>
    public void RefreshLocalizedTexts()
    {
        OnPropertyChanged(nameof(FilesProcessedText));
        OnPropertyChanged(nameof(SuccessfulText));
        OnPropertyChanged(nameof(FailedText));
    }
}