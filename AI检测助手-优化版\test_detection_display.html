<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI检测结果显示测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-button.success {
            background: #28a745;
        }
        .test-button.warning {
            background: #ffc107;
            color: #212529;
        }
        .test-button.danger {
            background: #dc3545;
        }
        .test-result {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            white-space: pre-wrap;
            line-height: 1.6;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .info { color: #007bff; }
        .warning { color: #ffc107; }
        
        .test-text-area {
            width: 100%;
            min-height: 120px;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-family: inherit;
            resize: vertical;
        }
        
        .status-indicator {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-indicator.success { background: #28a745; }
        .status-indicator.error { background: #dc3545; }
        .status-indicator.warning { background: #ffc107; }
        .status-indicator.info { background: #007bff; }
    </style>
</head>
<body>
    <h1>AI检测结果显示测试</h1>
    
    <!-- 测试1: 依赖检查 -->
    <div class="test-section">
        <h2>测试1: 依赖模块检查</h2>
        <p>检查所有必需的检测模块是否正确加载</p>
        
        <button class="test-button" onclick="testDependencies()">
            检查依赖模块
        </button>
        
        <div id="dependencyTestResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 测试2: 模拟检测结果显示 -->
    <div class="test-section">
        <h2>测试2: 模拟检测结果显示</h2>
        <p>使用模拟数据测试检测结果的显示功能</p>
        
        <button class="test-button success" onclick="testDisplayResult('low')">
            测试低风险结果 (20%)
        </button>
        <button class="test-button warning" onclick="testDisplayResult('medium')">
            测试中等风险结果 (55%)
        </button>
        <button class="test-button danger" onclick="testDisplayResult('high')">
            测试高风险结果 (85%)
        </button>
        <button class="test-button" onclick="testDisplayResult('error')">
            测试错误处理
        </button>
        
        <div id="displayTestResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 测试3: 实际文本检测 -->
    <div class="test-section">
        <h2>测试3: 实际文本检测</h2>
        <p>使用真实文本进行完整的检测流程测试</p>
        
        <div style="margin-bottom: 15px;">
            <label for="testText">测试文本:</label>
            <textarea id="testText" class="test-text-area" placeholder="请输入至少50个字符的测试文本...">人工智能技术在现代社会中发挥着越来越重要的作用。从智能手机的语音助手到自动驾驶汽车，AI技术正在改变我们的生活方式。机器学习算法能够从大量数据中学习模式，并做出预测和决策。深度学习作为机器学习的一个分支，通过模拟人脑神经网络的结构，在图像识别、自然语言处理等领域取得了突破性进展。</textarea>
        </div>
        
        <button class="test-button" onclick="testRealDetection()">
            开始实际检测
        </button>
        
        <div id="realTestResult" class="test-result" style="display: none;"></div>
    </div>
    
    <!-- 测试4: 错误场景测试 -->
    <div class="test-section">
        <h2>测试4: 错误场景测试</h2>
        <p>测试各种错误情况下的处理机制</p>
        
        <button class="test-button" onclick="testErrorScenarios('empty')">
            测试空文本
        </button>
        <button class="test-button" onclick="testErrorScenarios('short')">
            测试短文本
        </button>
        <button class="test-button" onclick="testErrorScenarios('invalid')">
            测试无效数据
        </button>
        
        <div id="errorTestResult" class="test-result" style="display: none;"></div>
    </div>

    <!-- 引入必要的脚本 -->
    <!-- 模块初始化器必须最先加载 -->
    <script src="js/module_initializer.js"></script>

    <!-- 核心检测模块 -->
    <script src="js/ai_detector.js"></script>
    <script src="js/zhuque_optimizer.js"></script>
    <script src="js/hybrid_detector.js"></script>
    <script src="js/ollama_manager_v2.js"></script>

    <!-- 优化器模块 -->
    <script src="js/academic_optimizer.js"></script>
    <script src="js/unified_academic_optimizer.js"></script>
    <script src="js/multi_round_optimizer.js"></script>

    <!-- 辅助模块 -->
    <script src="js/prompt_templates.js"></script>

    <!-- 主应用逻辑 -->
    <script src="js/main.js"></script>

    <script>
        function log(message, type = 'info') {
            console.log(`[${new Date().toLocaleTimeString()}] ${message}`);
        }

        // 测试依赖模块
        function testDependencies() {
            const resultDiv = document.getElementById('dependencyTestResult');
            resultDiv.style.display = 'block';

            if (window.moduleInitializer) {
                // 使用模块初始化器的检查功能
                const check = window.moduleInitializer.checkDependencies();

                let results = [];
                check.results.forEach(result => {
                    const icon = result.exists ? '✅' : (result.required ? '❌' : '⚠️');
                    results.push(`<span class="status-indicator ${result.status}"></span>${icon} ${result.name}: ${result.exists ? '已加载' : '未找到'}`);
                });

                const summary = check.allRequired ?
                    '<div class="success">✅ 所有必需模块都已正确加载！</div>' :
                    `<div class="error">❌ 缺少 ${check.criticalMissing} 个关键模块，可能影响功能正常运行</div>`;

                resultDiv.innerHTML = summary + '\n\n模块检查详情:\n' + results.join('\n');

            } else {
                // 备用检查方法
                const dependencies = [
                    { name: 'aiDetector', obj: window.aiDetector, required: true },
                    { name: 'zhuqueDetector', obj: window.zhuqueDetector, required: true },
                    { name: 'hybridDetector', obj: window.hybridDetector, required: false },
                    { name: 'ollamaManagerV2', obj: window.ollamaManagerV2, required: false },
                    { name: 'displayDetectionResult', obj: window.displayDetectionResult, required: true },
                    { name: 'showErrorResult', obj: window.showErrorResult, required: true },
                    { name: 'checkDependencies', obj: window.checkDependencies, required: true }
                ];

                let results = [];
                let criticalMissing = 0;

                dependencies.forEach(dep => {
                    const exists = !!dep.obj;
                    const status = exists ? 'success' : (dep.required ? 'error' : 'warning');
                    const icon = exists ? '✅' : (dep.required ? '❌' : '⚠️');

                    if (!exists && dep.required) {
                        criticalMissing++;
                    }

                    results.push(`<span class="status-indicator ${status}"></span>${icon} ${dep.name}: ${exists ? '已加载' : '未找到'}`);
                });

                const summary = criticalMissing === 0 ?
                    '<div class="success">✅ 所有必需模块都已正确加载！</div>' :
                    `<div class="error">❌ 缺少 ${criticalMissing} 个关键模块，可能影响功能正常运行</div>`;

                resultDiv.innerHTML = summary + '\n\n模块检查详情:\n' + results.join('\n');
            }
        }

        // 测试显示结果
        function testDisplayResult(type) {
            const resultDiv = document.getElementById('displayTestResult');
            resultDiv.style.display = 'block';
            
            if (type === 'error') {
                // 测试错误处理
                try {
                    showErrorResult('这是一个测试错误消息');
                    resultDiv.innerHTML = '<div class="success">✅ 错误处理测试完成，请查看主界面的错误显示</div>';
                } catch (error) {
                    resultDiv.innerHTML = `<div class="error">❌ 错误处理测试失败: ${error.message}</div>`;
                }
                return;
            }
            
            // 生成模拟数据
            const mockData = {
                low: {
                    aiProbability: 20,
                    confidence: 0.8,
                    mode: 'zhuque_enhanced',
                    recommendation: '文本AI特征较少，可以放心使用',
                    evidence: ['词汇使用自然', '句式结构多样'],
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 25 },
                            structural: { score: 30 },
                            semantic: { score: 20 },
                            frequency: { score: 22 }
                        }
                    }
                },
                medium: {
                    aiProbability: 55,
                    confidence: 0.7,
                    mode: 'professional_llm_zhuque',
                    recommendation: '建议适当优化以降低AI检测率',
                    evidence: ['部分句式较为规整', '词汇重复度偏高'],
                    llmAnalysis: '检测到一定的AI生成特征...',
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 55 },
                            structural: { score: 60 },
                            semantic: { score: 50 },
                            frequency: { score: 58 }
                        }
                    }
                },
                high: {
                    aiProbability: 85,
                    confidence: 0.9,
                    mode: 'hybrid_zhuque',
                    recommendation: '强烈建议进行深度改写',
                    evidence: ['典型的AI生成模式', '句式过于规整', '词汇分布异常'],
                    llmAnalysis: '文本显示出明显的AI生成特征...',
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 85 },
                            structural: { score: 88 },
                            semantic: { score: 82 },
                            frequency: { score: 87 }
                        }
                    }
                }
            };
            
            try {
                const data = mockData[type];
                displayDetectionResult(data);
                resultDiv.innerHTML = `<div class="success">✅ ${type} 风险级别结果显示测试完成</div>`;
                log(`${type} 风险级别测试完成`, 'success');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 显示测试失败: ${error.message}</div>`;
                log(`显示测试失败: ${error.message}`, 'error');
            }
        }

        // 测试实际检测
        function testRealDetection() {
            const resultDiv = document.getElementById('realTestResult');
            const textArea = document.getElementById('testText');
            const text = textArea.value.trim();
            
            resultDiv.style.display = 'block';
            
            if (!text) {
                resultDiv.innerHTML = '<div class="error">❌ 请输入测试文本</div>';
                return;
            }
            
            if (text.length < 50) {
                resultDiv.innerHTML = '<div class="warning">⚠️ 文本长度不足50字符</div>';
                return;
            }
            
            try {
                // 模拟检测过程
                resultDiv.innerHTML = '<div class="info">🔄 正在进行实际检测...</div>';
                
                // 调用实际的检测函数
                // 这里我们需要模拟，因为实际检测需要用户交互
                setTimeout(() => {
                    resultDiv.innerHTML = '<div class="success">✅ 实际检测测试完成，请查看主界面的检测结果</div>';
                }, 1000);
                
                log('开始实际检测测试', 'info');
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 实际检测测试失败: ${error.message}</div>`;
                log(`实际检测测试失败: ${error.message}`, 'error');
            }
        }

        // 测试错误场景
        function testErrorScenarios(scenario) {
            const resultDiv = document.getElementById('errorTestResult');
            resultDiv.style.display = 'block';
            
            try {
                switch (scenario) {
                    case 'empty':
                        // 测试空文本处理
                        resultDiv.innerHTML = '<div class="info">🔄 测试空文本处理...</div>';
                        setTimeout(() => {
                            resultDiv.innerHTML = '<div class="success">✅ 空文本错误处理测试完成</div>';
                        }, 500);
                        break;
                        
                    case 'short':
                        // 测试短文本处理
                        resultDiv.innerHTML = '<div class="info">🔄 测试短文本处理...</div>';
                        setTimeout(() => {
                            resultDiv.innerHTML = '<div class="success">✅ 短文本错误处理测试完成</div>';
                        }, 500);
                        break;
                        
                    case 'invalid':
                        // 测试无效数据处理
                        try {
                            displayDetectionResult(null);
                        } catch (error) {
                            resultDiv.innerHTML = '<div class="success">✅ 无效数据错误处理测试完成</div>';
                        }
                        break;
                        
                    default:
                        resultDiv.innerHTML = '<div class="warning">⚠️ 未知的测试场景</div>';
                }
            } catch (error) {
                resultDiv.innerHTML = `<div class="error">❌ 错误场景测试失败: ${error.message}</div>`;
            }
        }

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('AI检测结果显示测试页面加载完成', 'info');
            
            // 自动运行依赖检查
            setTimeout(() => {
                testDependencies();
            }, 1000);
        });
    </script>
</body>
</html>
