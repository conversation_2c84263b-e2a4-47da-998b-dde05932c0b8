# 应用程序数字签名脚本
# 用于对发布的应用程序进行数字签名

param(
    [Parameter(Mandatory=$true)]
    [string]$FilePath,
    
    [string]$CertificatePath = "",
    [string]$CertificatePassword = "",
    [string]$TimestampUrl = "http://timestamp.digicert.com",
    [switch]$UseCodeSigningCert = $false
)

# 设置错误处理
$ErrorActionPreference = "Stop"

Write-Host "开始数字签名流程..." -ForegroundColor Green
Write-Host "文件: $FilePath" -ForegroundColor Yellow

# 验证文件存在
if (!(Test-Path $FilePath)) {
    throw "文件不存在: $FilePath"
}

try {
    # 检查是否有可用的代码签名证书
    if ($UseCodeSigningCert) {
        Write-Host "查找代码签名证书..." -ForegroundColor Cyan
        
        # 从证书存储中查找代码签名证书
        $Certificates = Get-ChildItem -Path Cert:\CurrentUser\My | Where-Object {
            $_.EnhancedKeyUsageList -match "Code Signing"
        }
        
        if ($Certificates.Count -eq 0) {
            $Certificates = Get-ChildItem -Path Cert:\LocalMachine\My | Where-Object {
                $_.EnhancedKeyUsageList -match "Code Signing"
            }
        }
        
        if ($Certificates.Count -gt 0) {
            $Certificate = $Certificates[0]
            Write-Host "找到证书: $($Certificate.Subject)" -ForegroundColor Green
            
            # 使用 SignTool 进行签名
            $SignToolPath = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.22621.0\x64\signtool.exe"
            if (!(Test-Path $SignToolPath)) {
                # 尝试其他可能的路径
                $SignToolPath = "${env:ProgramFiles(x86)}\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\signtool.exe"
            }
            
            if (Test-Path $SignToolPath) {
                $SignArgs = @(
                    "sign"
                    "/sha1", $Certificate.Thumbprint
                    "/t", $TimestampUrl
                    "/fd", "SHA256"
                    "/v"
                    $FilePath
                )
                
                & $SignToolPath @SignArgs
                
                if ($LASTEXITCODE -eq 0) {
                    Write-Host "文件签名成功" -ForegroundColor Green
                } else {
                    throw "签名失败，退出代码: $LASTEXITCODE"
                }
            } else {
                Write-Host "未找到 SignTool，跳过签名" -ForegroundColor Yellow
            }
        } else {
            Write-Host "未找到代码签名证书，跳过签名" -ForegroundColor Yellow
        }
    }
    
    # 使用外部证书文件签名
    elseif ($CertificatePath -and (Test-Path $CertificatePath)) {
        Write-Host "使用证书文件签名..." -ForegroundColor Cyan
        
        $SignToolPath = "${env:ProgramFiles(x86)}\Windows Kits\10\bin\10.0.22621.0\x64\signtool.exe"
        if (!(Test-Path $SignToolPath)) {
            $SignToolPath = "${env:ProgramFiles(x86)}\Microsoft SDKs\Windows\v10.0A\bin\NETFX 4.8 Tools\x64\signtool.exe"
        }
        
        if (Test-Path $SignToolPath) {
            $SignArgs = @(
                "sign"
                "/f", $CertificatePath
                "/t", $TimestampUrl
                "/fd", "SHA256"
                "/v"
            )
            
            if ($CertificatePassword) {
                $SignArgs += "/p", $CertificatePassword
            }
            
            $SignArgs += $FilePath
            
            & $SignToolPath @SignArgs
            
            if ($LASTEXITCODE -eq 0) {
                Write-Host "文件签名成功" -ForegroundColor Green
            } else {
                throw "签名失败，退出代码: $LASTEXITCODE"
            }
        } else {
            Write-Host "未找到 SignTool，无法签名" -ForegroundColor Red
        }
    }
    
    # 创建自签名证书（仅用于测试）
    else {
        Write-Host "创建自签名证书用于测试..." -ForegroundColor Yellow
        
        try {
            # 创建自签名证书
            $Certificate = New-SelfSignedCertificate `
                -Subject "CN=Augment Code Test Certificate" `
                -Type CodeSigningCert `
                -KeyUsage DigitalSignature `
                -FriendlyName "DOCX Converter Test Certificate" `
                -CertStoreLocation Cert:\CurrentUser\My `
                -NotAfter (Get-Date).AddYears(1)
            
            Write-Host "自签名证书已创建: $($Certificate.Thumbprint)" -ForegroundColor Green
            
            # 使用 Set-AuthenticodeSignature 进行签名
            $SignResult = Set-AuthenticodeSignature -FilePath $FilePath -Certificate $Certificate -TimestampServer $TimestampUrl
            
            if ($SignResult.Status -eq "Valid") {
                Write-Host "文件签名成功（自签名证书）" -ForegroundColor Green
                Write-Host "注意: 这是测试证书，不适用于生产环境" -ForegroundColor Yellow
            } else {
                Write-Host "签名状态: $($SignResult.Status)" -ForegroundColor Yellow
                Write-Host "签名消息: $($SignResult.StatusMessage)" -ForegroundColor Yellow
            }
        } catch {
            Write-Host "创建自签名证书失败: $($_.Exception.Message)" -ForegroundColor Red
        }
    }
    
    # 验证签名
    Write-Host "验证文件签名..." -ForegroundColor Cyan
    
    try {
        $SignatureInfo = Get-AuthenticodeSignature -FilePath $FilePath
        
        Write-Host "签名状态: $($SignatureInfo.Status)" -ForegroundColor $(
            if ($SignatureInfo.Status -eq "Valid") { "Green" } 
            elseif ($SignatureInfo.Status -eq "UnknownError") { "Yellow" }
            else { "Red" }
        )
        
        if ($SignatureInfo.SignerCertificate) {
            Write-Host "签名者: $($SignatureInfo.SignerCertificate.Subject)" -ForegroundColor Cyan
            Write-Host "有效期: $($SignatureInfo.SignerCertificate.NotBefore) 到 $($SignatureInfo.SignerCertificate.NotAfter)" -ForegroundColor Cyan
        }
        
        if ($SignatureInfo.TimeStamperCertificate) {
            Write-Host "时间戳: $($SignatureInfo.TimeStamperCertificate.Subject)" -ForegroundColor Cyan
        }
    } catch {
        Write-Host "验证签名时发生错误: $($_.Exception.Message)" -ForegroundColor Yellow
    }

} catch {
    Write-Host "签名过程失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "数字签名流程完成" -ForegroundColor Green

# 生成签名报告
$ReportPath = [System.IO.Path]::ChangeExtension($FilePath, ".signature-report.txt")
$ReportContent = @"
数字签名报告
============

文件: $FilePath
签名时间: $(Get-Date)
时间戳服务器: $TimestampUrl

签名验证结果:
$(Get-AuthenticodeSignature -FilePath $FilePath | Format-List | Out-String)

注意事项:
- 如果使用自签名证书，用户可能会看到安全警告
- 生产环境建议使用来自受信任CA的代码签名证书
- 确保证书在整个软件生命周期内保持有效
"@

Set-Content -Path $ReportPath -Value $ReportContent -Encoding UTF8
Write-Host "签名报告已保存: $ReportPath" -ForegroundColor Green
