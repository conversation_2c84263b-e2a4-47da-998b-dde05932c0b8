﻿#pragma checksum "..\..\..\..\..\Controls\ThemeControl.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "7043C63B1D17607890758BB77B57A887118E0DD3"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using MaterialDesignThemes.Wpf;
using MaterialDesignThemes.Wpf.Converters;
using MaterialDesignThemes.Wpf.Transitions;
using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Forms.Integration;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace DocxToMarkdownConverter.Controls {
    
    
    /// <summary>
    /// ThemeControl
    /// </summary>
    public partial class ThemeControl : System.Windows.Controls.UserControl, System.Windows.Markup.IComponentConnector {
        
        
        #line 16 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton LightThemeRadio;
        
        #line default
        #line hidden
        
        
        #line 23 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton DarkThemeRadio;
        
        #line default
        #line hidden
        
        
        #line 30 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.RadioButton AutoThemeRadio;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button DeepPurpleButton;
        
        #line default
        #line hidden
        
        
        #line 65 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button BlueButton;
        
        #line default
        #line hidden
        
        
        #line 74 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TealButton;
        
        #line default
        #line hidden
        
        
        #line 83 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button GreenButton;
        
        #line default
        #line hidden
        
        
        #line 92 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button OrangeButton;
        
        #line default
        #line hidden
        
        
        #line 101 "..\..\..\..\..\Controls\ThemeControl.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RedButton;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/DocxToMarkdownConverter-v3.2.0;V3.2.0.0;component/controls/themecontrol.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\..\Controls\ThemeControl.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.7.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.LightThemeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 2:
            this.DarkThemeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 3:
            this.AutoThemeRadio = ((System.Windows.Controls.RadioButton)(target));
            return;
            case 4:
            this.DeepPurpleButton = ((System.Windows.Controls.Button)(target));
            return;
            case 5:
            this.BlueButton = ((System.Windows.Controls.Button)(target));
            return;
            case 6:
            this.TealButton = ((System.Windows.Controls.Button)(target));
            return;
            case 7:
            this.GreenButton = ((System.Windows.Controls.Button)(target));
            return;
            case 8:
            this.OrangeButton = ((System.Windows.Controls.Button)(target));
            return;
            case 9:
            this.RedButton = ((System.Windows.Controls.Button)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

