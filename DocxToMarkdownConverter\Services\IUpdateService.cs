using System;
using System.Threading;
using System.Threading.Tasks;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 自动更新服务接口
/// </summary>
public interface IUpdateService
{
    /// <summary>
    /// 检查更新
    /// </summary>
    Task<UpdateInfo> CheckForUpdatesAsync(CancellationToken cancellationToken = default);

    /// <summary>
    /// 下载更新
    /// </summary>
    Task<bool> DownloadUpdateAsync(UpdateInfo updateInfo, IProgress<DownloadProgress>? progress = null, CancellationToken cancellationToken = default);

    /// <summary>
    /// 安装更新
    /// </summary>
    Task<bool> InstallUpdateAsync(string updateFilePath);

    /// <summary>
    /// 获取当前版本信息
    /// </summary>
    VersionInfo GetCurrentVersion();

    /// <summary>
    /// 设置更新检查间隔
    /// </summary>
    void SetUpdateCheckInterval(TimeSpan interval);

    /// <summary>
    /// 启用或禁用自动更新检查
    /// </summary>
    void SetAutoUpdateEnabled(bool enabled);

    /// <summary>
    /// 更新可用事件
    /// </summary>
    event EventHandler<UpdateAvailableEventArgs>? UpdateAvailable;

    /// <summary>
    /// 更新下载完成事件
    /// </summary>
    event EventHandler<UpdateDownloadedEventArgs>? UpdateDownloaded;

    /// <summary>
    /// 更新错误事件
    /// </summary>
    event EventHandler<UpdateErrorEventArgs>? UpdateError;
}

/// <summary>
/// 更新信息
/// </summary>
public class UpdateInfo
{
    public string Version { get; set; } = string.Empty;
    public string DownloadUrl { get; set; } = string.Empty;
    public string ReleaseNotes { get; set; } = string.Empty;
    public DateTime ReleaseDate { get; set; }
    public long FileSize { get; set; }
    public string Checksum { get; set; } = string.Empty;
    public bool IsRequired { get; set; }
    public bool IsAvailable { get; set; }
}

/// <summary>
/// 版本信息
/// </summary>
public class VersionInfo
{
    public string Version { get; set; } = string.Empty;
    public string BuildDate { get; set; } = string.Empty;
    public string CommitHash { get; set; } = string.Empty;
    public string Branch { get; set; } = string.Empty;
}

/// <summary>
/// 下载进度
/// </summary>
public class DownloadProgress
{
    public long BytesReceived { get; set; }
    public long TotalBytes { get; set; }
    public double ProgressPercentage => TotalBytes > 0 ? (double)BytesReceived / TotalBytes * 100 : 0;
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan EstimatedTimeRemaining { get; set; }
    public double DownloadSpeed { get; set; } // bytes per second
}

/// <summary>
/// 更新可用事件参数
/// </summary>
public class UpdateAvailableEventArgs : EventArgs
{
    public UpdateInfo UpdateInfo { get; }

    public UpdateAvailableEventArgs(UpdateInfo updateInfo)
    {
        UpdateInfo = updateInfo;
    }
}

/// <summary>
/// 更新下载完成事件参数
/// </summary>
public class UpdateDownloadedEventArgs : EventArgs
{
    public string FilePath { get; }
    public UpdateInfo UpdateInfo { get; }

    public UpdateDownloadedEventArgs(string filePath, UpdateInfo updateInfo)
    {
        FilePath = filePath;
        UpdateInfo = updateInfo;
    }
}

/// <summary>
/// 更新错误事件参数
/// </summary>
public class UpdateErrorEventArgs : EventArgs
{
    public Exception Exception { get; }
    public string ErrorMessage { get; }

    public UpdateErrorEventArgs(Exception exception, string errorMessage)
    {
        Exception = exception;
        ErrorMessage = errorMessage;
    }
}
