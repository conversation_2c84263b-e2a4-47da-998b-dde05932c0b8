using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using System.Collections.ObjectModel;
using System.Windows.Input;

namespace DocxToMarkdownConverter.ViewModels;

/// <summary>
/// 进度显示 ViewModel，提供实时进度更新和统计信息
/// </summary>
public class ProgressViewModel : ViewModelBase, IDisposable
{
    private readonly IProgressTrackingService _progressTrackingService;
    private readonly ILoggingService _loggingService;
    private readonly TimeEstimationService _timeEstimationService;
    private bool _disposed = false;

    // 性能优化：属性更新节流
    private readonly System.Threading.Timer _updateTimer;
    private readonly object _updateLock = new object();
    private bool _hasUpdates = false;
    private DateTime _lastUpdate = DateTime.MinValue;
    private const int UPDATE_INTERVAL_MS = 100; // 限制UI更新频率为10fps

    /// <summary>
    /// 获取日志服务实例（用于共享给其他组件）
    /// </summary>
    public ILoggingService LoggingService => _loggingService;

    // 进度相关属性
    private ConversionProgress? _currentProgress;
    private BatchConversionProgress? _batchProgress;
    private ConversionStatistics _statistics;
    private bool _autoScrollEnabled = true;
    private string _logContent = string.Empty;

    // 显示属性
    private string _currentOperationText = "等待开始转换...";
    private double _overallProgress = 0;
    private string _overallProgressText = "0";
    private string _estimatedTimeText = "预计时间: --";
    private int _totalFiles = 0;
    private int _completedFiles = 0;
    private int _failedFiles = 0;
    private string _successRateText = "0%";
    private string _logStatisticsText = "日志: 0 条";
    private string _lastUpdateTimeText = "最后更新: --";
    private string _processingRateText = "处理速率: --";
    private string _throughputText = "吞吐量: --";

    // 命令
    public ICommand StartConversionCommand { get; }
    public ICommand PauseConversionCommand { get; }
    public ICommand StopConversionCommand { get; }
    public ICommand ClearLogCommand { get; }
    public ICommand SaveLogCommand { get; }

    public ProgressViewModel(
        IProgressTrackingService? progressTrackingService = null,
        ILoggingService? loggingService = null)
    {
        _progressTrackingService = progressTrackingService ?? new ProgressTrackingService(new SimpleLoggingService(Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleLoggingService>.Instance));
        _loggingService = loggingService ?? new SimpleLoggingService(Microsoft.Extensions.Logging.Abstractions.NullLogger<SimpleLoggingService>.Instance);
        _timeEstimationService = new TimeEstimationService();
        _statistics = new ConversionStatistics();

        // 初始化更新定时器
        _updateTimer = new System.Threading.Timer(OnTimerUpdate, null, Timeout.Infinite, Timeout.Infinite);

        // 订阅事件
        _progressTrackingService.ProgressUpdated += OnProgressUpdated;
        _progressTrackingService.BatchProgressUpdated += OnBatchProgressUpdated;
        _progressTrackingService.StatisticsUpdated += OnStatisticsUpdated;
        _loggingService.LogReceived += OnLogReceived;

        // 初始化命令
        StartConversionCommand = new RelayCommand(ExecuteStartConversion, CanStartConversion);
        PauseConversionCommand = new RelayCommand(ExecutePauseConversion, CanPauseConversion);
        StopConversionCommand = new RelayCommand(ExecuteStopConversion, CanStopConversion);
        ClearLogCommand = new RelayCommand(ExecuteClearLog);
        SaveLogCommand = new RelayCommand(ExecuteSaveLog, CanSaveLog);

        // 初始化显示
        UpdateDisplayProperties();
    }

    #region 属性

    public ConversionProgress? CurrentProgress
    {
        get => _currentProgress;
        set => SetProperty(ref _currentProgress, value);
    }

    public BatchConversionProgress? BatchProgress
    {
        get => _batchProgress;
        set => SetProperty(ref _batchProgress, value);
    }

    public ConversionStatistics Statistics
    {
        get => _statistics;
        set => SetProperty(ref _statistics, value);
    }

    public bool AutoScrollEnabled
    {
        get => _autoScrollEnabled;
        set => SetProperty(ref _autoScrollEnabled, value);
    }

    public string LogContent
    {
        get => _logContent;
        set => SetProperty(ref _logContent, value);
    }

    // 显示属性
    public string CurrentOperationText
    {
        get => _currentOperationText;
        set => SetProperty(ref _currentOperationText, value);
    }

    public double OverallProgress
    {
        get => _overallProgress;
        set => SetProperty(ref _overallProgress, value);
    }

    public string OverallProgressText
    {
        get => _overallProgressText;
        set => SetProperty(ref _overallProgressText, value);
    }

    public string EstimatedTimeText
    {
        get => _estimatedTimeText;
        set => SetProperty(ref _estimatedTimeText, value);
    }

    public int TotalFiles
    {
        get => _totalFiles;
        set => SetProperty(ref _totalFiles, value);
    }

    public int CompletedFiles
    {
        get => _completedFiles;
        set => SetProperty(ref _completedFiles, value);
    }

    public int FailedFiles
    {
        get => _failedFiles;
        set => SetProperty(ref _failedFiles, value);
    }

    public string SuccessRateText
    {
        get => _successRateText;
        set => SetProperty(ref _successRateText, value);
    }

    public string LogStatisticsText
    {
        get => _logStatisticsText;
        set => SetProperty(ref _logStatisticsText, value);
    }

    public string LastUpdateTimeText
    {
        get => _lastUpdateTimeText;
        set => SetProperty(ref _lastUpdateTimeText, value);
    }

    public string ProcessingRateText
    {
        get => _processingRateText;
        set => SetProperty(ref _processingRateText, value);
    }

    public string ThroughputText
    {
        get => _throughputText;
        set => SetProperty(ref _throughputText, value);
    }

    #endregion

    #region 事件处理

    private void OnProgressUpdated(object? sender, ConversionProgress progress)
    {
        CurrentProgress = progress;
        RequestUpdate(); // 使用节流更新
    }

    private void OnBatchProgressUpdated(object? sender, BatchConversionProgress batchProgress)
    {
        BatchProgress = batchProgress;
        RequestUpdate(); // 使用节流更新
    }

    private void OnStatisticsUpdated(object? sender, ConversionStatistics statistics)
    {
        Statistics = statistics;
        RequestUpdate(); // 使用节流更新
    }

    private void OnLogReceived(object? sender, LogEventArgs e)
    {
        System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
        {
            var timestamp = e.LogEntry.Timestamp.ToString("HH:mm:ss.fff");
            var level = e.LogEntry.Level.ToString().ToUpper();
            var message = e.LogEntry.Message;
            
            var logLine = $"[{timestamp}] [{level}] {message}";
            if (e.LogEntry.Exception != null)
            {
                logLine += $"\n    异常: {e.LogEntry.Exception.Message}";
            }

            LogContent += logLine + Environment.NewLine;
            
            // 限制日志内容长度
            var lines = LogContent.Split(Environment.NewLine);
            if (lines.Length > 1000)
            {
                LogContent = string.Join(Environment.NewLine, lines.Skip(lines.Length - 1000));
            }

            UpdateLogStatistics();
        });
    }

    #endregion

    #region 显示更新

    /// <summary>
    /// 节流更新方法，避免过于频繁的UI更新
    /// </summary>
    private void RequestUpdate()
    {
        lock (_updateLock)
        {
            _hasUpdates = true;

            // 如果距离上次更新时间太短，启动定时器延迟更新
            var timeSinceLastUpdate = DateTime.Now - _lastUpdate;
            if (timeSinceLastUpdate.TotalMilliseconds < UPDATE_INTERVAL_MS)
            {
                _updateTimer.Change(UPDATE_INTERVAL_MS, Timeout.Infinite);
            }
            else
            {
                // 立即更新
                PerformUpdate();
            }
        }
    }

    /// <summary>
    /// 定时器回调，执行延迟更新
    /// </summary>
    private void OnTimerUpdate(object? state)
    {
        lock (_updateLock)
        {
            if (_hasUpdates)
            {
                PerformUpdate();
            }
        }
    }

    /// <summary>
    /// 执行实际的UI更新
    /// </summary>
    private void PerformUpdate()
    {
        if (System.Windows.Application.Current?.Dispatcher.CheckAccess() == true)
        {
            UpdateDisplayProperties();
        }
        else
        {
            System.Windows.Application.Current?.Dispatcher.BeginInvoke(UpdateDisplayProperties);
        }

        _lastUpdate = DateTime.Now;
        _hasUpdates = false;
    }

    private void UpdateDisplayProperties()
    {
        // 更新当前操作文本
        if (CurrentProgress != null)
        {
            CurrentOperationText = $"{CurrentProgress.CurrentOperation} - {CurrentProgress.CurrentFile}";
            OverallProgress = CurrentProgress.ProgressPercentage;
            OverallProgressText = $"{CurrentProgress.ProgressPercentage:F0}";
            
            // 更新时间估算
            if (CurrentProgress.EstimatedTimeRemaining.HasValue)
            {
                var remaining = CurrentProgress.EstimatedTimeRemaining.Value;
                if (remaining.TotalHours >= 1)
                {
                    EstimatedTimeText = $"预计剩余: {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2}";
                }
                else
                {
                    EstimatedTimeText = $"预计剩余: {remaining.Minutes:D2}:{remaining.Seconds:D2}";
                }
            }
            else
            {
                EstimatedTimeText = "预计时间: 计算中...";
            }
        }
        else if (BatchProgress != null)
        {
            CurrentOperationText = $"批量转换进行中 ({BatchProgress.CompletedFiles}/{BatchProgress.TotalFiles})";
            OverallProgress = BatchProgress.OverallProgress;
            OverallProgressText = $"{BatchProgress.OverallProgress:F0}";
            
            if (BatchProgress.EstimatedTimeRemaining.HasValue)
            {
                var remaining = BatchProgress.EstimatedTimeRemaining.Value;
                if (remaining.TotalHours >= 1)
                {
                    EstimatedTimeText = $"预计剩余: {remaining.Hours:D2}:{remaining.Minutes:D2}:{remaining.Seconds:D2}";
                }
                else
                {
                    EstimatedTimeText = $"预计剩余: {remaining.Minutes:D2}:{remaining.Seconds:D2}";
                }
            }
        }

        // 更新批量进度统计
        if (BatchProgress != null)
        {
            TotalFiles = BatchProgress.TotalFiles;
            CompletedFiles = BatchProgress.CompletedFiles;
            FailedFiles = BatchProgress.FailedFiles;
            
            var successRate = TotalFiles > 0 ? (double)BatchProgress.SuccessfulFiles / TotalFiles * 100 : 0;
            SuccessRateText = $"{successRate:F0}%";
        }
        else if (Statistics != null)
        {
            TotalFiles = Statistics.TotalFiles;
            CompletedFiles = Statistics.CompletedFiles;
            FailedFiles = Statistics.FailedFiles;
            SuccessRateText = $"{Statistics.SuccessRate:F0}%";
        }

        LastUpdateTimeText = $"最后更新: {DateTime.Now:HH:mm:ss}";
        
        // 更新性能统计
        UpdatePerformanceStatistics();
    }

    private void UpdatePerformanceStatistics()
    {
        if (Statistics != null && Statistics.TotalBatchTime.TotalSeconds > 0)
        {
            var filesPerSecond = Statistics.TotalFilesProcessed / Statistics.TotalBatchTime.TotalSeconds;
            ProcessingRateText = $"处理速率: {filesPerSecond:F2} 文件/秒";
            
            if (Statistics.SuccessfulConversions > 0)
            {
                var avgTimePerFile = Statistics.AverageProcessingTime.TotalSeconds;
                ThroughputText = $"吞吐量: {avgTimePerFile:F2} 秒/文件";
            }
            else
            {
                ThroughputText = "吞吐量: --";
            }
        }
        else
        {
            ProcessingRateText = "处理速率: --";
            ThroughputText = "吞吐量: --";
        }
    }

    private void UpdateLogStatistics()
    {
        var lines = LogContent.Split(Environment.NewLine, StringSplitOptions.RemoveEmptyEntries);
        var errorCount = lines.Count(line => line.Contains("[ERROR]"));
        var warningCount = lines.Count(line => line.Contains("[WARNING]"));
        
        LogStatisticsText = $"日志: {lines.Length} 条 (错误: {errorCount}, 警告: {warningCount})";
    }

    #endregion

    #region 命令实现

    private void ExecuteStartConversion()
    {
        _loggingService.LogInfo("用户启动转换操作");
        // 这里应该触发主窗口的转换逻辑
        // 实际实现会在主窗口ViewModel中处理
    }

    private bool CanStartConversion()
    {
        return CurrentProgress == null || CurrentProgress.ProgressPercentage == 0 || CurrentProgress.ProgressPercentage >= 100;
    }

    private void ExecutePauseConversion()
    {
        _loggingService.LogInfo("用户暂停转换操作");
        // 暂停逻辑
    }

    private bool CanPauseConversion()
    {
        return CurrentProgress != null && CurrentProgress.ProgressPercentage > 0 && CurrentProgress.ProgressPercentage < 100;
    }

    private void ExecuteStopConversion()
    {
        _loggingService.LogInfo("用户停止转换操作");
        // 停止逻辑
    }

    private bool CanStopConversion()
    {
        return CurrentProgress != null && CurrentProgress.ProgressPercentage > 0 && CurrentProgress.ProgressPercentage < 100;
    }

    private void ExecuteClearLog()
    {
        LogContent = string.Empty;
        _loggingService.ClearLogs();
        UpdateLogStatistics();
        _loggingService.LogInfo("日志已清空");
    }

    private void ExecuteSaveLog()
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "保存日志文件",
                Filter = "文本文件 (*.txt)|*.txt|所有文件 (*.*)|*.*",
                DefaultExt = "txt",
                FileName = $"conversion_log_{DateTime.Now:yyyyMMdd_HHmmss}.txt"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                var content = GenerateLogFileContent();
                System.IO.File.WriteAllText(saveFileDialog.FileName, content, System.Text.Encoding.UTF8);
                _loggingService.LogInfo($"日志已保存到: {saveFileDialog.FileName}");
            }
        }
        catch (Exception ex)
        {
            _loggingService.LogError(ex, "保存日志失败");
        }
    }

    private bool CanSaveLog()
    {
        return !string.IsNullOrEmpty(LogContent);
    }

    private string GenerateLogFileContent()
    {
        var sb = new System.Text.StringBuilder();
        sb.AppendLine("DOCX to Markdown Converter - 转换日志");
        sb.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        sb.AppendLine($"总文件数: {TotalFiles}");
        sb.AppendLine($"完成数: {CompletedFiles}");
        sb.AppendLine($"失败数: {FailedFiles}");
        sb.AppendLine($"成功率: {SuccessRateText}");
        sb.AppendLine(new string('=', 80));
        sb.AppendLine();
        sb.AppendLine(LogContent);
        return sb.ToString();
    }

    #endregion

    #region IDisposable

    public new void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                // 取消订阅事件
                if (_progressTrackingService != null)
                {
                    _progressTrackingService.ProgressUpdated -= OnProgressUpdated;
                    _progressTrackingService.BatchProgressUpdated -= OnBatchProgressUpdated;
                    _progressTrackingService.StatisticsUpdated -= OnStatisticsUpdated;
                }

                if (_loggingService != null)
                {
                    _loggingService.LogReceived -= OnLogReceived;
                }

                // 清理定时器
                _updateTimer?.Dispose();
            }
            _disposed = true;
        }
    }

    #endregion
}