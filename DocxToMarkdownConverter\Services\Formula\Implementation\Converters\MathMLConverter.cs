using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;
using System.Text;
using System.Xml;

namespace DocxToMarkdownConverter.Services.Formula.Implementation.Converters;

/// <summary>
/// MathML转换器实现
/// </summary>
public class MathMLConverter : IFormulaConverter
{
    private readonly ILogger<MathMLConverter> _logger;
    private const string MathMLNamespace = "http://www.w3.org/1998/Math/MathML";

    public MathMLConverter(ILogger<MathMLConverter> logger)
    {
        _logger = logger;
    }

    public FormulaOutputFormat[] SupportedFormats => new[] { FormulaOutputFormat.MathML };

    public bool SupportsFormat(FormulaOutputFormat format)
    {
        return format == FormulaOutputFormat.MathML;
    }

    public async Task<string> ConvertAsync(FormulaStructure structure, FormulaOutputFormat format)
    {
        if (!SupportsFormat(format))
        {
            throw new ArgumentException($"Unsupported format: {format}");
        }

        try
        {
            _logger.LogDebug("Converting structure to MathML: {StructureType}", structure.GetType().Name);

            var doc = new XmlDocument();
            var mathElement = CreateMathElement(doc);
            
            var contentElement = structure switch
            {
                FractionStructure fraction => ConvertFraction(doc, fraction),
                MatrixStructure matrix => ConvertMatrix(doc, matrix),
                PiecewiseFunctionStructure piecewise => ConvertPiecewiseFunction(doc, piecewise),
                RadicalStructure radical => ConvertRadical(doc, radical),
                ScriptStructure script => ConvertScript(doc, script),
                _ => ConvertGeneric(doc, structure)
            };

            mathElement.AppendChild(contentElement);
            doc.AppendChild(mathElement);

            var result = FormatXmlOutput(doc);
            _logger.LogDebug("Successfully converted to MathML");
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting structure to MathML");
            return CreateErrorMathML(ex.Message);
        }
    }

    #region Specific Converters

    private XmlElement ConvertFraction(XmlDocument doc, FractionStructure fraction)
    {
        try
        {
            var mfrac = CreateElement(doc, "mfrac");

            // 分子
            var numerator = ConvertComponent(doc, fraction.Numerator);
            mfrac.AppendChild(numerator);

            // 分母
            var denominator = ConvertComponent(doc, fraction.Denominator);
            mfrac.AppendChild(denominator);

            // 设置分数类型属性
            if (fraction.FractionType != FractionType.Bar)
            {
                mfrac.SetAttribute("linethickness", GetLineThickness(fraction.FractionType));
            }

            return mfrac;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting fraction to MathML");
            return CreateErrorElement(doc, "Invalid fraction");
        }
    }

    private XmlElement ConvertMatrix(XmlDocument doc, MatrixStructure matrix)
    {
        try
        {
            var mtable = CreateElement(doc, "mtable");

            foreach (var row in matrix.Rows)
            {
                var mtr = CreateElement(doc, "mtr");

                foreach (var cell in row.Cells)
                {
                    var mtd = CreateElement(doc, "mtd");
                    var cellContent = ConvertComponent(doc, cell);
                    mtd.AppendChild(cellContent);
                    mtr.AppendChild(mtd);
                }

                mtable.AppendChild(mtr);
            }

            // 如果是分段函数，添加左大括号
            if (matrix.IsPiecewise)
            {
                var mrow = CreateElement(doc, "mrow");
                var mo = CreateElement(doc, "mo");
                mo.InnerText = "{";
                mrow.AppendChild(mo);
                mrow.AppendChild(mtable);
                return mrow;
            }

            return mtable;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting matrix to MathML");
            return CreateErrorElement(doc, "Invalid matrix");
        }
    }

    private XmlElement ConvertPiecewiseFunction(XmlDocument doc, PiecewiseFunctionStructure piecewise)
    {
        try
        {
            var mrow = CreateElement(doc, "mrow");
            
            // 左大括号
            var mo = CreateElement(doc, "mo");
            mo.InnerText = "{";
            mrow.AppendChild(mo);

            // 创建表格
            var mtable = CreateElement(doc, "mtable");

            foreach (var case_ in piecewise.Cases)
            {
                var mtr = CreateElement(doc, "mtr");

                // 表达式列
                var mtd1 = CreateElement(doc, "mtd");
                var expressionElement = CreateTextElement(doc, case_.Expression);
                mtd1.AppendChild(expressionElement);
                mtr.AppendChild(mtd1);

                // 条件列
                var mtd2 = CreateElement(doc, "mtd");
                var conditionElement = CreateTextElement(doc, case_.Condition);
                mtd2.AppendChild(conditionElement);
                mtr.AppendChild(mtd2);

                mtable.AppendChild(mtr);
            }

            mrow.AppendChild(mtable);
            return mrow;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting piecewise function to MathML");
            return CreateErrorElement(doc, "Invalid piecewise function");
        }
    }

    private XmlElement ConvertRadical(XmlDocument doc, RadicalStructure radical)
    {
        try
        {
            if (radical.Index != null)
            {
                // 带指数的根式
                var mroot = CreateElement(doc, "mroot");
                var radicand = ConvertComponent(doc, radical.Radicand);
                var index = ConvertComponent(doc, radical.Index);
                
                mroot.AppendChild(radicand);
                mroot.AppendChild(index);
                return mroot;
            }
            else
            {
                // 平方根
                var msqrt = CreateElement(doc, "msqrt");
                var radicand = ConvertComponent(doc, radical.Radicand);
                msqrt.AppendChild(radicand);
                return msqrt;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting radical to MathML");
            return CreateErrorElement(doc, "Invalid radical");
        }
    }

    private XmlElement ConvertScript(XmlDocument doc, ScriptStructure script)
    {
        try
        {
            var baseElement = ConvertComponent(doc, script.Base);

            if (script.Subscript != null && script.Superscript != null)
            {
                // 上下标
                var msubsup = CreateElement(doc, "msubsup");
                msubsup.AppendChild(baseElement);
                msubsup.AppendChild(ConvertComponent(doc, script.Subscript));
                msubsup.AppendChild(ConvertComponent(doc, script.Superscript));
                return msubsup;
            }
            else if (script.Subscript != null)
            {
                // 下标
                var msub = CreateElement(doc, "msub");
                msub.AppendChild(baseElement);
                msub.AppendChild(ConvertComponent(doc, script.Subscript));
                return msub;
            }
            else if (script.Superscript != null)
            {
                // 上标
                var msup = CreateElement(doc, "msup");
                msup.AppendChild(baseElement);
                msup.AppendChild(ConvertComponent(doc, script.Superscript));
                return msup;
            }

            return baseElement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting script to MathML");
            return CreateErrorElement(doc, "Invalid script");
        }
    }

    private XmlElement ConvertGeneric(XmlDocument doc, FormulaStructure structure)
    {
        try
        {
            var text = structure.GetTextRepresentation();
            return CreateTextElement(doc, text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting generic structure to MathML");
            return CreateErrorElement(doc, "Unknown structure");
        }
    }

    #endregion

    #region Helper Methods

    private XmlElement CreateMathElement(XmlDocument doc)
    {
        var math = doc.CreateElement("math", MathMLNamespace);
        math.SetAttribute("xmlns", MathMLNamespace);
        return math;
    }

    private XmlElement CreateElement(XmlDocument doc, string name)
    {
        return doc.CreateElement(name, MathMLNamespace);
    }

    private XmlElement CreateTextElement(XmlDocument doc, string text)
    {
        if (string.IsNullOrEmpty(text))
        {
            var mspace = CreateElement(doc, "mspace");
            return mspace;
        }

        // 判断是否为数字、标识符或运算符
        if (IsNumber(text))
        {
            var mn = CreateElement(doc, "mn");
            mn.InnerText = text;
            return mn;
        }
        else if (IsOperator(text))
        {
            var mo = CreateElement(doc, "mo");
            mo.InnerText = text;
            return mo;
        }
        else
        {
            var mi = CreateElement(doc, "mi");
            mi.InnerText = text;
            return mi;
        }
    }

    private XmlElement ConvertComponent(XmlDocument doc, FormulaComponent component)
    {
        try
        {
            if (component.Children.Any())
            {
                var mrow = CreateElement(doc, "mrow");
                foreach (var child in component.Children)
                {
                    var childElement = ConvertComponent(doc, child);
                    mrow.AppendChild(childElement);
                }
                return mrow;
            }

            var text = component.Data?.ToString() ?? "";
            return CreateTextElement(doc, text);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error converting component to MathML");
            return CreateErrorElement(doc, "Error");
        }
    }

    private XmlElement CreateErrorElement(XmlDocument doc, string message)
    {
        var merror = CreateElement(doc, "merror");
        var mtext = CreateElement(doc, "mtext");
        mtext.InnerText = message;
        merror.AppendChild(mtext);
        return merror;
    }

    private string GetLineThickness(FractionType fractionType)
    {
        return fractionType switch
        {
            FractionType.NoBar => "0",
            FractionType.Linear => "0",
            FractionType.Skewed => "thin",
            _ => "medium"
        };
    }

    private bool IsNumber(string text)
    {
        return double.TryParse(text, out _);
    }

    private bool IsOperator(string text)
    {
        var operators = new HashSet<string>
        {
            "+", "-", "×", "÷", "=", "≠", "≤", "≥", "<", ">",
            "∑", "∏", "∫", "∮", "∂", "∇", "±", "∓",
            "(", ")", "[", "]", "{", "}", "|",
            "∈", "∉", "⊂", "⊃", "∪", "∩", "∅"
        };

        return operators.Contains(text) || text.Length == 1 && char.IsSymbol(text[0]);
    }

    private string FormatXmlOutput(XmlDocument doc)
    {
        try
        {
            var settings = new XmlWriterSettings
            {
                Indent = true,
                IndentChars = "  ",
                NewLineChars = "\n",
                OmitXmlDeclaration = true
            };

            using var stringWriter = new StringWriter();
            using var xmlWriter = XmlWriter.Create(stringWriter, settings);
            doc.WriteTo(xmlWriter);
            xmlWriter.Flush();

            return stringWriter.ToString();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error formatting XML output");
            return doc.OuterXml;
        }
    }

    private string CreateErrorMathML(string message)
    {
        return $@"<math xmlns=""{MathMLNamespace}"">
  <merror>
    <mtext>Error: {message}</mtext>
  </merror>
</math>";
    }

    #endregion
}
