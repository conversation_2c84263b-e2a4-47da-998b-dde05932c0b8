using System.IO;
using System.Windows;
using System.Windows.Media;
using System.Windows.Media.Animation;
using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.Views;
using UserControl = System.Windows.Controls.UserControl;
using DragEventArgs = System.Windows.DragEventArgs;
using DragDropEffects = System.Windows.DragDropEffects;
using DataFormats = System.Windows.DataFormats;

using MessageBox = System.Windows.MessageBox;
using System.Windows.Controls;
using Orientation = System.Windows.Controls.Orientation;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using MaterialDesignThemes.Wpf;
using System;
using System.Text;
using Color = System.Windows.Media.Color;
using Point = System.Windows.Point;

namespace DocxToMarkdownConverter.Controls;



/// <summary>
/// Interaction logic for FileListControl.xaml
/// </summary>
public partial class FileListControl : UserControl
{
    private IAnimationManager? _animationManager;
    private bool _isDragOver = false;
    private ILoggingService? _loggingService = null;

    public FileListControl()
    {
        InitializeComponent();
        Loaded += OnControlLoaded;
    }
    
    private void OnControlLoaded(object sender, RoutedEventArgs e)
    {
        // Initialize animation manager and attach hover animations
        InitializeAnimationManager();
        AttachButtonHoverAnimations();
    }

    private void InitializeAnimationManager()
    {
        // Get animation manager from the main window
        if (System.Windows.Application.Current.MainWindow is MainWindow mainWindow)
        {
            _animationManager = GetAnimationManagerFromMainWindow(mainWindow);
        }
    }

    private void AttachButtonHoverAnimations()
    {
        if (_animationManager != null)
        {
            // Find and attach animations to all buttons in this control
            AttachAnimationsToChildButtons(this, _animationManager);
        }
    }
    
    private IAnimationManager? GetAnimationManagerFromMainWindow(MainWindow mainWindow)
    {
        // Use reflection to get the private field
        var field = typeof(MainWindow).GetField("_animationManager", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        return field?.GetValue(mainWindow) as IAnimationManager;
    }
    
    private void AttachAnimationsToChildButtons(DependencyObject parent, IAnimationManager animationManager)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);
            
            if (child is System.Windows.Controls.Button button)
            {
                animationManager.AttachHoverAnimations(button);
            }
            else
            {
                AttachAnimationsToChildButtons(child, animationManager);
            }
        }
    }

    #region File Processing

    /// <summary>
    /// 处理文件添加
    /// </summary>
    private void ProcessFiles(string[] validFiles, int invalidFileCount)
    {
        System.Diagnostics.Debug.WriteLine($"ProcessFiles: 开始处理 {validFiles.Length} 个文件");
        try
        {
            if (DataContext is MainWindowViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine("ProcessFiles: 调用 viewModel.AddFiles");
                // 直接调用AddFiles方法，让它处理异步逻辑
                viewModel.AddFiles(validFiles);
                System.Diagnostics.Debug.WriteLine("ProcessFiles: viewModel.AddFiles 调用完成");

                // 显示成功消息
                ShowSuccessMessage(validFiles.Length);
                if (invalidFileCount > 0)
                {
                    ShowErrorMessage($"成功添加 {validFiles.Length} 个文件，忽略了 {invalidFileCount} 个无效文件（仅支持.docx格式）");
                }
            }
            else
            {
                ShowErrorMessage("无法获取视图模型，文件添加失败");
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"添加文件时发生错误: {ex.Message}");
        }
    }

    #endregion

    #region Helper Methods

    /// <summary>
    /// 验证文件是否为有效的DOCX文件（仅检查扩展名，用于拖拽预检查）
    /// </summary>
    private bool IsValidDocxFile(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            // 在拖拽阶段只检查扩展名，避免阻塞UI线程
            // 实际的文件存在性检查将在AddFilesAsync中进行
            return Path.GetExtension(filePath).Equals(".docx", StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"验证文件扩展名时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 显示成功提示
    /// </summary>
    private void ShowSuccessMessage(int fileCount)
    {
        try
        {
            // 记录日志
            _loggingService?.LogInfo($"成功添加 {fileCount} 个文件");

            // 可以在这里添加更好的用户反馈，比如状态栏消息或临时提示
            // 暂时使用简单的日志记录
        }
        catch (Exception ex)
        {
            // 即使日志记录失败也不应该影响主要功能
            System.Diagnostics.Debug.WriteLine($"记录成功消息时发生错误: {ex.Message}");
        }
    }

    /// <summary>
    /// 显示错误提示
    /// </summary>
    private void ShowErrorMessage(string message)
    {
        try
        {
            // 记录错误日志
            _loggingService?.LogError(message);

            // 在UI线程中显示错误对话框
            if (Dispatcher.CheckAccess())
            {
                MessageBox.Show(message, "文件添加错误", MessageBoxButton.OK, MessageBoxImage.Warning);
            }
            else
            {
                Dispatcher.Invoke(() =>
                {
                    MessageBox.Show(message, "文件添加错误", MessageBoxButton.OK, MessageBoxImage.Warning);
                });
            }
        }
        catch (Exception ex)
        {
            // 如果连错误显示都失败了，至少输出到调试控制台
            System.Diagnostics.Debug.WriteLine($"显示错误消息时发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"原始错误消息: {message}");
        }
    }

    #endregion

    #region Drag and Drop Event Handlers

    private void OnDragEnter(object sender, DragEventArgs e)
    {
        try
        {
            if (HasValidFiles(e.Data))
            {
                e.Effects = DragDropEffects.Copy;
                ShowDragOverlay();
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"处理拖拽进入事件时发生错误: {ex.Message}");
            e.Effects = DragDropEffects.None;
        }
        e.Handled = true;
    }

    private void OnDragOver(object sender, DragEventArgs e)
    {
        try
        {
            if (HasValidFiles(e.Data))
            {
                e.Effects = DragDropEffects.Copy;
                // 确保覆盖层显示
                if (!_isDragOver)
                {
                    ShowDragOverlay();
                }
            }
            else
            {
                e.Effects = DragDropEffects.None;
            }
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"处理拖拽悬停事件时发生错误: {ex.Message}");
            e.Effects = DragDropEffects.None;
        }
        e.Handled = true;
    }

    private void OnDragLeave(object sender, DragEventArgs e)
    {
        try
        {
            HideDragOverlay();
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"处理拖拽离开事件时发生错误: {ex.Message}");
        }
        e.Handled = true;
    }

    private void OnDrop(object sender, DragEventArgs e)
    {
        System.Diagnostics.Debug.WriteLine("OnDrop: 开始处理拖拽事件");
        _isDragOver = false;
        HideDragOverlay();

        try
        {
            if (e.Data.GetDataPresent(DataFormats.FileDrop))
            {
                var files = (string[])e.Data.GetData(DataFormats.FileDrop);
                if (files == null || files.Length == 0)
                {
                    ShowErrorMessage("没有检测到拖拽的文件");
                    e.Handled = true;
                    return;
                }

                var validFiles = files.Where(IsValidDocxFile).ToArray();
                var invalidFileCount = files.Length - validFiles.Length;

                if (validFiles.Length > 0)
                {
                    System.Diagnostics.Debug.WriteLine($"OnDrop: 找到 {validFiles.Length} 个有效文件，开始处理");
                    // 直接在UI线程中调用处理方法
                    ProcessFiles(validFiles, invalidFileCount);
                    System.Diagnostics.Debug.WriteLine("OnDrop: 文件处理完成");
                }
                else
                {
                    if (invalidFileCount > 0)
                    {
                        ShowErrorMessage($"拖拽的 {invalidFileCount} 个文件都不是有效的DOCX文件。请只拖拽.docx格式的文件。");
                    }
                    else
                    {
                        ShowErrorMessage("没有找到有效的DOCX文件");
                    }
                }
            }
            else
            {
                ShowErrorMessage("拖拽的数据不包含文件");
            }
        }
        catch (Exception ex)
        {
            ShowErrorMessage($"处理拖拽文件时发生错误: {ex.Message}");
        }

        e.Handled = true;
    }

    /// <summary>
    /// 检查拖拽数据是否包含有效文件
    /// </summary>
    private bool HasValidFiles(System.Windows.IDataObject dataObject)
    {
        try
        {
            if (dataObject == null)
                return false;

            if (!dataObject.GetDataPresent(DataFormats.FileDrop))
                return false;

            var files = (string[])dataObject.GetData(DataFormats.FileDrop);
            if (files == null || files.Length == 0)
                return false;

            // 检查是否至少有一个有效的DOCX文件
            return files.Any(IsValidDocxFile);
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"检查拖拽文件有效性时发生错误: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// 显示拖拽覆盖层
    /// </summary>
    private void ShowDragOverlay()
    {
        if (DragDropOverlay != null && !_isDragOver)
        {
            _isDragOver = true;
            DragDropOverlay.Visibility = Visibility.Visible;
        }
    }

    /// <summary>
    /// 隐藏拖拽覆盖层
    /// </summary>
    private void HideDragOverlay()
    {
        if (DragDropOverlay != null && _isDragOver)
        {
            _isDragOver = false;
            DragDropOverlay.Visibility = Visibility.Collapsed;
        }
    }





    #endregion

    #region Public Methods

    /// <summary>
    /// Programmatically trigger file selection dialog
    /// </summary>
    public void ShowAddFilesDialog()
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.AddFilesCommand.Execute(null);
        }
    }

    /// <summary>
    /// Clear all files from the list
    /// </summary>
    public void ClearAllFiles()
    {
        if (DataContext is MainWindowViewModel viewModel)
        {
            viewModel.ClearFilesCommand.Execute(null);
        }
    }

    /// <summary>
    /// Get the current file count
    /// </summary>
    public int FileCount => (DataContext as MainWindowViewModel)?.Files.Count ?? 0;

    #endregion
}