using System.Globalization;
using System.Windows;
using System.Windows.Data;
using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts ConversionStatus to Visibility for progress bar
/// </summary>
public class StatusToProgressVisibilityConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ConversionStatus status)
        {
            return status switch
            {
                ConversionStatus.Processing => Visibility.Visible,
                ConversionStatus.InProgress => Visibility.Visible,
                _ => Visibility.Collapsed
            };
        }

        return Visibility.Collapsed;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}