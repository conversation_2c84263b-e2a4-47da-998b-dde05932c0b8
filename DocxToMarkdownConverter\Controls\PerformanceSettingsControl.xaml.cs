using System.Windows;
using System.Windows.Controls;
using System.Windows.Input;
using DocxToMarkdownConverter.ViewModels;

namespace DocxToMarkdownConverter.Controls;

/// <summary>
/// PerformanceSettingsControl.xaml 的交互逻辑
/// </summary>
public partial class PerformanceSettingsControl : System.Windows.Controls.UserControl
{
    public PerformanceSettingsControl()
    {
        InitializeComponent();
        InitializeScrollHandling();
    }

    public PerformanceSettingsControl(PerformanceSettingsViewModel viewModel)
    {
        InitializeComponent();
        DataContext = viewModel;
        InitializeScrollHandling();
    }

    /// <summary>
    /// 初始化滚动事件处理，确保滚动事件能够正确传递到父级ScrollViewer
    /// </summary>
    private void InitializeScrollHandling()
    {
        // 设置控件属性以确保滚动事件能够正确传递
        this.SetValue(ScrollViewer.CanContentScrollProperty, false);
        this.SetValue(ScrollViewer.HorizontalScrollBarVisibilityProperty, ScrollBarVisibility.Disabled);
        this.SetValue(ScrollViewer.VerticalScrollBarVisibilityProperty, ScrollBarVisibility.Disabled);
    }
}
