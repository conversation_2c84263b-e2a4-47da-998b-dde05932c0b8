using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Wordprocessing;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation;

/// <summary>
/// Office Math检测器
/// </summary>
public class OfficeMathDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.OfficeMath;

    public bool CanDetect(object element)
    {
        return element is OfficeMath or Paragraph or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            OfficeMath => true,
            Paragraph paragraph => paragraph.Descendants<OfficeMath>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<OfficeMath>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 分段函数检测器
/// </summary>
public class PiecewiseFunctionDetector : IFormulaTypeDetector
{
    private readonly ILogger<PiecewiseFunctionDetector> _logger;

    public PiecewiseFunctionDetector(ILogger<PiecewiseFunctionDetector> logger)
    {
        _logger = logger;
    }

    public FormulaType DetectedType => FormulaType.PiecewiseFunction;

    public bool CanDetect(object element)
    {
        return element is DocumentFormat.OpenXml.Math.Matrix or OfficeMath or 
               DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        try
        {
            return await Task.FromResult(element switch
            {
                DocumentFormat.OpenXml.Math.Matrix matrix => IsPiecewiseMatrix(matrix),
                OfficeMath officeMath => ContainsPiecewiseMatrix(officeMath),
                DocumentFormat.OpenXml.OpenXmlElement xmlElement => ContainsPiecewisePattern(xmlElement),
                _ => false
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error detecting piecewise function");
            return false;
        }
    }

    private bool IsPiecewiseMatrix(DocumentFormat.OpenXml.Math.Matrix matrix)
    {
        try
        {
            var matrixText = matrix.InnerText?.ToLower() ?? "";
            
            // 检查条件指示词
            var hasConditions = matrixText.Contains("if") ||
                              matrixText.Contains("when") ||
                              matrixText.Contains("otherwise") ||
                              matrixText.Contains("else") ||
                              matrixText.Contains("条件") ||
                              matrixText.Contains("如果") ||
                              matrixText.Contains("否则");

            // 检查比较运算符
            var hasComparisons = matrixText.Contains("≤") ||
                               matrixText.Contains("≥") ||
                               matrixText.Contains("<=") ||
                               matrixText.Contains(">=") ||
                               matrixText.Contains("<") ||
                               matrixText.Contains(">") ||
                               matrixText.Contains("=");

            // 检查多行结构
            var rows = matrix.Elements<MatrixRow>().ToList();
            var hasMultipleRows = rows.Count > 1;

            return hasConditions || (hasComparisons && hasMultipleRows);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking if matrix is piecewise");
            return false;
        }
    }

    private bool ContainsPiecewiseMatrix(OfficeMath officeMath)
    {
        try
        {
            var matrices = officeMath.Descendants<DocumentFormat.OpenXml.Math.Matrix>();
            return matrices.Any(IsPiecewiseMatrix);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking OfficeMath for piecewise matrix");
            return false;
        }
    }

    private bool ContainsPiecewisePattern(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        try
        {
            var text = element.InnerText?.ToLower() ?? "";
            
            // 检查分段函数的文本模式
            var patterns = new[]
            {
                "if.*else",
                "when.*otherwise",
                "\\{.*,.*\\}",
                "cases"
            };

            return patterns.Any(pattern => 
                System.Text.RegularExpressions.Regex.IsMatch(text, pattern, 
                    System.Text.RegularExpressions.RegexOptions.IgnoreCase));
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking element for piecewise pattern");
            return false;
        }
    }
}

/// <summary>
/// 矩阵检测器
/// </summary>
public class MatrixDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Matrix;

    public bool CanDetect(object element)
    {
        return element is DocumentFormat.OpenXml.Math.Matrix or OfficeMath or 
               DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            DocumentFormat.OpenXml.Math.Matrix => true,
            OfficeMath officeMath => officeMath.Descendants<DocumentFormat.OpenXml.Math.Matrix>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<DocumentFormat.OpenXml.Math.Matrix>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 分数检测器
/// </summary>
public class FractionDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Fraction;

    public bool CanDetect(object element)
    {
        return element is Fraction or OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            Fraction => true,
            OfficeMath officeMath => officeMath.Descendants<Fraction>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<Fraction>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 根式检测器
/// </summary>
public class RadicalDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Radical;

    public bool CanDetect(object element)
    {
        return element is Radical or OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            Radical => true,
            OfficeMath officeMath => officeMath.Descendants<Radical>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<Radical>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 上下标检测器
/// </summary>
public class ScriptDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Superscript; // 默认类型，实际会根据具体情况调整

    public bool CanDetect(object element)
    {
        return element is Superscript or Subscript or SubSuperscript or 
               OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            Superscript or Subscript or SubSuperscript => true,
            OfficeMath officeMath => HasScriptElements(officeMath),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => HasScriptElements(xmlElement),
            _ => false
        });
    }

    private bool HasScriptElements(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        return element.Descendants<Superscript>().Any() ||
               element.Descendants<Subscript>().Any() ||
               element.Descendants<SubSuperscript>().Any();
    }
}

/// <summary>
/// N元运算符检测器
/// </summary>
public class NaryDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Nary;

    public bool CanDetect(object element)
    {
        return element is Nary or OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            Nary => true,
            OfficeMath officeMath => officeMath.Descendants<Nary>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<Nary>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 分隔符检测器
/// </summary>
public class DelimiterDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.Delimiter;

    public bool CanDetect(object element)
    {
        return element is Delimiter or OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            Delimiter => true,
            OfficeMath officeMath => officeMath.Descendants<Delimiter>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<Delimiter>().Any(),
            _ => false
        });
    }
}

/// <summary>
/// 数学函数检测器
/// </summary>
public class FunctionDetector : IFormulaTypeDetector
{
    public FormulaType DetectedType => FormulaType.MathFunction;

    public bool CanDetect(object element)
    {
        return element is MathFunction or OfficeMath or DocumentFormat.OpenXml.OpenXmlElement;
    }

    public async Task<bool> DetectAsync(object element)
    {
        return await Task.FromResult(element switch
        {
            MathFunction => true,
            OfficeMath officeMath => officeMath.Descendants<MathFunction>().Any(),
            DocumentFormat.OpenXml.OpenXmlElement xmlElement => xmlElement.Descendants<MathFunction>().Any(),
            _ => false
        });
    }
}
