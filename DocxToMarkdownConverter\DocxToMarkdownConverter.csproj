<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0-windows</TargetFramework>
    <UseWPF>true</UseWPF>
    <UseWindowsForms>true</UseWindowsForms>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <StartupObject>DocxToMarkdownConverter.App</StartupObject>

    <!-- 应用程序信息 -->
    <AssemblyTitle>DOCX to Markdown Converter</AssemblyTitle>
    <AssemblyDescription>专业的DOCX文档转Markdown工具，支持批量转换、图片提取、表格转换等功能</AssemblyDescription>
    <AssemblyCompany>Augment Code</AssemblyCompany>
    <AssemblyProduct>DOCX to Markdown Converter</AssemblyProduct>
    <AssemblyCopyright>Copyright © 2024 Augment Code. All rights reserved.</AssemblyCopyright>
    <AssemblyVersion>*******</AssemblyVersion>
    <FileVersion>*******</FileVersion>
    <InformationalVersion>3.2.0</InformationalVersion>

    <!-- 应用程序图标和资源 -->
    <!-- <ApplicationIcon>Resources\AppIcon.ico</ApplicationIcon> -->
    <Win32Resource />

    <!-- 发布配置 -->
    <PublishSingleFile>true</PublishSingleFile>
    <SelfContained>true</SelfContained>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <PublishReadyToRun>true</PublishReadyToRun>
    <PublishTrimmed>false</PublishTrimmed>
    <IncludeNativeLibrariesForSelfExtract>true</IncludeNativeLibrariesForSelfExtract>
    <EnableCompressionInSingleFile>true</EnableCompressionInSingleFile>
    <DebugType>none</DebugType>
    <DebugSymbols>false</DebugSymbols>
    <AssemblyName>DocxToMarkdownConverter-v3.2.0</AssemblyName>

    <!-- 安全和签名 -->
    <SignAssembly>false</SignAssembly>
    <DelaySign>false</DelaySign>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.1" />
    <PackageReference Include="MaterialDesignThemes" Version="4.9.0" />
    <PackageReference Include="MaterialDesignColors" Version="2.1.4" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Serilog.Sinks.File" Version="5.0.0" />
    <PackageReference Include="Serilog.Sinks.Console" Version="5.0.1" />
    <PackageReference Include="System.Drawing.Common" Version="8.0.0" />
    <PackageReference Include="System.Text.Json" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <None Update="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </None>
  </ItemGroup>

  <ItemGroup>
    <Compile Remove="Tests\**" />
    <EmbeddedResource Remove="Tests\**" />
    <None Remove="Tests\**" />
    <Page Remove="Tests\**" />
  </ItemGroup>



</Project>