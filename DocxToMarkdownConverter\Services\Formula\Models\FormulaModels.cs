namespace DocxToMarkdownConverter.Services.Formula.Models;

/// <summary>
/// 公式处理请求
/// </summary>
public class FormulaProcessingRequest
{
    /// <summary>
    /// 源元素
    /// </summary>
    public object SourceElement { get; set; } = null!;
    
    /// <summary>
    /// 处理选项
    /// </summary>
    public FormulaProcessingOptions Options { get; set; } = new();
    
    /// <summary>
    /// 取消令牌
    /// </summary>
    public CancellationToken CancellationToken { get; set; } = default;
    
    /// <summary>
    /// 请求ID（用于跟踪和日志）
    /// </summary>
    public string RequestId { get; set; } = Guid.NewGuid().ToString();
}

/// <summary>
/// 公式处理结果
/// </summary>
public class FormulaProcessingResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 输出内容
    /// </summary>
    public string Output { get; set; } = string.Empty;
    
    /// <summary>
    /// 输出格式
    /// </summary>
    public FormulaOutputFormat Format { get; set; }
    
    /// <summary>
    /// 错误列表
    /// </summary>
    public IList<FormulaProcessingError> Errors { get; set; } = new List<FormulaProcessingError>();
    
    /// <summary>
    /// 元数据
    /// </summary>
    public FormulaMetadata Metadata { get; set; } = new();
    
    /// <summary>
    /// 处理时间（毫秒）
    /// </summary>
    public long ProcessingTimeMs { get; set; }
    
    /// <summary>
    /// 请求ID
    /// </summary>
    public string RequestId { get; set; } = string.Empty;
}

/// <summary>
/// 公式元素
/// </summary>
public class FormulaElement
{
    /// <summary>
    /// 唯一标识符
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();
    
    /// <summary>
    /// 公式类型
    /// </summary>
    public FormulaType Type { get; set; }
    
    /// <summary>
    /// 源元素
    /// </summary>
    public object SourceElement { get; set; } = null!;
    
    /// <summary>
    /// 公式结构
    /// </summary>
    public FormulaStructure? Structure { get; set; }
    
    /// <summary>
    /// 元数据
    /// </summary>
    public FormulaMetadata Metadata { get; set; } = new();
    
    /// <summary>
    /// 在容器中的位置
    /// </summary>
    public FormulaLocation? Location { get; set; }
}

/// <summary>
/// 公式检测结果
/// </summary>
public class FormulaDetectionResult
{
    /// <summary>
    /// 是否包含公式
    /// </summary>
    public bool HasFormulas { get; set; }
    
    /// <summary>
    /// 公式位置列表
    /// </summary>
    public IList<FormulaLocation> Locations { get; set; } = new List<FormulaLocation>();
    
    /// <summary>
    /// 公式复杂度
    /// </summary>
    public FormulaComplexity Complexity { get; set; }
    
    /// <summary>
    /// 检测到的公式类型
    /// </summary>
    public IList<FormulaType> DetectedTypes { get; set; } = new List<FormulaType>();
}

/// <summary>
/// 公式解析结果
/// </summary>
public class FormulaParseResult
{
    /// <summary>
    /// 是否成功
    /// </summary>
    public bool Success { get; set; }
    
    /// <summary>
    /// 公式结构
    /// </summary>
    public FormulaStructure? Structure { get; set; }
    
    /// <summary>
    /// 组件列表
    /// </summary>
    public IList<FormulaComponent> Components { get; set; } = new List<FormulaComponent>();
    
    /// <summary>
    /// 解析错误列表
    /// </summary>
    public IList<FormulaParseError> Errors { get; set; } = new List<FormulaParseError>();
    
    /// <summary>
    /// 解析时间（毫秒）
    /// </summary>
    public long ParseTimeMs { get; set; }
}

/// <summary>
/// 公式位置信息
/// </summary>
public class FormulaLocation
{
    /// <summary>
    /// 开始位置
    /// </summary>
    public int StartIndex { get; set; }
    
    /// <summary>
    /// 结束位置
    /// </summary>
    public int EndIndex { get; set; }
    
    /// <summary>
    /// 行号
    /// </summary>
    public int LineNumber { get; set; }
    
    /// <summary>
    /// 列号
    /// </summary>
    public int ColumnNumber { get; set; }
    
    /// <summary>
    /// 父容器
    /// </summary>
    public object? ParentContainer { get; set; }
}

/// <summary>
/// 公式元数据
/// </summary>
public class FormulaMetadata
{
    /// <summary>
    /// 创建时间
    /// </summary>
    public DateTime CreatedAt { get; set; } = DateTime.UtcNow;
    
    /// <summary>
    /// 源文档信息
    /// </summary>
    public string? SourceDocument { get; set; }
    
    /// <summary>
    /// 复杂度评分
    /// </summary>
    public int ComplexityScore { get; set; }
    
    /// <summary>
    /// 是否为内联公式
    /// </summary>
    public bool IsInline { get; set; }
    
    /// <summary>
    /// 是否为显示公式
    /// </summary>
    public bool IsDisplay { get; set; }
    
    /// <summary>
    /// 自定义属性
    /// </summary>
    public Dictionary<string, object> CustomProperties { get; set; } = new();
}

/// <summary>
/// 公式组件
/// </summary>
public class FormulaComponent
{
    /// <summary>
    /// 组件类型
    /// </summary>
    public string Type { get; set; } = string.Empty;
    
    /// <summary>
    /// 组件数据
    /// </summary>
    public object? Data { get; set; }
    
    /// <summary>
    /// 子组件
    /// </summary>
    public IList<FormulaComponent> Children { get; set; } = new List<FormulaComponent>();
    
    /// <summary>
    /// 组件属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();
}

/// <summary>
/// 公式处理错误
/// </summary>
public class FormulaProcessingError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误严重程度
    /// </summary>
    public FormulaErrorSeverity Severity { get; set; }
    
    /// <summary>
    /// 错误上下文
    /// </summary>
    public object? Context { get; set; }
    
    /// <summary>
    /// 内部异常
    /// </summary>
    public Exception? InnerException { get; set; }
    
    /// <summary>
    /// 错误位置
    /// </summary>
    public FormulaLocation? Location { get; set; }
}

/// <summary>
/// 公式解析错误
/// </summary>
public class FormulaParseError
{
    /// <summary>
    /// 错误代码
    /// </summary>
    public string Code { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误消息
    /// </summary>
    public string Message { get; set; } = string.Empty;
    
    /// <summary>
    /// 错误位置
    /// </summary>
    public FormulaLocation? Location { get; set; }
    
    /// <summary>
    /// 相关元素
    /// </summary>
    public object? RelatedElement { get; set; }
}

/// <summary>
/// 公式恢复结果
/// </summary>
public class FormulaRecoveryResult
{
    /// <summary>
    /// 是否成功恢复
    /// </summary>
    public bool Success { get; set; }

    /// <summary>
    /// 恢复后的输出
    /// </summary>
    public string Output { get; set; } = string.Empty;

    /// <summary>
    /// 恢复方法
    /// </summary>
    public string RecoveryMethod { get; set; } = string.Empty;

    /// <summary>
    /// 恢复质量评分（0-100）
    /// </summary>
    public int QualityScore { get; set; }
}

/// <summary>
/// 公式结构基类
/// </summary>
public abstract class FormulaStructure
{
    /// <summary>
    /// 唯一标识符
    /// </summary>
    public string Id { get; set; } = Guid.NewGuid().ToString();

    /// <summary>
    /// 公式类型
    /// </summary>
    public FormulaType Type { get; set; }

    /// <summary>
    /// 元数据
    /// </summary>
    public FormulaMetadata Metadata { get; set; } = new();

    /// <summary>
    /// 子结构
    /// </summary>
    public virtual IList<FormulaStructure> Children { get; set; } = new List<FormulaStructure>();

    /// <summary>
    /// 获取结构的文本表示
    /// </summary>
    /// <returns>文本表示</returns>
    public abstract string GetTextRepresentation();

    /// <summary>
    /// 计算复杂度
    /// </summary>
    /// <returns>复杂度评分</returns>
    public virtual int CalculateComplexity()
    {
        var baseComplexity = 1;
        var childrenComplexity = Children.Sum(child => child.CalculateComplexity());
        return baseComplexity + childrenComplexity;
    }
}

/// <summary>
/// 分数结构
/// </summary>
public class FractionStructure : FormulaStructure
{
    /// <summary>
    /// 分子
    /// </summary>
    public FormulaComponent Numerator { get; set; } = new();

    /// <summary>
    /// 分母
    /// </summary>
    public FormulaComponent Denominator { get; set; } = new();

    /// <summary>
    /// 分数类型
    /// </summary>
    public FractionType FractionType { get; set; } = FractionType.Bar;

    public FractionStructure()
    {
        Type = FormulaType.Fraction;
    }

    public override string GetTextRepresentation()
    {
        return $"({Numerator.Data})/({Denominator.Data})";
    }

    public override int CalculateComplexity()
    {
        return base.CalculateComplexity() + 2; // 分数增加额外复杂度
    }
}

/// <summary>
/// 矩阵结构
/// </summary>
public class MatrixStructure : FormulaStructure
{
    /// <summary>
    /// 矩阵行
    /// </summary>
    public IList<MatrixRow> Rows { get; set; } = new List<MatrixRow>();

    /// <summary>
    /// 是否为分段函数
    /// </summary>
    public bool IsPiecewise { get; set; }

    /// <summary>
    /// 对齐方式
    /// </summary>
    public MatrixAlignment Alignment { get; set; } = MatrixAlignment.Center;

    /// <summary>
    /// 列数
    /// </summary>
    public int ColumnCount => Rows.FirstOrDefault()?.Cells.Count ?? 0;

    /// <summary>
    /// 行数
    /// </summary>
    public int RowCount => Rows.Count;

    public MatrixStructure()
    {
        Type = FormulaType.Matrix;
    }

    public override string GetTextRepresentation()
    {
        var rows = Rows.Select(row => string.Join(" ", row.Cells.Select(cell => cell.Data?.ToString() ?? "")));
        return $"[{string.Join("; ", rows)}]";
    }

    public override int CalculateComplexity()
    {
        return base.CalculateComplexity() + (RowCount * ColumnCount);
    }
}

/// <summary>
/// 矩阵行
/// </summary>
public class MatrixRow
{
    /// <summary>
    /// 单元格列表
    /// </summary>
    public IList<FormulaComponent> Cells { get; set; } = new List<FormulaComponent>();

    /// <summary>
    /// 行属性
    /// </summary>
    public Dictionary<string, object> Properties { get; set; } = new();

    public MatrixRow()
    {
    }

    public MatrixRow(IEnumerable<FormulaComponent> cells)
    {
        Cells = cells.ToList();
    }
}

/// <summary>
/// 分段函数结构
/// </summary>
public class PiecewiseFunctionStructure : FormulaStructure
{
    /// <summary>
    /// 分段情况列表
    /// </summary>
    public IList<PiecewiseCase> Cases { get; set; } = new List<PiecewiseCase>();

    /// <summary>
    /// 默认情况
    /// </summary>
    public string? DefaultCase { get; set; }

    public PiecewiseFunctionStructure()
    {
        Type = FormulaType.PiecewiseFunction;
    }

    public override string GetTextRepresentation()
    {
        var cases = Cases.Select(c => $"{c.Expression} if {c.Condition}");
        return $"{{ {string.Join(", ", cases)} }}";
    }

    public override int CalculateComplexity()
    {
        return base.CalculateComplexity() + (Cases.Count * 2);
    }
}

/// <summary>
/// 分段情况
/// </summary>
public class PiecewiseCase
{
    /// <summary>
    /// 表达式
    /// </summary>
    public string Expression { get; set; } = string.Empty;

    /// <summary>
    /// 条件
    /// </summary>
    public string Condition { get; set; } = string.Empty;

    /// <summary>
    /// 是否为默认情况
    /// </summary>
    public bool IsDefault { get; set; }
}

/// <summary>
/// 根式结构
/// </summary>
public class RadicalStructure : FormulaStructure
{
    /// <summary>
    /// 被开方数
    /// </summary>
    public FormulaComponent Radicand { get; set; } = new();

    /// <summary>
    /// 根指数（可选）
    /// </summary>
    public FormulaComponent? Index { get; set; }

    public RadicalStructure()
    {
        Type = FormulaType.Radical;
    }

    public override string GetTextRepresentation()
    {
        if (Index != null)
        {
            return $"root({Index.Data}, {Radicand.Data})";
        }
        return $"sqrt({Radicand.Data})";
    }
}

/// <summary>
/// 上下标结构
/// </summary>
public class ScriptStructure : FormulaStructure
{
    /// <summary>
    /// 基础表达式
    /// </summary>
    public FormulaComponent Base { get; set; } = new();

    /// <summary>
    /// 上标（可选）
    /// </summary>
    public FormulaComponent? Superscript { get; set; }

    /// <summary>
    /// 下标（可选）
    /// </summary>
    public FormulaComponent? Subscript { get; set; }

    public ScriptStructure()
    {
        Type = FormulaType.Superscript; // 默认类型，会根据实际情况调整
    }

    public override string GetTextRepresentation()
    {
        var result = Base.Data?.ToString() ?? "";
        if (Subscript != null)
        {
            result += $"_{{{Subscript.Data}}}";
        }
        if (Superscript != null)
        {
            result += $"^{{{Superscript.Data}}}";
        }
        return result;
    }
}
