using System.IO;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Effects;
using DocxToMarkdownConverter.ViewModels;
using DocxToMarkdownConverter.Services;
using Button = System.Windows.Controls.Button;

namespace DocxToMarkdownConverter.Views;

/// <summary>
/// Interaction logic for MainWindow.xaml
/// </summary>
public partial class MainWindow : Window
{
    private readonly INavigationService _navigationService;
    private readonly IAnimationManager _animationManager;
    private readonly MainWindowViewModel _viewModel;
    private Button? _activeNavigationButton;

    public MainWindow(
        MainWindowViewModel viewModel,
        INavigationService navigationService,
        IAnimationManager animationManager)
    {
        System.Diagnostics.Debug.WriteLine("MainWindow: 开始构造函数");
        try
        {
            System.Diagnostics.Debug.WriteLine("MainWindow: 调用 InitializeComponent");
            InitializeComponent();

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置 ViewModel 和 DataContext");
            _viewModel = viewModel;
            DataContext = viewModel;
            _navigationService = navigationService;
            _animationManager = animationManager;

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置事件处理程序");
            Loaded += OnLoaded;
            Closing += OnClosing;

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置导航");
            SetupNavigation();

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置绑定");
            SetupBindings();

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置键盘快捷键");
            SetupKeyboardShortcuts();

            System.Diagnostics.Debug.WriteLine("MainWindow: 设置窗口状态管理");
            SetupWindowStateManagement();

            System.Diagnostics.Debug.WriteLine("MainWindow: 优化性能");
            OptimizePerformance();

            System.Diagnostics.Debug.WriteLine("MainWindow: 构造函数完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"MainWindow: 构造函数中发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"MainWindow: 异常堆栈: {ex.StackTrace}");
            throw;
        }
    }

    private async void OnLoaded(object sender, RoutedEventArgs e)
    {
        try
        {
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 开始 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("MainWindow.OnLoaded: 开始");

            // Restore window state from settings
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 恢复窗口状态 - {DateTime.Now}\n");
            RestoreWindowState();

            // Play entrance animation
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 播放入场动画 - {DateTime.Now}\n");
            await _animationManager.PlayEntranceAnimationAsync(this);

            // Set initial active button
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 设置活动按钮 - {DateTime.Now}\n");
            SetActiveNavigationButton(FilesButton);

            // Attach hover animations to navigation buttons
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 附加悬停动画 - {DateTime.Now}\n");
            AttachButtonHoverAnimations();

            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 完成 - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine("MainWindow.OnLoaded: 完成");
        }
        catch (Exception ex)
        {
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 发生异常: {ex.Message} - {DateTime.Now}\n");
            await File.AppendAllTextAsync("debug_startup.log", $"MainWindow.OnLoaded: 异常堆栈: {ex.StackTrace} - {DateTime.Now}\n");
            System.Diagnostics.Debug.WriteLine($"MainWindow.OnLoaded: 发生异常: {ex.Message}");
            throw;
        }
    }

    private void SetupNavigation()
    {
        // Subscribe to navigation events
        _navigationService.Navigated += OnNavigated;
        _navigationService.NavigationFailed += OnNavigationFailed;
    }

    private void SetupBindings()
    {
        // Bind busy state to loading overlay
        _viewModel.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(MainWindowViewModel.IsBusy))
            {
                Dispatcher.Invoke(() =>
                {
                    LoadingOverlay.Visibility = _viewModel.IsBusy ? Visibility.Visible : Visibility.Collapsed;
                });
            }
        };
    }

    private void SetupKeyboardShortcuts()
    {
        // Ctrl+O - Open files
        var openFilesGesture = new KeyGesture(Key.O, ModifierKeys.Control);
        var openFilesCommand = new RoutedCommand("OpenFiles", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(openFilesCommand, (s, e) =>
        {
            if (_viewModel.AddFilesCommand.CanExecute(null))
                _viewModel.AddFilesCommand.Execute(null);
        }));
        InputBindings.Add(new InputBinding(openFilesCommand, openFilesGesture));

        // F5 - Start conversion
        var startConversionGesture = new KeyGesture(Key.F5);
        var startConversionCommand = new RoutedCommand("StartConversion", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(startConversionCommand, (s, e) =>
        {
            if (_viewModel.StartConversionCommand.CanExecute(null))
                _viewModel.StartConversionCommand.Execute(null);
        }));
        InputBindings.Add(new InputBinding(startConversionCommand, startConversionGesture));

        // Ctrl+1-4 - Navigation shortcuts
        var navigateToFilesGesture = new KeyGesture(Key.D1, ModifierKeys.Control);
        var navigateToFilesCommand = new RoutedCommand("NavigateToFiles", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(navigateToFilesCommand, (s, e) =>
        {
            SetActiveNavigationButton(FilesButton);
            _ = NavigateToFiles();
        }));
        InputBindings.Add(new InputBinding(navigateToFilesCommand, navigateToFilesGesture));

        var navigateToSettingsGesture = new KeyGesture(Key.D2, ModifierKeys.Control);
        var navigateToSettingsCommand = new RoutedCommand("NavigateToSettings", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(navigateToSettingsCommand, (s, e) =>
        {
            SetActiveNavigationButton(SettingsButton);
            _ = NavigateToSettings();
        }));
        InputBindings.Add(new InputBinding(navigateToSettingsCommand, navigateToSettingsGesture));

        var navigateToProgressGesture = new KeyGesture(Key.D3, ModifierKeys.Control);
        var navigateToProgressCommand = new RoutedCommand("NavigateToProgress", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(navigateToProgressCommand, (s, e) =>
        {
            SetActiveNavigationButton(ProgressButton);
            _ = NavigateToProgress();
        }));
        InputBindings.Add(new InputBinding(navigateToProgressCommand, navigateToProgressGesture));

        var navigateToResultsGesture = new KeyGesture(Key.D4, ModifierKeys.Control);
        var navigateToResultsCommand = new RoutedCommand("NavigateToResults", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(navigateToResultsCommand, (s, e) =>
        {
            SetActiveNavigationButton(ResultsButton);
            _ = NavigateToResults();
        }));
        InputBindings.Add(new InputBinding(navigateToResultsCommand, navigateToResultsGesture));

        // Esc - Cancel operation (if busy)
        var cancelGesture = new KeyGesture(Key.Escape);
        var cancelCommand = new RoutedCommand("Cancel", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(cancelCommand, (s, e) =>
        {
            if (_viewModel.IsBusy && _viewModel.PauseConversionCommand.CanExecute(null))
            {
                _viewModel.PauseConversionCommand.Execute(null);
            }
        }));
        InputBindings.Add(new InputBinding(cancelCommand, cancelGesture));

        // Ctrl+Delete - Clear all files
        var clearFilesGesture = new KeyGesture(Key.Delete, ModifierKeys.Control);
        var clearFilesCommand = new RoutedCommand("ClearFiles", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(clearFilesCommand, (s, e) =>
        {
            if (_viewModel.ClearFilesCommand.CanExecute(null))
                _viewModel.ClearFilesCommand.Execute(null);
        }));
        InputBindings.Add(new InputBinding(clearFilesCommand, clearFilesGesture));

        // Ctrl+Shift+O - Open output directory
        var openOutputGesture = new KeyGesture(Key.O, ModifierKeys.Control | ModifierKeys.Shift);
        var openOutputCommand = new RoutedCommand("OpenOutput", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(openOutputCommand, (s, e) =>
        {
            if (_viewModel.OpenOutputDirectoryCommand.CanExecute(null))
                _viewModel.OpenOutputDirectoryCommand.Execute(null);
        }));
        InputBindings.Add(new InputBinding(openOutputCommand, openOutputGesture));

        // Ctrl+T - Toggle theme
        var toggleThemeGesture = new KeyGesture(Key.T, ModifierKeys.Control);
        var toggleThemeCommand = new RoutedCommand("ToggleTheme", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(toggleThemeCommand, (s, e) =>
        {
            if (_viewModel.ToggleThemeCommand.CanExecute(null))
                _viewModel.ToggleThemeCommand.Execute(null);
        }));
        InputBindings.Add(new InputBinding(toggleThemeCommand, toggleThemeGesture));

        // F1 - Show keyboard shortcuts help
        var showHelpGesture = new KeyGesture(Key.F1);
        var showHelpCommand = new RoutedCommand("ShowHelp", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(showHelpCommand, (s, e) => ShowKeyboardShortcutsHelp()));
        InputBindings.Add(new InputBinding(showHelpCommand, showHelpGesture));

        // Ctrl+Q - Quit application
        var quitGesture = new KeyGesture(Key.Q, ModifierKeys.Control);
        var quitCommand = new RoutedCommand("Quit", typeof(MainWindow));
        CommandBindings.Add(new CommandBinding(quitCommand, (s, e) => Close()));
        InputBindings.Add(new InputBinding(quitCommand, quitGesture));
    }

    private async void OnNavigated(object? sender, NavigationEventArgs e)
    {
        if (e.Page != null)
        {
            // Hide welcome content and show the new page
            await _animationManager.PlayExitAnimationAsync(WelcomeContent);
            WelcomeContent.Visibility = Visibility.Collapsed;

            // Set appropriate DataContext for the page
            if (e.Page.DataContext == null)
            {
                if (e.Page is ResultsView)
                {
                    e.Page.DataContext = _viewModel.ResultsViewModel;
                }
                else
                {
                    e.Page.DataContext = _viewModel;
                }
            }

            MainContentPresenter.Content = e.Page;
            await _animationManager.PlayEntranceAnimationAsync(e.Page);
        }
    }

    private void OnNavigationFailed(object? sender, NavigationFailedEventArgs e)
    {
        // Show error message to user
        var message = e.ErrorMessage ?? "Navigation failed";
        System.Windows.MessageBox.Show(message, "Navigation Error", MessageBoxButton.OK, MessageBoxImage.Warning);
    }

    private void SetActiveNavigationButton(Button button)
    {
        // Reset previous active button
        if (_activeNavigationButton != null)
        {
            _activeNavigationButton.Style = (Style)FindResource("NavigationButtonStyle");
        }

        // Set new active button
        _activeNavigationButton = button;
        button.Style = (Style)FindResource("ActiveNavigationButtonStyle");
    }

    private void ClearActiveNavigationButton()
    {
        // Reset active button to default style
        if (_activeNavigationButton != null)
        {
            _activeNavigationButton.Style = (Style)FindResource("NavigationButtonStyle");
            _activeNavigationButton = null;
        }
    }

    private async void NavigateToFiles_Click(object sender, RoutedEventArgs e)
    {
        SetActiveNavigationButton(FilesButton);
        await NavigateToFiles();
    }

    private async void NavigateToSettings_Click(object sender, RoutedEventArgs e)
    {
        SetActiveNavigationButton(SettingsButton);
        await NavigateToSettings();
    }

    private async void NavigateToProgress_Click(object sender, RoutedEventArgs e)
    {
        SetActiveNavigationButton(ProgressButton);
        await NavigateToProgress();
    }

    private async void NavigateToResults_Click(object sender, RoutedEventArgs e)
    {
        SetActiveNavigationButton(ResultsButton);
        await NavigateToResults();
    }

    private async void NavigateToWelcome_Click(object sender, RoutedEventArgs e)
    {
        // Clear active navigation button
        ClearActiveNavigationButton();
        await NavigateToWelcome();
    }

    private async Task NavigateToFiles()
    {
        try
        {
            await _navigationService.NavigateToAsync<FileListView>();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to navigate to Files: {ex.Message}", "Navigation Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task NavigateToSettings()
    {
        try
        {
            await _navigationService.NavigateToAsync<SettingsView>();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to navigate to Settings: {ex.Message}", "Navigation Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task NavigateToProgress()
    {
        try
        {
            await _navigationService.NavigateToAsync<ProgressView>();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to navigate to Progress: {ex.Message}", "Navigation Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task NavigateToResults()
    {
        try
        {
            await _navigationService.NavigateToAsync<ResultsView>();
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to navigate to Results: {ex.Message}", "Navigation Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }

    private async Task NavigateToWelcome()
    {
        try
        {
            // Hide current page content if any
            if (MainContentPresenter.Content != null)
            {
                var currentContent = MainContentPresenter.Content as FrameworkElement;
                if (currentContent != null)
                {
                    await _animationManager.PlayExitAnimationAsync(currentContent);
                }
                MainContentPresenter.Content = null;
            }

            // Show welcome content
            WelcomeContent.Visibility = Visibility.Visible;
            await _animationManager.PlayEntranceAnimationAsync(WelcomeContent);
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to navigate to Welcome: {ex.Message}", "Navigation Error",
                MessageBoxButton.OK, MessageBoxImage.Error);
        }
    }
    
    private void AttachButtonHoverAnimations()
    {
        // Attach hover animations to navigation buttons
        _animationManager.AttachHoverAnimations(HeaderButton);
        _animationManager.AttachHoverAnimations(FilesButton);
        _animationManager.AttachHoverAnimations(SettingsButton);
        _animationManager.AttachHoverAnimations(ProgressButton);
        _animationManager.AttachHoverAnimations(ResultsButton);
        _animationManager.AttachHoverAnimations(ThemeToggleButton);

        // Find and attach animations to any other buttons in the welcome content
        AttachAnimationsToChildButtons(WelcomeContent);
    }
    
    private void AttachAnimationsToChildButtons(DependencyObject parent)
    {
        for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
        {
            var child = VisualTreeHelper.GetChild(parent, i);

            if (child is Button button)
            {
                _animationManager.AttachHoverAnimations(button);
            }
            else
            {
                AttachAnimationsToChildButtons(child);
            }
        }
    }

    private void ShowKeyboardShortcutsHelp()
    {
        var helpMessage = @"Keyboard Shortcuts:

File Operations:
• Ctrl+O - Open files
• Ctrl+Delete - Clear all files
• Ctrl+Shift+O - Open output directory

Conversion:
• F5 - Start conversion
• Esc - Cancel/Pause conversion

Navigation:
• Ctrl+1 - Files page
• Ctrl+2 - Settings page
• Ctrl+3 - Progress page
• Ctrl+4 - Results page

Application:
• Ctrl+T - Toggle theme
• F1 - Show this help
• Ctrl+Q - Quit application";

        System.Windows.MessageBox.Show(helpMessage, "Keyboard Shortcuts",
            MessageBoxButton.OK, MessageBoxImage.Information);
    }

    #region Window State Management

    private void SetupWindowStateManagement()
    {
        // Subscribe to ViewModel property changes for window state
        _viewModel.PropertyChanged += (s, e) =>
        {
            if (e.PropertyName == nameof(MainWindowViewModel.ApplicationSettings))
            {
                RestoreWindowState();
            }
        };
    }

    private void RestoreWindowState()
    {
        try
        {
            var settings = _viewModel.ApplicationSettings;
            if (settings?.RememberWindowState == true)
            {
                // Only restore window maximized state
                WindowState = settings.IsMaximized ? WindowState.Maximized : WindowState.Normal;
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to restore window state: {ex.Message}");
        }
    }

    private void SaveWindowState()
    {
        try
        {
            var settings = _viewModel.ApplicationSettings;
            if (settings?.RememberWindowState == true)
            {
                // Save current window state (only maximized state)
                settings.IsMaximized = WindowState == WindowState.Maximized;

                // Trigger auto-save
                _viewModel.SaveSettingsAsync().ConfigureAwait(false);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to save window state: {ex.Message}");
        }
    }



    private async void OnClosing(object? sender, System.ComponentModel.CancelEventArgs e)
    {
        try
        {
            // Save window state before closing
            SaveWindowState();

            // Save any pending settings changes
            await _viewModel.SaveSettingsAsync();
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error during window closing: {ex.Message}");
        }
    }

    protected override void OnStateChanged(EventArgs e)
    {
        base.OnStateChanged(e);

        // Save window state when it changes
        if (IsLoaded)
        {
            SaveWindowState();
        }
    }

    protected override void OnLocationChanged(EventArgs e)
    {
        base.OnLocationChanged(e);
        // Location changes no longer need to be saved
    }

    protected override void OnRenderSizeChanged(SizeChangedInfo sizeInfo)
    {
        base.OnRenderSizeChanged(sizeInfo);
        // Size changes no longer need to be saved
    }

    #endregion

    #region 性能优化

    /// <summary>
    /// 优化窗口性能，特别是拖动性能
    /// </summary>
    private void OptimizePerformance()
    {
        // 启用硬件加速
        RenderOptions.SetBitmapScalingMode(this, BitmapScalingMode.Linear);
        RenderOptions.SetEdgeMode(this, EdgeMode.Aliased);

        // 优化文本渲染
        TextOptions.SetTextFormattingMode(this, TextFormattingMode.Display);
        TextOptions.SetTextRenderingMode(this, TextRenderingMode.ClearType);

        // 减少布局更新频率
        SetValue(FrameworkElement.UseLayoutRoundingProperty, true);

        // 启用缓存模式以提高拖动性能
        CacheMode = new BitmapCache
        {
            EnableClearType = false,
            RenderAtScale = 1.0,
            SnapsToDevicePixels = true
        };

        // 优化拖动时的渲染
        AllowsTransparency = false;
    }

    #endregion

    #region 自定义窗口控制

    private void TitleBar_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
    {
        if (e.ClickCount == 2)
        {
            WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
        }
        else
        {
            DragMove();
        }
    }

    private void MinimizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState.Minimized;
    }

    private void MaximizeButton_Click(object sender, RoutedEventArgs e)
    {
        WindowState = WindowState == WindowState.Maximized ? WindowState.Normal : WindowState.Maximized;
    }

    private void CloseButton_Click(object sender, RoutedEventArgs e)
    {
        Close();
    }

    #endregion
}