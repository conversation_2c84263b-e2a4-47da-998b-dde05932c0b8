using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using System.Windows.Input;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Controls;
using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.ViewModels;

public class SettingsViewModel : ViewModelBase
{
    private readonly IConfigurationService _configurationService;
    private readonly IThemeManager _themeManager;
    private readonly ILocalizationService _localizationService;
    private readonly IAnimationManager _animationManager;
    private readonly ILogger<SettingsViewModel> _logger;
    private ConversionOptions _conversionOptions = new();
    private ThemeSettings _themeSettings = new();
    private LanguageSettings _languageSettings = new();
    private ThemeControl? _themeControl;
    private LanguageControl? _languageControl;
    private PerformanceSettingsControl? _performanceControl;
    private bool _controlsInitialized = false;

    public SettingsViewModel(IConfigurationService configurationService, IThemeManager themeManager, ILocalizationService localizationService, IAnimationManager animationManager, ILogger<SettingsViewModel> logger)
    {
        _configurationService = configurationService;
        _themeManager = themeManager;
        _localizationService = localizationService;
        _animationManager = animationManager;
        _logger = logger;

        InitializeCommands();

        // Log construction completion
        _logger.LogInformation("SettingsViewModel constructed successfully");

        // Load settings asynchronously without blocking constructor
        _ = System.Windows.Application.Current.Dispatcher.BeginInvoke(new Action(async () =>
        {
            try
            {
                _logger.LogInformation("Starting to load settings asynchronously");
                await LoadSettingsAsync();
                _logger.LogInformation("Settings loaded successfully");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to load settings in SettingsViewModel");
            }
        }), System.Windows.Threading.DispatcherPriority.Background);
    }

    public ConversionOptions ConversionOptions
    {
        get => _conversionOptions;
        set => SetProperty(ref _conversionOptions, value);
    }



    public ThemeSettings ThemeSettings
    {
        get => _themeSettings;
        set => SetProperty(ref _themeSettings, value);
    }

    public ThemeControl? ThemeControl
    {
        get
        {
            if (_themeControl == null && !_controlsInitialized)
            {
                InitializeControlsLazily();
            }
            return _themeControl;
        }
        set => SetProperty(ref _themeControl, value);
    }

    public LanguageSettings LanguageSettings
    {
        get => _languageSettings;
        set => SetProperty(ref _languageSettings, value);
    }

    /// <summary>
    /// 可用的图片格式选项
    /// </summary>
    public IEnumerable<ImageFormat> AvailableImageFormats => Enum.GetValues<ImageFormat>();

    public LanguageControl? LanguageControl
    {
        get
        {
            if (_languageControl == null && !_controlsInitialized)
            {
                InitializeControlsLazily();
            }
            return _languageControl;
        }
        set => SetProperty(ref _languageControl, value);
    }

    public PerformanceSettingsControl? PerformanceControl
    {
        get
        {
            if (_performanceControl == null && !_controlsInitialized)
            {
                InitializeControlsLazily();
            }
            return _performanceControl;
        }
        set => SetProperty(ref _performanceControl, value);
    }

    public ICommand SelectOutputDirectoryCommand { get; private set; } = null!;

    private void InitializeCommands()
    {
        SelectOutputDirectoryCommand = CreateCommand(ExecuteSelectOutputDirectory);
    }

    private void InitializeControlsLazily()
    {
        if (_controlsInitialized) return;
        _controlsInitialized = true;

        _logger.LogInformation("Starting lazy initialization of settings controls");

        // Use a background task to avoid blocking UI thread
        Task.Run(async () =>
        {
            try
            {
                // Create ViewModels on background thread
                var themeControlViewModel = new ThemeControlViewModel(_themeManager);
                var languageControlViewModel = new LanguageControlViewModel(_localizationService, _logger);
                var performanceControlViewModel = new PerformanceSettingsViewModel(_configurationService, _animationManager,
                    Microsoft.Extensions.Logging.Abstractions.NullLogger<PerformanceSettingsViewModel>.Instance);

                // Switch to UI thread for control creation
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    try
                    {
                        if (_themeControl == null)
                        {
                            _themeControl = new ThemeControl(themeControlViewModel);
                            OnPropertyChanged(nameof(ThemeControl));
                        }

                        if (_languageControl == null)
                        {
                            _languageControl = new LanguageControl(languageControlViewModel);
                            OnPropertyChanged(nameof(LanguageControl));
                        }

                        if (_performanceControl == null)
                        {
                            _performanceControl = new PerformanceSettingsControl(performanceControlViewModel);
                            OnPropertyChanged(nameof(PerformanceControl));
                        }

                        _logger.LogInformation("Settings controls initialized successfully via lazy loading");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to create settings controls on UI thread during lazy loading");
                    }
                }, System.Windows.Threading.DispatcherPriority.Background);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to initialize settings controls during lazy loading");
            }
        });
    }

    private async Task LoadSettingsAsync()
    {
        try
        {
            _logger.LogInformation("Loading settings in SettingsViewModel");

            var appSettings = await _configurationService.LoadApplicationSettingsAsync();

            // Update properties on UI thread
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                UpdateSettingsProperties(appSettings);
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    UpdateSettingsProperties(appSettings);
                });
            }

            _logger.LogInformation("Settings loaded successfully in SettingsViewModel");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to load settings in SettingsViewModel");
            _ = GlobalExceptionHandler.HandleExceptionAsync(ex, "Loading Settings");
        }
    }

    private void UpdateSettingsProperties(ApplicationSettings appSettings)
    {
        ConversionOptions = appSettings.ConversionOptions ?? new ConversionOptions();
        ThemeSettings = appSettings.ThemeSettings ?? new ThemeSettings();
    }



    private void ExecuteSelectOutputDirectory()
    {
        var dialog = new System.Windows.Forms.FolderBrowserDialog
        {
            Description = "Select Output Directory",
            SelectedPath = ConversionOptions.OutputDirectory,
            ShowNewFolderButton = true
        };

        if (dialog.ShowDialog() == System.Windows.Forms.DialogResult.OK)
        {
            ConversionOptions.OutputDirectory = dialog.SelectedPath;
        }
    }
}