using Microsoft.Extensions.Logging;

namespace DocxToMarkdownConverter.Services;

public interface ILoggingService
{
    event EventHandler<LogEventArgs>? LogEvent;
    event EventHandler<LogEventArgs>? LogReceived; // For backward compatibility
    
    void LogInfo(string message);
    void LogInfo(string message, params object[] args);
    void LogWarning(string message);
    void LogWarning(string message, params object[] args);
    void LogError(string message);
    void LogError(Exception exception, string message);
    void LogError(string message, params object[] args);
    void LogDebug(string message);
    void LogDebug(string message, params object[] args);
    
    // Conversion-specific logging methods
    void LogConversionStart(string fileName, string operation);
    void LogConversionProgress(string fileName, string operation, double progress);
    void LogConversionComplete(string fileName, bool success, TimeSpan duration, string? errorMessage = null);
    void LogBatchStatistics(int totalFiles, int completedFiles, int failedFiles, TimeSpan totalDuration);
    
    void ClearLogs();
    IEnumerable<LogEntry> GetLogs();
}

public class LogEventArgs : EventArgs
{
    public DateTime Timestamp { get; }
    public LogLevel Level { get; }
    public string Category { get; }
    public string Message { get; }
    public Exception? Exception { get; }
    
    public LogEntry LogEntry { get; }
    
    public LogEventArgs(DateTime timestamp, LogLevel level, string category, string message, Exception? exception = null)
    {
        Timestamp = timestamp;
        Level = level;
        Category = category;
        Message = message;
        Exception = exception;
        
        LogEntry = new LogEntry
        {
            Timestamp = timestamp,
            Level = level,
            Message = message,
            Exception = exception
        };
    }
    
    public LogEventArgs(LogEntry logEntry)
    {
        LogEntry = logEntry ?? throw new ArgumentNullException(nameof(logEntry));
        Timestamp = logEntry.Timestamp;
        Level = logEntry.Level;
        Category = "General";
        Message = logEntry.Message;
        Exception = logEntry.Exception;
    }
}

public class LogEntry
{
    public DateTime Timestamp { get; set; }
    public LogLevel Level { get; set; }
    public string Message { get; set; } = string.Empty;
    public Exception? Exception { get; set; }
}

public enum LogLevel
{
    Debug,
    Info,
    Warning,
    Error
}