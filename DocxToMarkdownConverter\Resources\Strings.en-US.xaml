<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- Application Basic Information -->
    <system:String x:Key="App.Title">DOCX Converter</system:String>
    <system:String x:Key="App.Version">Version 3.2.0</system:String>
    <system:String x:Key="App.Description">Transform your DOCX documents into clean Markdown format with ease. Get started by selecting files to convert.</system:String>
    <system:String x:Key="App.Welcome">Welcome to DOCX Converter</system:String>

    <!-- Navigation Menu -->
    <system:String x:Key="Navigation.Files">Files</system:String>
    <system:String x:Key="Navigation.Settings">Settings</system:String>
    <system:String x:Key="Navigation.Progress">Progress</system:String>
    <system:String x:Key="Navigation.Results">Results</system:String>
    <system:String x:Key="Navigation.Statistics">Statistics</system:String>

    <!-- Tooltips -->
    <system:String x:Key="Tooltip.Files">Files (Ctrl+1)</system:String>
    <system:String x:Key="Tooltip.Settings">Settings (Ctrl+2)</system:String>
    <system:String x:Key="Tooltip.Progress">Progress (Ctrl+3)</system:String>
    <system:String x:Key="Tooltip.Results">Results (Ctrl+4)</system:String>
    <system:String x:Key="Tooltip.ToggleTheme">Toggle Theme (Ctrl+T)</system:String>
    <system:String x:Key="Tooltip.SelectFiles">Select Files (Ctrl+O)</system:String>
    <system:String x:Key="Tooltip.AddFiles">Add DOCX Files</system:String>
    <system:String x:Key="Tooltip.ClearAllFiles">Clear All Files</system:String>
    <system:String x:Key="Tooltip.RemoveFile">Remove File</system:String>

    <!-- Theme Settings -->
    <system:String x:Key="Theme.Settings">Theme Settings</system:String>
    <system:String x:Key="Theme.Mode">Theme Mode</system:String>
    <system:String x:Key="Theme.Light">Light</system:String>
    <system:String x:Key="Theme.Dark">Dark</system:String>
    <system:String x:Key="Theme.Auto">Follow System</system:String>
    <system:String x:Key="Theme.EnableTransitions">Enable theme transition animations</system:String>
    <system:String x:Key="Theme.EnableAnimations">Enable interface animations</system:String>
    <system:String x:Key="Theme.AnimationSpeed">Animation Speed:</system:String>
    <system:String x:Key="Theme.FollowSystem">Follow system theme changes</system:String>
    <system:String x:Key="Theme.Toggle">Toggle Theme</system:String>
    <system:String x:Key="Theme.Reset">Reset to Default</system:String>
    <system:String x:Key="Theme.AnimationTooltip">Disable this option to improve responsiveness on low-performance devices</system:String>

    <!-- Buttons -->
    <system:String x:Key="Button.OK">OK</system:String>
    <system:String x:Key="Button.Cancel">Cancel</system:String>
    <system:String x:Key="Button.Apply">Apply</system:String>
    <system:String x:Key="Button.Reset">Reset</system:String>
    <system:String x:Key="Button.Browse">Browse</system:String>
    <system:String x:Key="Button.Save">Save</system:String>
    <system:String x:Key="Button.Load">Load</system:String>
    <system:String x:Key="Button.Close">Close</system:String>
    <system:String x:Key="Button.Yes">Yes</system:String>
    <system:String x:Key="Button.No">No</system:String>

    <!-- File Operations -->
    <system:String x:Key="File.SelectFiles">Select Files</system:String>
    <system:String x:Key="File.SelectFolder">Select Folder</system:String>
    <system:String x:Key="File.OutputDirectory">Output Directory</system:String>
    <system:String x:Key="File.AddFiles">Add Files</system:String>
    <system:String x:Key="File.RemoveFiles">Remove Files</system:String>
    <system:String x:Key="File.ClearAll">Clear All</system:String>
    <system:String x:Key="File.StartConversion">Start Conversion</system:String>

    <!-- Status Information -->
    <system:String x:Key="Status.Ready">Ready</system:String>
    <system:String x:Key="Status.Processing">Processing</system:String>
    <system:String x:Key="Status.Completed">Completed</system:String>
    <system:String x:Key="Status.Failed">Failed</system:String>
    <system:String x:Key="Status.Cancelled">Cancelled</system:String>

    <!-- Error Messages -->
    <system:String x:Key="Error.FileNotFound">File not found</system:String>
    <system:String x:Key="Error.InvalidFile">Invalid file format</system:String>
    <system:String x:Key="Error.ConversionFailed">Conversion failed</system:String>
    <system:String x:Key="Error.AccessDenied">Access denied</system:String>
    <system:String x:Key="Error.UnknownError">Unknown error</system:String>

    <!-- Success Messages -->
    <system:String x:Key="Success.ConversionCompleted">Conversion completed</system:String>
    <system:String x:Key="Success.FilesSaved">Files saved</system:String>
    <system:String x:Key="Success.SettingsSaved">Settings saved</system:String>

    <!-- Confirmation Dialogs -->
    <system:String x:Key="Confirm.ClearFiles">Are you sure you want to clear all files?</system:String>
    <system:String x:Key="Confirm.OverwriteFile">File already exists. Do you want to overwrite it?</system:String>
    <system:String x:Key="Confirm.ExitApplication">Are you sure you want to exit the application?</system:String>
    <system:String x:Key="Confirm.ResetSettings">Are you sure you want to reset all settings?</system:String>

    <!-- Settings Page -->
    <system:String x:Key="Settings.General">General Settings</system:String>
    <system:String x:Key="Settings.Appearance">Appearance Settings</system:String>
    <system:String x:Key="Settings.Advanced">Advanced Settings</system:String>
    <system:String x:Key="Settings.Theme">Theme Settings</system:String>
    <system:String x:Key="Settings.Language">Language Settings</system:String>
    <system:String x:Key="Settings.About">About</system:String>

    <!-- Language Settings -->
    <system:String x:Key="Language.Settings">Language Settings</system:String>
    <system:String x:Key="Language.Current">Current Language</system:String>
    <system:String x:Key="Language.Chinese">中文</system:String>
    <system:String x:Key="Language.English">English</system:String>
    <system:String x:Key="Language.FollowSystem">Follow System Language</system:String>
    <system:String x:Key="Language.RestartRequired">Language changes will take effect after restarting the application</system:String>

    <!-- Progress Information -->
    <system:String x:Key="Progress.CurrentFile">Current File:</system:String>
    <system:String x:Key="Progress.FilesProcessed">Files Processed:</system:String>
    <system:String x:Key="Progress.TotalFiles">Total Files:</system:String>
    <system:String x:Key="Progress.ElapsedTime">Elapsed Time:</system:String>
    <system:String x:Key="Progress.EstimatedTime">Estimated Time:</system:String>

    <!-- Results Page -->
    <system:String x:Key="Results.Summary">Conversion Summary</system:String>
    <system:String x:Key="Results.SuccessfulConversions">Successful Conversions:</system:String>
    <system:String x:Key="Results.FailedConversions">Failed Conversions:</system:String>
    <system:String x:Key="Results.TotalProcessed">Total Processed:</system:String>
    <system:String x:Key="Results.OpenOutputFolder">Open Output Folder</system:String>
    <system:String x:Key="Results.ViewDetails">View Details</system:String>

    <!-- Conversion Options -->
    <system:String x:Key="Conversion.Options">Conversion Options</system:String>
    <system:String x:Key="Conversion.OutputSettings">Output Settings</system:String>
    <system:String x:Key="Conversion.ConversionSettings">Conversion Settings</system:String>
    <system:String x:Key="Conversion.ImageSettings">Image Settings</system:String>
    <system:String x:Key="Conversion.OutputDirectory">Output Directory</system:String>
    <system:String x:Key="Conversion.ImageDirectory">Image Directory (relative to output)</system:String>
    <system:String x:Key="Conversion.ExtractImages">Extract Images</system:String>
    <system:String x:Key="Conversion.ConvertTables">Convert Tables</system:String>
    <system:String x:Key="Conversion.ProcessFormulas">Process Mathematical Formulas</system:String>
    <system:String x:Key="Conversion.PreserveFormatting">Preserve Formatting</system:String>
    <system:String x:Key="Conversion.IncludeMetadata">Include Metadata</system:String>
    <system:String x:Key="Conversion.ImageFormat">Image Format</system:String>
    <system:String x:Key="Conversion.SelectImageFormat">Select Image Format</system:String>

    <!-- Animation Settings -->
    <system:String x:Key="Animation.Settings">Animation Settings</system:String>
    <system:String x:Key="Animation.EnablePageTransitions">Enable page transitions</system:String>
    <system:String x:Key="Animation.EnableButtonAnimations">Enable button animations</system:String>
    <system:String x:Key="Animation.EnableProgressAnimations">Enable progress animations</system:String>

    <!-- Statistics -->
    <system:String x:Key="Statistics.Title">Statistics</system:String>
    <system:String x:Key="Statistics.FilesProcessed">Files Processed: {0}</system:String>
    <system:String x:Key="Statistics.Successful">Successful: {0}</system:String>
    <system:String x:Key="Statistics.Failed">Failed: {0}</system:String>

    <!-- Files Interface -->
    <system:String x:Key="Files.Title">File Management</system:String>
    <system:String x:Key="Files.Description">Add DOCX files to convert them to Markdown format. You can drag and drop files or use the file browser.</system:String>
    <system:String x:Key="Files.ToConvert">Files to Convert</system:String>
    <system:String x:Key="Files.NoFiles">No files</system:String>
    <system:String x:Key="Files.DragDropHint">Drag files here or click button to add</system:String>
    <system:String x:Key="Files.SelectFiles">SELECT FILES</system:String>
    <system:String x:Key="Files.AddFiles">Add Files</system:String>
    <system:String x:Key="Files.ClearAll">Clear All</system:String>
    <system:String x:Key="Files.StartConversion">Start Conversion</system:String>
    <system:String x:Key="Files.StatusSummary">Total: {0} files • Ready: {1} • Completed: {2} • Failed: {3}</system:String>

    <!-- File Dialogs -->
    <system:String x:Key="Dialog.SelectDocxFiles">Select DOCX Files</system:String>
    <system:String x:Key="Dialog.WordDocuments">Word Documents (*.docx)</system:String>
    <system:String x:Key="Dialog.AllFiles">All Files (*.*)</system:String>

    <!-- Results Interface -->
    <system:String x:Key="Results.Title">Conversion Results</system:String>
    <system:String x:Key="Results.Description">View and manage your document conversion results</system:String>
    <system:String x:Key="Results.SearchHint">Search results...</system:String>
    <system:String x:Key="Results.NoResultsTitle">No Results Yet</system:String>
    <system:String x:Key="Results.NoResultsDescription">Conversion results will appear here after you process DOCX files. Start by adding files and running a conversion.</system:String>

    <!-- Results Statistics Cards -->
    <system:String x:Key="Results.TotalResults">Total Results</system:String>
    <system:String x:Key="Results.SuccessRate">Success Rate</system:String>
    <system:String x:Key="Results.AverageTime">Avg. Time</system:String>
    <system:String x:Key="Results.ImagesProcessed">Images Processed</system:String>

    <!-- Results Filter -->
    <system:String x:Key="Results.FilterByStatus">Filter by Status</system:String>
    <system:String x:Key="Results.FilterAll">All</system:String>
    <system:String x:Key="Results.FilterSuccess">Success</system:String>
    <system:String x:Key="Results.FilterFailed">Failed</system:String>

    <!-- Results Context Menu -->
    <system:String x:Key="Results.OpenOutputFile">Open Output File</system:String>
    <system:String x:Key="Results.OpenOutputDirectory">Open Output Directory</system:String>
    <system:String x:Key="Results.OpenInputFile">Open Input File</system:String>
    <system:String x:Key="Results.OpenInputDirectory">Open Input Directory</system:String>
    <system:String x:Key="Results.CopyOutputPath">Copy Output Path</system:String>
    <system:String x:Key="Results.RetryConversion">Retry Conversion</system:String>
    <system:String x:Key="Results.RemoveResult">Remove Result</system:String>

    <!-- Results Tooltips -->
    <system:String x:Key="Results.ExportResults">Export Results</system:String>
    <system:String x:Key="Results.ClearAllResults">Clear All Results</system:String>

</ResourceDictionary>
