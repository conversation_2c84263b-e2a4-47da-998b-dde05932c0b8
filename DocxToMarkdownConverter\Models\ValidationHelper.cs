using System.IO;

namespace DocxToMarkdownConverter.Models;

public static class ValidationHelper
{
    public static bool IsValidDocxFile(string filePath)
    {
        if (string.IsNullOrWhiteSpace(filePath))
            return false;

        if (!File.Exists(filePath))
            return false;

        var extension = Path.GetExtension(filePath).ToLowerInvariant();
        return extension == ".docx";
    }

    public static bool IsValidOutputDirectory(string directoryPath)
    {
        if (string.IsNullOrWhiteSpace(directoryPath))
            return false;

        try
        {
            // Check if the path is valid
            Path.GetFullPath(directoryPath);
            
            // Check if directory exists or can be created
            if (Directory.Exists(directoryPath))
                return true;

            // Try to create the directory to test if it's valid
            var parentDir = Path.GetDirectoryName(directoryPath);
            return parentDir != null && Directory.Exists(parentDir);
        }
        catch
        {
            return false;
        }
    }

    public static string GetFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB", "TB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    public static string GetDurationString(TimeSpan? duration)
    {
        if (!duration.HasValue)
            return "N/A";

        var d = duration.Value;
        if (d.TotalSeconds < 1)
            return "< 1s";
        if (d.TotalMinutes < 1)
            return $"{d.Seconds}s";
        if (d.TotalHours < 1)
            return $"{d.Minutes}m {d.Seconds}s";
        
        return $"{d.Hours}h {d.Minutes}m {d.Seconds}s";
    }
}