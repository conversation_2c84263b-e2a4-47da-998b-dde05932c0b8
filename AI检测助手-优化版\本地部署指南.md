# AI检测助手本地部署指南

## 🎯 部署概述

本指南将帮助您将AI检测助手部署到本地域名环境，提供更专业的访问体验和更好的Ollama集成支持。

## 📋 系统要求

### 基础要求
- **Node.js**: 14.0.0 或更高版本
- **操作系统**: Windows 10+, macOS 10.15+, Linux
- **内存**: 最低4GB，推荐8GB+（用于运行本地模型）
- **存储**: 至少1GB可用空间

### 可选要求
- **Ollama**: 用于本地大模型支持
- **管理员权限**: 用于配置hosts文件

## 🚀 快速部署

### 方法1: 自动部署脚本（推荐）

#### Windows用户
```cmd
# 双击运行部署脚本
deploy.bat

# 或在命令行中运行
.\deploy.bat
```

#### Linux/macOS用户
```bash
# 给脚本执行权限
chmod +x deploy.sh

# 运行部署脚本
./deploy.sh
```

### 方法2: 手动部署

#### 1. 安装Node.js
访问 [nodejs.org](https://nodejs.org/) 下载并安装最新LTS版本。

#### 2. 配置hosts文件

**Windows:**
1. 以管理员身份打开记事本
2. 打开文件：`C:\Windows\System32\drivers\etc\hosts`
3. 添加行：`127.0.0.1 ai-detector.local`
4. 保存文件

**Linux/macOS:**
```bash
sudo echo '127.0.0.1 ai-detector.local' >> /etc/hosts
```

#### 3. 启动服务器
```bash
# 使用默认配置启动
node server.js

# 或使用npm脚本
npm start

# 自定义配置启动
node server.js --port 3000 --domain ai-detector.local
```

## 🔧 配置选项

### 服务器配置

#### 命令行参数
```bash
node server.js [选项]

选项:
  --port <端口>      指定服务器端口 (默认: 3000)
  --host <主机>      指定绑定主机 (默认: localhost)
  --domain <域名>    指定自定义域名 (默认: ai-detector.local)
```

#### 环境变量
```bash
# 设置端口
export PORT=3000

# 设置域名
export DOMAIN=ai-detector.local

# 设置主机
export HOST=localhost
```

### Ollama CORS配置

#### 安全模式配置（推荐）
```bash
# Windows (命令提示符)
set OLLAMA_ORIGINS=http://ai-detector.local:3000,http://localhost,http://127.0.0.1,file://
set OLLAMA_HOST=127.0.0.1:11434
ollama serve

# Windows (PowerShell)
$env:OLLAMA_ORIGINS="http://ai-detector.local:3000,http://localhost,http://127.0.0.1,file://"
$env:OLLAMA_HOST="127.0.0.1:11434"
ollama serve

# Linux/macOS
export OLLAMA_ORIGINS="http://ai-detector.local:3000,http://localhost,http://127.0.0.1,file://"
export OLLAMA_HOST=127.0.0.1:11434
ollama serve
```

#### 兼容模式配置（开发测试）
```bash
# 允许所有来源（仅用于开发测试）
export OLLAMA_ORIGINS="*"
export OLLAMA_HOST=0.0.0.0:11434
ollama serve
```

## 🌐 访问方式

### 主要访问地址
- **自定义域名**: http://ai-detector.local:3000
- **本地地址**: http://localhost:3000
- **IP地址**: http://127.0.0.1:3000

### 功能验证
1. 打开浏览器访问上述任一地址
2. 检查所有功能模块是否正常加载
3. 测试Ollama连接功能
4. 验证AI检测和优化功能

## 🧪 测试验证

### 基础功能测试
1. **页面加载测试**
   - 访问主页面
   - 检查所有资源是否正确加载
   - 验证响应式布局

2. **导航功能测试**
   - 测试左侧导航栏
   - 验证面板切换功能
   - 检查移动端适配

3. **核心功能测试**
   - AI内容检测
   - 智能优化改写
   - 学术专业优化
   - 多轮优化

### Ollama集成测试
1. **连接测试**
   - 点击"LLM服务控制"
   - 点击"连接Ollama"
   - 验证连接状态

2. **模型测试**
   - 选择可用模型
   - 运行连接测试
   - 验证文本生成功能

3. **诊断测试**
   - 运行连接诊断
   - 检查诊断报告
   - 验证错误处理

## 🛠️ 故障排除

### 常见问题

#### 1. 端口被占用
**症状**: 启动时提示端口已被占用

**解决方案**:
```bash
# 查看端口占用
netstat -an | findstr :3000  # Windows
lsof -i :3000                # Linux/macOS

# 使用其他端口
node server.js --port 3001
```

#### 2. 域名无法访问
**症状**: ai-detector.local无法访问

**解决方案**:
1. 检查hosts文件配置
2. 清除DNS缓存：
   ```bash
   # Windows
   ipconfig /flushdns
   
   # macOS
   sudo dscacheutil -flushcache
   
   # Linux
   sudo systemctl restart systemd-resolved
   ```

#### 3. Ollama连接失败
**症状**: 无法连接到Ollama服务

**解决方案**:
1. 确认Ollama服务运行：`ollama serve`
2. 检查CORS配置
3. 验证端口11434未被占用
4. 使用诊断工具检查详细错误

#### 4. 静态资源加载失败
**症状**: CSS/JS文件无法加载

**解决方案**:
1. 检查文件路径
2. 验证MIME类型配置
3. 检查服务器日志
4. 清除浏览器缓存

### 调试模式

#### 启用详细日志
```bash
# 设置调试环境变量
export DEBUG=ai-detector:*
node server.js
```

#### 浏览器调试
1. 按F12打开开发者工具
2. 查看Console标签的错误信息
3. 检查Network标签的请求状态
4. 分析具体的错误原因

## 📊 性能优化

### 服务器优化
1. **启用压缩**: 添加gzip压缩中间件
2. **缓存策略**: 配置静态资源缓存
3. **并发处理**: 使用cluster模块
4. **监控日志**: 添加访问日志记录

### 客户端优化
1. **资源压缩**: 压缩CSS和JavaScript文件
2. **图片优化**: 使用WebP格式图片
3. **懒加载**: 实现组件懒加载
4. **缓存策略**: 配置浏览器缓存

## 🔒 安全配置

### 基础安全
1. **CORS配置**: 限制允许的来源域名
2. **路径验证**: 防止目录遍历攻击
3. **输入验证**: 验证用户输入数据
4. **错误处理**: 避免敏感信息泄露

### 生产环境安全
1. **HTTPS配置**: 使用SSL证书
2. **防火墙设置**: 限制端口访问
3. **访问日志**: 记录所有访问请求
4. **定期更新**: 保持依赖包最新

## 📈 监控和维护

### 健康检查
```bash
# 检查服务状态
curl http://ai-detector.local:3000/

# 检查Ollama连接
curl http://localhost:11434/api/tags
```

### 日志管理
- 服务器访问日志
- 错误日志记录
- 性能监控数据
- 用户行为分析

### 备份策略
- 配置文件备份
- 用户数据备份
- 模型文件备份
- 定期备份验证

---

## 📞 技术支持

如果在部署过程中遇到问题，请：

1. 查看本文档的故障排除部分
2. 检查浏览器控制台的错误信息
3. 运行内置的连接诊断工具
4. 提交Issue并附上详细的错误信息

**部署成功后，您将拥有一个专业的本地AI检测助手环境！** 🎉
