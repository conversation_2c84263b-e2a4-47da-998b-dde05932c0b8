using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using DocxToMarkdownConverter.Services.Formula.Core;
using System.Text.RegularExpressions;

namespace DocxToMarkdownConverter.Services.Formula.Implementation;

/// <summary>
/// 基于内存的公式缓存服务实现
/// </summary>
public class MemoryFormulaCacheService : IFormulaCacheService, IDisposable
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<MemoryFormulaCacheService> _logger;
    private readonly HashSet<string> _cacheKeys;
    private readonly object _lockObject = new();
    private bool _disposed = false;

    public MemoryFormulaCacheService(
        IMemoryCache cache,
        ILogger<MemoryFormulaCacheService> logger)
    {
        _cache = cache;
        _logger = logger;
        _cacheKeys = new HashSet<string>();
    }

    /// <summary>
    /// 获取或创建缓存项
    /// </summary>
    public async Task<T> GetOrCreateAsync<T>(string key, Func<Task<T>> factory, TimeSpan? expiration = null)
    {
        if (string.IsNullOrEmpty(key))
        {
            throw new ArgumentException("Cache key cannot be null or empty", nameof(key));
        }

        if (factory == null)
        {
            throw new ArgumentNullException(nameof(factory));
        }

        try
        {
            _logger.LogDebug("Getting or creating cache item with key: {CacheKey}", key);

            var result = await _cache.GetOrCreateAsync(key, async entry =>
            {
                _logger.LogDebug("Cache miss for key: {CacheKey}, creating new item", key);

                // 设置过期时间
                if (expiration.HasValue)
                {
                    entry.AbsoluteExpirationRelativeToNow = expiration.Value;
                }
                else
                {
                    entry.AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30); // 默认30分钟
                }

                // 设置缓存优先级
                entry.Priority = CacheItemPriority.Normal;

                // 设置移除回调
                entry.RegisterPostEvictionCallback(OnCacheItemRemoved);

                // 创建新项
                var item = await factory();

                // 记录缓存键
                lock (_lockObject)
                {
                    _cacheKeys.Add(key);
                }

                return item;
            });

            _logger.LogDebug("Successfully retrieved cache item for key: {CacheKey}", key);
            return result;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting or creating cache item for key: {CacheKey}", key);
            
            // 如果缓存失败，直接调用工厂方法
            return await factory();
        }
    }

    /// <summary>
    /// 使缓存失效
    /// </summary>
    public async Task InvalidateAsync(string pattern)
    {
        if (string.IsNullOrEmpty(pattern))
        {
            throw new ArgumentException("Pattern cannot be null or empty", nameof(pattern));
        }

        try
        {
            _logger.LogDebug("Invalidating cache items matching pattern: {Pattern}", pattern);

            var keysToRemove = new List<string>();

            lock (_lockObject)
            {
                // 查找匹配的键
                foreach (var key in _cacheKeys)
                {
                    if (IsPatternMatch(key, pattern))
                    {
                        keysToRemove.Add(key);
                    }
                }
            }

            // 移除匹配的缓存项
            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
                _logger.LogDebug("Removed cache item with key: {CacheKey}", key);
            }

            // 更新缓存键集合
            lock (_lockObject)
            {
                foreach (var key in keysToRemove)
                {
                    _cacheKeys.Remove(key);
                }
            }

            _logger.LogDebug("Invalidated {Count} cache items matching pattern: {Pattern}", 
                keysToRemove.Count, pattern);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error invalidating cache items with pattern: {Pattern}", pattern);
        }
    }

    /// <summary>
    /// 清除所有缓存
    /// </summary>
    public async Task ClearAsync()
    {
        try
        {
            _logger.LogDebug("Clearing all cache items");

            var keysToRemove = new List<string>();

            lock (_lockObject)
            {
                keysToRemove.AddRange(_cacheKeys);
            }

            // 移除所有缓存项
            foreach (var key in keysToRemove)
            {
                _cache.Remove(key);
            }

            // 清空缓存键集合
            lock (_lockObject)
            {
                _cacheKeys.Clear();
            }

            _logger.LogDebug("Cleared {Count} cache items", keysToRemove.Count);

            await Task.CompletedTask;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error clearing cache");
        }
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetStatistics()
    {
        lock (_lockObject)
        {
            return new CacheStatistics
            {
                TotalItems = _cacheKeys.Count,
                CacheKeys = _cacheKeys.ToList()
            };
        }
    }

    #region Private Methods

    private bool IsPatternMatch(string key, string pattern)
    {
        try
        {
            // 支持简单的通配符模式
            if (pattern.Contains("*"))
            {
                var regexPattern = "^" + Regex.Escape(pattern).Replace("\\*", ".*") + "$";
                return Regex.IsMatch(key, regexPattern, RegexOptions.IgnoreCase);
            }

            // 精确匹配
            return string.Equals(key, pattern, StringComparison.OrdinalIgnoreCase);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error matching pattern {Pattern} against key {Key}", pattern, key);
            return false;
        }
    }

    private void OnCacheItemRemoved(object key, object value, EvictionReason reason, object state)
    {
        try
        {
            var keyString = key?.ToString();
            if (!string.IsNullOrEmpty(keyString))
            {
                lock (_lockObject)
                {
                    _cacheKeys.Remove(keyString);
                }

                _logger.LogDebug("Cache item removed: {CacheKey}, Reason: {EvictionReason}", 
                    keyString, reason);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error in cache item removal callback");
        }
    }

    #endregion

    #region IDisposable

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed && disposing)
        {
            try
            {
                ClearAsync().GetAwaiter().GetResult();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing cache during disposal");
            }

            _disposed = true;
        }
    }

    #endregion
}

/// <summary>
/// 缓存统计信息
/// </summary>
public class CacheStatistics
{
    /// <summary>
    /// 缓存项总数
    /// </summary>
    public int TotalItems { get; set; }

    /// <summary>
    /// 缓存键列表
    /// </summary>
    public List<string> CacheKeys { get; set; } = new();

    /// <summary>
    /// 缓存命中率（如果有统计的话）
    /// </summary>
    public double? HitRate { get; set; }

    /// <summary>
    /// 缓存大小（字节）
    /// </summary>
    public long? SizeInBytes { get; set; }
}
