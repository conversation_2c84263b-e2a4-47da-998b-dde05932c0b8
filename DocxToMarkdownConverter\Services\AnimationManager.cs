using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using System.Windows.Media.Animation;
using DocxToMarkdownConverter.Models;
using Button = System.Windows.Controls.Button;
using ProgressBar = System.Windows.Controls.ProgressBar;
using Point = System.Windows.Point;
using Color = System.Windows.Media.Color;

namespace DocxToMarkdownConverter.Services;

public class AnimationManager : IAnimationManager
{
    private double _animationSpeed = 1.0;
    private bool _animationsEnabled = true;
    private readonly Dictionary<Button, System.Windows.Input.MouseEventHandler[]> _attachedHoverHandlers = new();
    private readonly IAdaptiveAnimationManager? _adaptiveAnimationManager;

    public AnimationManager(IAdaptiveAnimationManager? adaptiveAnimationManager = null)
    {
        _adaptiveAnimationManager = adaptiveAnimationManager;
    }

    public void EnableAnimations(bool enabled)
    {
        _animationsEnabled = enabled;
    }

    public void SetAnimationSpeed(double speed)
    {
        _animationSpeed = Math.Max(0.1, speed);
    }

    public async Task PlayEntranceAnimationAsync(FrameworkElement element)
    {
        if (!_animationsEnabled)
        {
            element.Opacity = 1;
            return;
        }

        // Set initial state
        element.Opacity = 0;

        if (_adaptiveAnimationManager != null)
        {
            var config = new AnimationConfig
            {
                Type = AnimationType.FadeIn,
                From = 0,
                To = 1,
                PropertyName = "Opacity",
                Priority = AnimationPriority.Low // 降低优先级
            };

            if (!_adaptiveAnimationManager.ShouldSkipAnimation(config))
            {
                var storyboard = _adaptiveAnimationManager.CreateAdaptiveAnimation(element, config);
                var tcs = new TaskCompletionSource<bool>();
                storyboard.Completed += (s, e) =>
                {
                    try
                    {
                        if (!tcs.Task.IsCompleted)
                        {
                            tcs.SetResult(true);
                        }
                    }
                    catch (InvalidOperationException)
                    {
                        // TaskCompletionSource已经完成，忽略此错误
                    }
                };
                storyboard.Begin();
                await tcs.Task;
                return;
            }
        }

        // Fallback to simple animation
        var animation = new DoubleAnimation(0, 1, TimeSpan.FromMilliseconds(400 / _animationSpeed))
        {
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseOut }
        };

        var fallbackTcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!fallbackTcs.Task.IsCompleted)
                {
                    fallbackTcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }
        };

        element.BeginAnimation(UIElement.OpacityProperty, animation);
        await fallbackTcs.Task;
    }

    public async Task PlayExitAnimationAsync(FrameworkElement element)
    {
        if (!_animationsEnabled) return;

        // Simple fade out animation
        var animation = new DoubleAnimation(element.Opacity, 0, TimeSpan.FromMilliseconds(250 / _animationSpeed))
        {
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseIn }
        };

        var tcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!tcs.Task.IsCompleted)
                {
                    tcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }
        };
        
        element.BeginAnimation(UIElement.OpacityProperty, animation);
        await tcs.Task;
    }

    public async Task PlayPageTransitionAsync(FrameworkElement from, FrameworkElement to)
    {
        if (!_animationsEnabled) return;

        // Simple cross-fade transition
        var fadeOutTask = PlayFadeAnimationAsync(from, from.Opacity, 0);
        await Task.Delay(TimeSpan.FromMilliseconds(100 / _animationSpeed));
        var fadeInTask = PlayFadeAnimationAsync(to, 0, 1);
        
        await Task.WhenAll(fadeOutTask, fadeInTask);
    }

    private readonly Dictionary<ProgressBar, DateTime> _lastProgressUpdate = new();
    private readonly Dictionary<ProgressBar, double> _pendingProgressValues = new();

    public async Task PlayProgressAnimationAsync(ProgressBar progressBar, double targetValue)
    {
        if (!_animationsEnabled)
        {
            progressBar.Value = targetValue;
            return;
        }

        // 节流：限制进度条动画频率，避免过于频繁的更新
        var now = DateTime.Now;
        if (_lastProgressUpdate.TryGetValue(progressBar, out var lastUpdate))
        {
            var timeSinceLastUpdate = now - lastUpdate;
            if (timeSinceLastUpdate.TotalMilliseconds < 100) // 最小间隔100ms
            {
                _pendingProgressValues[progressBar] = targetValue;
                return;
            }
        }

        _lastProgressUpdate[progressBar] = now;
        _pendingProgressValues.Remove(progressBar);

        // 使用更短的动画时间以提高响应性
        var duration = TimeSpan.FromMilliseconds(300 / _animationSpeed);
        var animation = new DoubleAnimation(progressBar.Value, targetValue, duration)
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut } // 使用更轻量的缓动函数
        };

        var tcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!tcs.Task.IsCompleted)
                {
                    tcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }

            // 检查是否有待处理的值需要更新
            if (_pendingProgressValues.TryGetValue(progressBar, out var pendingValue))
            {
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await PlayProgressAnimationAsync(progressBar, pendingValue);
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但不抛出，避免影响UI线程
                        System.Diagnostics.Debug.WriteLine($"Progress animation error: {ex.Message}");
                    }
                });
            }
        };

        progressBar.BeginAnimation(ProgressBar.ValueProperty, animation);
        await tcs.Task;
    }

    private readonly Dictionary<Button, bool> _buttonHoverStates = new();

    public async Task PlayButtonHoverAnimationAsync(Button button, bool isEntering)
    {
        if (!_animationsEnabled) return;

        // 避免重复的悬停动画
        if (_buttonHoverStates.TryGetValue(button, out var currentState) && currentState == isEntering)
            return;

        _buttonHoverStates[button] = isEntering;

        // 使用Transform而不是Opacity以获得更好的性能
        var transform = button.RenderTransform as ScaleTransform ?? new ScaleTransform(1, 1);
        if (button.RenderTransform != transform)
        {
            button.RenderTransform = transform;
            button.RenderTransformOrigin = new Point(0.5, 0.5);
        }

        var targetScale = isEntering ? 1.05 : 1.0;
        var animation = new DoubleAnimation(transform.ScaleX, targetScale, TimeSpan.FromMilliseconds(100 / _animationSpeed))
        {
            EasingFunction = new QuadraticEase { EasingMode = EasingMode.EaseOut }
        };

        var tcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!tcs.Task.IsCompleted)
                {
                    tcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }
        };

        transform.BeginAnimation(ScaleTransform.ScaleXProperty, animation);
        transform.BeginAnimation(ScaleTransform.ScaleYProperty, animation);
        await tcs.Task;
    }

    public async Task PlayScaleAnimationAsync(FrameworkElement element, double fromScale, double toScale)
    {
        if (!_animationsEnabled) return;

        // Use opacity as a simple scale effect substitute
        var currentOpacity = element.Opacity;
        var targetOpacity = toScale > fromScale ? Math.Min(1.0, currentOpacity * 1.2) : Math.Max(0.5, currentOpacity * 0.8);
        
        await PlayFadeAnimationAsync(element, currentOpacity, targetOpacity);
        await Task.Delay(TimeSpan.FromMilliseconds(100 / _animationSpeed));
        await PlayFadeAnimationAsync(element, targetOpacity, currentOpacity);
    }

    public async Task PlayFadeAnimationAsync(FrameworkElement element, double fromOpacity, double toOpacity)
    {
        if (!_animationsEnabled)
        {
            element.Opacity = toOpacity;
            return;
        }

        var animation = new DoubleAnimation(fromOpacity, toOpacity, TimeSpan.FromMilliseconds(300 / _animationSpeed))
        {
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
        };

        var tcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!tcs.Task.IsCompleted)
                {
                    tcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }
        };
        
        element.BeginAnimation(UIElement.OpacityProperty, animation);
        await tcs.Task;
    }

    public async Task PlaySlideAnimationAsync(FrameworkElement element, Point fromPosition, Point toPosition)
    {
        if (!_animationsEnabled) return;

        // Simple fade animation as substitute for slide
        await PlayFadeAnimationAsync(element, 1.0, 0.3);
        await Task.Delay(TimeSpan.FromMilliseconds(200 / _animationSpeed));
        await PlayFadeAnimationAsync(element, 0.3, 1.0);
    }

    public async Task PlayRotateAnimationAsync(FrameworkElement element, double fromAngle, double toAngle)
    {
        if (!_animationsEnabled) return;

        // Simple pulse effect as substitute for rotation
        await PlayPulseAnimationAsync(element);
    }

    public async Task PlayColorAnimationAsync(SolidColorBrush brush, Color fromColor, Color toColor)
    {
        if (!_animationsEnabled)
        {
            brush.Color = toColor;
            return;
        }

        var animation = new ColorAnimation(fromColor, toColor, TimeSpan.FromMilliseconds(300 / _animationSpeed))
        {
            EasingFunction = new CubicEase { EasingMode = EasingMode.EaseInOut }
        };

        var tcs = new TaskCompletionSource<bool>();
        animation.Completed += (s, e) =>
        {
            try
            {
                if (!tcs.Task.IsCompleted)
                {
                    tcs.SetResult(true);
                }
            }
            catch (InvalidOperationException)
            {
                // TaskCompletionSource已经完成，忽略此错误
            }
        };
        
        brush.BeginAnimation(SolidColorBrush.ColorProperty, animation);
        await tcs.Task;
    }

    public async Task PlayPulseAnimationAsync(FrameworkElement element)
    {
        if (!_animationsEnabled) return;

        // Simple opacity pulse effect
        var currentOpacity = element.Opacity;
        await PlayFadeAnimationAsync(element, currentOpacity, 0.6);
        await PlayFadeAnimationAsync(element, 0.6, currentOpacity);
    }

    public async Task PlayShakeAnimationAsync(FrameworkElement element)
    {
        if (!_animationsEnabled) return;

        // Simple rapid pulse effect as substitute for shake
        var currentOpacity = element.Opacity;
        for (int i = 0; i < 3; i++)
        {
            await PlayFadeAnimationAsync(element, currentOpacity, 0.7);
            await PlayFadeAnimationAsync(element, 0.7, currentOpacity);
        }
    }

    public void AttachHoverAnimations(Button button)
    {
        if (_attachedHoverHandlers.ContainsKey(button))
            return;

        System.Windows.Input.MouseEventHandler mouseEnterHandler = async (s, e) => await PlayButtonHoverAnimationAsync(button, true);
        System.Windows.Input.MouseEventHandler mouseLeaveHandler = async (s, e) => await PlayButtonHoverAnimationAsync(button, false);

        button.MouseEnter += mouseEnterHandler;
        button.MouseLeave += mouseLeaveHandler;

        _attachedHoverHandlers[button] = new[] { mouseEnterHandler, mouseLeaveHandler };
    }

    public void DetachHoverAnimations(Button button)
    {
        if (!_attachedHoverHandlers.TryGetValue(button, out var handlers))
            return;

        button.MouseEnter -= handlers[0];
        button.MouseLeave -= handlers[1];

        _attachedHoverHandlers.Remove(button);
    }
}
