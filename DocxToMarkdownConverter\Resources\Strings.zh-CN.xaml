<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=mscorlib">

    <!-- 应用程序基本信息 -->
    <system:String x:Key="App.Title">DOCX转换器</system:String>
    <system:String x:Key="App.Version">版本 3.2.0</system:String>
    <system:String x:Key="App.Description">将您的DOCX文档轻松转换为清洁的Markdown格式。选择要转换的文件开始使用。</system:String>
    <system:String x:Key="App.Welcome">欢迎使用DOCX转换器</system:String>

    <!-- 导航菜单 -->
    <system:String x:Key="Navigation.Files">文件</system:String>
    <system:String x:Key="Navigation.Settings">设置</system:String>
    <system:String x:Key="Navigation.Progress">进度</system:String>
    <system:String x:Key="Navigation.Results">结果</system:String>
    <system:String x:Key="Navigation.Statistics">统计信息</system:String>

    <!-- 工具提示 -->
    <system:String x:Key="Tooltip.Files">文件 (Ctrl+1)</system:String>
    <system:String x:Key="Tooltip.Settings">设置 (Ctrl+2)</system:String>
    <system:String x:Key="Tooltip.Progress">进度 (Ctrl+3)</system:String>
    <system:String x:Key="Tooltip.Results">结果 (Ctrl+4)</system:String>
    <system:String x:Key="Tooltip.ToggleTheme">切换主题 (Ctrl+T)</system:String>
    <system:String x:Key="Tooltip.SelectFiles">选择文件 (Ctrl+O)</system:String>
    <system:String x:Key="Tooltip.AddFiles">添加DOCX文件</system:String>
    <system:String x:Key="Tooltip.ClearAllFiles">清空所有文件</system:String>
    <system:String x:Key="Tooltip.RemoveFile">移除文件</system:String>

    <!-- 主题设置 -->
    <system:String x:Key="Theme.Settings">主题设置</system:String>
    <system:String x:Key="Theme.Mode">主题模式</system:String>
    <system:String x:Key="Theme.Light">浅色</system:String>
    <system:String x:Key="Theme.Dark">深色</system:String>
    <system:String x:Key="Theme.Auto">跟随系统</system:String>
    <system:String x:Key="Theme.EnableTransitions">启用主题切换动画</system:String>
    <system:String x:Key="Theme.EnableAnimations">启用界面动画效果</system:String>
    <system:String x:Key="Theme.AnimationSpeed">动画速度:</system:String>
    <system:String x:Key="Theme.FollowSystem">跟随系统主题变化</system:String>
    <system:String x:Key="Theme.Toggle">切换主题</system:String>
    <system:String x:Key="Theme.Reset">重置为默认</system:String>
    <system:String x:Key="Theme.AnimationTooltip">禁用此选项可提升低性能设备上的响应速度</system:String>

    <!-- 按钮 -->
    <system:String x:Key="Button.OK">确定</system:String>
    <system:String x:Key="Button.Cancel">取消</system:String>
    <system:String x:Key="Button.Apply">应用</system:String>
    <system:String x:Key="Button.Reset">重置</system:String>
    <system:String x:Key="Button.Browse">浏览</system:String>
    <system:String x:Key="Button.Save">保存</system:String>
    <system:String x:Key="Button.Load">加载</system:String>
    <system:String x:Key="Button.Close">关闭</system:String>
    <system:String x:Key="Button.Yes">是</system:String>
    <system:String x:Key="Button.No">否</system:String>

    <!-- 文件操作 -->
    <system:String x:Key="File.SelectFiles">选择文件</system:String>
    <system:String x:Key="File.SelectFolder">选择文件夹</system:String>
    <system:String x:Key="File.OutputDirectory">输出目录</system:String>
    <system:String x:Key="File.AddFiles">添加文件</system:String>
    <system:String x:Key="File.RemoveFiles">移除文件</system:String>
    <system:String x:Key="File.ClearAll">清空所有</system:String>
    <system:String x:Key="File.StartConversion">开始转换</system:String>

    <!-- 状态信息 -->
    <system:String x:Key="Status.Ready">就绪</system:String>
    <system:String x:Key="Status.Processing">处理中</system:String>
    <system:String x:Key="Status.Completed">已完成</system:String>
    <system:String x:Key="Status.Failed">失败</system:String>
    <system:String x:Key="Status.Cancelled">已取消</system:String>

    <!-- 错误消息 -->
    <system:String x:Key="Error.FileNotFound">文件未找到</system:String>
    <system:String x:Key="Error.InvalidFile">无效的文件格式</system:String>
    <system:String x:Key="Error.ConversionFailed">转换失败</system:String>
    <system:String x:Key="Error.AccessDenied">访问被拒绝</system:String>
    <system:String x:Key="Error.UnknownError">未知错误</system:String>

    <!-- 成功消息 -->
    <system:String x:Key="Success.ConversionCompleted">转换完成</system:String>
    <system:String x:Key="Success.FilesSaved">文件已保存</system:String>
    <system:String x:Key="Success.SettingsSaved">设置已保存</system:String>

    <!-- 确认对话框 -->
    <system:String x:Key="Confirm.ClearFiles">确定要清空所有文件吗？</system:String>
    <system:String x:Key="Confirm.OverwriteFile">文件已存在，是否覆盖？</system:String>
    <system:String x:Key="Confirm.ExitApplication">确定要退出应用程序吗？</system:String>
    <system:String x:Key="Confirm.ResetSettings">确定要重置所有设置吗？</system:String>

    <!-- 设置页面 -->
    <system:String x:Key="Settings.General">常规设置</system:String>
    <system:String x:Key="Settings.Appearance">外观设置</system:String>
    <system:String x:Key="Settings.Advanced">高级设置</system:String>
    <system:String x:Key="Settings.Theme">主题设置</system:String>
    <system:String x:Key="Settings.Language">语言设置</system:String>
    <system:String x:Key="Settings.About">关于</system:String>

    <!-- 语言设置 -->
    <system:String x:Key="Language.Settings">语言设置</system:String>
    <system:String x:Key="Language.Current">当前语言</system:String>
    <system:String x:Key="Language.Chinese">中文</system:String>
    <system:String x:Key="Language.English">English</system:String>
    <system:String x:Key="Language.FollowSystem">跟随系统语言</system:String>
    <system:String x:Key="Language.RestartRequired">语言更改将在重启应用程序后生效</system:String>

    <!-- 进度信息 -->
    <system:String x:Key="Progress.CurrentFile">当前文件:</system:String>
    <system:String x:Key="Progress.FilesProcessed">已处理文件:</system:String>
    <system:String x:Key="Progress.TotalFiles">总文件数:</system:String>
    <system:String x:Key="Progress.ElapsedTime">已用时间:</system:String>
    <system:String x:Key="Progress.EstimatedTime">预计时间:</system:String>

    <!-- 结果页面 -->
    <system:String x:Key="Results.Summary">转换摘要</system:String>
    <system:String x:Key="Results.SuccessfulConversions">成功转换:</system:String>
    <system:String x:Key="Results.FailedConversions">转换失败:</system:String>
    <system:String x:Key="Results.TotalProcessed">总计处理:</system:String>
    <system:String x:Key="Results.OpenOutputFolder">打开输出文件夹</system:String>
    <system:String x:Key="Results.ViewDetails">查看详情</system:String>

    <!-- 转换选项 -->
    <system:String x:Key="Conversion.Options">转换选项</system:String>
    <system:String x:Key="Conversion.OutputSettings">输出设置</system:String>
    <system:String x:Key="Conversion.ConversionSettings">转换设置</system:String>
    <system:String x:Key="Conversion.ImageSettings">图像设置</system:String>
    <system:String x:Key="Conversion.OutputDirectory">输出目录</system:String>
    <system:String x:Key="Conversion.ImageDirectory">图片目录（相对于输出目录）</system:String>
    <system:String x:Key="Conversion.ExtractImages">提取图片</system:String>
    <system:String x:Key="Conversion.ConvertTables">转换表格</system:String>
    <system:String x:Key="Conversion.ProcessFormulas">处理数学公式</system:String>
    <system:String x:Key="Conversion.PreserveFormatting">保留格式</system:String>
    <system:String x:Key="Conversion.IncludeMetadata">包含元数据</system:String>
    <system:String x:Key="Conversion.ImageFormat">图片格式</system:String>
    <system:String x:Key="Conversion.SelectImageFormat">选择图片格式</system:String>

    <!-- 动画设置 -->
    <system:String x:Key="Animation.Settings">动画设置</system:String>
    <system:String x:Key="Animation.EnablePageTransitions">启用页面切换动画</system:String>
    <system:String x:Key="Animation.EnableButtonAnimations">启用按钮动画</system:String>
    <system:String x:Key="Animation.EnableProgressAnimations">启用进度动画</system:String>

    <!-- 统计信息 -->
    <system:String x:Key="Statistics.Title">统计信息</system:String>
    <system:String x:Key="Statistics.FilesProcessed">已处理文件: {0}</system:String>
    <system:String x:Key="Statistics.Successful">成功: {0}</system:String>
    <system:String x:Key="Statistics.Failed">失败: {0}</system:String>

    <!-- 文件界面 -->
    <system:String x:Key="Files.Title">文件管理</system:String>
    <system:String x:Key="Files.Description">添加DOCX文件以将其转换为Markdown格式。您可以拖放文件或使用文件浏览器。</system:String>
    <system:String x:Key="Files.ToConvert">待转换文件</system:String>
    <system:String x:Key="Files.NoFiles">暂无文件</system:String>
    <system:String x:Key="Files.DragDropHint">拖拽文件到此处或点击按钮添加</system:String>
    <system:String x:Key="Files.SelectFiles">选择文件</system:String>
    <system:String x:Key="Files.AddFiles">添加文件</system:String>
    <system:String x:Key="Files.ClearAll">清空全部</system:String>
    <system:String x:Key="Files.StartConversion">开始转换</system:String>
    <system:String x:Key="Files.StatusSummary">总计: {0} 文件 • 就绪: {1} • 已完成: {2} • 失败: {3}</system:String>

    <!-- 文件对话框 -->
    <system:String x:Key="Dialog.SelectDocxFiles">选择DOCX文件</system:String>
    <system:String x:Key="Dialog.WordDocuments">Word文档 (*.docx)</system:String>
    <system:String x:Key="Dialog.AllFiles">所有文件 (*.*)</system:String>

    <!-- 结果界面 -->
    <system:String x:Key="Results.Title">转换结果</system:String>
    <system:String x:Key="Results.Description">查看和管理您的文档转换结果</system:String>
    <system:String x:Key="Results.SearchHint">搜索结果...</system:String>
    <system:String x:Key="Results.NoResultsTitle">暂无结果</system:String>
    <system:String x:Key="Results.NoResultsDescription">处理DOCX文件后，转换结果将显示在此处。首先添加文件并运行转换。</system:String>

    <!-- 结果统计卡片 -->
    <system:String x:Key="Results.TotalResults">总结果数</system:String>
    <system:String x:Key="Results.SuccessRate">成功率</system:String>
    <system:String x:Key="Results.AverageTime">平均时间</system:String>
    <system:String x:Key="Results.ImagesProcessed">已处理图片</system:String>

    <!-- 结果筛选 -->
    <system:String x:Key="Results.FilterByStatus">按状态筛选</system:String>
    <system:String x:Key="Results.FilterAll">全部</system:String>
    <system:String x:Key="Results.FilterSuccess">成功</system:String>
    <system:String x:Key="Results.FilterFailed">失败</system:String>

    <!-- 结果上下文菜单 -->
    <system:String x:Key="Results.OpenOutputFile">打开输出文件</system:String>
    <system:String x:Key="Results.OpenOutputDirectory">打开输出目录</system:String>
    <system:String x:Key="Results.OpenInputFile">打开输入文件</system:String>
    <system:String x:Key="Results.OpenInputDirectory">打开输入目录</system:String>
    <system:String x:Key="Results.CopyOutputPath">复制输出路径</system:String>
    <system:String x:Key="Results.RetryConversion">重试转换</system:String>
    <system:String x:Key="Results.RemoveResult">移除结果</system:String>

    <!-- 结果工具提示 -->
    <system:String x:Key="Results.ExportResults">导出结果</system:String>
    <system:String x:Key="Results.ClearAllResults">清空所有结果</system:String>

</ResourceDictionary>
