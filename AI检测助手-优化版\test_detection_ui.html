<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI检测助手 - UI测试</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="js/detector_ui.css" rel="stylesheet">
    <style>
        body {
            font-family: 'Microsoft YaHei', 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: #f5f7fa;
            min-height: 100vh;
            color: #333;
            margin: 0;
            padding: 20px;
            overflow-x: hidden;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-actions {
            margin-bottom: 20px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }
        
        .btn {
            padding: 10px 20px;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            font-weight: 500;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #007bff;
            color: white;
        }
        
        .btn-secondary {
            background: #6c757d;
            color: white;
        }
        
        .btn-success {
            background: #28a745;
            color: white;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
        }
        
        .loading {
            text-align: center;
            padding: 20px;
            display: none;
        }
        
        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .notification {
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 15px 20px;
            background: #333;
            color: white;
            border-radius: 8px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            opacity: 0;
            transform: translateY(20px);
            transition: all 0.3s ease;
            z-index: 1000;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .notification.show {
            opacity: 1;
            transform: translateY(0);
        }
        
        .notification.success {
            background: #28a745;
        }
        
        .notification.error {
            background: #dc3545;
        }
        
        .notification.info {
            background: #17a2b8;
        }
        
        .log-area {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            border: 1px solid #dee2e6;
        }
        
        .log-entry {
            margin-bottom: 5px;
            padding: 5px;
            border-radius: 4px;
        }
        
        .log-entry.success {
            background: #d4edda;
            color: #155724;
        }
        
        .log-entry.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .log-entry.info {
            background: #d1ecf1;
            color: #0c5460;
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>AI检测助手 - UI测试</h1>
            <p>测试AI检测结果的显示与交互</p>
        </div>
        
        <div class="test-actions">
            <button class="btn btn-primary" onclick="testLowAIScore()">测试低AI分数</button>
            <button class="btn btn-primary" onclick="testMediumAIScore()">测试中等AI分数</button>
            <button class="btn btn-primary" onclick="testHighAIScore()">测试高AI分数</button>
            <button class="btn btn-secondary" onclick="clearResult()">清空结果</button>
        </div>
        
        <div class="loading" id="testLoading">
            <div class="spinner"></div>
            <p>正在模拟检测过程...</p>
        </div>
        
        <div class="result-area" id="detectResult" style="display: none;">
            <div class="ai-detection-result">
                <!-- AI检测结果警告提示 -->
                <div class="ai-detection-warning" id="aiDetectionWarning" style="display: none;">
                    <div class="warning-icon">
                        <i class="fas fa-exclamation-circle"></i>
                    </div>
                    <div class="warning-content">
                        <div class="warning-title">检测到高度AI特征</div>
                        <div class="warning-description">您的文本被多个检测平台识别为AI生成，建议使用优化功能降低AI特征</div>
                    </div>
                </div>
                
                <!-- 检测结果头部 -->
                <div class="detection-header">
                    <div class="detection-score-main" id="detectionScoreMain">0%</div>
                    <div class="detection-status" id="detectionStatus">等待检测...</div>
                    <div class="detection-mode" id="detectionMode">📝 基础检测</div>
                </div>

                <!-- 饼图和分类展示 -->
                <div class="pie-chart-container">
                    <div class="pie-chart">
                        <svg viewBox="0 0 100 100">
                            <circle cx="50" cy="50" r="40" fill="none" stroke="#e9ecef" stroke-width="8"/>
                            <circle id="aiScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#dc3545"
                                    stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                            <circle id="humanScoreCircle" cx="50" cy="50" r="40" fill="none" stroke="#28a745"
                                    stroke-width="8" stroke-dasharray="0 251.2" stroke-linecap="round"/>
                        </svg>
                        <div class="pie-chart-center">
                            <div class="pie-chart-score" id="pieChartScore">0%</div>
                            <div class="pie-chart-label">AI检测度</div>
                        </div>
                    </div>

                    <div class="detection-breakdown">
                        <div class="breakdown-item human-written">
                            <div class="breakdown-percentage human-written" id="humanPercentage">0%</div>
                            <div class="breakdown-label">人工写作</div>
                        </div>
                        <div class="breakdown-item ai-generated">
                            <div class="breakdown-percentage ai-generated" id="aiPercentage">0%</div>
                            <div class="breakdown-label">AI生成</div>
                        </div>
                        <div class="breakdown-item suspicious">
                            <div class="breakdown-percentage suspicious" id="suspiciousPercentage">0%</div>
                            <div class="breakdown-label">疑似AI</div>
                        </div>
                    </div>
                </div>

                <!-- 图例 -->
                <div class="chart-legend">
                    <div class="legend-item">
                        <div class="legend-color human"></div>
                        <span>人工写作</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color ai"></div>
                        <span>AI生成</span>
                    </div>
                    <div class="legend-item">
                        <div class="legend-color suspicious"></div>
                        <span>疑似AI</span>
                    </div>
                </div>

                <!-- 进度条展示 -->
                <div class="progress-bars">
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>AI检测分数</span>
                            <span id="aiScoreLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill ai-score" id="aiScoreProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>检测置信度</span>
                            <span id="confidenceLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill confidence" id="confidenceProgress" style="width: 0%"></div>
                        </div>
                    </div>
                    <div class="progress-item">
                        <div class="progress-label">
                            <span>文本复杂度</span>
                            <span id="complexityLabel">0%</span>
                        </div>
                        <div class="progress-bar-container">
                            <div class="progress-bar-fill complexity" id="complexityProgress" style="width: 0%"></div>
                        </div>
                    </div>
                </div>

                <!-- 详细结果 -->
                <div class="result-details" id="resultDetails"></div>
                
                <!-- 操作按钮 -->
                <div class="action-buttons">
                    <button class="action-button optimize" onclick="copyToOptimizer()">
                        <i class="fas fa-magic"></i> 智能优化
                    </button>
                    <button class="action-button copy" onclick="copyDetectionResult()">
                        <i class="fas fa-copy"></i> 复制报告
                    </button>
                </div>
            </div>
        </div>
        
        <div class="log-area" id="logArea">
            <div class="log-entry info">准备开始测试...</div>
        </div>
    </div>
    
    <div class="notification" id="notification">
        <i class="fas fa-info-circle"></i>
        <span id="notificationText"></span>
    </div>

    <script>
        // 测试显示低AI分数的检测结果
        function testLowAIScore() {
            showLoading();
            log('开始测试低AI分数展示...');
            
            setTimeout(() => {
                const mockResult = {
                    aiProbability: 15,
                    confidence: 0.85,
                    mode: 'zhuque_rule',
                    recommendation: '当前文本AI特征很少，可以放心使用。',
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 12, details: { adjustedPerplexity: 4.85 } },
                            structural: { score: 18, details: { consistencyRatio: 0.92 } },
                            semantic: { score: 15, details: { lexicalDiversity: 0.88 } },
                            frequency: { score: 10, details: { entropy: 5.43 } }
                        },
                        technicalDetails: {
                            method: '朱雀四维度分析',
                            features: ['困惑度', '结构化', '语义一致性', '频域特征'],
                            confidence_interval: [0.08, 0.22]
                        }
                    },
                    evidence: [
                        '语义连贯性高，符合人类写作风格',
                        '词汇多样性指数在人工写作范围内',
                        '句子长度变化自然且符合人类写作习惯'
                    ]
                };
                
                displayDetectionResult(mockResult);
                hideLoading();
                log('低AI分数结果展示成功', 'success');
            }, 1500);
        }
        
        // 测试显示中等AI分数的检测结果
        function testMediumAIScore() {
            showLoading();
            log('开始测试中等AI分数展示...');
            
            setTimeout(() => {
                const mockResult = {
                    aiProbability: 45,
                    confidence: 0.72,
                    mode: 'hybrid_zhuque',
                    recommendation: '建议使用智能优化功能进行改进，降低AI检测率。',
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 52, details: { adjustedPerplexity: 3.25 } },
                            structural: { score: 48, details: { consistencyRatio: 0.68 } },
                            semantic: { score: 41, details: { lexicalDiversity: 0.72 } },
                            frequency: { score: 39, details: { entropy: 4.15 } }
                        },
                        technicalDetails: {
                            method: '朱雀四维度分析',
                            features: ['困惑度', '结构化', '语义一致性', '频域特征'],
                            confidence_interval: [0.38, 0.52]
                        }
                    },
                    evidence: [
                        '文本存在部分AI特征，但不明显',
                        '词汇选择偏向规范化，缺乏个性表达',
                        '句式结构较为规律，建议增加变化'
                    ]
                };
                
                displayDetectionResult(mockResult);
                hideLoading();
                log('中等AI分数结果展示成功', 'success');
            }, 1500);
        }
        
        // 测试显示高AI分数的检测结果
        function testHighAIScore() {
            showLoading();
            log('开始测试高AI分数展示...');
            
            setTimeout(() => {
                const mockResult = {
                    aiProbability: 88,
                    confidence: 0.91,
                    mode: 'professional_llm_zhuque',
                    recommendation: '强烈建议使用学术专业优化功能进行深度改写。',
                    zhuqueAnalysis: {
                        analysis: {
                            perplexity: { score: 85, details: { adjustedPerplexity: 2.15 } },
                            structural: { score: 92, details: { consistencyRatio: 0.41 } },
                            semantic: { score: 87, details: { lexicalDiversity: 0.48 } },
                            frequency: { score: 91, details: { entropy: 3.32 } }
                        },
                        technicalDetails: {
                            method: '朱雀四维度分析',
                            features: ['困惑度', '结构化', '语义一致性', '频域特征'],
                            confidence_interval: [0.82, 0.94]
                        }
                    },
                    evidence: [
                        '检测到典型的AI生成句式结构',
                        '词汇使用模式符合AI生成特征',
                        '句子长度分布过于规律',
                        '缺乏人类写作的随机性和变化',
                        '语义连贯性过高，不符合人类自然写作'
                    ],
                    llmAnalysis: '文本中存在多处特征表明这是由人工智能生成的内容。首先，句子结构过于规整，缺乏人类写作时常见的不规则变化。其次，词汇选择模式显示出AI生成的特征，如过度使用某些连接词和过渡词。此外，文本的语义连贯性异常高，而人类写作通常会有一定程度的思维跳跃。综合这些特征分析，该文本几乎可以确定是由AI生成的，建议进行深度改写，增加人类写作的自然变化和个性化表达。'
                };
                
                displayDetectionResult(mockResult);
                hideLoading();
                log('高AI分数结果展示成功', 'success');
            }, 1500);
        }
        
        // 清空检测结果
        function clearResult() {
            document.getElementById('detectResult').style.display = 'none';
            log('清空检测结果', 'info');
        }
        
        // 模拟复制到优化器
        function copyToOptimizer() {
            showNotification('已复制文本到优化器', 'success');
            log('文本已复制到优化器', 'success');
        }
        
        // 显示加载状态
        function showLoading() {
            document.getElementById('testLoading').style.display = 'block';
        }
        
        // 隐藏加载状态
        function hideLoading() {
            document.getElementById('testLoading').style.display = 'none';
        }
        
        // 显示通知
        function showNotification(message, type = 'info') {
            const notification = document.getElementById('notification');
            const notificationText = document.getElementById('notificationText');
            
            notification.className = 'notification ' + type;
            notificationText.textContent = message;
            
            notification.classList.add('show');
            
            setTimeout(() => {
                notification.classList.remove('show');
            }, 3000);
        }
        
        // 记录日志
        function log(message, type = 'info') {
            const logArea = document.getElementById('logArea');
            const entry = document.createElement('div');
            entry.className = 'log-entry ' + type;
            entry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logArea.appendChild(entry);
            logArea.scrollTop = logArea.scrollHeight;
        }
        
        // 以下为复制自main.js的必要函数
        
        function displayDetectionResult(result) {
            const resultArea = document.getElementById('detectResult');
            const resultDetails = document.getElementById('resultDetails');

            if (!resultArea || !resultDetails) {
                console.error('detectResult或resultDetails元素不存在');
                return;
            }

            // 兼容新旧结果格式
            const aiScore = result.aiProbability || result.score || 0;
            const humanScore = Math.max(0, 100 - aiScore);
            const suspiciousScore = Math.min(aiScore, 20); // 疑似AI分数，最多20%

            // 更新主要分数显示
            updateMainScore(aiScore);

            // 更新饼图
            updatePieChart(aiScore, humanScore);

            // 更新分类展示
            updateBreakdown(humanScore, aiScore, suspiciousScore);

            // 更新进度条
            updateProgressBars(aiScore, result);

            // 更新检测状态样式
            updateDetectionStatus(aiScore);

            // 获取检测模式标签
            const modeLabels = {
                'professional_llm_zhuque': '🔬 专业LLM+朱雀',
                'zhuque_enhanced': '⚡ 朱雀增强',
                'zhuque_only': '🏮 朱雀算法',
                'hybrid_zhuque': '🔄 混合+朱雀',
                'zhuque_rule': '📊 朱雀+规则',
                'rule_only': '📝 规则检测'
            };
            const modeLabel = modeLabels[result.mode] || '📝 基础检测';

            // 更新检测模式
            updateDetectionMode(result.mode);

            // 根据分数设置建议文本
            let recommendation;
            if (aiScore < 25) {
                recommendation = result.recommendation || '当前文本AI特征很少，可以放心使用。';
            } else if (aiScore < 60) {
                recommendation = result.recommendation || '建议使用智能优化功能进行改进，降低AI检测率。';
            } else {
                recommendation = result.recommendation || '强烈建议使用学术专业优化功能进行深度改写。';
            }

            // 生成朱雀分析详情
            let zhuqueDetails = '';
            if (result.zhuqueAnalysis && result.zhuqueAnalysis.analysis) {
                const analysis = result.zhuqueAnalysis.analysis;
                zhuqueDetails = `
                    <div class="result-card">
                        <h5><i class="fas fa-microscope"></i> 朱雀算法分析</h5>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; margin: 10px 0;">
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>困惑度分析:</strong> ${analysis.perplexity?.score || 'N/A'}/100
                                ${analysis.perplexity?.details ? `<br><small>调整困惑度: ${Math.round(analysis.perplexity.details.adjustedPerplexity * 100) / 100}</small>` : ''}
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>结构化特征:</strong> ${analysis.structural?.score || 'N/A'}/100
                                ${analysis.structural?.details ? `<br><small>句子一致性: ${analysis.structural.details.consistencyRatio}</small>` : ''}
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>语义一致性:</strong> ${analysis.semantic?.score || 'N/A'}/100
                                ${analysis.semantic?.details ? `<br><small>词汇多样性: ${analysis.semantic.details.lexicalDiversity}</small>` : ''}
                            </div>
                            <div style="background: white; padding: 10px; border-radius: 5px;">
                                <strong>频域特征:</strong> ${analysis.frequency?.score || 'N/A'}/100
                                ${analysis.frequency?.details ? `<br><small>字符熵: ${analysis.frequency.details.entropy}</small>` : ''}
                            </div>
                        </div>
                        
                        ${result.zhuqueAnalysis.technicalDetails ? `
                            <div style="margin-top: 10px; padding: 10px; background: white; border-radius: 5px;">
                                <strong>技术细节:</strong> ${result.zhuqueAnalysis.technicalDetails.method}<br>
                                <strong>特征维度:</strong> ${result.zhuqueAnalysis.technicalDetails.features.join(', ')}<br>
                                <strong>置信区间:</strong> [${result.zhuqueAnalysis.technicalDetails.confidence_interval.map(x => Math.round(x * 100) / 100).join(', ')}]
                            </div>
                        ` : ''}
                    </div>
                `;
            }

            // 显示详细信息
            resultDetails.innerHTML = `
                <h4><i class="fas fa-chart-line"></i> 检测详情</h4>
                <div class="result-card">
                    <p><strong>🎯 检测建议：</strong>${recommendation}</p>
                    <p><strong>📊 置信度：</strong>${typeof result.confidence === 'number' ?
                        Math.round(result.confidence * 100) + '%' :
                        (result.confidence === 'high' ? '高' : result.confidence === 'medium' ? '中' : '低')}</p>
                    <p><strong>🔍 检测模式：</strong>${modeLabel}</p>
                </div>

                ${zhuqueDetails}

                ${result.evidence && result.evidence.length > 0 ? `
                    <div class="result-card">
                        <h5>📋 检测证据：</h5>
                        <ul>
                            ${result.evidence.map(evidence => `<li>${evidence}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}

                ${result.details && result.details.length > 0 ? `
                    <div class="result-card">
                        <h5>📋 分析明细：</h5>
                        <ul>
                            ${result.details.map(detail => `<li>${detail}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}

                ${result.llmAnalysis ? `
                    <div class="result-card">
                        <h5><i class="fas fa-robot"></i> LLM分析</h5>
                        <div style="max-height: 150px; overflow-y: auto; background: white; padding: 10px; border-radius: 5px;">
                            ${result.llmAnalysis.substring(0, 500)}${result.llmAnalysis.length > 500 ? '...' : ''}
                        </div>
                    </div>
                ` : ''}
            `;
            
            resultArea.style.display = 'block';
        }
        
        function updateMainScore(aiScore) {
            const scoreElement = document.getElementById('detectionScoreMain');
            const pieScoreElement = document.getElementById('pieChartScore');
            const warningElement = document.getElementById('aiDetectionWarning');

            if (scoreElement) {
                scoreElement.textContent = aiScore + '%';

                // 设置颜色类
                scoreElement.className = 'detection-score-main';
                if (aiScore < 25) {
                    scoreElement.classList.add('low');
                } else if (aiScore < 60) {
                    scoreElement.classList.add('medium');
                } else {
                    scoreElement.classList.add('high');
                }
            }

            if (pieScoreElement) {
                pieScoreElement.textContent = aiScore + '%';
            }
            
            // 显示或隐藏警告
            if (warningElement) {
                if (aiScore >= 75) {
                    warningElement.style.display = 'flex';
                } else {
                    warningElement.style.display = 'none';
                }
            }
        }
        
        function updatePieChart(aiScore, humanScore) {
            const aiCircle = document.getElementById('aiScoreCircle');
            const humanCircle = document.getElementById('humanScoreCircle');

            if (aiCircle && humanCircle) {
                const circumference = 2 * Math.PI * 40; // r=40

                // 计算AI部分的弧长
                const aiArcLength = (aiScore / 100) * circumference;
                const humanArcLength = (humanScore / 100) * circumference;

                // 设置AI部分（红色）
                aiCircle.style.strokeDasharray = `${aiArcLength} ${circumference}`;
                aiCircle.style.strokeDashoffset = '0';

                // 设置人工部分（绿色），从AI部分结束的地方开始
                humanCircle.style.strokeDasharray = `${humanArcLength} ${circumference}`;
                humanCircle.style.strokeDashoffset = `-${aiArcLength}`;

                // 添加动画效果
                aiCircle.style.transition = 'stroke-dasharray 1s ease-in-out';
                humanCircle.style.transition = 'stroke-dasharray 1s ease-in-out, stroke-dashoffset 1s ease-in-out';
            }
        }
        
        function updateBreakdown(humanScore, aiScore, suspiciousScore) {
            const humanElement = document.getElementById('humanPercentage');
            const aiElement = document.getElementById('aiPercentage');
            const suspiciousElement = document.getElementById('suspiciousPercentage');

            if (humanElement) {
                humanElement.textContent = Math.round(humanScore) + '%';
            }
            if (aiElement) {
                aiElement.textContent = Math.round(aiScore) + '%';
            }
            if (suspiciousElement) {
                suspiciousElement.textContent = Math.round(suspiciousScore) + '%';
            }
        }
        
        function updateProgressBars(aiScore, result) {
            // AI检测分数进度条
            const aiScoreProgress = document.getElementById('aiScoreProgress');
            const aiScoreLabel = document.getElementById('aiScoreLabel');
            if (aiScoreProgress && aiScoreLabel) {
                setTimeout(() => {
                    aiScoreProgress.style.width = aiScore + '%';
                }, 100);
                aiScoreLabel.textContent = aiScore + '%';
            }

            // 置信度进度条
            const confidenceProgress = document.getElementById('confidenceProgress');
            const confidenceLabel = document.getElementById('confidenceLabel');
            if (confidenceProgress && confidenceLabel) {
                let confidenceValue = 0;
                if (typeof result.confidence === 'number') {
                    confidenceValue = Math.round(result.confidence * 100);
                } else if (result.confidence === 'high') {
                    confidenceValue = 85;
                } else if (result.confidence === 'medium') {
                    confidenceValue = 65;
                } else {
                    confidenceValue = 45;
                }

                setTimeout(() => {
                    confidenceProgress.style.width = confidenceValue + '%';
                }, 200);
                confidenceLabel.textContent = confidenceValue + '%';
            }

            // 文本复杂度进度条
            const complexityProgress = document.getElementById('complexityProgress');
            const complexityLabel = document.getElementById('complexityLabel');
            if (complexityProgress && complexityLabel) {
                // 基于文本特征计算复杂度
                let complexityValue = 50; // 默认值
                if (result.zhuqueAnalysis && result.zhuqueAnalysis.analysis) {
                    const analysis = result.zhuqueAnalysis.analysis;
                    complexityValue = Math.round((
                        (analysis.perplexity?.score || 50) +
                        (analysis.structural?.score || 50) +
                        (analysis.semantic?.score || 50)
                    ) / 3);
                }

                setTimeout(() => {
                    complexityProgress.style.width = complexityValue + '%';
                }, 300);
                complexityLabel.textContent = complexityValue + '%';
            }
        }
        
        function updateDetectionStatus(aiScore) {
            const detectionStatus = document.getElementById('detectionStatus');
            if (!detectionStatus) return;
            
            // 移除所有状态类
            detectionStatus.classList.remove('low', 'medium', 'high');
            
            // 根据分数设置状态和描述
            let description, statusClass;
            if (aiScore < 25) {
                description = '✅ AI特征较少，内容较为自然';
                statusClass = 'low';
            } else if (aiScore < 60) {
                description = '⚠️ 存在一定AI特征，建议适当优化';
                statusClass = 'medium';
            } else {
                description = '🚨 AI特征明显，需要重点优化';
                statusClass = 'high';
            }
            
            // 更新状态描述和样式
            detectionStatus.textContent = description;
            detectionStatus.classList.add(statusClass);
        }
        
        function updateDetectionMode(mode) {
            const modeElement = document.getElementById('detectionMode');
            if (modeElement) {
                const modeLabels = {
                    'professional_llm_zhuque': '🔬 专业LLM+朱雀',
                    'zhuque_enhanced': '⚡ 朱雀增强',
                    'zhuque_only': '🏮 朱雀算法',
                    'hybrid_zhuque': '🔄 混合+朱雀',
                    'zhuque_rule': '📊 朱雀+规则',
                    'rule_only': '📝 规则检测'
                };

                modeElement.textContent = modeLabels[mode] || '📝 基础检测';
            }
        }
        
        function copyDetectionResult() {
            const scoreElement = document.getElementById('detectionScoreMain');
            const statusElement = document.getElementById('detectionStatus');
            const modeElement = document.getElementById('detectionMode');
            const detailsElement = document.getElementById('resultDetails');
            
            if (!scoreElement || !statusElement || !detailsElement) {
                alert('未找到检测结果');
                return;
            }
            
            // 创建文本报告
            const score = scoreElement.textContent;
            const status = statusElement.textContent;
            const mode = modeElement.textContent;
            
            // 简单提取详情内容的文本，移除HTML标签
            const detailsText = detailsElement.textContent.replace(/\s+/g, ' ').trim();
            
            // 生成报告文本
            const report = `
==== AI检测报告 ====
AI检测度: ${score}
检测状态: ${status}
检测模式: ${mode}

检测详情:
${detailsText.substring(0, 500)}${detailsText.length > 500 ? '...' : ''}

（报告生成时间: ${new Date().toLocaleString()}）
            `.trim();
            
            // 模拟复制到剪贴板
            console.log('复制的报告内容:', report);
            showNotification('检测报告已复制到剪贴板', 'success');
            log('已复制检测报告', 'success');
        }
    </script>
</body>
</html> 