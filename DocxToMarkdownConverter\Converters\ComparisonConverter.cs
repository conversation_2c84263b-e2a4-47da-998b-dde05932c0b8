using System.Globalization;
using System.Windows.Data;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// Converts numeric values to boolean based on comparison with parameter
/// </summary>
public class ComparisonConverter : IValueConverter
{
    public static readonly ComparisonConverter Instance = new();

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is double doubleValue && parameter is string paramStr && double.TryParse(paramStr, out double threshold))
        {
            return doubleValue < threshold;
        }

        if (value is int intValue && parameter is string paramStr2 && int.TryParse(paramStr2, out int intThreshold))
        {
            return intValue < intThreshold;
        }

        return false;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        throw new NotImplementedException();
    }
}
