using System.ComponentModel;
using System.Runtime.CompilerServices;
using System.Text;
using System.Text.Json.Serialization;

namespace DocxToMarkdownConverter.Models;

public class ConversionOptions : INotifyPropertyChanged
{
    private string _outputDirectory = string.Empty;
    private bool _extractImages = true;
    private bool _convertTables = true;
    private bool _processFormulas = true;
    private ImageFormat _imageFormat = ImageFormat.Png;
    private string _imageDirectory = "images";
    private bool _preserveFormatting = true;
    private Encoding _outputEncoding = Encoding.UTF8;

    public string OutputDirectory
    {
        get => _outputDirectory;
        set => SetProperty(ref _outputDirectory, value);
    }

    public bool ExtractImages
    {
        get => _extractImages;
        set => SetProperty(ref _extractImages, value);
    }

    public bool ConvertTables
    {
        get => _convertTables;
        set => SetProperty(ref _convertTables, value);
    }

    public bool ProcessFormulas
    {
        get => _processFormulas;
        set => SetProperty(ref _processFormulas, value);
    }

    public ImageFormat ImageFormat
    {
        get => _imageFormat;
        set => SetProperty(ref _imageFormat, value);
    }

    public string ImageDirectory
    {
        get => _imageDirectory;
        set => SetProperty(ref _imageDirectory, value);
    }

    public bool PreserveFormatting
    {
        get => _preserveFormatting;
        set => SetProperty(ref _preserveFormatting, value);
    }

    [JsonIgnore]
    public Encoding OutputEncoding
    {
        get => _outputEncoding;
        set => SetProperty(ref _outputEncoding, value);
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}

public enum ImageFormat
{
    Png,
    Jpg,
    Gif,
    Bmp
}