using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Commands;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Windows.Data;
using System.Windows.Input;

namespace DocxToMarkdownConverter.ViewModels;

/// <summary>
/// ViewModel for displaying conversion results and providing file operations
/// </summary>
public class ResultsViewModel : ViewModelBase
{
    private ConversionResult? _selectedResult;
    private string _searchText = string.Empty;
    private string _statusFilter = "All";
    private readonly ICollectionView _resultsView;

    public ResultsViewModel()
    {
        Results = new ObservableCollection<ConversionResult>();
        
        // Create collection view for filtering and sorting
        _resultsView = CollectionViewSource.GetDefaultView(Results);
        _resultsView.Filter = FilterResults;
        _resultsView.SortDescriptions.Add(new SortDescription(nameof(ConversionResult.EndTime), ListSortDirection.Descending));
        
        InitializeCommands();
        SubscribeToEvents();
    }

    #region Properties

    /// <summary>
    /// Collection of conversion results
    /// </summary>
    public ObservableCollection<ConversionResult> Results { get; }

    /// <summary>
    /// Filtered view of results for display
    /// </summary>
    public ICollectionView ResultsView => _resultsView;

    /// <summary>
    /// Currently selected conversion result
    /// </summary>
    public ConversionResult? SelectedResult
    {
        get => _selectedResult;
        set => SetProperty(ref _selectedResult, value);
    }

    /// <summary>
    /// Search text for filtering results
    /// </summary>
    public string SearchText
    {
        get => _searchText;
        set
        {
            if (SetProperty(ref _searchText, value))
            {
                _resultsView.Refresh();
            }
        }
    }

    /// <summary>
    /// Status filter for results (All, Success, Failed)
    /// </summary>
    public string StatusFilter
    {
        get => _statusFilter;
        set
        {
            if (SetProperty(ref _statusFilter, value))
            {
                _resultsView.Refresh();
            }
        }
    }

    /// <summary>
    /// Total number of results
    /// </summary>
    public int TotalResults => Results.Count;

    /// <summary>
    /// Number of successful conversions
    /// </summary>
    public int SuccessfulResults => Results.Count(r => r.IsSuccess);

    /// <summary>
    /// Number of failed conversions
    /// </summary>
    public int FailedResults => Results.Count(r => !r.IsSuccess);

    /// <summary>
    /// Success rate percentage
    /// </summary>
    public double SuccessRate => TotalResults > 0 ? (double)SuccessfulResults / TotalResults * 100 : 0;

    /// <summary>
    /// Total processing time for all conversions
    /// </summary>
    public TimeSpan TotalProcessingTime => TimeSpan.FromTicks(Results.Sum(r => r.Duration.Ticks));

    /// <summary>
    /// Average processing time per file
    /// </summary>
    public TimeSpan AverageProcessingTime => TotalResults > 0 
        ? TimeSpan.FromTicks(TotalProcessingTime.Ticks / TotalResults) 
        : TimeSpan.Zero;

    /// <summary>
    /// Total input size in bytes
    /// </summary>
    public long TotalInputSize => Results.Sum(r => r.InputSize);

    /// <summary>
    /// Total output size in bytes
    /// </summary>
    public long TotalOutputSize => Results.Sum(r => r.OutputSize);

    /// <summary>
    /// Total number of images processed
    /// </summary>
    public int TotalImagesProcessed => Results.Sum(r => r.ImageCount);

    /// <summary>
    /// Total number of tables processed
    /// </summary>
    public int TotalTablesProcessed => Results.Sum(r => r.TableCount);

    /// <summary>
    /// Total number of formulas processed
    /// </summary>
    public int TotalFormulasProcessed => Results.Sum(r => r.FormulaCount);

    #endregion

    #region Commands

    public ICommand OpenOutputFileCommand { get; private set; } = null!;
    public ICommand OpenOutputDirectoryCommand { get; private set; } = null!;
    public ICommand OpenInputFileCommand { get; private set; } = null!;
    public ICommand OpenInputDirectoryCommand { get; private set; } = null!;
    public ICommand CopyOutputPathCommand { get; private set; } = null!;
    public ICommand RetryConversionCommand { get; private set; } = null!;
    public ICommand RemoveResultCommand { get; private set; } = null!;
    public ICommand ClearAllResultsCommand { get; private set; } = null!;
    public ICommand ExportResultsCommand { get; private set; } = null!;
    public ICommand RefreshResultsCommand { get; private set; } = null!;

    #endregion

    #region Initialization

    private void InitializeCommands()
    {
        OpenOutputFileCommand = CreateCommand<ConversionResult>(ExecuteOpenOutputFile, CanOpenOutputFile);
        OpenOutputDirectoryCommand = CreateCommand<ConversionResult>(ExecuteOpenOutputDirectory, CanOpenOutputDirectory);
        OpenInputFileCommand = CreateCommand<ConversionResult>(ExecuteOpenInputFile, CanOpenInputFile);
        OpenInputDirectoryCommand = CreateCommand<ConversionResult>(ExecuteOpenInputDirectory, CanOpenInputDirectory);
        CopyOutputPathCommand = CreateCommand<ConversionResult>(ExecuteCopyOutputPath, CanCopyOutputPath);
        RetryConversionCommand = CreateCommand<ConversionResult>(ExecuteRetryConversion, CanRetryConversion);
        RemoveResultCommand = CreateCommand<ConversionResult>(ExecuteRemoveResult, CanRemoveResult);
        ClearAllResultsCommand = CreateCommand(ExecuteClearAllResults, CanClearAllResults);
        ExportResultsCommand = CreateAsyncCommand(ExecuteExportResultsAsync, CanExportResults);
        RefreshResultsCommand = CreateCommand(ExecuteRefreshResults);
    }

    private void SubscribeToEvents()
    {
        Results.CollectionChanged += (s, e) =>
        {
            // Update statistics when results change
            RaisePropertyChanged(nameof(TotalResults));
            RaisePropertyChanged(nameof(SuccessfulResults));
            RaisePropertyChanged(nameof(FailedResults));
            RaisePropertyChanged(nameof(SuccessRate));
            RaisePropertyChanged(nameof(TotalProcessingTime));
            RaisePropertyChanged(nameof(AverageProcessingTime));
            RaisePropertyChanged(nameof(TotalInputSize));
            RaisePropertyChanged(nameof(TotalOutputSize));
            RaisePropertyChanged(nameof(TotalImagesProcessed));
            RaisePropertyChanged(nameof(TotalTablesProcessed));
            RaisePropertyChanged(nameof(TotalFormulasProcessed));
            
            // Refresh command states
            System.Windows.Input.CommandManager.InvalidateRequerySuggested();
        };
    }

    #endregion

    #region Command Implementations

    private void ExecuteOpenOutputFile(ConversionResult? result)
    {
        if (result?.IsSuccess == true && File.Exists(result.OutputPath))
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = result.OutputPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                ShowError($"Failed to open output file: {ex.Message}");
            }
        }
    }

    private bool CanOpenOutputFile(ConversionResult? result)
    {
        return result?.IsSuccess == true && File.Exists(result.OutputPath);
    }

    private void ExecuteOpenOutputDirectory(ConversionResult? result)
    {
        if (result?.IsSuccess == true && !string.IsNullOrEmpty(result.OutputPath))
        {
            try
            {
                var directory = Path.GetDirectoryName(result.OutputPath);
                if (!string.IsNullOrEmpty(directory) && Directory.Exists(directory))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = directory,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                ShowError($"Failed to open output directory: {ex.Message}");
            }
        }
    }

    private bool CanOpenOutputDirectory(ConversionResult? result)
    {
        if (result?.IsSuccess != true || string.IsNullOrEmpty(result.OutputPath))
            return false;
            
        var directory = Path.GetDirectoryName(result.OutputPath);
        return !string.IsNullOrEmpty(directory) && Directory.Exists(directory);
    }

    private void ExecuteOpenInputFile(ConversionResult? result)
    {
        if (result != null && File.Exists(result.InputPath))
        {
            try
            {
                Process.Start(new ProcessStartInfo
                {
                    FileName = result.InputPath,
                    UseShellExecute = true
                });
            }
            catch (Exception ex)
            {
                ShowError($"Failed to open input file: {ex.Message}");
            }
        }
    }

    private bool CanOpenInputFile(ConversionResult? result)
    {
        return result != null && File.Exists(result.InputPath);
    }

    private void ExecuteOpenInputDirectory(ConversionResult? result)
    {
        if (result != null && !string.IsNullOrEmpty(result.InputPath))
        {
            try
            {
                var directory = Path.GetDirectoryName(result.InputPath);
                if (!string.IsNullOrEmpty(directory) && Directory.Exists(directory))
                {
                    Process.Start(new ProcessStartInfo
                    {
                        FileName = directory,
                        UseShellExecute = true
                    });
                }
            }
            catch (Exception ex)
            {
                ShowError($"Failed to open input directory: {ex.Message}");
            }
        }
    }

    private bool CanOpenInputDirectory(ConversionResult? result)
    {
        if (result == null || string.IsNullOrEmpty(result.InputPath))
            return false;
            
        var directory = Path.GetDirectoryName(result.InputPath);
        return !string.IsNullOrEmpty(directory) && Directory.Exists(directory);
    }

    private void ExecuteCopyOutputPath(ConversionResult? result)
    {
        if (result?.IsSuccess == true && !string.IsNullOrEmpty(result.OutputPath))
        {
            try
            {
                System.Windows.Clipboard.SetText(result.OutputPath);
            }
            catch (Exception ex)
            {
                ShowError($"Failed to copy path to clipboard: {ex.Message}");
            }
        }
    }

    private bool CanCopyOutputPath(ConversionResult? result)
    {
        return result?.IsSuccess == true && !string.IsNullOrEmpty(result.OutputPath);
    }

    private void ExecuteRetryConversion(ConversionResult? result)
    {
        if (result != null)
        {
            // TODO: Implement retry logic - this would need to communicate with the main conversion service
            // For now, just show a message
            System.Windows.MessageBox.Show("Retry functionality will be implemented when integrated with the conversion service.", 
                "Retry Conversion", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
        }
    }

    private bool CanRetryConversion(ConversionResult? result)
    {
        return result?.IsSuccess == false && File.Exists(result.InputPath);
    }

    private void ExecuteRemoveResult(ConversionResult? result)
    {
        if (result != null && Results.Contains(result))
        {
            Results.Remove(result);
        }
    }

    private bool CanRemoveResult(ConversionResult? result)
    {
        return result != null && Results.Contains(result);
    }

    private void ExecuteClearAllResults()
    {
        var result = System.Windows.MessageBox.Show(
            "Are you sure you want to clear all conversion results?",
            "Clear Results",
            System.Windows.MessageBoxButton.YesNo,
            System.Windows.MessageBoxImage.Question);

        if (result == System.Windows.MessageBoxResult.Yes)
        {
            Results.Clear();
        }
    }

    private bool CanClearAllResults()
    {
        return Results.Count > 0;
    }

    private async Task ExecuteExportResultsAsync()
    {
        try
        {
            var saveFileDialog = new Microsoft.Win32.SaveFileDialog
            {
                Title = "Export Results",
                Filter = "CSV Files (*.csv)|*.csv|Text Files (*.txt)|*.txt|All Files (*.*)|*.*",
                DefaultExt = "csv",
                FileName = $"ConversionResults_{DateTime.Now:yyyyMMdd_HHmmss}"
            };

            if (saveFileDialog.ShowDialog() == true)
            {
                await ExportResultsToFileAsync(saveFileDialog.FileName);
                System.Windows.MessageBox.Show($"Results exported successfully to:\n{saveFileDialog.FileName}",
                    "Export Complete", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Information);
            }
        }
        catch (Exception ex)
        {
            ShowError($"Failed to export results: {ex.Message}");
        }
    }

    private bool CanExportResults()
    {
        return Results.Count > 0;
    }

    private void ExecuteRefreshResults()
    {
        _resultsView.Refresh();
    }

    #endregion

    #region Helper Methods

    private bool FilterResults(object obj)
    {
        if (obj is not ConversionResult result)
            return false;

        // Apply status filter
        if (StatusFilter != "All")
        {
            var isSuccess = StatusFilter == "Success";
            if (result.IsSuccess != isSuccess)
                return false;
        }

        // Apply search filter
        if (!string.IsNullOrEmpty(SearchText))
        {
            var searchLower = SearchText.ToLower();
            return result.InputPath.ToLower().Contains(searchLower) ||
                   result.OutputPath.ToLower().Contains(searchLower) ||
                   (result.ErrorMessage?.ToLower().Contains(searchLower) == true);
        }

        return true;
    }

    private async Task ExportResultsToFileAsync(string filePath)
    {
        var extension = Path.GetExtension(filePath).ToLower();
        
        if (extension == ".csv")
        {
            await ExportToCsvAsync(filePath);
        }
        else
        {
            await ExportToTextAsync(filePath);
        }
    }

    private async Task ExportToCsvAsync(string filePath)
    {
        using var writer = new StreamWriter(filePath);
        
        // Write header
        await writer.WriteLineAsync("Input File,Output File,Success,Duration (ms),Input Size (bytes),Output Size (bytes),Images,Tables,Formulas,Start Time,End Time,Error Message");
        
        // Write data
        foreach (var result in Results)
        {
            var line = $"\"{result.InputPath}\",\"{result.OutputPath}\",{result.IsSuccess},{result.Duration.TotalMilliseconds},{result.InputSize},{result.OutputSize},{result.ImageCount},{result.TableCount},{result.FormulaCount},\"{result.StartTime:yyyy-MM-dd HH:mm:ss}\",\"{result.EndTime:yyyy-MM-dd HH:mm:ss}\",\"{result.ErrorMessage?.Replace("\"", "\"\"")}\"";
            await writer.WriteLineAsync(line);
        }
    }

    private async Task ExportToTextAsync(string filePath)
    {
        using var writer = new StreamWriter(filePath);
        
        await writer.WriteLineAsync("DOCX to Markdown Conversion Results");
        await writer.WriteLineAsync("=====================================");
        await writer.WriteLineAsync($"Generated: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        await writer.WriteLineAsync();
        
        await writer.WriteLineAsync("Summary:");
        await writer.WriteLineAsync($"  Total Results: {TotalResults}");
        await writer.WriteLineAsync($"  Successful: {SuccessfulResults}");
        await writer.WriteLineAsync($"  Failed: {FailedResults}");
        await writer.WriteLineAsync($"  Success Rate: {SuccessRate:F1}%");
        await writer.WriteLineAsync($"  Total Processing Time: {TotalProcessingTime}");
        await writer.WriteLineAsync($"  Average Processing Time: {AverageProcessingTime}");
        await writer.WriteLineAsync();
        
        await writer.WriteLineAsync("Detailed Results:");
        await writer.WriteLineAsync("=================");
        
        foreach (var result in Results)
        {
            await writer.WriteLineAsync($"Input: {result.InputPath}");
            await writer.WriteLineAsync($"Output: {result.OutputPath}");
            await writer.WriteLineAsync($"Status: {(result.IsSuccess ? "Success" : "Failed")}");
            await writer.WriteLineAsync($"Duration: {result.Duration}");
            await writer.WriteLineAsync($"Input Size: {FormatFileSize(result.InputSize)}");
            await writer.WriteLineAsync($"Output Size: {FormatFileSize(result.OutputSize)}");
            await writer.WriteLineAsync($"Images: {result.ImageCount}, Tables: {result.TableCount}, Formulas: {result.FormulaCount}");
            await writer.WriteLineAsync($"Start Time: {result.StartTime:yyyy-MM-dd HH:mm:ss}");
            await writer.WriteLineAsync($"End Time: {result.EndTime:yyyy-MM-dd HH:mm:ss}");
            
            if (!string.IsNullOrEmpty(result.ErrorMessage))
            {
                await writer.WriteLineAsync($"Error: {result.ErrorMessage}");
            }
            
            await writer.WriteLineAsync();
        }
    }

    private void ShowError(string message)
    {
        System.Windows.MessageBox.Show(message, "Error", 
            System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
    }

    private static string FormatFileSize(long bytes)
    {
        string[] sizes = { "B", "KB", "MB", "GB" };
        double len = bytes;
        int order = 0;
        while (len >= 1024 && order < sizes.Length - 1)
        {
            order++;
            len = len / 1024;
        }
        return $"{len:0.##} {sizes[order]}";
    }

    #endregion

    #region Public Methods

    /// <summary>
    /// Add a conversion result to the collection
    /// </summary>
    public void AddResult(ConversionResult result)
    {
        if (result != null)
        {
            Results.Add(result);
        }
    }

    /// <summary>
    /// Add multiple conversion results to the collection
    /// </summary>
    public void AddResults(IEnumerable<ConversionResult> results)
    {
        foreach (var result in results)
        {
            AddResult(result);
        }
    }

    /// <summary>
    /// Clear all results
    /// </summary>
    public void ClearResults()
    {
        Results.Clear();
    }

    #endregion
}