using System.Collections.ObjectModel;
using System.Windows.Input;
using System.Windows;
using DocxToMarkdownConverter.Models;
using DocxToMarkdownConverter.Services;
using DocxToMarkdownConverter.Commands;
using DocxToMarkdownConverter.Views;
using DocxToMarkdownConverter.Controls;
using System.IO;
using System.Diagnostics;
using System.Linq;
using Microsoft.Extensions.DependencyInjection;
using OpenFileDialog = Microsoft.Win32.OpenFileDialog;

namespace DocxToMarkdownConverter.ViewModels;

public class MainWindowViewModel : ViewModelBase
{
    private readonly INavigationService _navigationService;
    private readonly IThemeManager _themeManager;
    private readonly IConfigurationService _configurationService;
    private readonly ILocalizationService _localizationService;
    private readonly ILoggingService? _loggingService;
    private readonly IServiceProvider? _serviceProvider;
    private ConversionOptions _options = new();
    private ConversionStatistics _statistics = new();
    private ApplicationSettings _applicationSettings = new();
    private readonly ResultsViewModel _resultsViewModel;
    private readonly System.Threading.Timer _autoSaveTimer;
    private bool _settingsChanged = false;
    private bool _isLoadingSettings = false;

    public MainWindowViewModel(
        INavigationService navigationService,
        IThemeManager themeManager,
        IConfigurationService configurationService,
        ILocalizationService localizationService,
        IServiceProvider serviceProvider,
        ILoggingService? loggingService = null)
    {
        System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 开始构造函数");
        try
        {
            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 设置服务依赖");
            _navigationService = navigationService;
            _themeManager = themeManager;
            _configurationService = configurationService;
            _localizationService = localizationService;
            _serviceProvider = serviceProvider;
            _loggingService = loggingService;

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 初始化集合");
            Files = new ObservableCollection<ConversionFileItem>();
            _resultsViewModel = new ResultsViewModel();

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 初始化自动保存定时器");
            // Initialize auto-save timer (save every 30 seconds if settings changed)
            _autoSaveTimer = new System.Threading.Timer(AutoSaveSettings, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 初始化命令");
            InitializeCommands();

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 加载设置");
            LoadSettings();

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 订阅事件");
            SubscribeToEvents();

            System.Diagnostics.Debug.WriteLine("MainWindowViewModel: 构造函数完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"MainWindowViewModel: 构造函数中发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"MainWindowViewModel: 异常堆栈: {ex.StackTrace}");
            throw;
        }
    }

    #region Properties

    public ObservableCollection<ConversionFileItem> Files { get; }

    public ConversionOptions Options
    {
        get => _options;
        set
        {
            if (SetProperty(ref _options, value))
            {
                OnSettingsChanged();
                UpdateApplicationSettings();
            }
        }
    }

    public ConversionStatistics Statistics
    {
        get => _statistics;
        set
        {
            if (SetProperty(ref _statistics, value))
            {
                OnSettingsChanged();
                UpdateApplicationSettings();
            }
        }
    }

    public ApplicationSettings ApplicationSettings
    {
        get => _applicationSettings;
        set => SetProperty(ref _applicationSettings, value);
    }

    public ResultsViewModel ResultsViewModel => _resultsViewModel;

    #endregion

    #region Commands

    public RelayCommand AddFilesCommand { get; private set; } = null!;
    public RelayCommand<ConversionFileItem> RemoveFileCommand { get; private set; } = null!;
    public RelayCommand ClearFilesCommand { get; private set; } = null!;
    public AsyncRelayCommand StartConversionCommand { get; private set; } = null!;
    public RelayCommand PauseConversionCommand { get; private set; } = null!;
    public RelayCommand OpenOutputDirectoryCommand { get; private set; } = null!;
    public AsyncRelayCommand ToggleThemeCommand { get; private set; } = null!;
    public AsyncRelayCommand NavigateToFilesCommand { get; private set; } = null!;
    public AsyncRelayCommand NavigateToSettingsCommand { get; private set; } = null!;
    public AsyncRelayCommand NavigateToProgressCommand { get; private set; } = null!;
    public AsyncRelayCommand NavigateToResultsCommand { get; private set; } = null!;

    // 键盘快捷键命令
    public RelayCommand OpenFileCommand { get; private set; } = null!;
    public RelayCommand<object> NavigateToPageCommand { get; private set; } = null!;
    public RelayCommand CancelOperationCommand { get; private set; } = null!;

    #endregion

    #region Initialization

    private void InitializeCommands()
    {
        AddFilesCommand = CreateCommand(ExecuteAddFiles);
        RemoveFileCommand = CreateCommand<ConversionFileItem>(ExecuteRemoveFile);
        ClearFilesCommand = CreateCommand(ExecuteClearFiles, CanClearFiles);
        StartConversionCommand = CreateAsyncCommand(ExecuteStartConversionAsync, CanStartConversion);
        PauseConversionCommand = CreateCommand(ExecutePauseConversion, CanPauseConversion);
        OpenOutputDirectoryCommand = CreateCommand(ExecuteOpenOutputDirectory, CanOpenOutputDirectory);
        ToggleThemeCommand = CreateAsyncCommand(ExecuteToggleThemeAsync);
        
        // Navigation commands
        NavigateToFilesCommand = CreateAsyncCommand(ExecuteNavigateToFilesAsync);
        NavigateToSettingsCommand = CreateAsyncCommand(ExecuteNavigateToSettingsAsync);
        NavigateToProgressCommand = CreateAsyncCommand(ExecuteNavigateToProgressAsync);
        NavigateToResultsCommand = CreateAsyncCommand(ExecuteNavigateToResultsAsync);

        // 键盘快捷键命令
        OpenFileCommand = CreateCommand(ExecuteAddFiles); // 复用添加文件功能
        NavigateToPageCommand = CreateCommand<object>(ExecuteNavigateToPage);
        CancelOperationCommand = CreateCommand(ExecuteCancelOperation);
    }

    private async void LoadSettings()
    {
        try
        {
            await ExecuteWithBusyAsync(async () =>
            {
                var settings = await _configurationService.LoadApplicationSettingsAsync();
                if (settings != null)
                {
                    ApplicationSettings = settings;
                    Options = settings.ConversionOptions ?? new ConversionOptions();
                    Statistics = settings.Statistics ?? new ConversionStatistics();

                    // Apply window state if configured
                    if (settings.RememberWindowState)
                    {
                        ApplyWindowSettings(settings);
                    }

                    // Subscribe to property changes for auto-save
                    SubscribeToSettingsChanges();
                }
            }, "Loading settings...");
        }
        catch (Exception ex)
        {
            // Log error but don't show to user during startup
            System.Diagnostics.Debug.WriteLine($"Failed to load settings: {ex.Message}");

            // Use default settings if loading fails
            ApplicationSettings = new ApplicationSettings();
            Options = new ConversionOptions();
            Statistics = new ConversionStatistics();
        }
    }

    private void SubscribeToEvents()
    {
        // Subscribe to theme changes
        _themeManager.ThemeChanged += OnThemeChanged;

        // Subscribe to configuration changes
        _configurationService.ConfigurationChanged += OnConfigurationChanged;

        // Subscribe to language changes
        _localizationService.LanguageChanged += OnLanguageChanged;

        // Subscribe to file collection changes
        Files.CollectionChanged += (s, e) =>
        {
            RaisePropertyChanged(nameof(Files));
            // Update command states
            System.Windows.Input.CommandManager.InvalidateRequerySuggested();
        };
    }

    #endregion

    #region Command Implementations

    private void ExecuteAddFiles()
    {
        var title = _localizationService.GetString("Dialog.SelectDocxFiles", "Select DOCX Files");
        var wordDocs = _localizationService.GetString("Dialog.WordDocuments", "Word Documents (*.docx)");
        var allFiles = _localizationService.GetString("Dialog.AllFiles", "All Files (*.*)");

        var openFileDialog = new OpenFileDialog
        {
            Title = title,
            Filter = $"{wordDocs}|*.docx|{allFiles}|*.*",
            Multiselect = true,
            CheckFileExists = true,
            CheckPathExists = true
        };

        if (openFileDialog.ShowDialog() == true)
        {
            AddFiles(openFileDialog.FileNames);
        }
    }

    private void ExecuteRemoveFile(ConversionFileItem? item)
    {
        if (item != null && Files.Contains(item))
        {
            Files.Remove(item);
            UpdateStatistics();
        }
    }

    private void ExecuteClearFiles()
    {
        Files.Clear();
        UpdateStatistics();
    }

    private bool CanClearFiles()
    {
        return Files.Count > 0 && !IsBusy;
    }

    private async Task ExecuteStartConversionAsync()
    {
        var conversionStartTime = DateTime.Now;
        var conversionId = Guid.NewGuid().ToString("N")[..8]; // 短ID用于跟踪

        await ExecuteWithBusyAsync(async () =>
        {
            try
            {
                // 记录转换开始的详细信息
                await LogConversionStart(conversionId, conversionStartTime);

                // Get the converter service
                var converter = _serviceProvider?.GetService<IDocxConverter>();
                if (converter == null)
                {
                    var errorMsg = "DocxConverter service not available from DI container";
                    await LogToFile($"[{conversionId}] CRITICAL ERROR: {errorMsg}");
                    _loggingService?.LogError(errorMsg);
                    await ShowConversionError("转换服务不可用", "无法获取转换服务，请重启应用程序");
                    return;
                }

                await LogToFile($"[{conversionId}] ✓ DocxConverter service obtained successfully");

                // Validate and setup output directory
                await ValidateAndSetupOutputDirectory(conversionId);

                // Convert each file
                var totalFiles = Files.Where(f => f.Status != ConversionStatus.Completed).Count();
                var processedFiles = 0;
                var successfulFiles = 0;
                var failedFiles = 0;

                await LogToFile($"[{conversionId}] Starting conversion of {totalFiles} files");

                foreach (var file in Files.Where(f => f.Status != ConversionStatus.Completed))
                {
                    processedFiles++;
                    var fileConversionId = $"{conversionId}-F{processedFiles}";

                    try
                    {
                        await LogToFile($"[{fileConversionId}] Starting conversion of: {file.FileName}");
                        await LogToFile($"[{fileConversionId}] Input path: {file.FilePath}");
                        await LogToFile($"[{fileConversionId}] File size: {file.FileSize} bytes");

                        // 确保状态更新在UI线程上执行
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            file.Status = ConversionStatus.Processing;
                            file.Progress = 0;
                        });

                        // Validate input file
                        if (!File.Exists(file.FilePath))
                        {
                            throw new FileNotFoundException($"Input file not found: {file.FilePath}");
                        }

                        // Generate output path
                        var fileName = Path.GetFileNameWithoutExtension(file.FileName);
                        var outputPath = Path.Combine(Options.OutputDirectory, $"{fileName}.md");
                        await LogToFile($"[{fileConversionId}] Output path: {outputPath}");

                        // Progress reporting with detailed logging and thread safety
                        var progress = new Progress<Models.ConversionProgress>(p =>
                        {
                            // 确保进度更新在UI线程上执行
                            System.Windows.Application.Current.Dispatcher.BeginInvoke(() =>
                            {
                                try
                                {
                                    file.Progress = (int)p.ProgressPercentage;
                                    _ = LogToFile($"[{fileConversionId}] Progress: {p.ProgressPercentage:F1}% - {p.CurrentOperation}");
                                }
                                catch (Exception progressEx)
                                {
                                    _ = LogToFile($"[{fileConversionId}] Progress update error: {progressEx.Message}");
                                }
                            });
                        });

                        // Perform the actual conversion
                        await LogToFile($"[{fileConversionId}] Calling DocxConverter.ConvertAsync...");

                        ConversionResult result;
                        try
                        {
                            // 添加超时控制，防止无限等待
                            using var cts = new CancellationTokenSource(TimeSpan.FromMinutes(10)); // 10分钟超时
                            result = await converter.ConvertAsync(file.FilePath, outputPath, Options, progress, cts.Token);
                            await LogToFile($"[{fileConversionId}] DocxConverter.ConvertAsync completed successfully");
                        }
                        catch (OperationCanceledException)
                        {
                            await LogToFile($"[{fileConversionId}] Conversion timed out after 10 minutes");
                            throw new TimeoutException("转换操作超时（10分钟），可能文件过大或存在问题");
                        }
                        catch (Exception conversionEx)
                        {
                            await LogToFile($"[{fileConversionId}] DocxConverter.ConvertAsync failed: {conversionEx.GetType().Name}: {conversionEx.Message}");
                            await LogToFile($"[{fileConversionId}] Conversion stack trace: {conversionEx.StackTrace}");
                            throw;
                        }

                        // Validate conversion result
                        await ValidateConversionResult(fileConversionId, file, result, outputPath);

                        if (result.IsSuccess)
                        {
                            successfulFiles++;
                            await LogToFile($"[{fileConversionId}] ✓ Conversion successful");
                        }
                        else
                        {
                            failedFiles++;
                            await LogToFile($"[{fileConversionId}] ✗ Conversion failed: {result.ErrorMessage}");
                        }
                    }
                    catch (Exception ex)
                    {
                        failedFiles++;
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            file.Status = ConversionStatus.Failed;
                            file.Progress = 0;
                        });

                        await LogToFile($"[{fileConversionId}] ✗ EXCEPTION during conversion:");
                        await LogToFile($"[{fileConversionId}] Exception Type: {ex.GetType().Name}");
                        await LogToFile($"[{fileConversionId}] Exception Message: {ex.Message}");
                        await LogToFile($"[{fileConversionId}] Stack Trace: {ex.StackTrace}");

                        if (ex.InnerException != null)
                        {
                            await LogToFile($"[{fileConversionId}] Inner Exception: {ex.InnerException.Message}");
                        }

                        _loggingService?.LogError($"Exception converting {file.FileName}: {ex.Message}");
                    }
                }

                // 记录转换完成的详细统计
                await LogConversionCompletion(conversionId, conversionStartTime, totalFiles, successfulFiles, failedFiles);

                UpdateStatistics();
            }
            catch (Exception ex)
            {
                await LogToFile($"[{conversionId}] CRITICAL ERROR in conversion process:");
                await LogToFile($"[{conversionId}] Exception Type: {ex.GetType().Name}");
                await LogToFile($"[{conversionId}] Exception Message: {ex.Message}");
                await LogToFile($"[{conversionId}] Stack Trace: {ex.StackTrace}");

                _loggingService?.LogError($"Critical error during conversion process: {ex.Message}");
                await ShowConversionError("转换过程出错", $"转换过程中发生严重错误: {ex.Message}");
            }
        }, "Converting files...");
    }

    private bool CanStartConversion()
    {
        return Files.Count > 0 && !IsBusy && Files.Any(f => f.Status != ConversionStatus.Completed);
    }

    private void ExecutePauseConversion()
    {
        // TODO: Implement conversion pause logic
    }

    private bool CanPauseConversion()
    {
        return IsBusy; // Can only pause when conversion is running
    }

    private void ExecuteOpenOutputDirectory()
    {
        try
        {
            var outputPath = Options.OutputDirectory;
            if (string.IsNullOrEmpty(outputPath))
            {
                outputPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "DOCX Converter Output");
            }

            if (!Directory.Exists(outputPath))
            {
                Directory.CreateDirectory(outputPath);
            }

            Process.Start(new ProcessStartInfo
            {
                FileName = outputPath,
                UseShellExecute = true,
                Verb = "open"
            });
        }
        catch (Exception ex)
        {
            System.Windows.MessageBox.Show($"Failed to open output directory: {ex.Message}", 
                "Error", System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        }
    }

    private bool CanOpenOutputDirectory()
    {
        return !string.IsNullOrEmpty(Options.OutputDirectory) || Statistics.TotalFilesProcessed > 0;
    }

    private async Task ExecuteToggleThemeAsync()
    {
        await ExecuteWithBusyAsync(async () =>
        {
            await _themeManager.ToggleThemeAsync();
        }, "Switching theme...");
    }

    // Navigation command implementations
    private async Task ExecuteNavigateToFilesAsync()
    {
        await _navigationService.NavigateToAsync<FileListView>();
    }

    private async Task ExecuteNavigateToSettingsAsync()
    {
        await _navigationService.NavigateToAsync<SettingsView>();
    }

    private async Task ExecuteNavigateToProgressAsync()
    {
        await _navigationService.NavigateToAsync<ProgressControl>();
    }

    private async Task ExecuteNavigateToResultsAsync()
    {
        await _navigationService.NavigateToAsync<ResultsView>();
    }

    #endregion

    #region Helper Methods

    public void AddFiles(IEnumerable<string> filePaths)
    {
        System.Diagnostics.Debug.WriteLine("AddFiles: 开始添加文件");
        // 启动异步处理，并处理可能的异常
        _ = Task.Run(async () =>
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("AddFiles: 调用 AddFilesAsync");
                await AddFilesAsync(filePaths);
                System.Diagnostics.Debug.WriteLine("AddFiles: AddFilesAsync 完成");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AddFiles: 发生异常 - {ex.Message}");
                // 在UI线程中显示错误
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    _loggingService?.LogError($"添加文件时发生错误: {ex.Message}");
                    // 这里可以添加用户通知，比如消息框或状态栏消息
                });
            }
        });
        System.Diagnostics.Debug.WriteLine("AddFiles: Task.Run 已启动");
    }

    public async Task AddFilesAsync(IEnumerable<string> filePaths)
    {
        System.Diagnostics.Debug.WriteLine("AddFilesAsync: 开始异步添加文件");
        try
        {
            System.Diagnostics.Debug.WriteLine("AddFilesAsync: 设置 IsBusy = true");

            // 确保在UI线程中设置IsBusy
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                IsBusy = true;
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => IsBusy = true);
            }

            var filePathsList = filePaths.ToList();
            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 处理 {filePathsList.Count} 个文件路径");

            // 获取现有文件路径
            HashSet<string> existingFilePaths;
            try
            {
                if (System.Windows.Application.Current.Dispatcher.CheckAccess())
                {
                    existingFilePaths = Files.Select(f => f.FilePath).ToHashSet();
                }
                else
                {
                    existingFilePaths = await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        Files.Select(f => f.FilePath).ToHashSet());
                }
                System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 获取到 {existingFilePaths.Count} 个现有文件路径");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 获取现有文件路径时发生异常: {ex.Message}");
                existingFilePaths = new HashSet<string>();
            }

            // 在后台线程处理文件验证和信息获取
            var validFiles = await Task.Run(() =>
            {
                System.Diagnostics.Debug.WriteLine("AddFilesAsync: 开始后台文件处理");
                var results = new List<ConversionFileItem>();

                foreach (var filePath in filePathsList)
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 处理文件 {filePath}");

                        // 检查文件是否已存在于列表中
                        if (existingFilePaths.Contains(filePath))
                        {
                            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 文件已存在，跳过 {filePath}");
                            continue;
                        }

                        // 验证文件
                        if (IsValidDocxFile(filePath))
                        {
                            var fileInfo = new FileInfo(filePath);
                            var fileItem = new ConversionFileItem
                            {
                                FilePath = filePath,
                                FileName = fileInfo.Name,
                                FileSize = fileInfo.Length,
                                Status = ConversionStatus.Pending,
                                Progress = 0
                            };

                            results.Add(fileItem);
                            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 成功添加文件 {filePath}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 文件验证失败 {filePath}");
                        }
                    }
                    catch (Exception ex)
                    {
                        // 记录错误但继续处理其他文件
                        System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 处理文件 {filePath} 时发生错误: {ex.Message}");
                        _loggingService?.LogError($"处理文件 {filePath} 时发生错误: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 后台处理完成，有效文件数: {results.Count}");
                return results;
            });

            // 确保在UI线程中更新UI
            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 调用 AddFilesToCollection，有效文件数: {validFiles.Count}");

            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                AddFilesToCollection(validFiles);
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => AddFilesToCollection(validFiles));
            }

            System.Diagnostics.Debug.WriteLine("AddFilesAsync: AddFilesToCollection 完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"AddFilesAsync: 发生异常 - {ex.Message}");
            _loggingService?.LogError($"添加文件时发生错误: {ex.Message}");
            throw;
        }
        finally
        {
            System.Diagnostics.Debug.WriteLine("AddFilesAsync: 设置 IsBusy = false");

            // 确保在UI线程中重置IsBusy
            if (System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                IsBusy = false;
            }
            else
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() => IsBusy = false);
            }
        }
    }

    /// <summary>
    /// 在UI线程中添加文件到集合（必须在UI线程中调用）
    /// </summary>
    private void AddFilesToCollection(List<ConversionFileItem> validFiles)
    {
        try
        {
            System.Diagnostics.Debug.WriteLine($"AddFilesToCollection: 开始添加 {validFiles.Count} 个文件");

            // 检查是否在UI线程中
            if (!System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                throw new InvalidOperationException("AddFilesToCollection 必须在UI线程中调用");
            }

            if (validFiles.Count > 0)
            {
                for (int i = 0; i < validFiles.Count; i++)
                {
                    try
                    {
                        var fileItem = validFiles[i];
                        System.Diagnostics.Debug.WriteLine($"AddFilesToCollection: 添加文件 {i + 1}/{validFiles.Count}: {fileItem.FileName}");
                        Files.Add(fileItem);
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"AddFilesToCollection: 添加单个文件时发生异常: {ex.Message}");
                        _loggingService?.LogError($"添加文件 {validFiles[i].FileName} 时发生错误: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine("AddFilesToCollection: 开始更新统计信息");
                UpdateStatistics();
                System.Diagnostics.Debug.WriteLine("AddFilesToCollection: 统计信息更新完成");

                _loggingService?.LogInfo($"成功添加 {validFiles.Count} 个文件到转换列表");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("AddFilesToCollection: 没有有效文件可添加");
                _loggingService?.LogWarning("没有找到有效的文件可以添加");
            }

            System.Diagnostics.Debug.WriteLine("AddFilesToCollection: 完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"AddFilesToCollection: 发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"AddFilesToCollection: 异常堆栈: {ex.StackTrace}");
            _loggingService?.LogError($"AddFilesToCollection 发生错误: {ex.Message}");
            throw; // 重新抛出异常，让调用者知道操作失败
        }
    }

    private bool IsValidDocxFile(string filePath)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(filePath))
                return false;

            // 检查扩展名
            if (!Path.GetExtension(filePath).Equals(".docx", StringComparison.OrdinalIgnoreCase))
                return false;

            // 检查文件是否存在（在后台线程中安全执行）
            return File.Exists(filePath);
        }
        catch (Exception ex)
        {
            _loggingService?.LogWarning($"验证文件 {filePath} 时发生错误: {ex.Message}");
            return false;
        }
    }

    private void UpdateStatistics()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("UpdateStatistics: 开始更新统计信息");

            // 检查是否在UI线程中
            if (!System.Windows.Application.Current.Dispatcher.CheckAccess())
            {
                System.Diagnostics.Debug.WriteLine("UpdateStatistics: 不在UI线程中，调度到UI线程");
                System.Windows.Application.Current.Dispatcher.Invoke(() => UpdateStatistics());
                return;
            }

            Statistics.TotalFilesProcessed = Files.Count;
            Statistics.SuccessfulConversions = Files.Count(f => f.Status == ConversionStatus.Completed);
            Statistics.FailedConversions = Files.Count(f => f.Status == ConversionStatus.Failed);
            Statistics.PendingConversions = Files.Count(f => f.Status == ConversionStatus.Pending);

            System.Diagnostics.Debug.WriteLine($"UpdateStatistics: 统计信息 - 总计:{Statistics.TotalFilesProcessed}, 成功:{Statistics.SuccessfulConversions}, 失败:{Statistics.FailedConversions}, 待处理:{Statistics.PendingConversions}");

            RaisePropertyChanged(nameof(Statistics));
            System.Diagnostics.Debug.WriteLine("UpdateStatistics: 完成");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"UpdateStatistics: 发生异常: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"UpdateStatistics: 异常堆栈: {ex.StackTrace}");
            _loggingService?.LogError($"更新统计信息时发生错误: {ex.Message}");
        }
    }

    private void OnThemeChanged(object? sender, ThemeChangedEventArgs e)
    {
        // Handle theme change if needed
        // The theme manager will handle the UI updates automatically
    }

    private void OnLanguageChanged(object? sender, LanguageChangedEventArgs e)
    {
        // Refresh localized texts in statistics
        Statistics.RefreshLocalizedTexts();
    }

    #endregion

    #region Cleanup

    protected override void OnDispose()
    {
        _themeManager.ThemeChanged -= OnThemeChanged;
        _configurationService.ConfigurationChanged -= OnConfigurationChanged;
        _localizationService.LanguageChanged -= OnLanguageChanged;

        // Save settings before disposing
        SaveSettingsSync();

        // Dispose auto-save timer
        _autoSaveTimer?.Dispose();

        base.OnDispose();
    }

    #endregion

    #region Configuration Management

    private void OnSettingsChanged()
    {
        _settingsChanged = true;
    }

    private void UpdateApplicationSettings()
    {
        if (ApplicationSettings != null)
        {
            ApplicationSettings.ConversionOptions = Options;
            ApplicationSettings.Statistics = Statistics;
        }
    }

    private void SubscribeToSettingsChanges()
    {
        if (Options != null)
        {
            Options.PropertyChanged += (s, e) => OnSettingsChanged();
        }

        if (Statistics != null)
        {
            Statistics.PropertyChanged += (s, e) => OnSettingsChanged();
        }

        if (ApplicationSettings?.AnimationSettings != null)
        {
            ApplicationSettings.AnimationSettings.PropertyChanged += (s, e) => OnSettingsChanged();
        }

        if (ApplicationSettings?.ThemeSettings != null)
        {
            ApplicationSettings.ThemeSettings.PropertyChanged += (s, e) => OnSettingsChanged();
        }
    }

    private void ApplyWindowSettings(ApplicationSettings settings)
    {
        // This would be called from the View to apply window settings
        // The actual implementation would be in the MainWindow code-behind
        RaisePropertyChanged(nameof(ApplicationSettings));
    }

    private async void AutoSaveSettings(object? state)
    {
        if (_settingsChanged && ApplicationSettings != null && !_isLoadingSettings)
        {
            try
            {
                _isLoadingSettings = true;
                try
                {
                    await _configurationService.SaveApplicationSettingsAsync(ApplicationSettings);
                    _settingsChanged = false;
                }
                finally
                {
                    _isLoadingSettings = false;
                }
            }
            catch (Exception ex)
            {
                _isLoadingSettings = false;
                System.Diagnostics.Debug.WriteLine($"Auto-save failed: {ex.Message}");
            }
        }
    }

    private void SaveSettingsSync()
    {
        if (_settingsChanged && ApplicationSettings != null)
        {
            try
            {
                // Synchronous save for disposal
                var task = _configurationService.SaveApplicationSettingsAsync(ApplicationSettings);
                task.Wait(TimeSpan.FromSeconds(5)); // Wait max 5 seconds
                _settingsChanged = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Sync save failed: {ex.Message}");
            }
        }
    }

    private async void OnConfigurationChanged(object? sender, ConfigurationChangedEventArgs e)
    {
        try
        {
            // Prevent infinite loop when we're already loading settings
            if (_isLoadingSettings) return;

            // Reload settings when configuration file changes externally
            if (e.SettingsType == typeof(ApplicationSettings))
            {
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(async () =>
                {
                    _isLoadingSettings = true;
                    try
                    {
                        var settings = await _configurationService.LoadApplicationSettingsAsync();
                        if (settings != null)
                        {
                            ApplicationSettings = settings;
                            Options = settings.ConversionOptions ?? new ConversionOptions();
                            Statistics = settings.Statistics ?? new ConversionStatistics();
                        }
                    }
                    finally
                    {
                        _isLoadingSettings = false;
                    }
                });
            }
        }
        catch (Exception ex)
        {
            _isLoadingSettings = false;
            System.Diagnostics.Debug.WriteLine($"Failed to handle configuration change: {ex.Message}");
        }
    }

    public async Task SaveSettingsAsync()
    {
        try
        {
            if (ApplicationSettings != null && !_isLoadingSettings)
            {
                _isLoadingSettings = true;
                try
                {
                    UpdateApplicationSettings();
                    await _configurationService.SaveApplicationSettingsAsync(ApplicationSettings);
                    _settingsChanged = false;
                }
                finally
                {
                    _isLoadingSettings = false;
                }
            }
        }
        catch (Exception ex)
        {
            _isLoadingSettings = false;
            System.Diagnostics.Debug.WriteLine($"Failed to save settings: {ex.Message}");
            throw;
        }
    }

    public async Task ResetSettingsAsync()
    {
        try
        {
            await _configurationService.ResetToDefaultsAsync();
            LoadSettings(); // Reload the default settings
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to reset settings: {ex.Message}");
            throw;
        }
    }

    public async Task<bool> ExportSettingsAsync(string filePath)
    {
        try
        {
            return await _configurationService.ExportSettingsAsync<ApplicationSettings>(filePath);
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to export settings: {ex.Message}");
            return false;
        }
    }

    public async Task<bool> ImportSettingsAsync(string filePath)
    {
        try
        {
            var importedSettings = await _configurationService.ImportSettingsAsync<ApplicationSettings>(filePath);
            if (importedSettings != null)
            {
                ApplicationSettings = importedSettings;
                Options = importedSettings.ConversionOptions ?? new ConversionOptions();
                Statistics = importedSettings.Statistics ?? new ConversionStatistics();
                await SaveSettingsAsync();
                return true;
            }
            return false;
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Failed to import settings: {ex.Message}");
            return false;
        }
    }

    #region 键盘快捷键命令实现

    private void ExecuteNavigateToPage(object? parameter)
    {
        if (parameter is string pageIndexStr && int.TryParse(pageIndexStr, out int pageIndex))
        {
            switch (pageIndex)
            {
                case 0:
                    _ = ExecuteNavigateToFilesAsync();
                    break;
                case 1:
                    _ = ExecuteNavigateToSettingsAsync();
                    break;
                case 2:
                    _ = ExecuteNavigateToProgressAsync();
                    break;
                case 3:
                    _ = ExecuteNavigateToResultsAsync();
                    break;
            }
        }
    }

    private void ExecuteCancelOperation()
    {
        // 如果正在转换，则暂停转换
        if (IsBusy)
        {
            ExecutePauseConversion();
        }

        // 可以添加其他取消操作的逻辑，比如关闭对话框等
        // 这里可以通过事件通知其他组件执行取消操作
    }

    #endregion

    #region 转换日志和验证方法

    private async Task LogToFile(string message)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"{timestamp} - {message}\n";
            await File.AppendAllTextAsync("conversion_debug.log", logMessage);
        }
        catch
        {
            // 静默处理日志写入失败，避免影响主流程
        }
    }

    private async Task LogConversionStart(string conversionId, DateTime startTime)
    {
        await LogToFile($"[{conversionId}] ==================== CONVERSION STARTED ====================");
        await LogToFile($"[{conversionId}] Start Time: {startTime:yyyy-MM-dd HH:mm:ss.fff}");
        await LogToFile($"[{conversionId}] Files to convert: {Files.Count}");
        await LogToFile($"[{conversionId}] Output Directory: {Options.OutputDirectory}");
        await LogToFile($"[{conversionId}] Extract Images: {Options.ExtractImages}");
        await LogToFile($"[{conversionId}] Convert Tables: {Options.ConvertTables}");
        await LogToFile($"[{conversionId}] Process Formulas: {Options.ProcessFormulas}");
        await LogToFile($"[{conversionId}] Preserve Formatting: {Options.PreserveFormatting}");

        foreach (var file in Files)
        {
            await LogToFile($"[{conversionId}] File: {file.FileName} ({file.FileSize} bytes) - Status: {file.Status}");
        }
    }

    private async Task ValidateAndSetupOutputDirectory(string conversionId)
    {
        await LogToFile($"[{conversionId}] Validating output directory setup...");

        // Ensure output directory is set
        if (string.IsNullOrEmpty(Options.OutputDirectory))
        {
            Options.OutputDirectory = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.MyDocuments), "ConvertedMarkdown");
            await LogToFile($"[{conversionId}] Output directory was empty, set to default: {Options.OutputDirectory}");
        }

        await LogToFile($"[{conversionId}] Target output directory: {Options.OutputDirectory}");

        // Check if directory exists
        if (!Directory.Exists(Options.OutputDirectory))
        {
            await LogToFile($"[{conversionId}] Output directory does not exist, creating...");
            try
            {
                Directory.CreateDirectory(Options.OutputDirectory);
                await LogToFile($"[{conversionId}] ✓ Output directory created successfully");
            }
            catch (Exception ex)
            {
                await LogToFile($"[{conversionId}] ✗ Failed to create output directory: {ex.Message}");
                throw new InvalidOperationException($"无法创建输出目录: {ex.Message}", ex);
            }
        }
        else
        {
            await LogToFile($"[{conversionId}] ✓ Output directory already exists");
        }

        // Test write permissions
        var testFile = Path.Combine(Options.OutputDirectory, $"test_write_{Guid.NewGuid():N}.tmp");
        try
        {
            await File.WriteAllTextAsync(testFile, "test");
            File.Delete(testFile);
            await LogToFile($"[{conversionId}] ✓ Write permissions verified");
        }
        catch (Exception ex)
        {
            await LogToFile($"[{conversionId}] ✗ Write permission test failed: {ex.Message}");
            throw new UnauthorizedAccessException($"输出目录没有写入权限: {ex.Message}", ex);
        }
    }

    private async Task ValidateConversionResult(string fileConversionId, ConversionFileItem file, ConversionResult result, string outputPath)
    {
        await LogToFile($"[{fileConversionId}] Validating conversion result...");
        await LogToFile($"[{fileConversionId}] Result.IsSuccess: {result.IsSuccess}");
        await LogToFile($"[{fileConversionId}] Result.ErrorMessage: {result.ErrorMessage ?? "null"}");
        await LogToFile($"[{fileConversionId}] Result.Duration: {result.Duration}");
        await LogToFile($"[{fileConversionId}] Result.InputSize: {result.InputSize}");
        await LogToFile($"[{fileConversionId}] Result.OutputSize: {result.OutputSize}");

        if (result.IsSuccess)
        {
            // 验证输出文件是否真正存在
            if (File.Exists(outputPath))
            {
                var fileInfo = new FileInfo(outputPath);
                await LogToFile($"[{fileConversionId}] ✓ Output file exists: {outputPath}");
                await LogToFile($"[{fileConversionId}] ✓ Output file size: {fileInfo.Length} bytes");
                await LogToFile($"[{fileConversionId}] ✓ Output file created: {fileInfo.CreationTime:yyyy-MM-dd HH:mm:ss}");
                await LogToFile($"[{fileConversionId}] ✓ Output file modified: {fileInfo.LastWriteTime:yyyy-MM-dd HH:mm:ss}");

                if (fileInfo.Length == 0)
                {
                    await LogToFile($"[{fileConversionId}] ⚠️ WARNING: Output file is empty!");
                    await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                    {
                        file.Status = ConversionStatus.Failed;
                        file.Progress = 0;
                    });
                    result.IsSuccess = false;
                    result.ErrorMessage = "输出文件为空";
                }
                else
                {
                    // 读取并验证文件内容
                    try
                    {
                        var content = await File.ReadAllTextAsync(outputPath);
                        var lines = content.Split('\n').Length;
                        var chars = content.Length;
                        await LogToFile($"[{fileConversionId}] ✓ Content validation: {lines} lines, {chars} characters");

                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            file.Status = ConversionStatus.Completed;
                            file.Progress = 100;
                        });
                        _loggingService?.LogInfo($"Successfully converted: {file.FileName} -> {fileInfo.Length} bytes");
                    }
                    catch (Exception ex)
                    {
                        await LogToFile($"[{fileConversionId}] ✗ Failed to read output file: {ex.Message}");
                        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                        {
                            file.Status = ConversionStatus.Failed;
                            file.Progress = 0;
                        });
                        result.IsSuccess = false;
                        result.ErrorMessage = $"无法读取输出文件: {ex.Message}";
                    }
                }
            }
            else
            {
                await LogToFile($"[{fileConversionId}] ✗ CRITICAL: Output file does not exist despite success status!");
                await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
                {
                    file.Status = ConversionStatus.Failed;
                    file.Progress = 0;
                });
                result.IsSuccess = false;
                result.ErrorMessage = "转换报告成功但输出文件不存在";
                _loggingService?.LogError($"Conversion reported success but output file missing: {outputPath}");
            }
        }
        else
        {
            await LogToFile($"[{fileConversionId}] ✗ Conversion failed as reported");
            await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
            {
                file.Status = ConversionStatus.Failed;
                file.Progress = 0;
            });
            _loggingService?.LogError($"Failed to convert {file.FileName}: {result.ErrorMessage}");
        }
    }

    private async Task LogConversionCompletion(string conversionId, DateTime startTime, int totalFiles, int successfulFiles, int failedFiles)
    {
        var endTime = DateTime.Now;
        var duration = endTime - startTime;

        await LogToFile($"[{conversionId}] ==================== CONVERSION COMPLETED ====================");
        await LogToFile($"[{conversionId}] End Time: {endTime:yyyy-MM-dd HH:mm:ss.fff}");
        await LogToFile($"[{conversionId}] Total Duration: {duration.TotalSeconds:F2} seconds");
        await LogToFile($"[{conversionId}] Total Files: {totalFiles}");
        await LogToFile($"[{conversionId}] Successful: {successfulFiles}");
        await LogToFile($"[{conversionId}] Failed: {failedFiles}");
        await LogToFile($"[{conversionId}] Success Rate: {(totalFiles > 0 ? (successfulFiles * 100.0 / totalFiles):0):F1}%");

        _loggingService?.LogInfo($"Conversion completed: {successfulFiles}/{totalFiles} files successful in {duration.TotalSeconds:F2}s");
    }

    private async Task ShowConversionError(string title, string message)
    {
        await System.Windows.Application.Current.Dispatcher.InvokeAsync(() =>
        {
            System.Windows.MessageBox.Show(message, title, System.Windows.MessageBoxButton.OK, System.Windows.MessageBoxImage.Error);
        });
    }

    #endregion

    #endregion
}