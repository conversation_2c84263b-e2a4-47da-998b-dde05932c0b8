using Microsoft.Extensions.Logging;
using DocumentFormat.OpenXml.Math;
using DocumentFormat.OpenXml.Wordprocessing;
using DocxToMarkdownConverter.Services.Formula.Core;
using DocxToMarkdownConverter.Services.Formula.Models;

namespace DocxToMarkdownConverter.Services.Formula.Implementation;

/// <summary>
/// 公式提取器实现
/// </summary>
public class FormulaExtractor : IFormulaExtractor
{
    private readonly ILogger<FormulaExtractor> _logger;
    private readonly IFormulaDetector _detector;

    public FormulaExtractor(
        ILogger<FormulaExtractor> logger,
        IFormulaDetector detector)
    {
        _logger = logger;
        _detector = detector;
    }

    /// <summary>
    /// 从容器中提取所有公式
    /// </summary>
    public async Task<IEnumerable<FormulaElement>> ExtractAsync(object container)
    {
        var formulas = new List<FormulaElement>();

        try
        {
            _logger.LogDebug("Extracting formulas from container: {ContainerType}", container?.GetType().Name);

            switch (container)
            {
                case Paragraph paragraph:
                    formulas.AddRange(await ExtractFromParagraphAsync(paragraph));
                    break;
                    
                case OfficeMath officeMath:
                    var element = await ExtractSingleAsync(officeMath);
                    if (element != null)
                    {
                        formulas.Add(element);
                    }
                    break;
                    
                case DocumentFormat.OpenXml.OpenXmlElement xmlElement:
                    formulas.AddRange(await ExtractFromXmlElementAsync(xmlElement));
                    break;
                    
                default:
                    _logger.LogWarning("Unsupported container type: {ContainerType}", container?.GetType().Name);
                    break;
            }

            _logger.LogDebug("Extracted {FormulaCount} formulas from container", formulas.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting formulas from container");
        }

        return formulas;
    }

    /// <summary>
    /// 提取单个公式元素
    /// </summary>
    public async Task<FormulaElement> ExtractSingleAsync(object element)
    {
        try
        {
            _logger.LogDebug("Extracting single formula element: {ElementType}", element?.GetType().Name);

            var formulaType = _detector.GetFormulaType(element);
            var metadata = await CreateMetadataAsync(element);
            var location = CreateLocation(element);

            var formulaElement = new FormulaElement
            {
                Type = formulaType,
                SourceElement = element,
                Metadata = metadata,
                Location = location
            };

            _logger.LogDebug("Extracted formula element with type: {FormulaType}", formulaType);
            return formulaElement;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting single formula element");
            throw;
        }
    }

    #region Private Methods

    private async Task<IEnumerable<FormulaElement>> ExtractFromParagraphAsync(Paragraph paragraph)
    {
        var formulas = new List<FormulaElement>();

        try
        {
            // 提取OfficeMath元素
            var officeMathElements = paragraph.Descendants<OfficeMath>();
            foreach (var officeMath in officeMathElements)
            {
                var element = await ExtractSingleAsync(officeMath);
                formulas.Add(element);
            }

            // 提取其他数学元素
            var mathElements = paragraph.Descendants().Where(IsMathElement);
            foreach (var mathElement in mathElements)
            {
                // 避免重复提取已经作为OfficeMath子元素的元素
                if (!IsChildOfOfficeMath(mathElement))
                {
                    var element = await ExtractSingleAsync(mathElement);
                    formulas.Add(element);
                }
            }

            _logger.LogDebug("Extracted {FormulaCount} formulas from paragraph", formulas.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting formulas from paragraph");
        }

        return formulas;
    }

    private async Task<IEnumerable<FormulaElement>> ExtractFromXmlElementAsync(DocumentFormat.OpenXml.OpenXmlElement xmlElement)
    {
        var formulas = new List<FormulaElement>();

        try
        {
            // 检查元素本身是否是公式
            if (await _detector.ContainsFormulasAsync(xmlElement))
            {
                var formulaType = _detector.GetFormulaType(xmlElement);
                if (formulaType != FormulaType.Unknown)
                {
                    var element = await ExtractSingleAsync(xmlElement);
                    formulas.Add(element);
                }
            }

            // 递归提取子元素中的公式
            foreach (var child in xmlElement.Elements())
            {
                if (IsMathElement(child))
                {
                    var element = await ExtractSingleAsync(child);
                    formulas.Add(element);
                }
                else if (await _detector.ContainsFormulasAsync(child))
                {
                    var childFormulas = await ExtractFromXmlElementAsync(child);
                    formulas.AddRange(childFormulas);
                }
            }

            _logger.LogDebug("Extracted {FormulaCount} formulas from XML element", formulas.Count);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error extracting formulas from XML element");
        }

        return formulas;
    }

    private async Task<FormulaMetadata> CreateMetadataAsync(object element)
    {
        var metadata = new FormulaMetadata
        {
            CreatedAt = DateTime.UtcNow
        };

        try
        {
            // 计算复杂度
            metadata.ComplexityScore = CalculateComplexityScore(element);

            // 确定是否为内联或显示公式
            var (isInline, isDisplay) = DetermineDisplayMode(element);
            metadata.IsInline = isInline;
            metadata.IsDisplay = isDisplay;

            // 添加自定义属性
            if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
            {
                metadata.CustomProperties["ElementName"] = xmlElement.LocalName;
                metadata.CustomProperties["NamespaceUri"] = xmlElement.NamespaceUri;
                
                if (!string.IsNullOrEmpty(xmlElement.InnerText))
                {
                    metadata.CustomProperties["TextLength"] = xmlElement.InnerText.Length;
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating metadata for formula element");
        }

        return metadata;
    }

    private FormulaLocation CreateLocation(object element)
    {
        var location = new FormulaLocation();

        try
        {
            if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
            {
                location.ParentContainer = xmlElement.Parent;
                
                // 尝试确定在父容器中的位置
                if (xmlElement.Parent != null)
                {
                    var siblings = xmlElement.Parent.Elements().ToList();
                    var index = siblings.IndexOf(xmlElement);
                    if (index >= 0)
                    {
                        location.StartIndex = index;
                        location.EndIndex = index + 1;
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error creating location for formula element");
        }

        return location;
    }

    private bool IsMathElement(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        // 检查命名空间
        if (element.NamespaceUri == "http://schemas.openxmlformats.org/officeDocument/2006/math")
        {
            return true;
        }

        // 检查元素类型
        return element is OfficeMath or Fraction or Radical or Superscript or Subscript or 
               SubSuperscript or Nary or Delimiter or DocumentFormat.OpenXml.Math.Matrix or 
               MathFunction or Accent or BorderBox or GroupCharacter;
    }

    private bool IsChildOfOfficeMath(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        var parent = element.Parent;
        while (parent != null)
        {
            if (parent is OfficeMath)
            {
                return true;
            }
            parent = parent.Parent;
        }
        return false;
    }

    private int CalculateComplexityScore(object element)
    {
        try
        {
            var score = 1; // 基础分数

            if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
            {
                // 基于元素类型增加分数
                score += xmlElement switch
                {
                    OfficeMath => 2,
                    Fraction => 3,
                    Radical => 3,
                    DocumentFormat.OpenXml.Math.Matrix => 5,
                    Nary => 4,
                    Superscript or Subscript or SubSuperscript => 2,
                    Delimiter => 2,
                    MathFunction => 2,
                    _ => 1
                };

                // 基于嵌套深度增加分数
                var depth = CalculateNestingDepth(xmlElement);
                score += depth;

                // 基于子元素数量增加分数
                var childCount = xmlElement.Elements().Count();
                score += childCount / 2;

                // 基于文本长度增加分数
                var textLength = xmlElement.InnerText?.Length ?? 0;
                score += textLength / 10;
            }

            return Math.Max(1, score);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error calculating complexity score");
            return 1;
        }
    }

    private int CalculateNestingDepth(DocumentFormat.OpenXml.OpenXmlElement element)
    {
        var maxDepth = 0;
        
        foreach (var child in element.Elements())
        {
            if (IsMathElement(child))
            {
                var childDepth = 1 + CalculateNestingDepth(child);
                maxDepth = Math.Max(maxDepth, childDepth);
            }
        }
        
        return maxDepth;
    }

    private (bool isInline, bool isDisplay) DetermineDisplayMode(object element)
    {
        try
        {
            if (element is DocumentFormat.OpenXml.OpenXmlElement xmlElement)
            {
                // 检查父容器
                var parent = xmlElement.Parent;
                if (parent is Paragraph paragraph)
                {
                    // 如果段落只包含这个公式（和可能的空白），则为显示模式
                    var textContent = paragraph.InnerText?.Trim() ?? "";
                    var formulaContent = xmlElement.InnerText?.Trim() ?? "";
                    
                    if (string.IsNullOrEmpty(textContent) || textContent == formulaContent)
                    {
                        return (false, true); // 显示模式
                    }
                }

                // 检查OfficeMath的显示属性
                if (element is OfficeMath officeMath)
                {
                    var mathProperties = officeMath.GetFirstChild<MathProperties>();
                    // 这里可以检查具体的显示属性，但OpenXML可能不总是提供这些信息
                }
            }

            // 默认为内联模式
            return (true, false);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error determining display mode");
            return (true, false); // 默认内联模式
        }
    }

    #endregion
}
