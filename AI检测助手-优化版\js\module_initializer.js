/**
 * 模块初始化管理器
 * 确保所有检测模块按正确顺序加载和初始化
 */

class ModuleInitializer {
    constructor() {
        this.modules = new Map();
        this.initializationOrder = [
            'aiDetector',
            'textOptimizer', 
            'zhuqueDetector',
            'zhuqueOptimizer',
            'hybridDetector',
            'ollamaManagerV2'
        ];
        this.initialized = false;
        this.initPromise = null;
    }

    /**
     * 注册模块
     */
    registerModule(name, moduleClass, dependencies = []) {
        this.modules.set(name, {
            class: moduleClass,
            dependencies,
            instance: null,
            initialized: false
        });
    }

    /**
     * 初始化所有模块
     */
    async initializeModules() {
        if (this.initPromise) {
            return this.initPromise;
        }

        this.initPromise = this._doInitialize();
        return this.initPromise;
    }

    async _doInitialize() {
        console.log('🚀 开始初始化检测模块...');
        
        try {
            // 按顺序初始化模块
            for (const moduleName of this.initializationOrder) {
                await this._initializeModule(moduleName);
            }
            
            this.initialized = true;
            console.log('✅ 所有检测模块初始化完成');
            
            // 触发初始化完成事件
            window.dispatchEvent(new CustomEvent('modulesInitialized', {
                detail: { modules: Array.from(this.modules.keys()) }
            }));
            
        } catch (error) {
            console.error('❌ 模块初始化失败:', error);
            throw error;
        }
    }

    async _initializeModule(name) {
        try {
            console.log(`🔄 正在初始化 ${name}...`);
            
            let instance;
            switch (name) {
                case 'aiDetector':
                    if (typeof AIDetector !== 'undefined') {
                        instance = new AIDetector();
                        window.aiDetector = instance;
                    }
                    break;
                    
                case 'textOptimizer':
                    if (typeof TextOptimizer !== 'undefined') {
                        instance = new TextOptimizer();
                        window.textOptimizer = instance;
                    }
                    break;
                    
                case 'zhuqueDetector':
                    if (typeof ZhuqueDetector !== 'undefined') {
                        instance = new ZhuqueDetector();
                        window.zhuqueDetector = instance;
                    }
                    break;
                    
                case 'zhuqueOptimizer':
                    if (typeof ZhuqueOptimizer !== 'undefined') {
                        instance = new ZhuqueOptimizer();
                        window.zhuqueOptimizer = instance;
                    }
                    break;
                    
                case 'hybridDetector':
                    if (typeof HybridDetector !== 'undefined') {
                        instance = new HybridDetector();
                        window.hybridDetector = instance;
                    }
                    break;
                    
                case 'ollamaManagerV2':
                    if (typeof OllamaManagerV2 !== 'undefined') {
                        instance = new OllamaManagerV2();
                        window.ollamaManagerV2 = instance;
                    }
                    break;
                    
                default:
                    console.warn(`⚠️ 未知模块: ${name}`);
                    return;
            }
            
            if (instance) {
                console.log(`✅ ${name} 初始化成功`);
                if (this.modules.has(name)) {
                    this.modules.get(name).instance = instance;
                    this.modules.get(name).initialized = true;
                }
            } else {
                console.warn(`⚠️ ${name} 类未定义，跳过初始化`);
            }
            
        } catch (error) {
            console.error(`❌ ${name} 初始化失败:`, error);
            // 不抛出错误，允许其他模块继续初始化
        }
    }

    /**
     * 检查模块是否已初始化
     */
    isModuleInitialized(name) {
        const module = this.modules.get(name);
        return module ? module.initialized : false;
    }

    /**
     * 获取模块实例
     */
    getModule(name) {
        const module = this.modules.get(name);
        return module ? module.instance : null;
    }

    /**
     * 检查所有必需模块是否已加载
     */
    checkDependencies() {
        const results = [];
        const requiredModules = ['aiDetector', 'zhuqueDetector'];
        const optionalModules = ['hybridDetector', 'ollamaManagerV2'];
        
        let criticalMissing = 0;
        
        // 检查必需模块
        for (const moduleName of requiredModules) {
            const exists = !!window[moduleName];
            if (!exists) criticalMissing++;
            
            results.push({
                name: moduleName,
                exists,
                required: true,
                status: exists ? 'success' : 'error'
            });
        }
        
        // 检查可选模块
        for (const moduleName of optionalModules) {
            const exists = !!window[moduleName];
            
            results.push({
                name: moduleName,
                exists,
                required: false,
                status: exists ? 'success' : 'warning'
            });
        }
        
        // 检查函数
        const functions = ['displayDetectionResult', 'showErrorResult', 'checkDependencies'];
        for (const funcName of functions) {
            const exists = typeof window[funcName] === 'function';
            
            results.push({
                name: funcName,
                exists,
                required: true,
                status: exists ? 'success' : 'error'
            });
        }
        
        return {
            results,
            criticalMissing,
            allRequired: criticalMissing === 0
        };
    }

    /**
     * 等待模块初始化完成
     */
    async waitForInitialization() {
        if (this.initialized) {
            return true;
        }
        
        if (this.initPromise) {
            await this.initPromise;
            return this.initialized;
        }
        
        // 如果还没开始初始化，等待DOM加载完成后自动初始化
        return new Promise((resolve) => {
            if (document.readyState === 'loading') {
                document.addEventListener('DOMContentLoaded', async () => {
                    await this.initializeModules();
                    resolve(this.initialized);
                });
            } else {
                this.initializeModules().then(() => {
                    resolve(this.initialized);
                });
            }
        });
    }
}

// 创建全局模块初始化器实例
const moduleInitializer = new ModuleInitializer();

// 页面加载完成后自动初始化
document.addEventListener('DOMContentLoaded', async function() {
    console.log('📄 DOM加载完成，开始初始化模块...');
    
    // 延迟一点时间确保所有脚本都已加载
    setTimeout(async () => {
        try {
            await moduleInitializer.initializeModules();
        } catch (error) {
            console.error('模块初始化过程中出错:', error);
        }
    }, 100);
});

// 导出供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { ModuleInitializer };
}

// 全局访问
window.moduleInitializer = moduleInitializer;
