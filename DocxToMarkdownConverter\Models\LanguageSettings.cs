using System.ComponentModel;
using System.Runtime.CompilerServices;

namespace DocxToMarkdownConverter.Models;

/// <summary>
/// 语言设置模型
/// </summary>
public class LanguageSettings : INotifyPropertyChanged
{
    private string _currentLanguage = "zh-CN";
    private bool _followSystemLanguage = false;
    private bool _showLanguageRestartWarning = true;

    /// <summary>
    /// 当前语言代码
    /// </summary>
    public string CurrentLanguage
    {
        get => _currentLanguage;
        set => SetProperty(ref _currentLanguage, value);
    }

    /// <summary>
    /// 是否跟随系统语言
    /// </summary>
    public bool FollowSystemLanguage
    {
        get => _followSystemLanguage;
        set => SetProperty(ref _followSystemLanguage, value);
    }

    /// <summary>
    /// 是否显示语言重启警告
    /// </summary>
    public bool ShowLanguageRestartWarning
    {
        get => _showLanguageRestartWarning;
        set => SetProperty(ref _showLanguageRestartWarning, value);
    }

    public event PropertyChangedEventHandler? PropertyChanged;

    protected virtual void OnPropertyChanged([CallerMemberName] string? propertyName = null)
    {
        PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
    }

    protected bool SetProperty<T>(ref T field, T value, [CallerMemberName] string? propertyName = null)
    {
        if (EqualityComparer<T>.Default.Equals(field, value)) return false;
        field = value;
        OnPropertyChanged(propertyName);
        return true;
    }
}
