using DocxToMarkdownConverter.Models;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml;
using System.IO;
using System.IO.Compression;

namespace DocxToMarkdownConverter.Services;

public class DocxConverter : IDocxConverter, IDisposable
{
    private readonly IDocumentProcessor _documentProcessor;
    private readonly SemaphoreSlim _pauseSemaphore = new(1, 1);
    private volatile bool _isPaused = false;

    public DocxConverter(IDocumentProcessor documentProcessor)
    {
        _documentProcessor = documentProcessor ?? throw new ArgumentNullException(nameof(documentProcessor));
    }

    public bool IsBatchConversionPaused => _isPaused;

    public void PauseBatchConversion()
    {
        _isPaused = true;
    }

    public void ResumeBatchConversion()
    {
        _isPaused = false;
        _pauseSemaphore.Release();
    }

    public async Task<ConversionResult> ConvertAsync(string inputPath, string outputPath,
        ConversionOptions options, IProgress<Models.ConversionProgress>? progress,
        CancellationToken cancellationToken)
    {
        var startTime = DateTime.Now;
        var conversionId = Path.GetFileNameWithoutExtension(inputPath) + "_" + DateTime.Now.ToString("HHmmss");

        var result = new ConversionResult
        {
            InputPath = inputPath,
            OutputPath = outputPath
        };

        await LogConversionStep(conversionId, "CONVERSION_START", $"Starting conversion: {inputPath} -> {outputPath}");

        try
        {
            // Validate input file
            await LogConversionStep(conversionId, "VALIDATE_INPUT", "Validating input file...");
            if (!File.Exists(inputPath))
            {
                var error = $"Input file not found: {inputPath}";
                await LogConversionStep(conversionId, "VALIDATE_INPUT_FAILED", error);
                throw new FileNotFoundException(error);
            }

            var inputFileInfo = new FileInfo(inputPath);
            result.InputSize = inputFileInfo.Length;
            await LogConversionStep(conversionId, "VALIDATE_INPUT_SUCCESS", $"Input file validated. Size: {result.InputSize} bytes, Modified: {inputFileInfo.LastWriteTime}");

            // Enhanced file format validation
            await ValidateDocxFormat(conversionId, inputPath);

            // Report progress
            progress?.Report(new Models.ConversionProgress
            {
                ProgressPercentage = 10,
                CurrentOperation = "Opening DOCX file",
                CurrentFile = Path.GetFileName(inputPath)
            });

            // Open and process the DOCX document with enhanced error handling
            await LogConversionStep(conversionId, "OPEN_DOCUMENT", "Opening DOCX document...");
            WordprocessingDocument? document = null;

            try
            {
                // Try different opening strategies
                document = await OpenDocumentWithFallback(conversionId, inputPath);

                if (document.MainDocumentPart == null)
                {
                    var error = "The DOCX file does not contain a main document part.";
                    await LogConversionStep(conversionId, "OPEN_DOCUMENT_FAILED", error);
                    throw new InvalidOperationException(error);
                }

                await LogConversionStep(conversionId, "OPEN_DOCUMENT_SUCCESS", "DOCX document opened successfully");
            }
            catch (Exception ex)
            {
                await LogConversionStep(conversionId, "OPEN_DOCUMENT_EXCEPTION", $"Failed to open document: {ex.GetType().Name}: {ex.Message}");

                // Try to provide more specific error messages
                var userFriendlyError = GetUserFriendlyError(ex);
                await LogConversionStep(conversionId, "USER_FRIENDLY_ERROR", userFriendlyError);

                throw new InvalidOperationException(userFriendlyError, ex);
            }

            // Log document structure information
            await LogDocumentStructure(conversionId, document);

            progress?.Report(new Models.ConversionProgress
            {
                ProgressPercentage = 30,
                CurrentOperation = "Processing document content",
                CurrentFile = Path.GetFileName(inputPath)
            });

            // Process the document to markdown
            await LogConversionStep(conversionId, "PROCESS_CONTENT", "Processing document content to markdown...");
            var markdownContent = await _documentProcessor.ProcessDocumentAsync(document, options);

            if (string.IsNullOrEmpty(markdownContent))
            {
                await LogConversionStep(conversionId, "PROCESS_CONTENT_WARNING", "Document processing returned empty content");
                markdownContent = "<!-- Document appears to be empty or could not be processed -->";
            }
            else
            {
                var lines = markdownContent.Split('\n').Length;
                var chars = markdownContent.Length;
                await LogConversionStep(conversionId, "PROCESS_CONTENT_SUCCESS", $"Content processed successfully. Lines: {lines}, Characters: {chars}");
            }

            progress?.Report(new Models.ConversionProgress
            {
                ProgressPercentage = 80,
                CurrentOperation = "Writing output file",
                CurrentFile = Path.GetFileName(inputPath)
            });

            // Ensure output directory exists
            await LogConversionStep(conversionId, "PREPARE_OUTPUT", "Preparing output directory...");
            var outputDir = Path.GetDirectoryName(outputPath);
            if (!string.IsNullOrEmpty(outputDir))
            {
                if (!Directory.Exists(outputDir))
                {
                    await LogConversionStep(conversionId, "CREATE_OUTPUT_DIR", $"Creating output directory: {outputDir}");
                    Directory.CreateDirectory(outputDir);
                    await LogConversionStep(conversionId, "CREATE_OUTPUT_DIR_SUCCESS", "Output directory created successfully");
                }
                else
                {
                    await LogConversionStep(conversionId, "OUTPUT_DIR_EXISTS", $"Output directory already exists: {outputDir}");
                }
            }

            // Write the markdown content to file
            await LogConversionStep(conversionId, "WRITE_OUTPUT", $"Writing markdown content to file: {outputPath}");
            await File.WriteAllTextAsync(outputPath, markdownContent, options.OutputEncoding, cancellationToken);

            // Verify the written file
            if (File.Exists(outputPath))
            {
                var outputFileInfo = new FileInfo(outputPath);
                result.OutputSize = outputFileInfo.Length;
                await LogConversionStep(conversionId, "WRITE_OUTPUT_SUCCESS", $"File written successfully. Size: {result.OutputSize} bytes");

                if (result.OutputSize == 0)
                {
                    await LogConversionStep(conversionId, "WRITE_OUTPUT_WARNING", "Output file is empty!");
                }
            }
            else
            {
                var error = "Output file was not created despite no exceptions";
                await LogConversionStep(conversionId, "WRITE_OUTPUT_FAILED", error);
                throw new InvalidOperationException(error);
            }

            result.IsSuccess = true;

            progress?.Report(new Models.ConversionProgress
            {
                ProgressPercentage = 100,
                CurrentOperation = "Conversion completed",
                CurrentFile = Path.GetFileName(inputPath)
            });

            await LogConversionStep(conversionId, "CONVERSION_SUCCESS", $"Conversion completed successfully in {(DateTime.Now - startTime).TotalSeconds:F2} seconds");
        }
        catch (Exception ex)
        {
            result.IsSuccess = false;
            result.ErrorMessage = ex.Message;
            result.Exception = ex;

            await LogConversionStep(conversionId, "CONVERSION_FAILED", $"Conversion failed: {ex.GetType().Name}: {ex.Message}");
            await LogConversionStep(conversionId, "CONVERSION_STACK_TRACE", ex.StackTrace ?? "No stack trace available");

            if (ex.InnerException != null)
            {
                await LogConversionStep(conversionId, "INNER_EXCEPTION", $"Inner exception: {ex.InnerException.GetType().Name}: {ex.InnerException.Message}");
            }
        }
        finally
        {
            result.Duration = DateTime.Now - startTime;
            await LogConversionStep(conversionId, "CONVERSION_END", $"Conversion ended. Duration: {result.Duration.TotalSeconds:F2}s, Success: {result.IsSuccess}");
        }

        return result;
    }

    public async Task<IEnumerable<ConversionResult>> ConvertBatchAsync(
        IEnumerable<ConversionTask> tasks, 
        IProgress<Models.BatchConversionProgress>? progress, 
        CancellationToken cancellationToken)
    {
        var taskList = tasks.ToList();
        var results = new ConversionResult[taskList.Count];
        var completedCount = 0L;
        var successfulCount = 0L;
        var failedCount = 0L;
        var startTime = DateTime.Now;
        
        // Reset pause state at the beginning of batch conversion
        _isPaused = false;
        
        // Use a semaphore to limit concurrent operations to prevent resource exhaustion
        var maxConcurrency = Math.Min(Environment.ProcessorCount, 4);
        using var semaphore = new SemaphoreSlim(maxConcurrency, maxConcurrency);
        
        // Create tasks for parallel processing
        var conversionTasks = taskList.Select(async (task, index) =>
        {
            await semaphore.WaitAsync(cancellationToken).ConfigureAwait(false);

            try
            {
                // Handle pause/resume functionality
                await HandlePauseAsync(cancellationToken).ConfigureAwait(false);
                
                // Create progress reporter for individual file
                var fileProgress = new Progress<Models.ConversionProgress>(p =>
                {
                    var currentCompleted = Interlocked.Read(ref completedCount);
                    var currentSuccessful = Interlocked.Read(ref successfulCount);
                    var currentFailed = Interlocked.Read(ref failedCount);
                    var elapsed = DateTime.Now - startTime;
                    
                    progress?.Report(new Models.BatchConversionProgress
                    {
                        CompletedFiles = (int)currentCompleted,
                        TotalFiles = taskList.Count,
                        SuccessfulFiles = (int)currentSuccessful,
                        FailedFiles = (int)currentFailed,
                        CurrentFileProgress = p,
                        ElapsedTime = elapsed,
                        EstimatedTimeRemaining = EstimateRemainingTime(elapsed, (int)currentCompleted, taskList.Count)
                    });
                });

                // Perform conversion with retry mechanism
                var result = await ConvertWithRetryAsync(task, fileProgress, cancellationToken).ConfigureAwait(false);
                results[index] = result;
                
                // Update counters atomically
                Interlocked.Increment(ref completedCount);
                if (result.IsSuccess)
                {
                    Interlocked.Increment(ref successfulCount);
                }
                else
                {
                    Interlocked.Increment(ref failedCount);
                }
                
                // Report overall progress
                var finalCompleted = Interlocked.Read(ref completedCount);
                var finalSuccessful = Interlocked.Read(ref successfulCount);
                var finalFailed = Interlocked.Read(ref failedCount);
                var finalElapsed = DateTime.Now - startTime;
                
                progress?.Report(new Models.BatchConversionProgress
                {
                    CompletedFiles = (int)finalCompleted,
                    TotalFiles = taskList.Count,
                    SuccessfulFiles = (int)finalSuccessful,
                    FailedFiles = (int)finalFailed,
                    CurrentFileProgress = null,
                    ElapsedTime = finalElapsed,
                    EstimatedTimeRemaining = EstimateRemainingTime(finalElapsed, (int)finalCompleted, taskList.Count)
                });
            }
            finally
            {
                semaphore.Release();
            }
        });

        // Wait for all conversions to complete
        try
        {
            await Task.WhenAll(conversionTasks).ConfigureAwait(false);
        }
        catch (OperationCanceledException)
        {
            // Handle cancellation gracefully
            // Fill any null results with cancelled results
            for (int i = 0; i < results.Length; i++)
            {
                if (results[i] == null)
                {
                    results[i] = new ConversionResult
                    {
                        InputPath = taskList[i].InputPath,
                        OutputPath = taskList[i].OutputPath,
                        IsSuccess = false,
                        ErrorMessage = "Operation was cancelled",
                        Duration = DateTime.Now - startTime
                    };
                }
            }
        }

        return results.Where(r => r != null);
    }

    private async Task<ConversionResult> ConvertWithRetryAsync(
        ConversionTask task, 
        IProgress<Models.ConversionProgress> progress, 
        CancellationToken cancellationToken)
    {
        const int maxRetries = 3;
        const int baseDelayMs = 1000;
        
        for (int attempt = 0; attempt < maxRetries; attempt++)
        {
            try
            {
                return await ConvertAsync(task.InputPath, task.OutputPath, task.Options, progress, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex) when (IsRetryableException(ex) && attempt < maxRetries - 1)
            {
                // Exponential backoff delay
                var delay = TimeSpan.FromMilliseconds(baseDelayMs * Math.Pow(2, attempt));
                
                progress?.Report(new Models.ConversionProgress
                {
                    CurrentFile = Path.GetFileName(task.InputPath),
                    CurrentOperation = $"Retrying conversion (attempt {attempt + 2}/{maxRetries})",
                    ProgressPercentage = 0
                });
                
                await Task.Delay(delay, cancellationToken).ConfigureAwait(false);
            }
            catch (Exception ex)
            {
                // Final attempt failed or non-retryable exception
                return new ConversionResult
                {
                    InputPath = task.InputPath,
                    OutputPath = task.OutputPath,
                    IsSuccess = false,
                    ErrorMessage = ex.Message,
                    Exception = ex,
                    Duration = TimeSpan.Zero
                };
            }
        }
        
        // This should never be reached, but just in case
        return new ConversionResult
        {
            InputPath = task.InputPath,
            OutputPath = task.OutputPath,
            IsSuccess = false,
            ErrorMessage = "Maximum retry attempts exceeded",
            Duration = TimeSpan.Zero
        };
    }

    private static bool IsRetryableException(Exception ex)
    {
        return ex switch
        {
            IOException => true,
            UnauthorizedAccessException => true,
            TimeoutException => true,
            InvalidOperationException when ex.Message.Contains("locked") => true,
            _ => false
        };
    }

    private static TimeSpan? EstimateRemainingTime(TimeSpan elapsed, int completed, int total)
    {
        if (completed == 0 || completed >= total)
            return null;
            
        var averageTimePerFile = elapsed.TotalMilliseconds / completed;
        var remainingFiles = total - completed;
        var estimatedRemainingMs = averageTimePerFile * remainingFiles;
        
        return TimeSpan.FromMilliseconds(estimatedRemainingMs);
    }

    private async Task HandlePauseAsync(CancellationToken cancellationToken)
    {
        while (_isPaused && !cancellationToken.IsCancellationRequested)
        {
            try
            {
                // Wait for resume signal or cancellation
                await _pauseSemaphore.WaitAsync(100, cancellationToken).ConfigureAwait(false);
                _pauseSemaphore.Release();

                if (!_isPaused)
                    break;

                // Small delay to prevent busy waiting
                await Task.Delay(50, cancellationToken).ConfigureAwait(false);
            }
            catch (OperationCanceledException)
            {
                // If cancelled while paused, break out of the loop
                break;
            }
        }
    }

    public void Dispose()
    {
        _pauseSemaphore?.Dispose();
    }

    #region 文件验证和文档处理方法

    private async Task ValidateDocxFormat(string conversionId, string inputPath)
    {
        await LogConversionStep(conversionId, "FORMAT_VALIDATION", "Validating DOCX file format...");

        try
        {
            // Check file extension
            var extension = Path.GetExtension(inputPath).ToLowerInvariant();
            if (extension != ".docx")
            {
                await LogConversionStep(conversionId, "FORMAT_WARNING", $"File extension is '{extension}', expected '.docx'");
            }

            // Check file size
            var fileInfo = new FileInfo(inputPath);
            if (fileInfo.Length == 0)
            {
                throw new InvalidOperationException("文件为空，无法处理");
            }

            if (fileInfo.Length < 1024) // Less than 1KB is suspicious for a DOCX file
            {
                await LogConversionStep(conversionId, "FORMAT_WARNING", $"File size is very small ({fileInfo.Length} bytes), may not be a valid DOCX file");
            }

            // Check if file is actually a ZIP file (DOCX is a ZIP archive)
            await ValidateZipStructure(conversionId, inputPath);

            await LogConversionStep(conversionId, "FORMAT_VALIDATION_SUCCESS", "DOCX format validation passed");
        }
        catch (Exception ex)
        {
            await LogConversionStep(conversionId, "FORMAT_VALIDATION_FAILED", $"Format validation failed: {ex.Message}");
            throw new InvalidOperationException($"文件格式验证失败: {ex.Message}", ex);
        }
    }

    private async Task ValidateZipStructure(string conversionId, string inputPath)
    {
        try
        {
            using var fileStream = new FileStream(inputPath, FileMode.Open, FileAccess.Read);
            using var archive = new System.IO.Compression.ZipArchive(fileStream, System.IO.Compression.ZipArchiveMode.Read);

            // Check for essential DOCX files
            var hasContentTypes = archive.Entries.Any(e => e.FullName == "[Content_Types].xml");
            var hasDocumentXml = archive.Entries.Any(e => e.FullName == "word/document.xml");
            var hasRels = archive.Entries.Any(e => e.FullName.StartsWith("_rels/"));

            await LogConversionStep(conversionId, "ZIP_STRUCTURE", $"ZIP validation - ContentTypes: {hasContentTypes}, Document: {hasDocumentXml}, Rels: {hasRels}");

            if (!hasContentTypes || !hasDocumentXml)
            {
                throw new InvalidOperationException("文件不是有效的DOCX格式：缺少必要的内部文件");
            }

            await LogConversionStep(conversionId, "ZIP_STRUCTURE_SUCCESS", "ZIP structure validation passed");
        }
        catch (Exception ex) when (!(ex is InvalidOperationException))
        {
            await LogConversionStep(conversionId, "ZIP_STRUCTURE_FAILED", $"ZIP structure validation failed: {ex.Message}");
            throw new InvalidOperationException("文件不是有效的ZIP/DOCX格式", ex);
        }
    }

    private async Task<WordprocessingDocument> OpenDocumentWithFallback(string conversionId, string inputPath)
    {
        WordprocessingDocument? document = null;
        Exception? lastException = null;

        // Strategy 1: Normal opening
        try
        {
            await LogConversionStep(conversionId, "OPEN_STRATEGY_1", "Trying normal document opening...");
            document = WordprocessingDocument.Open(inputPath, false);
            await LogConversionStep(conversionId, "OPEN_STRATEGY_1_SUCCESS", "Normal opening succeeded");
            return document;
        }
        catch (Exception ex)
        {
            lastException = ex;
            await LogConversionStep(conversionId, "OPEN_STRATEGY_1_FAILED", $"Normal opening failed: {ex.Message}");
            document?.Dispose();
        }

        // Strategy 2: Opening with repair
        try
        {
            await LogConversionStep(conversionId, "OPEN_STRATEGY_2", "Trying document opening with auto-repair...");
            var openSettings = new OpenSettings
            {
                AutoSave = false,
                MarkupCompatibilityProcessSettings = new MarkupCompatibilityProcessSettings(
                    MarkupCompatibilityProcessMode.ProcessAllParts,
                    FileFormatVersions.Office2019)
            };
            document = WordprocessingDocument.Open(inputPath, false, openSettings);
            await LogConversionStep(conversionId, "OPEN_STRATEGY_2_SUCCESS", "Opening with repair succeeded");
            return document;
        }
        catch (Exception ex)
        {
            lastException = ex;
            await LogConversionStep(conversionId, "OPEN_STRATEGY_2_FAILED", $"Opening with repair failed: {ex.Message}");
            document?.Dispose();
        }

        // Strategy 3: Copy and open
        try
        {
            await LogConversionStep(conversionId, "OPEN_STRATEGY_3", "Trying copy-and-open strategy...");
            var tempPath = Path.GetTempFileName() + ".docx";
            File.Copy(inputPath, tempPath, true);
            document = WordprocessingDocument.Open(tempPath, false);
            await LogConversionStep(conversionId, "OPEN_STRATEGY_3_SUCCESS", "Copy-and-open succeeded");
            return document;
        }
        catch (Exception ex)
        {
            lastException = ex;
            await LogConversionStep(conversionId, "OPEN_STRATEGY_3_FAILED", $"Copy-and-open failed: {ex.Message}");
            document?.Dispose();
        }

        // All strategies failed
        await LogConversionStep(conversionId, "OPEN_ALL_FAILED", "All opening strategies failed");
        throw lastException ?? new InvalidOperationException("无法打开DOCX文件");
    }

    private string GetUserFriendlyError(Exception ex)
    {
        return ex switch
        {
            FileFormatException => "文件格式错误：这不是一个有效的DOCX文件，或者文件已损坏",
            InvalidDataException => "文件数据无效：DOCX文件内容损坏或格式不正确",
            UnauthorizedAccessException => "文件访问被拒绝：请检查文件是否被其他程序占用或权限不足",
            DirectoryNotFoundException => "文件路径不存在：请检查文件路径是否正确",
            PathTooLongException => "文件路径过长：请将文件移动到路径较短的位置",
            _ => $"未知错误：{ex.Message}"
        };
    }

    #endregion

    #region 日志记录方法

    private async Task LogConversionStep(string conversionId, string step, string message)
    {
        try
        {
            var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
            var logMessage = $"{timestamp} - [{conversionId}] {step}: {message}\n";
            await File.AppendAllTextAsync("docx_converter_debug.log", logMessage);
        }
        catch
        {
            // 静默处理日志写入失败，避免影响主流程
        }
    }

    private async Task LogDocumentStructure(string conversionId, WordprocessingDocument document)
    {
        try
        {
            await LogConversionStep(conversionId, "DOCUMENT_STRUCTURE", "Analyzing document structure...");

            var mainPart = document.MainDocumentPart;
            if (mainPart?.Document?.Body != null)
            {
                var body = mainPart.Document.Body;
                var paragraphs = body.Elements<DocumentFormat.OpenXml.Wordprocessing.Paragraph>().Count();
                var tables = body.Elements<DocumentFormat.OpenXml.Wordprocessing.Table>().Count();
                var sections = body.Elements<DocumentFormat.OpenXml.Wordprocessing.SectionProperties>().Count();

                await LogConversionStep(conversionId, "DOCUMENT_STATS", $"Paragraphs: {paragraphs}, Tables: {tables}, Sections: {sections}");

                // Log image parts
                var imageParts = mainPart.ImageParts.Count();
                await LogConversionStep(conversionId, "DOCUMENT_IMAGES", $"Image parts found: {imageParts}");

                // Log relationship parts
                var relationships = mainPart.Parts.Count();
                await LogConversionStep(conversionId, "DOCUMENT_RELATIONSHIPS", $"Relationship parts: {relationships}");
            }
            else
            {
                await LogConversionStep(conversionId, "DOCUMENT_STRUCTURE_WARNING", "Document body is null or empty");
            }
        }
        catch (Exception ex)
        {
            await LogConversionStep(conversionId, "DOCUMENT_STRUCTURE_ERROR", $"Failed to analyze document structure: {ex.Message}");
        }
    }

    #endregion
}