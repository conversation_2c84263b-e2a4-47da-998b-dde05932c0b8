<UserControl x:Class="DocxToMarkdownConverter.Controls.FileListControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:models="clr-namespace:DocxToMarkdownConverter.Models"
             xmlns:behaviors="clr-namespace:DocxToMarkdownConverter.Behaviors"
             xmlns:converters="clr-namespace:DocxToMarkdownConverter.Converters"
             mc:Ignorable="d"
             d:DesignHeight="450" d:DesignWidth="800"
             AllowDrop="True"
             Drop="OnDrop"
             DragOver="OnDragOver"
             DragEnter="OnDragEnter"
             DragLeave="OnDragLeave">

    <UserControl.Resources>
        <!-- Local Converters -->
        <converters:FileSizeConverter x:Key="FileSizeConverter"/>

        <Style x:Key="FileItemStyle" TargetType="ListViewItem">
            <Setter Property="HorizontalContentAlignment" Value="Stretch"/>
            <Setter Property="Margin" Value="0,2"/>
            <Setter Property="Padding" Value="8"/>
            <Setter Property="Background" Value="Transparent"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ListViewItem">
                        <Border Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                CornerRadius="4"
                                Padding="{TemplateBinding Padding}">
                            <ContentPresenter/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                            </Trigger>
                            <Trigger Property="IsSelected" Value="True">
                                <Setter Property="Background" Value="{DynamicResource MaterialDesignSelection}"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <DataTemplate x:Key="FileItemTemplate" DataType="{x:Type models:ConversionFileItem}">
            <Border Background="LightGray" Margin="2" Padding="8">
                <StackPanel>
                    <TextBlock Text="{Binding FileName}" FontWeight="Bold"/>
                    <TextBlock FontSize="10">
                        <TextBlock.Text>
                            <MultiBinding StringFormat="{}{0} • {1}">
                                <Binding Path="FilePath"/>
                                <Binding Path="FileSize" Converter="{StaticResource FileSizeConverter}"/>
                            </MultiBinding>
                        </TextBlock.Text>
                    </TextBlock>
                    <TextBlock Text="{Binding Status}" FontSize="10" Foreground="Blue"/>
                </StackPanel>
            </Border>
        </DataTemplate>
    </UserControl.Resources>

    <Grid behaviors:AnimationBehaviors.AutoAttachHoverAnimations="True">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="4,4,0,0"
                Padding="16,12">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="0" Orientation="Horizontal">
                    <materialDesign:PackIcon Kind="FileMultiple" 
                                           Width="20" Height="20"
                                           VerticalAlignment="Center"
                                           Margin="0,0,8,0"
                                           Foreground="{DynamicResource PrimaryHueMidBrush}"/>
                    <TextBlock Text="{DynamicResource Files.ToConvert}"
                             FontSize="16"
                             FontWeight="Medium"
                             VerticalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBody}"/>
                    <TextBlock Text="{Binding Files.Count, StringFormat='({0})'}" 
                             FontSize="14"
                             VerticalAlignment="Center"
                             Margin="8,0,0,0"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"/>
                </StackPanel>

                <StackPanel Grid.Column="1" Orientation="Horizontal">
                    <!-- Add Files Button -->
                    <Button Style="{StaticResource SmartGlowButtonStyle}"
                            Height="32"
                            Padding="12,4"
                            Margin="0,0,8,0"
                            Command="{Binding AddFilesCommand}"
                            ToolTip="{DynamicResource Tooltip.AddFiles}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="FileDocumentPlusOutline" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource Files.AddFiles}">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=ActualWidth, Converter={StaticResource ComparisonConverter}, ConverterParameter=500}" Value="True">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Button>

                    <!-- Clear Files Button -->
                    <Button Style="{StaticResource SmartGlowButtonStyle}"
                            Height="32"
                            Padding="12,4"
                            Command="{Binding ClearFilesCommand}"
                            ToolTip="{DynamicResource Tooltip.ClearAllFiles}">
                        <StackPanel Orientation="Horizontal">
                            <materialDesign:PackIcon Kind="DeleteOutline" Width="16" Height="16" Margin="0,0,4,0"/>
                            <TextBlock Text="{DynamicResource Files.ClearAll}">
                                <TextBlock.Style>
                                    <Style TargetType="TextBlock">
                                        <Style.Triggers>
                                            <DataTrigger Binding="{Binding RelativeSource={RelativeSource AncestorType=UserControl}, Path=ActualWidth, Converter={StaticResource ComparisonConverter}, ConverterParameter=500}" Value="True">
                                                <Setter Property="Visibility" Value="Collapsed"/>
                                            </DataTrigger>
                                        </Style.Triggers>
                                    </Style>
                                </TextBlock.Style>
                            </TextBlock>
                        </StackPanel>
                    </Button>
                </StackPanel>
            </Grid>
        </Border>

        <!-- File List -->
        <Border Grid.Row="1" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                BorderThickness="0">
            <Grid>
                <!-- Empty State -->
                <StackPanel VerticalAlignment="Center" 
                          HorizontalAlignment="Center"
                          Margin="32">
                    <StackPanel.Visibility>
                        <Binding Path="Files.Count" Converter="{StaticResource CountToVisibilityConverter}" ConverterParameter="0"/>
                    </StackPanel.Visibility>
                    
                    <materialDesign:PackIcon Kind="FileDocumentPlusOutline" 
                                           Width="64" Height="64"
                                           HorizontalAlignment="Center"
                                           Foreground="{DynamicResource MaterialDesignBodyLight}"
                                           Margin="0,0,0,16"/>
                    
                    <TextBlock Text="{DynamicResource Files.NoFiles}"
                             FontSize="18"
                             FontWeight="Medium"
                             HorizontalAlignment="Center"
                             Foreground="{DynamicResource MaterialDesignBody}"
                             Margin="0,0,0,8"/>

                    <TextBlock Text="{DynamicResource Files.DragDropHint}"
                             FontSize="14"
                             HorizontalAlignment="Center"
                             TextAlignment="Center"
                             TextWrapping="Wrap"
                             Foreground="{DynamicResource MaterialDesignBodyLight}"
                             MaxWidth="320"/>
                </StackPanel>

                <!-- Drag Drop Overlay -->
                <Border x:Name="DragDropOverlay"
                        Background="{DynamicResource PrimaryHueLightBrush}"
                        Opacity="0.1"
                        Visibility="Collapsed"
                        CornerRadius="8"
                        BorderThickness="2"
                        BorderBrush="{DynamicResource PrimaryHueMidBrush}">
                    <Border.Effect>
                        <DropShadowEffect Color="{DynamicResource PrimaryHueMidColor}"
                                        BlurRadius="20"
                                        ShadowDepth="0"
                                        Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel VerticalAlignment="Center" HorizontalAlignment="Center">
                        <!-- 动画图标容器 -->
                        <Grid Width="64" Height="64" Margin="0,0,0,16">
                            <!-- 背景圆圈 -->
                            <Ellipse Fill="{DynamicResource PrimaryHueMidBrush}"
                                   Opacity="0.1"
                                   Width="64" Height="64"/>

                            <!-- 主图标 -->
                            <materialDesign:PackIcon Kind="FileUpload"
                                                   Width="32" Height="32"
                                                   Foreground="{DynamicResource PrimaryHueMidBrush}"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"/>

                            <!-- 脉冲效果圆圈 -->
                            <Ellipse x:Name="PulseCircle"
                                   Stroke="{DynamicResource PrimaryHueMidBrush}"
                                   StrokeThickness="2"
                                   Fill="Transparent"
                                   Width="64" Height="64"
                                   Opacity="0">
                                <Ellipse.Triggers>
                                    <EventTrigger RoutedEvent="Loaded">
                                        <BeginStoryboard>
                                            <Storyboard RepeatBehavior="Forever">
                                                <DoubleAnimation Storyboard.TargetProperty="Opacity"
                                                               From="0" To="1" Duration="0:0:1"
                                                               AutoReverse="True"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Width"
                                                               From="64" To="80" Duration="0:0:1"
                                                               AutoReverse="True"/>
                                                <DoubleAnimation Storyboard.TargetProperty="Height"
                                                               From="64" To="80" Duration="0:0:1"
                                                               AutoReverse="True"/>
                                            </Storyboard>
                                        </BeginStoryboard>
                                    </EventTrigger>
                                </Ellipse.Triggers>
                            </Ellipse>
                        </Grid>

                        <!-- 主要文本 -->
                        <TextBlock Text="拖拽 DOCX 文件到此处"
                                 FontSize="18"
                                 FontWeight="Medium"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 HorizontalAlignment="Center"
                                 Margin="0,0,0,8"/>

                        <!-- 辅助文本 -->
                        <TextBlock Text="支持多文件同时拖拽"
                                 FontSize="14"
                                 FontWeight="Normal"
                                 Foreground="{DynamicResource PrimaryHueMidBrush}"
                                 Opacity="0.8"
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Border>

                <!-- File List View -->
                <ListView x:Name="FileListView"
                        ItemsSource="{Binding Files}"
                        ItemTemplate="{StaticResource FileItemTemplate}"
                        ItemContainerStyle="{StaticResource FileItemStyle}"
                        ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                        ScrollViewer.VerticalScrollBarVisibility="Auto"
                        ScrollViewer.CanContentScroll="True"
                        VirtualizingPanel.IsVirtualizing="True"
                        VirtualizingPanel.VirtualizationMode="Recycling"
                        VirtualizingPanel.IsContainerVirtualizable="True"
                        VirtualizingPanel.ScrollUnit="Pixel"
                        Background="Transparent"
                        BorderThickness="0"
                        Padding="16,8">
                    <ListView.Visibility>
                        <Binding Path="Files.Count" Converter="{StaticResource CountToVisibilityConverter}" ConverterParameter="1"/>
                    </ListView.Visibility>
                </ListView>
            </Grid>
        </Border>

        <!-- Footer -->
        <Border Grid.Row="2" 
                Background="{DynamicResource MaterialDesignCardBackground}"
                CornerRadius="0,0,4,4"
                Padding="16,8"
                BorderThickness="0,1,0,0"
                BorderBrush="{DynamicResource MaterialDesignDivider}">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0"
                         VerticalAlignment="Center"
                         FontSize="12"
                         Foreground="{DynamicResource MaterialDesignBodyLight}">
                    <TextBlock.Text>
                        <MultiBinding Converter="{StaticResource LocalizedStatusConverter}">
                            <Binding Path="Files.Count"/>
                            <Binding Path="Statistics.PendingConversions"/>
                            <Binding Path="Statistics.SuccessfulConversions"/>
                            <Binding Path="Statistics.FailedConversions"/>
                        </MultiBinding>
                    </TextBlock.Text>
                </TextBlock>

                <Button Grid.Column="1"
                      Style="{StaticResource MaterialDesignRaisedButton}"
                      Height="32"
                      Padding="16,4"
                      Command="{Binding StartConversionCommand}"
                      IsEnabled="{Binding Files.Count, Converter={StaticResource CountToBooleanConverter}}">
                    <StackPanel Orientation="Horizontal">
                        <materialDesign:PackIcon Kind="Play" Width="16" Height="16" Margin="0,0,4,0"/>
                        <TextBlock Text="{DynamicResource Files.StartConversion}"/>
                    </StackPanel>
                </Button>
            </Grid>
        </Border>
    </Grid>
</UserControl>