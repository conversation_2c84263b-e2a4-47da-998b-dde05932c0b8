App.OnStartup: 开始启动应用程序 - 2025/7/24 23:01:35
App.OnStartup: 调用 base.OnStartup - 2025/7/24 23:01:35
App.OnStartup: base.OnStartup 完成 - 2025/7/24 23:01:35
App.OnStartup: 启用渲染优化 - 2025/7/24 23:01:35
App.OnStartup: 配置服务 - 2025/7/24 23:01:35
App.OnStartup: 配置日志 - 2025/7/24 23:01:35
ConfigureLogging: 开始配置日志 - 2025/7/24 23:01:35
ConfigureLogging: Serilog配置完成 - 2025/7/24 23:01:35
ConfigureLogging: 日志配置完成 - 2025/7/24 23:01:35
App.OnStartup: 设置异常处理 - 2025/7/24 23:01:35
App.OnStartup: 异常处理设置完成 - 2025/7/24 23:01:35
App.OnStartup: 初始化配置服务 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 开始 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 获取配置服务结果: True - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 开始配置监视 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 配置监视启动完成 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 确保默认设置存在 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 默认设置确保完成 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 初始化本地化服务 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 本地化服务初始化完成 - 2025/7/24 23:01:35
InitializeConfigurationServiceAsync: 配置服务初始化成功 - 2025/7/24 23:01:35
App.OnStartup: 配置服务初始化完成 - 2025/7/24 23:01:35
App.OnStartup: 创建主窗口 - 2025/7/24 23:01:35
App.OnStartup: 调用 GetRequiredService<MainWindow> - 2025/7/24 23:01:35
App.OnStartup: 主窗口创建结果: True - 2025/7/24 23:01:35
App.OnStartup: 设置 MainWindow 属性 - 2025/7/24 23:01:35
App.OnStartup: 显示主窗口 - 2025/7/24 23:01:35
MainWindow.OnLoaded: 开始 - 2025/7/24 23:01:37
App.OnStartup: 主窗口显示完成 - 2025/7/24 23:01:37
MainWindow.OnLoaded: 恢复窗口状态 - 2025/7/24 23:01:37
MainWindow.OnLoaded: 播放入场动画 - 2025/7/24 23:01:37
MainWindow.OnLoaded: 设置活动按钮 - 2025/7/24 23:01:37
MainWindow.OnLoaded: 附加悬停动画 - 2025/7/24 23:01:37
MainWindow.OnLoaded: 完成 - 2025/7/24 23:01:37
