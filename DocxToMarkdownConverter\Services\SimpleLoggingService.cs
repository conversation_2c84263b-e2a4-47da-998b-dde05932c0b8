using Microsoft.Extensions.Logging;
using System.Collections.Concurrent;

namespace DocxToMarkdownConverter.Services;

/// <summary>
/// 简化的日志服务实现
/// </summary>
public class SimpleLoggingService : ILoggingService
{
    private readonly ILogger<SimpleLoggingService> _logger;
    private readonly ConcurrentQueue<LogEntry> _logs = new();

    public SimpleLoggingService(ILogger<SimpleLoggingService> logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public event EventHandler<LogEventArgs>? LogEvent;
    public event EventHandler<LogEventArgs>? LogReceived; // For backward compatibility

    public void LogDebug(string message)
    {
        _logger.LogDebug(message);
        AddLogEntry(LogLevel.Debug, "Debug", message);
    }

    public void LogDebug(string message, params object[] args)
    {
        var formattedMessage = string.Format(message, args);
        _logger.LogDebug(formattedMessage);
        AddLogEntry(LogLevel.Debug, "Debug", formattedMessage);
    }

    public void LogInfo(string message)
    {
        _logger.LogInformation(message);
        AddLogEntry(LogLevel.Info, "Info", message);
    }

    public void LogInfo(string message, params object[] args)
    {
        var formattedMessage = string.Format(message, args);
        _logger.LogInformation(formattedMessage);
        AddLogEntry(LogLevel.Info, "Info", formattedMessage);
    }

    public void LogWarning(string message)
    {
        _logger.LogWarning(message);
        AddLogEntry(LogLevel.Warning, "Warning", message);
    }

    public void LogWarning(string message, params object[] args)
    {
        var formattedMessage = string.Format(message, args);
        _logger.LogWarning(formattedMessage);
        AddLogEntry(LogLevel.Warning, "Warning", formattedMessage);
    }

    public void LogError(string message)
    {
        _logger.LogError(message);
        AddLogEntry(LogLevel.Error, "Error", message);
    }

    public void LogError(string message, params object[] args)
    {
        var formattedMessage = string.Format(message, args);
        _logger.LogError(formattedMessage);
        AddLogEntry(LogLevel.Error, "Error", formattedMessage);
    }

    public void LogError(Exception exception, string message)
    {
        _logger.LogError(exception, message);
        AddLogEntry(LogLevel.Error, "Error", message, exception);
    }

    public void LogConversionStart(string fileName, string operation)
    {
        var message = $"开始转换文件: {fileName} - {operation}";
        _logger.LogInformation(message);
        AddLogEntry(LogLevel.Info, "Conversion", message);
    }

    public void LogConversionProgress(string fileName, string operation, double progress)
    {
        var message = $"转换进度: {fileName} - {operation} ({progress:F1}%)";
        _logger.LogDebug(message);
        AddLogEntry(LogLevel.Debug, "Conversion", message);
    }

    public void LogConversionComplete(string fileName, bool success, TimeSpan duration, string? errorMessage = null)
    {
        var status = success ? "成功" : "失败";
        var message = $"转换完成: {fileName} - {status} (耗时: {duration.TotalSeconds:F2}秒)";
        
        if (!success && !string.IsNullOrEmpty(errorMessage))
        {
            message += $" - 错误: {errorMessage}";
        }

        var level = success ? LogLevel.Info : LogLevel.Error;
        _logger.Log(success ? Microsoft.Extensions.Logging.LogLevel.Information : Microsoft.Extensions.Logging.LogLevel.Error, message);
        AddLogEntry(level, "Conversion", message);
    }

    public void LogBatchStatistics(int totalFiles, int completedFiles, int failedFiles, TimeSpan totalDuration)
    {
        var message = $"批量转换完成 - 总计: {totalFiles}, 成功: {completedFiles}, 失败: {failedFiles}, 总耗时: {totalDuration.TotalSeconds:F2}秒";
        _logger.LogInformation(message);
        AddLogEntry(LogLevel.Info, "Batch", message);
    }

    public void ClearLogs()
    {
        while (_logs.TryDequeue(out _)) { }
    }

    public IEnumerable<LogEntry> GetLogs()
    {
        return _logs.ToArray();
    }

    private void AddLogEntry(LogLevel level, string category, string message, Exception? exception = null)
    {
        var timestamp = DateTime.Now;
        var logEntry = new LogEntry
        {
            Timestamp = timestamp,
            Level = level,
            Message = message,
            Exception = exception
        };

        _logs.Enqueue(logEntry);
        
        // Keep only the last 1000 log entries to prevent memory issues
        while (_logs.Count > 1000)
        {
            _logs.TryDequeue(out _);
        }

        var eventArgs = new LogEventArgs(timestamp, level, category, message, exception);
        LogEvent?.Invoke(this, eventArgs);
        LogReceived?.Invoke(this, eventArgs); // For backward compatibility
    }
}