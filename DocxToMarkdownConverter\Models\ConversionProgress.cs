namespace DocxToMarkdownConverter.Models;

public class ConversionProgress
{
    public string CurrentFile { get; set; } = string.Empty;
    public double ProgressPercentage { get; set; }
    public string CurrentOperation { get; set; } = string.Empty;
    public int ProcessedItems { get; set; }
    public int TotalItems { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}

public class BatchConversionProgress
{
    public int CompletedFiles { get; set; }
    public int TotalFiles { get; set; }
    public int SuccessfulFiles { get; set; }
    public int FailedFiles { get; set; }
    public double OverallProgress => TotalFiles > 0 ? (double)CompletedFiles / TotalFiles * 100 : 0;
    public ConversionProgress? CurrentFileProgress { get; set; }
    public TimeSpan ElapsedTime { get; set; }
    public TimeSpan? EstimatedTimeRemaining { get; set; }
}