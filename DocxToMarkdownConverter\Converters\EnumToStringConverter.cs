using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Windows.Data;
using DocxToMarkdownConverter.Models;

namespace DocxToMarkdownConverter.Converters;

/// <summary>
/// 将枚举值转换为字符串，用于ComboBox绑定
/// </summary>
public class EnumToStringConverter : IValueConverter
{
    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ImageFormat imageFormat)
        {
            return imageFormat.ToString();
        }

        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string stringValue && targetType == typeof(ImageFormat))
        {
            if (Enum.TryParse<ImageFormat>(stringValue, true, out var result))
            {
                return result;
            }
        }

        return System.Windows.Data.Binding.DoNothing;
    }
}

/// <summary>
/// ImageFormat枚举的显示名称转换器
/// </summary>
public class ImageFormatDisplayConverter : IValueConverter
{
    private static readonly Dictionary<ImageFormat, string> DisplayNames = new()
    {
        { ImageFormat.Png, "PNG" },
        { ImageFormat.Jpg, "JPEG" },
        { ImageFormat.Gif, "GIF" },
        { ImageFormat.Bmp, "BMP" }
    };

    public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is ImageFormat format && DisplayNames.TryGetValue(format, out var displayName))
        {
            return displayName;
        }

        return value?.ToString() ?? string.Empty;
    }

    public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
    {
        if (value is string displayName)
        {
            var kvp = DisplayNames.FirstOrDefault(x => x.Value == displayName);
            if (!kvp.Equals(default(KeyValuePair<ImageFormat, string>)))
            {
                return kvp.Key;
            }
        }

        return System.Windows.Data.Binding.DoNothing;
    }
}
