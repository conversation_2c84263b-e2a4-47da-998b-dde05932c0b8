<UserControl x:Class="DocxToMarkdownConverter.Views.SettingsView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             xmlns:controls="clr-namespace:DocxToMarkdownConverter.Controls"
             xmlns:converters="clr-namespace:DocxToMarkdownConverter.Converters"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="800">

    <UserControl.Resources>
        <converters:EnumToStringConverter x:Key="EnumToStringConverter"/>
        <converters:ImageFormatDisplayConverter x:Key="ImageFormatDisplayConverter"/>
    </UserControl.Resources>

    <ScrollViewer Style="{StaticResource SmoothScrollViewerStyle}"
                  Padding="24"
                  VerticalScrollBarVisibility="Auto"
                  HorizontalScrollBarVisibility="Disabled"
                  CanContentScroll="False"
                  PanningMode="VerticalOnly"
                  ScrollViewer.IsDeferredScrollingEnabled="False">
        <StackPanel MaxWidth="600"
                    Style="{StaticResource OptimizedSettingsStackPanel}"
                    ScrollViewer.CanContentScroll="False">
            
            <!-- Header -->
            <TextBlock Text="{DynamicResource Navigation.Settings}"
                       Style="{StaticResource MaterialDesignHeadline4TextBlock}"
                       Margin="0,0,0,32"/>
            
            <!-- Conversion Options Section -->
            <materialDesign:Card Style="{StaticResource OptimizedSettingsCardStyle}">
                <StackPanel>
                    <TextBlock Text="{DynamicResource Conversion.Options}"
                               Style="{StaticResource SettingsCardHeaderStyle}"/>

                    <!-- Nested Cards Container -->
                    <StackPanel>
                        <!-- Output Settings Card -->
                        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource Conversion.OutputSettings}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                                           Margin="0,0,0,12"/>

                                <!-- Output Directory -->
                                <Grid Margin="0,0,0,12">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                    </Grid.ColumnDefinitions>

                                    <TextBox Grid.Column="0"
                                             Text="{Binding ConversionOptions.OutputDirectory, UpdateSourceTrigger=PropertyChanged}"
                                             materialDesign:HintAssist.Hint="{DynamicResource Conversion.OutputDirectory}"
                                             Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                             Foreground="{DynamicResource AppTextPrimaryBrush}"
                                             Margin="0,0,8,0"/>

                                    <Button Grid.Column="1"
                                            Command="{Binding SelectOutputDirectoryCommand}"
                                            Style="{StaticResource MaterialDesignOutlinedButton}"
                                            Content="{DynamicResource Button.Browse}"
                                            Foreground="{DynamicResource AppTextPrimaryBrush}"
                                            BorderBrush="{DynamicResource AppBorderBrush}"
                                            Padding="16,8"/>
                                </Grid>

                                <!-- Image Directory -->
                                <TextBox Text="{Binding ConversionOptions.ImageDirectory, UpdateSourceTrigger=PropertyChanged}"
                                         materialDesign:HintAssist.Hint="{DynamicResource Conversion.ImageDirectory}"
                                         Style="{StaticResource MaterialDesignOutlinedTextBox}"
                                         Foreground="{DynamicResource AppTextPrimaryBrush}"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Conversion Settings Card -->
                        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource Conversion.ConversionSettings}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                                           Margin="0,0,0,12"/>

                                <!-- Conversion Options Checkboxes -->
                                <CheckBox IsChecked="{Binding ConversionOptions.ExtractImages}"
                                          Content="{DynamicResource Conversion.ExtractImages}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                                          Margin="0,0,0,8"/>

                                <CheckBox IsChecked="{Binding ConversionOptions.ConvertTables}"
                                          Content="{DynamicResource Conversion.ConvertTables}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                                          Margin="0,0,0,8"/>

                                <CheckBox IsChecked="{Binding ConversionOptions.ProcessFormulas}"
                                          Content="{DynamicResource Conversion.ProcessFormulas}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Foreground="{DynamicResource AppTextPrimaryBrush}"
                                          Margin="0,0,0,8"/>

                                <CheckBox IsChecked="{Binding ConversionOptions.PreserveFormatting}"
                                          Content="{DynamicResource Conversion.PreserveFormatting}"
                                          Style="{StaticResource MaterialDesignCheckBox}"
                                          Foreground="{DynamicResource AppTextPrimaryBrush}"/>
                            </StackPanel>
                        </materialDesign:Card>

                        <!-- Image Settings Card -->
                        <materialDesign:Card Style="{StaticResource SettingGroupStyle}">
                            <StackPanel>
                                <TextBlock Text="{DynamicResource Conversion.ImageSettings}"
                                           Style="{StaticResource MaterialDesignSubtitle1TextBlock}"
                                           Foreground="{DynamicResource AppTextPrimaryBrush}"
                                           Margin="0,0,0,12"/>

                                <!-- Image Format -->
                                <ComboBox SelectedItem="{Binding ConversionOptions.ImageFormat}"
                                          ItemsSource="{Binding AvailableImageFormats}"
                                          Style="{StaticResource MaterialDesignOutlinedComboBox}"
                                          materialDesign:HintAssist.Hint="{DynamicResource Conversion.SelectImageFormat}">
                                    <ComboBox.ItemTemplate>
                                        <DataTemplate>
                                            <TextBlock Text="{Binding Converter={StaticResource ImageFormatDisplayConverter}}"
                                                       Foreground="{DynamicResource AppTextPrimaryBrush}"/>
                                        </DataTemplate>
                                    </ComboBox.ItemTemplate>
                                </ComboBox>
                            </StackPanel>
                        </materialDesign:Card>
                    </StackPanel>
                </StackPanel>
            </materialDesign:Card>
            
            <!-- Theme Settings Section -->
            <materialDesign:Card Style="{StaticResource OptimizedSettingsCardStyle}">
                <StackPanel>
                    <TextBlock Text="{DynamicResource Settings.Theme}"
                               Style="{StaticResource SettingsCardHeaderStyle}"/>

                    <ContentPresenter Content="{Binding ThemeControl}">
                        <ContentPresenter.Style>
                            <Style TargetType="ContentPresenter">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding ThemeControl}" Value="{x:Null}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock Text="正在加载主题设置..."
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource AppTextSecondaryBrush}"
                                                           Margin="16"/>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ContentPresenter.Style>
                    </ContentPresenter>
                </StackPanel>
            </materialDesign:Card>

            <!-- Language Settings -->
            <materialDesign:Card Style="{StaticResource OptimizedSettingsCardStyle}">
                <StackPanel>
                    <TextBlock Text="{DynamicResource Settings.Language}"
                               Style="{StaticResource SettingsCardHeaderStyle}"/>

                    <!-- Language Control -->
                    <ContentPresenter Content="{Binding LanguageControl}">
                        <ContentPresenter.Style>
                            <Style TargetType="ContentPresenter">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding LanguageControl}" Value="{x:Null}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock Text="正在加载语言设置..."
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource AppTextSecondaryBrush}"
                                                           Margin="16"/>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ContentPresenter.Style>
                    </ContentPresenter>
                </StackPanel>
            </materialDesign:Card>

            <!-- Performance Settings -->
            <materialDesign:Card Style="{StaticResource OptimizedSettingsCardStyle}">
                <StackPanel>
                    <TextBlock Text="性能设置"
                               Style="{StaticResource SettingsCardHeaderStyle}"/>

                    <!-- Performance Control -->
                    <ContentPresenter Content="{Binding PerformanceControl}">
                        <ContentPresenter.Style>
                            <Style TargetType="ContentPresenter">
                                <Style.Triggers>
                                    <DataTrigger Binding="{Binding PerformanceControl}" Value="{x:Null}">
                                        <Setter Property="Content">
                                            <Setter.Value>
                                                <TextBlock Text="正在加载性能设置..."
                                                           HorizontalAlignment="Center"
                                                           Foreground="{DynamicResource AppTextSecondaryBrush}"
                                                           Margin="16"/>
                                            </Setter.Value>
                                        </Setter>
                                    </DataTrigger>
                                </Style.Triggers>
                            </Style>
                        </ContentPresenter.Style>
                    </ContentPresenter>
                </StackPanel>
            </materialDesign:Card>

        </StackPanel>
    </ScrollViewer>
</UserControl>